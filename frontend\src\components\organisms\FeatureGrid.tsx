import React from "react";
import { clsx } from "clsx";
import { FeatureCard, type FeatureCardProps } from "../atoms/FeatureCard";
import { Badge } from "../atoms/Badge";

export interface FeatureGridProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Grid layout variant */
  layout?: "grid" | "masonry" | "list" | "carousel";
  /** Number of columns for grid layout */
  columns?: 1 | 2 | 3 | 4 | 6;
  /** Grid spacing */
  spacing?: "tight" | "normal" | "loose";
  /** Optional section title */
  title?: string;
  /** Optional section subtitle */
  subtitle?: string;
  /** Optional section badge/announcement */
  badge?: {
    text: string;
    variant?: "primary" | "success" | "warning";
    icon?: React.ReactNode;
  };
  /** Array of features to display */
  features: Array<
    FeatureCardProps & {
      id: string;
    }
  >;
  /** Feature card variant to apply to all cards */
  cardVariant?: FeatureCardProps["variant"];
  /** Feature card size to apply to all cards */
  cardSize?: FeatureCardProps["size"];
  /** Feature card orientation to apply to all cards */
  cardOrientation?: FeatureCardProps["orientation"];
  /** Whether feature cards should be interactive */
  interactive?: boolean;
  /** Custom render function for individual features */
  renderFeature?: (
    feature: FeatureGridProps["features"][0],
    index: number,
  ) => React.ReactNode;
  /** Show more/less functionality */
  collapsible?: {
    initialCount: number;
    showMoreText?: string;
    showLessText?: string;
  };
}

const layoutClasses = {
  grid: "grid gap-6",
  masonry: "columns-1 md:columns-2 lg:columns-3 gap-6 space-y-6",
  list: "space-y-4",
  carousel: "flex gap-6 overflow-x-auto pb-4 snap-x snap-mandatory",
};

const columnClasses = {
  1: "grid-cols-1",
  2: "grid-cols-1 md:grid-cols-2",
  3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
  4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
  6: "grid-cols-2 md:grid-cols-3 lg:grid-cols-6",
};

const spacingClasses = {
  tight: "gap-4",
  normal: "gap-6",
  loose: "gap-8",
};

export const FeatureGrid: React.FC<FeatureGridProps> = ({
  layout = "grid",
  columns = 3,
  spacing = "normal",
  title,
  subtitle,
  badge,
  features,
  cardVariant = "default",
  cardSize = "md",
  cardOrientation = "vertical",
  interactive = false,
  renderFeature,
  collapsible,
  className,
  ...props
}) => {
  const [showAll, setShowAll] = React.useState(!collapsible);
  const [isExpanded, setIsExpanded] = React.useState(false);

  const displayedFeatures = React.useMemo(() => {
    if (!collapsible || showAll) return features;
    return features.slice(0, collapsible.initialCount);
  }, [features, collapsible, showAll]);

  const handleToggleExpand = () => {
    setShowAll(!showAll);
    setIsExpanded(!isExpanded);
  };

  const renderHeader = () => {
    if (!title && !subtitle && !badge) return null;

    return (
      <div className="text-center mb-12">
        {badge && (
          <div className="mb-4 flex justify-center">
            <Badge
              variant={badge.variant || "primary"}
              size="md"
              icon={badge.icon}
              className="px-4 py-2 text-sm font-medium"
            >
              {badge.text}
            </Badge>
          </div>
        )}

        {title && (
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            {title}
          </h2>
        )}

        {subtitle && (
          <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
            {subtitle}
          </p>
        )}
      </div>
    );
  };

  const renderFeatureCard = (
    feature: FeatureGridProps["features"][0],
    index: number,
  ) => {
    if (renderFeature) {
      return renderFeature(feature, index);
    }

    const cardProps = {
      ...feature,
      variant: feature.variant || cardVariant,
      size: feature.size || cardSize,
      orientation: feature.orientation || cardOrientation,
      interactive:
        feature.interactive !== undefined ? feature.interactive : interactive,
    };

    return (
      <div
        key={feature.id}
        className={clsx(
          layout === "carousel" && "flex-shrink-0 w-80 snap-start",
          layout === "masonry" && "break-inside-avoid",
        )}
      >
        <FeatureCard {...cardProps} />
      </div>
    );
  };

  const renderToggleButton = () => {
    if (!collapsible || features.length <= collapsible.initialCount)
      return null;

    return (
      <div className="mt-8 text-center">
        <button
          onClick={handleToggleExpand}
          className="inline-flex items-center px-6 py-3 text-base font-medium text-primary-600 bg-white border-2 border-primary-200 rounded-lg hover:bg-primary-50 hover:border-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200"
        >
          {showAll
            ? collapsible.showLessText || "Show Less"
            : collapsible.showMoreText ||
              `Show All ${features.length} Features`}
        </button>
      </div>
    );
  };

  const gridClasses = clsx(
    layout === "grid" && layoutClasses.grid,
    layout === "grid" && columnClasses[columns],
    layout === "grid" && spacingClasses[spacing],
    layout === "masonry" && layoutClasses.masonry,
    layout === "list" && layoutClasses.list,
    layout === "carousel" && layoutClasses.carousel,
  );

  return (
    <div className={clsx("w-full", className)} {...props}>
      {renderHeader()}

      <div className={gridClasses}>
        {displayedFeatures.map((feature, index) =>
          renderFeatureCard(feature, index),
        )}
      </div>

      {renderToggleButton()}
    </div>
  );
};
