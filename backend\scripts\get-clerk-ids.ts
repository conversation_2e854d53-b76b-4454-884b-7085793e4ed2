#!/usr/bin/env ts-node

/**
 * Helper script to get Clerk User IDs for seeding
 *
 * This script fetches users from your Clerk instance and displays their IDs
 * so you can easily copy them into the seed script.
 *
 * Usage: npm run get-clerk-ids
 */

import { clerkClient } from '@clerk/clerk-sdk-node';
import { config } from 'dotenv';
import path from 'path';

// Load environment variables
config({ path: path.resolve(__dirname, '../.env') });

async function getClerkIds() {
  try {
    console.log('🔍 Fetching users from Clerk...\n');

    // Get all users from Clerk
    const users = await clerkClient.users.getUserList({
      limit: 10,
      orderBy: 'created_at',
    });

    if (users.data.length === 0) {
      console.log('❌ No users found in Clerk instance');
      return;
    }

    console.log('📋 Found users in Clerk:\n');

    users.data.forEach((user, index) => {
      const primaryEmail = user.emailAddresses.find(
        (email) => email.id === user.primaryEmailAddressId
      );
      const email = primaryEmail?.emailAddress || 'No email';

      console.log(`${index + 1}. User ID: ${user.id}`);
      console.log(`   Email: ${email}`);
      console.log(
        `   Name: ${user.firstName || 'N/A'} ${user.lastName || 'N/A'}`
      );
      console.log(`   Username: ${user.username || 'N/A'}`);
      console.log(
        `   Created: ${new Date(user.createdAt).toLocaleDateString()}`
      );
      console.log('');
    });

    console.log('📝 To update the seed script:');
    console.log('1. Open backend/prisma/seed.ts');
    console.log('2. Replace the placeholder Clerk IDs:');
    console.log('   - "dev-system-admin-clerk-id" → User ID for testadmin');
    console.log(
      '   - "dev-company-admin-clerk-id" → User ID for dev-companyadmin'
    );
    console.log('   - "dev-company-tech-clerk-id" → User ID for devtech');
    console.log('3. Run: npm run db:seed');
  } catch (error) {
    console.error('❌ Error fetching Clerk users:', error);

    if (error instanceof Error && error.message.includes('Unauthorized')) {
      console.log(
        '\n💡 Make sure your CLERK_SECRET_KEY is set correctly in backend/.env'
      );
    }
  }
}

// Run the script
getClerkIds()
  .catch(console.error)
  .finally(() => process.exit(0));
