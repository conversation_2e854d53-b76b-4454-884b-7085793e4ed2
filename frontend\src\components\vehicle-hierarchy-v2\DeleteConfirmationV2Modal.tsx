import React from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { <PERSON><PERSON>, Button, Alert } from "../index";
import {
  useTypedApi,
  type VehicleBrandV2,
  type VehicleSubBrandV2,
  type VehicleModelV2,
} from "../../services/api-client";
import { AlertTriangle, Trash2 } from "lucide-react";

type DeleteItem = VehicleBrandV2 | VehicleSubBrandV2 | VehicleModelV2;

interface DeleteConfirmationV2ModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: DeleteItem | null;
  itemType: "brand" | "sub-brand" | "model";
  cascadeInfo?: {
    subBrands?: number;
    models?: number;
    yearAssociations?: number;
  };
}

export const DeleteConfirmationV2Modal: React.FC<
  DeleteConfirmationV2ModalProps
> = ({ isOpen, onClose, item, itemType, cascadeInfo }) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const deleteMutation = useMutation({
    mutationFn: async () => {
      if (!item) throw new Error("No item selected for deletion");

      switch (itemType) {
        case "brand":
          return api.vehicleHierarchyV2.deleteBrand(item.id);
        case "sub-brand":
          return api.vehicleHierarchyV2.deleteSubBrand(item.id);
        case "model":
          return api.vehicleHierarchyV2.deleteModel(item.id);
        default:
          throw new Error("Invalid item type");
      }
    },
    onSuccess: () => {
      toast.success(`${getItemTypeLabel(itemType)} deleted successfully`);
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-v2"] });
      onClose();
    },
    onError: (error: Error) => {
      toast.error(
        error.message ||
          `Failed to delete ${getItemTypeLabel(itemType).toLowerCase()}`,
      );
    },
  });

  const handleConfirmDelete = () => {
    deleteMutation.mutate();
  };

  const getItemTypeLabel = (type: string): string => {
    switch (type) {
      case "brand":
        return "Brand";
      case "sub-brand":
        return "Sub-Brand";
      case "model":
        return "Model";
      default:
        return "Item";
    }
  };

  const getItemDisplayName = (): string => {
    if (!item) return "";

    switch (itemType) {
      case "brand":
        return item.name;
      case "sub-brand": {
        const subBrand = item as VehicleSubBrandV2;
        return `${subBrand.brand?.name} → ${subBrand.name}`;
      }
      case "model": {
        const model = item as VehicleModelV2;
        return `${model.subBrand?.brand?.name} → ${model.subBrand?.name} → ${model.name}`;
      }
      default:
        return item.name;
    }
  };

  const getCascadeWarning = (): string | null => {
    if (!cascadeInfo) return null;

    const warnings: string[] = [];

    if (cascadeInfo.subBrands && cascadeInfo.subBrands > 0) {
      warnings.push(
        `${cascadeInfo.subBrands} sub-brand${cascadeInfo.subBrands > 1 ? "s" : ""}`,
      );
    }

    if (cascadeInfo.models && cascadeInfo.models > 0) {
      warnings.push(
        `${cascadeInfo.models} model${cascadeInfo.models > 1 ? "s" : ""}`,
      );
    }

    if (cascadeInfo.yearAssociations && cascadeInfo.yearAssociations > 0) {
      warnings.push(
        `${cascadeInfo.yearAssociations} year association${cascadeInfo.yearAssociations > 1 ? "s" : ""}`,
      );
    }

    if (warnings.length === 0) return null;

    return `This will also delete ${warnings.join(", ")}.`;
  };

  const hasBlockingDependencies = (): boolean => {
    if (!cascadeInfo) return false;

    // For brands: can't delete if has sub-brands
    if (
      itemType === "brand" &&
      cascadeInfo.subBrands &&
      cascadeInfo.subBrands > 0
    ) {
      return true;
    }

    // For sub-brands: can't delete if has models
    if (
      itemType === "sub-brand" &&
      cascadeInfo.models &&
      cascadeInfo.models > 0
    ) {
      return true;
    }

    // For models: can't delete if has year associations
    if (
      itemType === "model" &&
      cascadeInfo.yearAssociations &&
      cascadeInfo.yearAssociations > 0
    ) {
      return true;
    }

    return false;
  };

  const getBlockingMessage = (): string | null => {
    if (!hasBlockingDependencies()) return null;

    switch (itemType) {
      case "brand":
        return `Cannot delete this brand because it has ${cascadeInfo?.subBrands} sub-brand${cascadeInfo?.subBrands !== 1 ? "s" : ""}. Please delete all sub-brands first.`;
      case "sub-brand":
        return `Cannot delete this sub-brand because it has ${cascadeInfo?.models} model${cascadeInfo?.models !== 1 ? "s" : ""}. Please delete all models first.`;
      case "model":
        return `Cannot delete this model because it has ${cascadeInfo?.yearAssociations} year association${cascadeInfo?.yearAssociations !== 1 ? "s" : ""}. Please remove all year associations first.`;
      default:
        return "Cannot delete this item due to existing dependencies.";
    }
  };

  if (!item) return null;

  const isBlocked = hasBlockingDependencies();
  const cascadeWarning = getCascadeWarning();
  const blockingMessage = getBlockingMessage();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Delete ${getItemTypeLabel(itemType)}`}
      size="md"
    >
      <div className="space-y-6">
        <div className="flex items-center space-x-3 text-gray-600">
          <Trash2 className="h-5 w-5 text-red-500" />
          <span>
            Confirm deletion of {getItemTypeLabel(itemType).toLowerCase()}
          </span>
        </div>

        <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <p className="text-sm font-medium text-gray-700 mb-1">
            {getItemTypeLabel(itemType)} to delete:
          </p>
          <p className="text-gray-900 font-medium">{getItemDisplayName()}</p>
        </div>

        {isBlocked ? (
          <Alert variant="error">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-red-900">Cannot Delete</h3>
                <p className="text-red-800 mt-1">{blockingMessage}</p>
              </div>
            </div>
          </Alert>
        ) : (
          <Alert variant="warning">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-orange-900">
                  Confirm Deletion
                </h3>
                <p className="text-orange-800 mt-1">
                  Are you sure you want to delete this{" "}
                  {getItemTypeLabel(itemType).toLowerCase()}?
                  {cascadeWarning && (
                    <span className="block mt-2 font-medium">
                      ⚠️ {cascadeWarning}
                    </span>
                  )}
                  <span className="block mt-2">
                    This action cannot be undone.
                  </span>
                </p>
              </div>
            </div>
          </Alert>
        )}

        {deleteMutation.isError && (
          <Alert variant="error">
            <p>
              Failed to delete {getItemTypeLabel(itemType).toLowerCase()}.
              Please try again.
            </p>
          </Alert>
        )}

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={deleteMutation.isPending}
          >
            Cancel
          </Button>
          {!isBlocked && (
            <Button
              type="button"
              variant="outline"
              onClick={handleConfirmDelete}
              disabled={deleteMutation.isPending}
              className="border-red-300 text-red-700 hover:bg-red-50"
            >
              {deleteMutation.isPending
                ? "Deleting..."
                : `Delete ${getItemTypeLabel(itemType)}`}
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};
