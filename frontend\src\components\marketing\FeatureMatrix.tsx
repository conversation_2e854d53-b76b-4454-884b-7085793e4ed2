import React from "react";
import { Card } from "../atoms/Card";
import { Badge } from "../atoms/Badge";
import { SetupRequiredBadge } from "../atoms/SetupRequiredBadge";
import type { FeatureGroup, PricingTier } from "../../types/pricing.types";
import { Check, X } from "lucide-react";
import { clsx } from "clsx";

interface FeatureMatrixProps {
  features: FeatureGroup[];
  tiers: PricingTier[];
}

const renderFeatureValue = (value: boolean | string) => {
  if (typeof value === "boolean") {
    return value ? (
      <Check className="h-5 w-5 text-green-600 mx-auto" />
    ) : (
      <X className="h-5 w-5 text-gray-400 mx-auto" />
    );
  }

  // For string values like "Basic", "Enhanced"
  return (
    <Badge variant="primary" size="sm">
      {value}
    </Badge>
  );
};

export const FeatureMatrix: React.FC<FeatureMatrixProps> = ({
  features,
  tiers,
}) => {
  return (
    <Card className="overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* Header */}
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 w-1/3">
                Features
              </th>
              {tiers.map((tier) => (
                <th
                  key={tier.name}
                  className="px-6 py-4 text-center text-sm font-semibold w-1/6 text-gray-900"
                >
                  {tier.name}
                </th>
              ))}
            </tr>
          </thead>

          {/* Feature Groups */}
          <tbody className="divide-y divide-gray-200">
            {features.map((group, groupIndex) => (
              <React.Fragment key={group.groupName}>
                {/* Group Header */}
                <tr className="bg-gray-25">
                  <td
                    colSpan={tiers.length + 1}
                    className="px-6 py-4 text-sm font-semibold text-gray-900 bg-gray-50"
                  >
                    {group.groupName}
                  </td>
                </tr>

                {/* Group Features */}
                {group.features.map((feature, featureIndex) => (
                  <tr
                    key={`${group.groupName}-${feature.name}`}
                    className={clsx(
                      "hover:bg-gray-50",
                      (groupIndex + featureIndex) % 2 === 0
                        ? "bg-white"
                        : "bg-gray-25",
                    )}
                  >
                    <td className="px-6 py-4">
                      <div>
                        <div className="text-sm font-medium text-gray-900 flex items-center gap-2">
                          <span>{feature.name}</span>
                          {feature.setupFee && <SetupRequiredBadge />}
                        </div>
                        <div className="text-sm text-gray-600 mt-1">
                          {feature.description}
                        </div>
                      </div>
                    </td>

                    {/* Feature availability for each tier */}
                    <td className="px-6 py-4 text-center">
                      {renderFeatureValue(feature.basic)}
                    </td>
                    <td className="px-6 py-4 text-center">
                      {renderFeatureValue(feature.plus)}
                    </td>
                    <td className="px-6 py-4 text-center">
                      {renderFeatureValue(feature.pro)}
                    </td>
                    <td className="px-6 py-4 text-center">
                      {renderFeatureValue(feature.enterprise)}
                    </td>
                  </tr>
                ))}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </Card>
  );
};
