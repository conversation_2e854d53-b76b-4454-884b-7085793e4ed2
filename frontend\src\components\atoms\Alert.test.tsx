import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import { Alert } from "./Alert";

describe("Alert", () => {
  describe("Rendering", () => {
    it("should render alert with default info variant", () => {
      render(<Alert>This is an info alert</Alert>);

      const alert = screen.getByText("This is an info alert");
      expect(alert).toBeInTheDocument();

      // Get the outermost container (the alert root)
      const container = alert.closest('[class*="border"]');
      expect(container).toHaveClass(
        "border",
        "rounded-xl",
        "p-5",
        "flex",
        "items-start",
        "gap-4",
      );
      expect(container).toHaveClass(
        "bg-gradient-to-r",
        "from-primary-50",
        "to-primary-50/80",
      );
      expect(container).toHaveClass(
        "border-primary-200",
        "shadow-xs",
        "ring-1",
        "ring-primary-200/50",
      );
    });

    it("should render alert with custom className", () => {
      render(<Alert className="custom-class">Custom alert</Alert>);

      const alert = screen.getByText("Custom alert");
      const container = alert.closest('[class*="custom-class"]');
      expect(container).toHaveClass("custom-class");
    });
  });

  describe("Variants", () => {
    it("should render info variant with primary colors", () => {
      render(<Alert variant="info">Info message</Alert>);

      const alert = screen.getByText("Info message");
      const container = alert.closest('[class*="border"]');

      expect(container).toHaveClass(
        "bg-gradient-to-r",
        "from-primary-50",
        "to-primary-50/80",
      );
      expect(container).toHaveClass(
        "border-primary-200",
        "ring-primary-200/50",
      );
      expect(alert).toHaveClass("text-primary-800");
    });

    it("should render success variant with green colors", () => {
      render(<Alert variant="success">Success message</Alert>);

      const alert = screen.getByText("Success message");
      const container = alert.closest('[class*="border"]');

      expect(container).toHaveClass(
        "bg-gradient-to-r",
        "from-success-50",
        "to-success-50/80",
      );
      expect(container).toHaveClass(
        "border-success-200",
        "ring-success-200/50",
      );
      expect(alert).toHaveClass("text-success-800");
    });

    it("should render warning variant with yellow colors", () => {
      render(<Alert variant="warning">Warning message</Alert>);

      const alert = screen.getByText("Warning message");
      const container = alert.closest('[class*="border"]');

      expect(container).toHaveClass(
        "bg-gradient-to-r",
        "from-warning-50",
        "to-warning-50/80",
      );
      expect(container).toHaveClass(
        "border-warning-200",
        "ring-warning-200/50",
      );
      expect(alert).toHaveClass("text-warning-800");
    });

    it("should render error variant with red colors", () => {
      render(<Alert variant="error">Error message</Alert>);

      const alert = screen.getByText("Error message");
      const container = alert.closest('[class*="border"]');

      expect(container).toHaveClass(
        "bg-gradient-to-r",
        "from-error-50",
        "to-error-50/80",
      );
      expect(container).toHaveClass("border-error-200", "ring-error-200/50");
      expect(alert).toHaveClass("text-error-800");
    });
  });

  describe("Icons", () => {
    it("should render info icon for info variant", () => {
      render(<Alert variant="info">Info with icon</Alert>);

      const container = screen
        .getByText("Info with icon")
        .closest('[class*="border"]');
      const icon = container?.querySelector("svg");

      expect(icon).toBeInTheDocument();
      expect(icon).toHaveClass(
        "h-5",
        "w-5",
        "mt-0.5",
        "flex-shrink-0",
        "text-primary-600",
      );
    });

    it("should render success icon for success variant", () => {
      render(<Alert variant="success">Success with icon</Alert>);

      const container = screen
        .getByText("Success with icon")
        .closest('[class*="border"]');
      const icon = container?.querySelector("svg");

      expect(icon).toBeInTheDocument();
      expect(icon).toHaveClass("text-success-600");
    });

    it("should render warning icon for warning variant", () => {
      render(<Alert variant="warning">Warning with icon</Alert>);

      const container = screen
        .getByText("Warning with icon")
        .closest('[class*="border"]');
      const icon = container?.querySelector("svg");

      expect(icon).toBeInTheDocument();
      expect(icon).toHaveClass("text-warning-600");
    });

    it("should render error icon for error variant", () => {
      render(<Alert variant="error">Error with icon</Alert>);

      const container = screen
        .getByText("Error with icon")
        .closest('[class*="border"]');
      const icon = container?.querySelector("svg");

      expect(icon).toBeInTheDocument();
      expect(icon).toHaveClass("text-error-600");
    });
  });

  describe("Title", () => {
    it("should render alert with title", () => {
      render(
        <Alert title="Alert Title" variant="info">
          Alert content
        </Alert>,
      );

      const title = screen.getByText("Alert Title");
      const content = screen.getByText("Alert content");

      expect(title).toBeInTheDocument();
      expect(title).toHaveClass(
        "text-sm",
        "text-primary-900",
        "font-semibold",
        "tracking-tight",
      );
      expect(content).toBeInTheDocument();
    });

    it("should render alert without title when not provided", () => {
      render(<Alert>Content only</Alert>);

      const content = screen.getByText("Content only");
      expect(content).toBeInTheDocument();
      expect(content).toHaveClass("text-sm", "leading-relaxed");
    });

    it("should apply correct title styling for different variants", () => {
      render(
        <Alert title="Success Title" variant="success">
          Success content
        </Alert>,
      );

      const title = screen.getByText("Success Title");
      expect(title).toHaveClass(
        "text-success-900",
        "font-semibold",
        "tracking-tight",
      );
    });
  });

  describe("Dismissible Alert", () => {
    it("should render dismissible alert with close button", () => {
      const handleDismiss = vi.fn();
      render(
        <Alert dismissible onDismiss={handleDismiss}>
          Dismissible alert
        </Alert>,
      );

      const alert = screen.getByText("Dismissible alert");
      const dismissButton = screen.getByRole("button", { name: "Dismiss" });

      expect(alert).toBeInTheDocument();
      expect(dismissButton).toBeInTheDocument();
      expect(dismissButton).toHaveClass(
        "hover:opacity-75",
        "focus:outline-none",
      );
      expect(dismissButton).toHaveClass(
        "focus:ring-2",
        "focus:ring-current",
        "focus:ring-opacity-20",
      );
    });

    it("should call onDismiss when dismiss button is clicked", () => {
      const handleDismiss = vi.fn();
      render(
        <Alert dismissible onDismiss={handleDismiss}>
          Clickable dismiss
        </Alert>,
      );

      const dismissButton = screen.getByRole("button", { name: "Dismiss" });
      dismissButton.click();

      expect(handleDismiss).toHaveBeenCalledOnce();
    });

    it("should not render dismiss button when not dismissible", () => {
      render(<Alert>Non-dismissible alert</Alert>);

      const alert = screen.getByText("Non-dismissible alert");
      expect(alert).toBeInTheDocument();
      expect(
        screen.queryByRole("button", { name: "Dismiss" }),
      ).not.toBeInTheDocument();
    });

    it("should not render dismiss button when dismissible but no onDismiss handler", () => {
      render(<Alert dismissible>Alert without handler</Alert>);

      const alert = screen.getByText("Alert without handler");
      expect(alert).toBeInTheDocument();
      expect(
        screen.queryByRole("button", { name: "Dismiss" }),
      ).not.toBeInTheDocument();
    });
  });

  describe("Enhanced Styling", () => {
    it("should have transition classes", () => {
      render(<Alert>Animated alert</Alert>);

      const alert = screen.getByText("Animated alert");
      const container = alert.closest('[class*="border"]');
      expect(container).toHaveClass("transition-all", "duration-300");
    });

    it("should have enhanced content spacing", () => {
      render(<Alert title="Spaced Alert">Content with spacing</Alert>);

      const content = screen.getByText("Content with spacing");
      const contentContainer = content.parentElement;
      expect(contentContainer).toHaveClass("flex-1", "space-y-1");
      expect(content).toHaveClass("leading-relaxed");
    });
  });

  describe("Complete Alert with All Features", () => {
    it("should render complete alert with title and dismiss functionality", () => {
      const handleDismiss = vi.fn();

      render(
        <Alert
          variant="warning"
          title="Complete Alert"
          dismissible
          onDismiss={handleDismiss}
        >
          This is a complete alert with all features enabled.
        </Alert>,
      );

      const title = screen.getByText("Complete Alert");
      const content = screen.getByText(
        "This is a complete alert with all features enabled.",
      );
      const dismissButton = screen.getByRole("button", { name: "Dismiss" });
      const container = content.closest('[class*="border"]');

      expect(title).toBeInTheDocument();
      expect(content).toBeInTheDocument();
      expect(dismissButton).toBeInTheDocument();

      // Verify warning variant styling
      expect(container).toHaveClass(
        "bg-gradient-to-r",
        "from-warning-50",
        "to-warning-50/80",
      );
      expect(title).toHaveClass("text-warning-900");
      expect(content).toHaveClass("text-warning-800");
    });
  });

  describe("Props", () => {
    it("should pass through HTML div props", () => {
      const handleClick = vi.fn();
      render(
        <Alert onClick={handleClick} data-testid="test-alert">
          Clickable alert
        </Alert>,
      );

      const alert = screen.getByTestId("test-alert");
      alert.click();
      expect(handleClick).toHaveBeenCalledOnce();
    });
  });
});
