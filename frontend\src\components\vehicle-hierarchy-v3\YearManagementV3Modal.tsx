import React from "react";
import { useTypedApi } from "../../services/api-client";
import type { VehicleYearV3 } from "../../services/api-client";
import { SimpleEntityModal } from "./SimpleEntityModal";
import { useBaseCRUD } from "../../hooks/useBaseCRUD";
import { createYearConfig } from "./configs/entity-configs";

interface YearManagementV3ModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const YearManagementV3Modal: React.FC<YearManagementV3ModalProps> = ({
  isOpen,
  onClose,
}) => {
  const api = useTypedApi();

  // Create Year configuration
  const yearConfig = createYearConfig(api);

  // Use base CRUD hook with Year configuration
  const crud = useBaseCRUD<VehicleYearV3>(yearConfig, isOpen);

  return (
    <SimpleEntityModal
      isOpen={isOpen}
      onClose={onClose}
      title={yearConfig.ui.title}
      entityName={yearConfig.entityName}
      icon={yearConfig.ui.icon}
      colorScheme={yearConfig.ui.colorScheme}
      placeholder={yearConfig.ui.placeholder}
      emptyStateMessage={yearConfig.ui.emptyStateMessage}
      crud={crud}
    />
  );
};

