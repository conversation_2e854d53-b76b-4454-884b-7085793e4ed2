import { describe, it, expect, beforeEach } from '@jest/globals';
import { BackendAuthContext } from '@tech-notes/shared';

import {
  createMockLogger,
  createMockPermissionService,
  testData,
  MockLogger,
  MockPermissionService,
} from '../__tests__/simple-mocks.js';

import {
  createSystemAdminMiddleware,
  createPermissionMiddleware,
} from './permission.middleware.js';

describe('RBAC Middleware', () => {
  let mockLogger: MockLogger;
  let mockPermissionService: MockPermissionService;
  let systemAdminContext: BackendAuthContext;
  let companyAdminContext: BackendAuthContext;

  beforeEach(() => {
    // Create simple, direct mocks
    mockLogger = createMockLogger();
    mockPermissionService = createMockPermissionService();

    // Mock auth contexts for tests
    systemAdminContext = {
      ...testData.BackendAuthContext,
      canBypassTenantScope: true, // System admin
    };

    companyAdminContext = {
      ...testData.BackendAuthContext,
      canBypassTenantScope: false, // Company admin
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createSystemAdminMiddleware', () => {
    it('should allow system admin to proceed', async () => {
      // Arrange
      const middleware = createSystemAdminMiddleware({
        permissionService: mockPermissionService as any,
        logger: mockLogger as any,
      });
      const mockReq = { user: systemAdminContext } as any;
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;
      const mockNext = jest.fn();

      // Act
      await middleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should deny non-system admin', async () => {
      // Arrange
      const middleware = createSystemAdminMiddleware({
        permissionService: mockPermissionService as any,
        logger: mockLogger as any,
      });
      const mockReq = { user: companyAdminContext } as any;
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;
      const mockNext = jest.fn();

      // Act
      await middleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Forbidden',
          message: 'System Administrator access required',
        })
      );
    });
  });

  describe('createPermissionMiddleware', () => {
    it('should allow user with required permission', async () => {
      // Arrange
      const middleware = createPermissionMiddleware({
        permissionService: mockPermissionService as any,
        logger: mockLogger as any,
      });
      const permissionMiddleware = middleware({
        resource: 'USER',
        action: 'READ',
      });

      // Mock user has permission
      jest
        .spyOn(mockPermissionService, 'hasPermission')
        .mockResolvedValue(true);

      const mockReq = { user: companyAdminContext } as any;
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;
      const mockNext = jest.fn();

      // Act
      await permissionMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should deny user without required permission', async () => {
      // Arrange
      const middleware = createPermissionMiddleware({
        permissionService: mockPermissionService as any,
        logger: mockLogger as any,
      });
      const permissionMiddleware = middleware({
        resource: 'USER',
        action: 'DELETE',
      });

      // Mock user lacks permission
      jest
        .spyOn(mockPermissionService, 'hasPermission')
        .mockResolvedValue(false);

      const mockReq = { user: companyAdminContext } as any;
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;
      const mockNext = jest.fn();

      // Act
      await permissionMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Forbidden',
          message: 'Insufficient permissions: USER:DELETE',
        })
      );
    });
  });
});
