// Auto-generated from CSV data - DO NOT EDIT MANUALLY
// Source: public/data/features.csv

import type { FeatureGroup } from '../types/pricing.types';

export const FEATURE_GROUPS: FeatureGroup[] = [
  {
    "groupName": "Team Management",
    "features": [
      {
        "name": "Admin Managed User Access",
        "description": "All user management handled by company administrator.",
        "basic": true,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "One-Time Signup Codes",
        "description": "Generate one-time codes for new users to register.",
        "basic": false,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "Self Signup Requests",
        "description": "Users can request access with admin approval required.",
        "basic": false,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "Automated Invite Links",
        "description": "Send branded signup invitations automatically.",
        "basic": false,
        "plus": false,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "Delegated User Management",
        "description": "Delegate user management to regional leads by location.",
        "basic": false,
        "plus": false,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "Advanced Security Policies",
        "description": "Federated signup and custom password expiration policies.",
        "basic": false,
        "plus": false,
        "pro": false,
        "enterprise": true,
        "includeOnCard": false,
        "setupFee": false
      }
    ]
  },
  {
    "groupName": "Reporting & Analytics",
    "features": [
      {
        "name": "Login Reports",
        "description": "Track user login frequency and identify adoption gaps.",
        "basic": true,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "User Engagement Reports",
        "description": "Understand user engagement patterns and content usage.",
        "basic": false,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "Most Accessed Content",
        "description": "Identify high-impact content to focus your efforts.",
        "basic": false,
        "plus": false,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "Location-Level Reports",
        "description": "Location admins get full reporting for their users.",
        "basic": false,
        "plus": false,
        "pro": false,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "Custom Reporting Dashboards",
        "description": "Create customized dashboards tailored to your needs.",
        "basic": false,
        "plus": false,
        "pro": false,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": true
      },
      {
        "name": "Data Export",
        "description": "Export all data to CSV and Excel formats.",
        "basic": false,
        "plus": false,
        "pro": false,
        "enterprise": true,
        "includeOnCard": false,
        "setupFee": false
      }
    ]
  },
  {
    "groupName": "Content & Metadata",
    "features": [
      {
        "name": "Custom Vehicle Groupings",
        "description": "Create and manage Year/Make/Model associations.",
        "basic": true,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": false,
        "setupFee": false
      },
      {
        "name": "Custom Filter and Search Tags",
        "description": "Create and manage tags to organize and surface content.",
        "basic": true,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "Custom Document Creation",
        "description": "In-app editor to create and manage content.",
        "basic": true,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": false,
        "setupFee": false
      },
      {
        "name": "Upload Your Own Content",
        "description": "Upload documents and tag them to your metadata.",
        "basic": true,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "VIN Upload",
        "description": "Automatically create Year/Make/Model combinations from VINs.",
        "basic": false,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": true
      },
      {
        "name": "Google Drive Integration",
        "description": "Link documents from Google Drive into Tech Notes.",
        "basic": false,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "AI Content Organization",
        "description": "Automatically create and apply metadata tags.",
        "basic": false,
        "plus": false,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "VIN-Specific Part Mapping",
        "description": "Automatically map and share VIN-level part lists.",
        "basic": false,
        "plus": false,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": true
      },
      {
        "name": "VIN-Specific Labor Rates",
        "description": "Automatically map and share VIN-level labor rates.",
        "basic": false,
        "plus": false,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": true
      },
      {
        "name": "Bulk Content Import",
        "description": "Process hundreds or thousands of documents at once.",
        "basic": false,
        "plus": false,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": true
      },
      {
        "name": "Auto Metadata Mapping",
        "description": "Auto-detect and map key metadata during file imports.",
        "basic": false,
        "plus": false,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": true
      },
      {
        "name": "Content Versioning",
        "description": "Track and manage content version history.",
        "basic": false,
        "plus": false,
        "pro": false,
        "enterprise": true,
        "includeOnCard": false,
        "setupFee": false
      }
    ]
  },
  {
    "groupName": "Push Notifications",
    "features": [
      {
        "name": "Announcement Push Notifications",
        "description": "Send push notification messages to users' devices.",
        "basic": true,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": false,
        "setupFee": false
      },
      {
        "name": "Password Reset Reminders",
        "description": "Automated reminders to reset passwords.",
        "basic": false,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": false,
        "setupFee": false
      }
    ]
  },
  {
    "groupName": "Integrations & Customization",
    "features": [
      {
        "name": "White-Label Branding",
        "description": "Customize the app with your company logo and brand colors.",
        "basic": false,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "VIN Decoding",
        "description": "Integrate with VIN decode APIs to automate vehicle data.",
        "basic": false,
        "plus": false,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": true
      },
      {
        "name": "API Access",
        "description": "Connect with your existing tools and systems.",
        "basic": false,
        "plus": false,
        "pro": true,
        "enterprise": true,
        "includeOnCard": false,
        "setupFee": true
      },
      {
        "name": "Custom Workflows",
        "description": "Build automated processes for your team.",
        "basic": false,
        "plus": false,
        "pro": true,
        "enterprise": true,
        "includeOnCard": false,
        "setupFee": false
      },
      {
        "name": "Single Sign-On",
        "description": "Single sign-on with your company directory.",
        "basic": false,
        "plus": false,
        "pro": false,
        "enterprise": true,
        "includeOnCard": false,
        "setupFee": false
      }
    ]
  },
  {
    "groupName": "Mobile App Access",
    "features": [
      {
        "name": "Mobile Optimized Design",
        "description": "User interface designed for shop use with large navigation.",
        "basic": true,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "Home Screen Icon",
        "description": "Save as a home screen icon with basic offline capabilities.",
        "basic": true,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": false,
        "setupFee": false
      },
      {
        "name": "Browse and Search",
        "description": "Browse and search by Year Make and Model.",
        "basic": true,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "QR Code Scanning",
        "description": "Scan QR codes to find linked content in the system.",
        "basic": true,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "VIN Scanning",
        "description": "Scan VIN tags to automatically find all related content.",
        "basic": false,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "Corporate Support Contact",
        "description": "Contact details for when the app doesn't have what you need.",
        "basic": false,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": false,
        "setupFee": false
      },
      {
        "name": "Offline Mode",
        "description": "Access content without an internet connection.",
        "basic": false,
        "plus": false,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      },
      {
        "name": "Native Mobile App",
        "description": "Enhanced mobile features and better offline support.",
        "basic": false,
        "plus": false,
        "pro": true,
        "enterprise": true,
        "includeOnCard": false,
        "setupFee": false
      },
      {
        "name": "In-App Support",
        "description": "Get support directly from engineering and corporate teams.",
        "basic": false,
        "plus": false,
        "pro": true,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": true
      },
      {
        "name": "AI Image Parts Search",
        "description": "Snap a picture of a part to get quick search results.",
        "basic": false,
        "plus": false,
        "pro": false,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": true
      }
    ]
  },
  {
    "groupName": "Onboarding & Support",
    "features": [
      {
        "name": "Email Support",
        "description": "Get help via email during business hours.",
        "basic": true,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": false,
        "setupFee": false
      },
      {
        "name": "Priority Support",
        "description": "Faster response times and dedicated support.",
        "basic": false,
        "plus": true,
        "pro": true,
        "enterprise": true,
        "includeOnCard": false,
        "setupFee": false
      },
      {
        "name": "Phone Support",
        "description": "Direct phone access to our support team.",
        "basic": false,
        "plus": false,
        "pro": true,
        "enterprise": true,
        "includeOnCard": false,
        "setupFee": false
      },
      {
        "name": "Dedicated Account Manager",
        "description": "Personal contact for enterprise customers.",
        "basic": false,
        "plus": false,
        "pro": false,
        "enterprise": true,
        "includeOnCard": true,
        "setupFee": false
      }
    ]
  }
];

export const PRICING_CONFIG = {
  companyName: 'Tech Notes',
  tagline: 'An all-in-one tool for making sure your service technicians have the exact content they need right when they need it',
  pricingNote: 'All prices are per month per location',
  ctaHeadline: 'Ready to Get Started?',
  ctaDescription: 'Join thousands of teams already using Tech Notes to streamline their operations. Start your free trial today.',
  trialButtonText: 'Start Free Trial',
  demoButtonText: 'Schedule Demo'
};

export const PRICING_TIERS = [
  {
    name: 'Basic',
    price: '$7',
    period: 'month',
    description: 'Perfect for small teams getting started',
    icon: 'Users',
    popular: false,
    buttonText: 'Get Started',
    buttonVariant: 'primary' as const,
    order: 1
  },
  {
    name: 'Plus',
    price: '$15',
    period: 'month',
    description: 'Everything in Basic plus advanced features',
    icon: 'Zap',
    popular: false,
    buttonText: 'Get Started',
    buttonVariant: 'primary' as const,
    order: 2
  },
  {
    name: 'Pro',
    price: '$30',
    period: 'month',
    description: 'Everything in Plus plus automation',
    icon: 'Star',
    popular: true,
    buttonText: 'Get Started',
    buttonVariant: 'primary' as const,
    order: 3
  },
  {
    name: 'Enterprise',
    price: 'Custom',
    period: 'month',
    description: 'Everything in Pro plus enterprise features',
    icon: 'Shield',
    popular: false,
    buttonText: 'Contact Sales',
    buttonVariant: 'secondary' as const,
    order: 4
  }
];
