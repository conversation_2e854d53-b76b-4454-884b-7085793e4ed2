import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "../../components";
import {
  <PERSON><PERSON>,
  FileText,
  Camera,
  Phone,
  MessageSquare,
  Clock,
  AlertTriangle,
  CheckCircle,
  Settings,
  HelpCircle,
} from "lucide-react";

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  action: () => void;
  category: "work" | "communication" | "tools" | "help";
}

export const QuickActionsPage: React.FC = () => {
  const quickActions: QuickAction[] = [
    // Work Actions
    {
      id: "clock-in",
      title: "Clock In/Out",
      description: "Track your work hours",
      icon: <Clock className="h-6 w-6" />,
      color: "bg-blue-500",
      category: "work",
      action: () => alert("Clock in/out functionality"),
    },
    {
      id: "report-issue",
      title: "Report Issue",
      description: "Report equipment or safety issues",
      icon: <AlertTriangle className="h-6 w-6" />,
      color: "bg-red-500",
      category: "work",
      action: () => alert("Issue reporting functionality"),
    },
    {
      id: "complete-task",
      title: "Mark Complete",
      description: "Complete current task",
      icon: <CheckCircle className="h-6 w-6" />,
      color: "bg-green-500",
      category: "work",
      action: () => alert("Task completion functionality"),
    },
    {
      id: "take-photo",
      title: "Take Photo",
      description: "Document work progress",
      icon: <Camera className="h-6 w-6" />,
      color: "bg-purple-500",
      category: "work",
      action: () => alert("Photo capture functionality"),
    },

    // Communication Actions
    {
      id: "call-supervisor",
      title: "Call Supervisor",
      description: "Quick call to your supervisor",
      icon: <Phone className="h-6 w-6" />,
      color: "bg-orange-500",
      category: "communication",
      action: () => alert("Calling supervisor..."),
    },
    {
      id: "send-message",
      title: "Send Message",
      description: "Message your team",
      icon: <MessageSquare className="h-6 w-6" />,
      color: "bg-indigo-500",
      category: "communication",
      action: () => alert("Messaging functionality"),
    },

    // Tools Actions
    {
      id: "work-notes",
      title: "Work Notes",
      description: "Add notes to current job",
      icon: <FileText className="h-6 w-6" />,
      color: "bg-gray-500",
      category: "tools",
      action: () => alert("Work notes functionality"),
    },
    {
      id: "tools-checklist",
      title: "Tools Checklist",
      description: "Check required tools",
      icon: <Wrench className="h-6 w-6" />,
      color: "bg-yellow-600",
      category: "tools",
      action: () => alert("Tools checklist functionality"),
    },
    {
      id: "settings",
      title: "Settings",
      description: "App preferences",
      icon: <Settings className="h-6 w-6" />,
      color: "bg-gray-600",
      category: "tools",
      action: () => alert("Settings functionality"),
    },

    // Help Actions
    {
      id: "help",
      title: "Get Help",
      description: "Access help and support",
      icon: <HelpCircle className="h-6 w-6" />,
      color: "bg-teal-500",
      category: "help",
      action: () => alert("Help functionality"),
    },
  ];

  const categories = [
    {
      id: "work",
      title: "Work Actions",
      description: "Core work-related tasks",
    },
    {
      id: "communication",
      title: "Communication",
      description: "Contact team members",
    },
    {
      id: "tools",
      title: "Tools & Notes",
      description: "Utilities and documentation",
    },
    {
      id: "help",
      title: "Help & Support",
      description: "Get assistance when needed",
    },
  ];

  const getActionsByCategory = (category: string) => {
    return quickActions.filter((action) => action.category === category);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Quick Actions</h1>
        <p className="text-gray-600">Common tasks at your fingertips</p>
      </div>

      {/* Action Categories */}
      <div className="space-y-6">
        {categories.map((category) => {
          const categoryActions = getActionsByCategory(category.id);

          return (
            <div key={category.id}>
              <div className="mb-4">
                <h2 className="text-lg font-semibold text-gray-900">
                  {category.title}
                </h2>
                <p className="text-sm text-gray-600">{category.description}</p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {categoryActions.map((action) => (
                  <Card
                    key={action.id}
                    className="hover:shadow-md transition-shadow cursor-pointer"
                    onClick={action.action}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        {/* Icon */}
                        <div
                          className={`${action.color} text-white p-2 rounded-lg flex-shrink-0`}
                        >
                          {action.icon}
                        </div>

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-gray-900 mb-1">
                            {action.title}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {action.description}
                          </p>
                        </div>

                        {/* Arrow */}
                        <div className="flex-shrink-0">
                          <svg
                            className="h-4 w-4 text-gray-400"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 5l7 7-7 7"
                            />
                          </svg>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      {/* Emergency Actions */}
      <Card className="border-red-200 bg-red-50">
        <CardHeader title="Emergency Actions" className="text-red-900" />
        <CardContent>
          <div className="space-y-3">
            <Button
              variant="outline"
              className="w-full h-12 border-red-300 text-red-700 hover:bg-red-100"
              onClick={() => alert("Emergency contact functionality")}
            >
              <Phone className="h-5 w-5 mr-2" />
              Emergency Contact
            </Button>
            <Button
              variant="outline"
              className="w-full h-12 border-red-300 text-red-700 hover:bg-red-100"
              onClick={() => alert("Safety incident reporting")}
            >
              <AlertTriangle className="h-5 w-5 mr-2" />
              Report Safety Incident
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
