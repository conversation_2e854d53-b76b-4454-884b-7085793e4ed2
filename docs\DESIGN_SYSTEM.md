# Design System - Tech Notes

**Salient-Inspired Design System** for consistent, professional UI across marketing pages, authenticated app, and future React Native implementation.

---

## 🎨 **Design Principles**

### **Visual Hierarchy**

- **Clear information architecture** with consistent spacing and typography scales
- **Professional aesthetic** that builds user trust and confidence
- **Accessibility-first** with proper contrast ratios and focus states

### **Brand Identity**

- **Primary blue palette** (#2563eb) for trust and professionalism
- **Clean typography** using Inter font family for readability
- **Subtle shadows and elevation** for depth without distraction

### **Cross-Platform Consistency**

- **Unified color palette** across web and future React Native app
- **Semantic naming** for maintainable theming (`primary-600` vs `blue-600`)
- **Responsive design tokens** that adapt to different screen sizes

---

## 🏗️ **Architecture**

### **Token Structure**

```
frontend/src/design-system/tokens/
├── colors.ts          # Color palette and semantic mappings
├── typography.ts      # Font scales, weights, and text styles
├── spacing.ts         # 8px grid system and semantic spacing
├── shadows.ts         # Elevation system and interactive shadows
└── index.ts           # Unified exports and utilities
```

### **Integration with Tailwind**

- **Extended Tailwind config** with design tokens
- **Semantic class names** for consistent usage
- **Backward compatibility** with existing components

---

## 🎯 **Color System**

### **Primary Palette**

```css
primary-50:  #eff6ff  /* Light backgrounds */
primary-500: #3b82f6  /* Brand color */
primary-600: #2563eb  /* Primary actions */
primary-700: #1d4ed8  /* Hover states */
primary-900: #1e3a8a  /* Dark text */
```

### **Semantic Colors**

```css
success-500: #10b981  /* Success states */
warning-500: #f59e0b  /* Warning states */
error-500:   #ef4444  /* Error states */
```

### **Usage Guidelines**

- **Primary colors**: Buttons, links, brand elements
- **Gray scale**: Text, borders, backgrounds
- **Semantic colors**: Status indicators, alerts, feedback

---

## ✍️ **Typography**

### **Font Scale**

```css
text-xs:   12px  /* Captions, badges */
text-sm:   14px  /* UI text, labels */
text-base: 16px  /* Body text */
text-lg:   18px  /* Large body text */
text-xl:   20px  /* Small headings */
text-2xl:  24px  /* Section headings */
text-4xl:  36px  /* Hero headings */
text-6xl:  60px  /* Large hero */
```

### **Text Styles**

- **Headings**: font-weight-600 to font-weight-800
- **Body text**: font-weight-400 with 1.5 line-height
- **UI elements**: font-weight-500 to font-weight-600

---

## 📏 **Spacing System**

### **8px Grid System**

```css
space-1:  4px   /* Tight component spacing */
space-2:  8px   /* Standard component spacing */
space-4:  16px  /* Comfortable spacing */
space-6:  24px  /* Section spacing */
space-8:  32px  /* Large section spacing */
space-12: 48px  /* Hero section spacing */
space-20: 80px  /* Page section spacing */
```

### **Semantic Spacing**

- **Component spacing**: 4px - 32px for internal component layout
- **Layout spacing**: 16px - 64px for page layout structure
- **Section spacing**: 48px - 128px for major page sections

---

## 🌟 **Shadow System**

### **Elevation Levels**

```css
shadow-xs:   Subtle card shadows
shadow-sm:   Standard card elevation
shadow-base: Interactive element shadows
shadow-lg:   Modal and dropdown shadows
shadow-xl:   Maximum elevation overlays
```

### **Interactive Shadows**

- **Resting state**: `shadow-xs` or `shadow-sm`
- **Hover state**: `shadow-base` or `shadow-lg`
- **Active state**: `shadow-inner` for pressed effect

---

## 🧱 **Component Guidelines**

### **Buttons**

```typescript
// Primary action
<Button variant="primary" size="md">Get Started</Button>

// Secondary action
<Button variant="outline" size="md">Learn More</Button>

// Sizes: xs, sm, md, lg, xl
```

### **Cards**

```typescript
// Standard card
<Card className="shadow-sm hover:shadow-base">
  <CardContent>...</CardContent>
</Card>

// Elevated card
<Card className="shadow-base">
  <CardContent>...</CardContent>
</Card>
```

### **Typography**

```typescript
// Hero heading
<h1 className="text-4xl font-bold text-gray-900">

// Section heading
<h2 className="text-2xl font-semibold text-gray-800">

// Body text
<p className="text-base text-gray-600">
```

---

## 📱 **Responsive Design**

### **Breakpoint Strategy**

- **Desktop-first** approach matching Salient template
- **Mobile-compatible** with proper touch targets
- **Consistent spacing** across all screen sizes

### **Mobile Considerations**

- **Minimum touch targets**: 44px for accessibility
- **Readable text sizes**: Minimum 16px on mobile
- **Adequate spacing**: Increased padding on smaller screens

---

## 🔧 **Usage Examples**

### **Marketing Hero Section**

```tsx
<div className="bg-gradient-to-b from-gray-50 to-white py-20 sm:py-32">
  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <h1 className="text-4xl font-bold text-gray-900 sm:text-6xl">
      Your Hero Title
    </h1>
    <p className="mt-6 text-xl text-gray-600">Supporting description text</p>
    <Button variant="primary" size="xl" className="mt-8">
      Get Started
    </Button>
  </div>
</div>
```

### **Card Component**

```tsx
<Card className="shadow-sm hover:shadow-base transition-shadow">
  <CardHeader>
    <h3 className="text-lg font-semibold text-gray-900">Card Title</h3>
  </CardHeader>
  <CardContent>
    <p className="text-gray-600">Card content with proper spacing</p>
  </CardContent>
</Card>
```

### **Marketing Organisms** _(New in Phase 3)_

#### **MarketingHero**

```tsx
<MarketingHero
  title="Your Product Title"
  description="Compelling description that converts visitors"
  announcement={{
    text: "🚀 New Feature Available",
    variant: "primary",
    icon: <Star className="h-4 w-4" />,
  }}
  primaryCta={{
    text: "Get Started Free",
    href: "/sign-in",
    icon: <ArrowRight className="h-5 w-5" />,
  }}
  secondaryCta={{
    text: "View Pricing",
    href: "/pricing",
    variant: "outline",
  }}
  socialProof={{
    text: "Trusted by 500+ companies",
    stats: [
      { value: "10,000+", label: "Active Users" },
      { value: "99.9%", label: "Uptime" },
    ],
  }}
/>
```

#### **FeatureGrid**

```tsx
<FeatureGrid
  title="Why choose our platform?"
  subtitle="Everything you need to succeed"
  features={[
    {
      id: "feature-1",
      title: "Feature Name",
      description: "Feature description with benefits",
      icon: <Zap className="h-6 w-6" />,
      variant: "elevated",
      interactive: true,
    },
  ]}
/>
```

#### **PricingCards**

```tsx
<PricingCards
  tiers={pricingTiers}
  title="Choose your plan"
  subtitle="All plans include 14-day free trial"
  highlightPopular={true}
  billingPeriod={{
    current: billingPeriod,
    onToggle: setBillingPeriod,
    discount: "Save 20%",
  }}
/>
```

---

## ✅ **Implementation Status**

### **Phase 1: Design Foundation** ✅ **COMPLETE**

- [x] Color tokens defined and integrated with Tailwind
- [x] Typography scale established with semantic naming
- [x] Spacing system based on 8px grid
- [x] Shadow system for elevation and interactivity
- [x] Tailwind configuration updated with design tokens

### **Phase 2: Atomic Components** ✅ **COMPLETE**

- [x] **Button**: Gradients, shadows, hover states, micro-interactions
- [x] **Card**: Enhanced shadows, borders, spacing hierarchy
- [x] **Input**: Salient form styling, focus states, validation
- [x] **Badge**: Refined colors, gradients, role-specific variants
- [x] **Alert**: Improved styling with gradients and visual hierarchy

### **Phase 2.5: Marketing Atoms** ✅ **COMPLETE**

- [x] **HeroSection**: Gradient backgrounds, CTA styling, responsive layouts
- [x] **FeatureCard**: Icon integration, hover effects, multiple orientations
- [x] **StatDisplay**: Trend indicators, themes, professional number display
- [x] **TestimonialCard**: Author attribution, ratings, quote styling
- [x] **88 comprehensive tests** added for enhanced styling

### **Phase 3: Marketing Pages** ✅ **COMPLETE**

- [x] **MarketingHero organism**: Professional hero sections with Salient styling
- [x] **FeatureGrid organism**: Interactive feature showcases with hover effects
- [x] **TestimonialSection organism**: Social proof with carousel functionality
- [x] **PricingCards organism**: Professional pricing displays with comparisons
- [x] **PublicLayout enhancement**: Sticky navigation with mobile-first design
- [x] **MarketingHome redesign**: Cohesive homepage with all new organisms
- [x] **PricingPage redesign**: Enhanced pricing with billing toggles and features
- [x] **201 comprehensive tests** added for marketing organisms
- [x] **561/561 tests passing** with full validation

### **Phase 4: Authenticated App** ⏳ **NEXT**

- [ ] AppLayout updated with consistent styling
- [ ] Dashboard pages enhanced with better data visualization
- [ ] Admin pages with professional table and form styling

### **Phase 5: Future Preparation** ⏳ **PENDING**

- [ ] React Native design tokens prepared for mobile consistency
- [ ] Performance optimization and accessibility audit

---

**Current Status**: ✅ **Marketing transformation complete** - Professional Salient-inspired marketing pages with comprehensive organism library

**Next Steps**: Proceed to authenticated app redesign (Phase 4) to complete the design system implementation
