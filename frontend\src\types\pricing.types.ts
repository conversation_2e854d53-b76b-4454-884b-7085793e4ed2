export interface PricingTier {
  name: string;
  price: string;
  period: string;
  description: string;
  icon: string; // Icon name from lucide-react
  popular: boolean;
  buttonText: string;
  buttonVariant: "primary" | "secondary" | "outline";
  order: number;
}

export interface Feature {
  name: string;
  description: string;
  basic: boolean | string;
  plus: boolean | string;
  pro: boolean | string;
  enterprise: boolean | string;
  includeOnCard: boolean;
  setupFee: boolean;
}

export interface FeatureGroup {
  groupName: string;
  features: Feature[];
}

export interface PricingConfig {
  companyName: string;
  tagline: string;
  pricingNote: string;
  ctaHeadline: string;
  ctaDescription: string;
  trialButtonText: string;
  demoButtonText: string;
}

export interface RawFeature {
  groupName: string;
  featureName: string;
  featureDescription: string;
  basic: string;
  plus: string;
  pro: string;
  enterprise: string;
  includeOnCard: boolean;
}

export interface TierFeatures {
  tier: string;
  features: Feature[];
}
