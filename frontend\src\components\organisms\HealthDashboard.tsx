import React from "react";
import { useHealth } from "../../hooks/useHealth";
import HealthCard from "../molecules/HealthCard";
import { ErrorBoundary } from "../atoms/ErrorBoundary";
import type { HealthDashboardProps } from "../../types/api.types";

const HealthDashboard: React.FC<HealthDashboardProps> = ({
  healthService,
  refreshInterval = 60000,
}) => {
  const { data, isLoading, error, refetch } = useHealth(
    healthService,
    refreshInterval,
  );

  const getStatus = () => {
    if (isLoading) return "loading";
    if (error) return "error";
    return data?.data.status === "ok" ? "ok" : "error";
  };

  const handleRefresh = () => {
    refetch();
  };

  return (
    <ErrorBoundary>
      <div className="max-w-md mx-auto">
        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <h3 className="text-sm font-medium text-red-800">
              Connection Error
            </h3>
            <p className="text-sm text-red-600 mt-1">
              Unable to connect to backend. Please ensure the backend server is
              running.
            </p>
            <details className="mt-2">
              <summary className="text-xs text-red-500 cursor-pointer">
                Debug Info
              </summary>
              <pre className="text-xs text-red-500 mt-1 whitespace-pre-wrap">
                {error instanceof Error ? error.message : String(error)}
              </pre>
              <p className="text-xs text-red-500 mt-1">
                API URL: {import.meta.env.VITE_API_URL || "Not set"}
              </p>
            </details>
          </div>
        )}

        <HealthCard
          status={getStatus()}
          environment={data?.data.environment}
          database={data?.data.database}
          timestamp={data?.timestamp}
          onRefresh={handleRefresh}
        />
      </div>
    </ErrorBoundary>
  );
};

export default HealthDashboard;
