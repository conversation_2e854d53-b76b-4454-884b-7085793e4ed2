import { Permission } from '@prisma/client';
import { BackendAuthContext, UserRoleWithContext } from '@tech-notes/shared';

import { NotFoundError, ForbiddenError } from '../types/error.types.js';
import {
  CreatePermissionData,
  PermissionCheck,
  RoleContext,
} from '../types/rbac.types.js';

import { BaseTenantService } from './base-tenant.service.js';

export class PermissionService extends BaseTenantService {
  /**
   * Check if a user has a specific permission
   */
  async hasPermission(
    userId: string,
    permissionCheck: PermissionCheck,
    BackendAuthContext: BackendAuthContext
  ): Promise<boolean> {
    this.logOperation('hasPermission', {
      userId,
      permission: `${permissionCheck.resource}:${permissionCheck.action}`,
      context: permissionCheck.context,
    });

    try {
      // System Admins automatically have all permissions
      if (BackendAuthContext.canBypassTenantScope) {
        this.logger.debug('Permission granted for System Admin', {
          userId,
          permission: `${permissionCheck.resource}:${permissionCheck.action}`,
        });
        return true;
      }

      // Get user's roles with permissions
      const userRoles = await this.getUserRolesWithPermissions(
        userId,
        BackendAuthContext
      );

      // Check if any role has the required permission
      for (const userRole of userRoles) {
        // Skip roles that don't match the context
        if (!this.roleMatchesContext(userRole, permissionCheck.context)) {
          continue;
        }

        // Check if role has the permission
        const hasPermission = userRole.role.permissions?.some(
          (rp) =>
            rp.permission.resource === permissionCheck.resource &&
            rp.permission.action === permissionCheck.action
        );

        if (hasPermission) {
          this.logger.debug('Permission granted via role', {
            userId,
            permission: `${permissionCheck.resource}:${permissionCheck.action}`,
            roleName: userRole.role.name,
          });
          return true;
        }
      }

      this.logger.debug('Permission denied', {
        userId,
        permission: `${permissionCheck.resource}:${permissionCheck.action}`,
        context: permissionCheck.context,
      });
      return false;
    } catch (error) {
      this.logError('hasPermission', error as Error, {
        userId,
        permission: `${permissionCheck.resource}:${permissionCheck.action}`,
      });
      // For security, return false on error
      return false;
    }
  }

  /**
   * Get all permissions for a user (flattened list)
   */
  async getUserPermissions(
    userId: string,
    BackendAuthContext: BackendAuthContext
  ): Promise<string[]> {
    this.logOperation('getUserPermissions', { userId });

    // System Admins have all permissions
    if (BackendAuthContext.canBypassTenantScope) {
      const allPermissions = await this.prisma.permission.findMany({
        select: { resource: true, action: true },
      });
      return allPermissions.map((p) => `${p.resource}:${p.action}`);
    }

    const userRoles = await this.getUserRolesWithPermissions(
      userId,
      BackendAuthContext
    );
    const permissions = new Set<string>();

    for (const userRole of userRoles) {
      if (userRole.role.permissions) {
        for (const rolePermission of userRole.role.permissions) {
          permissions.add(
            `${rolePermission.permission.resource}:${rolePermission.permission.action}`
          );
        }
      }
    }

    return Array.from(permissions).sort();
  }

  /**
   * Create a new permission (System Admin only)
   */
  async createPermission(
    permissionData: CreatePermissionData,
    BackendAuthContext: BackendAuthContext
  ): Promise<Permission> {
    this.logOperation('createPermission', {
      name: permissionData.name,
      resource: permissionData.resource,
      action: permissionData.action,
      userId: BackendAuthContext.id,
    });

    if (!BackendAuthContext.canBypassTenantScope) {
      throw new ForbiddenError('Only System Admins can create permissions');
    }

    try {
      return await this.prisma.permission.create({
        data: permissionData,
      });
    } catch (error) {
      this.logError(
        'createPermission',
        error as Error,
        permissionData as unknown as Record<string, unknown>
      );
      throw error;
    }
  }

  /**
   * Get all permissions (System Admin only)
   */
  async getAllPermissions(
    BackendAuthContext: BackendAuthContext
  ): Promise<Permission[]> {
    this.logOperation('getAllPermissions', { userId: BackendAuthContext.id });

    if (!BackendAuthContext.canBypassTenantScope) {
      throw new ForbiddenError('Only System Admins can view all permissions');
    }

    return await this.prisma.permission.findMany({
      orderBy: [{ resource: 'asc' }, { action: 'asc' }],
    });
  }

  /**
   * Get permissions for a specific role
   */
  async getRolePermissions(
    roleId: string,
    BackendAuthContext: BackendAuthContext
  ): Promise<Permission[]> {
    this.logOperation('getRolePermissions', {
      roleId,
      userId: BackendAuthContext.id,
    });

    const role = await this.prisma.role.findUnique({
      where: { id: roleId },
      select: { isSystemRole: true },
    });

    if (!role) {
      throw new NotFoundError(`Role ${roleId} not found`);
    }

    // Only System Admins can view system role permissions
    if (role.isSystemRole && !BackendAuthContext.canBypassTenantScope) {
      throw new ForbiddenError(
        'Only System Admins can view system role permissions'
      );
    }

    const rolePermissions = await this.prisma.rolePermission.findMany({
      where: { roleId },
      include: {
        permission: true,
      },
      orderBy: {
        permission: {
          resource: 'asc',
        },
      },
    });

    return rolePermissions.map((rp) => rp.permission);
  }

  /**
   * Private helper: Get user roles with their permissions
   */
  private async getUserRolesWithPermissions(
    userId: string,
    BackendAuthContext: BackendAuthContext
  ): Promise<
    Array<
      UserRoleWithContext & {
        role: { permissions?: Array<{ permission: Permission }> };
      }
    >
  > {
    // Validate access to user
    if (!BackendAuthContext.canBypassTenantScope) {
      const targetUser = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { tenantId: true },
      });

      if (!targetUser || targetUser.tenantId !== BackendAuthContext.tenantId) {
        throw new ForbiddenError('Cannot access user from different tenant');
      }
    }

    const userRoles = await this.prisma.userRole.findMany({
      where: { userId },
      include: {
        role: {
          include: {
            permissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
    });

    return userRoles.map((userRole) => ({
      id: userRole.id,
      role: {
        id: userRole.role.id,
        name: userRole.role.name,
        type: userRole.role.type,
        isSystemRole: userRole.role.isSystemRole,
        permissions: userRole.role.permissions,
      },
      tenantId: userRole.tenantId,
    }));
  }

  /**
   * Private helper: Check if a role matches the given context
   */
  private roleMatchesContext(
    userRole: UserRoleWithContext,
    context?: RoleContext
  ): boolean {
    // System roles (tenantId = null) match any context
    if (userRole.tenantId === null) {
      return true;
    }

    // If no context provided, role must be in user's current tenant
    if (!context) {
      return true; // Let the calling code handle tenant validation
    }

    // Role must match the context tenant
    return userRole.tenantId === context.tenantId;
  }

  /**
   * Batch permission check for multiple permissions
   */
  async hasPermissions(
    userId: string,
    permissionChecks: PermissionCheck[],
    BackendAuthContext: BackendAuthContext
  ): Promise<Record<string, boolean>> {
    this.logOperation('hasPermissions', {
      userId,
      permissionCount: permissionChecks.length,
    });

    const results: Record<string, boolean> = {};

    // System Admins automatically have all permissions
    if (BackendAuthContext.canBypassTenantScope) {
      for (const check of permissionChecks) {
        const key = `${check.resource}:${check.action}`;
        results[key] = true;
      }
      return results;
    }

    // Get user roles once for efficiency
    const userRoles = await this.getUserRolesWithPermissions(
      userId,
      BackendAuthContext
    );

    for (const check of permissionChecks) {
      const key = `${check.resource}:${check.action}`;
      results[key] = false;

      for (const userRole of userRoles) {
        if (!this.roleMatchesContext(userRole, check.context)) {
          continue;
        }

        const hasPermission = userRole.role.permissions?.some(
          (rp) =>
            rp.permission.resource === check.resource &&
            rp.permission.action === check.action
        );

        if (hasPermission) {
          results[key] = true;
          break;
        }
      }
    }

    return results;
  }
}
