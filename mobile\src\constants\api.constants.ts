/**
 * API endpoint constants for Tech Notes Mobile application
 *
 * Copied from shared workspace to avoid dependency conflicts.
 * All endpoints are relative to the API base URL.
 */

export const API_ENDPOINTS = {
  // Health endpoints
  HEALTH: "/api/v1/health",

  // Authentication endpoints
  AUTH: {
    STATUS: "/api/v1/auth/status",
    ONBOARD: "/api/v1/auth/onboard",
  },

  // User endpoints
  USERS: {
    BASE: "/api/v1/users",
    BY_ID: (userId: string) => `/api/v1/users/${userId}`,
    TOGGLE_STATUS: (userId: string) => `/api/v1/users/${userId}/toggle-status`,
  },

  // Tenant endpoints
  TENANTS: {
    BASE: "/api/v1/tenants",
    BY_ID: (tenantId: string) => `/api/v1/tenants/${tenantId}`,
  },

  // Role endpoints
  ROLES: {
    BASE: "/api/v1/roles",
    BY_ID: (roleId: string) => `/api/v1/roles/${roleId}`,
    BY_TENANT: (tenantId: string) => `/api/v1/roles/tenant/${tenantId}`,
    BY_USER: (userId: string) => `/api/v1/roles/user/${userId}`,
    ASSIGN: "/api/v1/roles/assign",
    UNASSIGN: "/api/v1/roles/unassign",
  },

  // Permission endpoints
  PERMISSIONS: {
    BASE: "/api/v1/permissions",
    BY_ID: (permissionId: string) => `/api/v1/permissions/${permissionId}`,
    BY_USER: (userId: string) => `/api/v1/permissions/user/${userId}`,
    CURRENT: "/api/v1/permissions/me",
  },



  // Engagement endpoints
  ENGAGEMENT: {
    METRICS: "/api/v1/engagement/metrics",
    METRICS_BY_TENANT: (tenantId: string) =>
      `/api/v1/engagement/metrics?tenantId=${encodeURIComponent(tenantId)}`,
    INACTIVE_USERS: "/api/v1/engagement/inactive-users",
    USER_ACTIVITY: "/api/v1/engagement/user-activity",
  },

  // Invitation endpoints
  INVITATIONS: {
    BASE: "/api/v1/invitations",
    BY_ID: (invitationId: string) => `/api/v1/invitations/${invitationId}`,
    ACCEPT: (invitationId: string) =>
      `/api/v1/invitations/${invitationId}/accept`,
    RESEND: (invitationId: string) =>
      `/api/v1/invitations/${invitationId}/resend`,
    CANCEL: (invitationId: string) =>
      `/api/v1/invitations/${invitationId}/cancel`,
    CREATE_TENANT_WITH_ADMIN: "/api/v1/invitations/create-tenant-with-admin",
  },
} as const;

/**
 * Helper function to build query parameters
 */
export function buildQueryParams(
  params: Record<string, string | number | boolean | undefined>,
): string {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.append(key, String(value));
    }
  });

  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : "";
}

/**
 * Helper function to build engagement endpoints with query parameters
 */
export const ENGAGEMENT_ENDPOINTS = {
  metrics: (tenantId?: string) =>
    `${API_ENDPOINTS.ENGAGEMENT.METRICS}${buildQueryParams({ tenantId })}`,

  inactiveUsers: (thresholdDays?: number, tenantId?: string) =>
    `${API_ENDPOINTS.ENGAGEMENT.INACTIVE_USERS}${buildQueryParams({ thresholdDays, tenantId })}`,

  userActivity: (days?: number, tenantId?: string) =>
    `${API_ENDPOINTS.ENGAGEMENT.USER_ACTIVITY}${buildQueryParams({ days, tenantId })}`,
} as const;
