import React, { useState } from "react";
import { <PERSON><PERSON>, Badge, CompanyAdminOnly } from "../index";
import type {
  VehicleHierarchyTreeDataV2,
  VehicleBrandV2,
  VehicleSubBrandV2,
  VehicleModelV2,
} from "../../services/api-client";
import {
  ChevronDown,
  ChevronRight,
  Building2,
  Tag,
  Car,
  Calendar,
  Plus,
  Edit,
  Trash2,
} from "lucide-react";

interface VehicleHierarchyV2TreeProps {
  hierarchy: VehicleHierarchyTreeDataV2;
  onCreateSubBrand: (brand: VehicleBrandV2) => void;
  onCreateModel: (subBrand: VehicleSubBrandV2) => void;
  onEditBrand: (brand: VehicleBrandV2) => void;
  onEditSubBrand: (subBrand: VehicleSubBrandV2) => void;
  onEditModel: (model: VehicleModelV2) => void;
  onDeleteBrand: (brand: VehicleBrandV2) => void;
  onDeleteSubBrand: (subBrand: VehicleSubBrandV2) => void;
  onDeleteModel: (model: VehicleModelV2) => void;
  onManageModelYears: (model: VehicleModelV2) => void;
  searchQuery?: string;
}

export const VehicleHierarchyV2Tree: React.FC<VehicleHierarchyV2TreeProps> =
  React.memo(
    ({
      hierarchy,
      onCreateSubBrand,
      onCreateModel,
      onEditBrand,
      onEditSubBrand,
      onEditModel,
      onDeleteBrand,
      onDeleteSubBrand,
      onDeleteModel,
      onManageModelYears,
      searchQuery = "",
    }) => {
      const [expandedBrands, setExpandedBrands] = useState<Set<string>>(
        new Set(),
      );
      const [expandedSubBrands, setExpandedSubBrands] = useState<Set<string>>(
        new Set(),
      );

      const toggleBrand = (brandId: string) => {
        const newExpanded = new Set(expandedBrands);
        if (newExpanded.has(brandId)) {
          newExpanded.delete(brandId);
        } else {
          newExpanded.add(brandId);
        }
        setExpandedBrands(newExpanded);
      };

      const toggleSubBrand = (subBrandId: string) => {
        const newExpanded = new Set(expandedSubBrands);
        if (newExpanded.has(subBrandId)) {
          newExpanded.delete(subBrandId);
        } else {
          newExpanded.add(subBrandId);
        }
        setExpandedSubBrands(newExpanded);
      };

      const expandAll = () => {
        const allBrandIds = new Set(
          hierarchy.brands.map((brand: VehicleBrandV2) => brand.id),
        );
        const allSubBrandIds = new Set(
          hierarchy.brands.flatMap(
            (brand: VehicleBrandV2) =>
              brand.subBrands?.map((sb: VehicleSubBrandV2) => sb.id) || [],
          ),
        );
        setExpandedBrands(allBrandIds);
        setExpandedSubBrands(allSubBrandIds);
      };

      const collapseAll = () => {
        setExpandedBrands(new Set());
        setExpandedSubBrands(new Set());
      };

      // Search highlighting utility
      const highlightText = (text: string, query: string) => {
        if (!query.trim()) return text;

        const regex = new RegExp(
          `(${query.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
          "gi",
        );
        const parts = text.split(regex);

        return parts.map((part, index) =>
          regex.test(part) ? (
            <mark key={index} className="bg-yellow-200 px-1 rounded">
              {part}
            </mark>
          ) : (
            part
          ),
        );
      };

      // Calculate statistics
      const totalSubBrands = hierarchy.brands.reduce(
        (total: number, brand: VehicleBrandV2) =>
          total + (brand.subBrands?.length || 0),
        0,
      );
      const totalModels = hierarchy.brands.reduce(
        (total: number, brand: VehicleBrandV2) =>
          total +
          (brand.subBrands?.reduce(
            (sbTotal: number, sb: VehicleSubBrandV2) =>
              sbTotal + (sb.models?.length || 0),
            0,
          ) || 0),
        0,
      );

      if (!hierarchy.brands.length) {
        return (
          <div className="text-center py-12">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No Brands Found
            </h3>
            <p className="text-gray-600 mb-4">
              Start by creating your first brand to build the vehicle hierarchy.
            </p>
          </div>
        );
      }

      return (
        <div className="space-y-4">
          {/* Tree Controls */}
          <div className="flex items-center justify-between border-b border-gray-200 pb-4">
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={expandAll}>
                Expand All
              </Button>
              <Button variant="outline" size="sm" onClick={collapseAll}>
                Collapse All
              </Button>
            </div>
            <div className="text-sm text-gray-600">
              {hierarchy.brands.length} brands, {totalSubBrands} sub-brands,{" "}
              {totalModels} models
            </div>
          </div>

          {/* Tree Structure */}
          <div className="space-y-2">
            {hierarchy.brands
              .sort((a: VehicleBrandV2, b: VehicleBrandV2) =>
                a.name.localeCompare(b.name),
              ) // Sort brands alphabetically
              .map((brand: VehicleBrandV2) => (
                <div
                  key={brand.id}
                  className="border border-gray-200 rounded-lg"
                >
                  {/* Brand Level */}
                  <div className="flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors">
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => toggleBrand(brand.id)}
                        className="flex items-center justify-center w-6 h-6 rounded hover:bg-gray-200 transition-colors"
                        disabled={!brand.subBrands?.length}
                      >
                        {brand.subBrands &&
                          brand.subBrands.length > 0 &&
                          (expandedBrands.has(brand.id) ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          ))}
                      </button>
                      <Building2 className="h-5 w-5 text-blue-600" />
                      <span className="font-semibold text-gray-900">
                        {highlightText(brand.name, searchQuery)}
                      </span>
                      {!brand.isActive && (
                        <Badge variant="warning">Inactive</Badge>
                      )}
                      <Badge variant="secondary">
                        {brand.subBrands?.length || 0} sub-brand
                        {(brand.subBrands?.length || 0) !== 1 ? "s" : ""}
                      </Badge>
                    </div>

                    <CompanyAdminOnly>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onCreateSubBrand(brand)}
                          className="text-green-600 hover:text-green-700 hover:bg-green-50"
                          title="Add sub-brand"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onEditBrand(brand)}
                          className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          title="Edit brand"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDeleteBrand(brand)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          title="Delete brand"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CompanyAdminOnly>
                  </div>

                  {/* Sub-Brands Level */}
                  {expandedBrands.has(brand.id) &&
                    brand.subBrands &&
                    brand.subBrands.length > 0 && (
                      <div className="border-t border-gray-200">
                        {brand.subBrands
                          .sort((a: VehicleSubBrandV2, b: VehicleSubBrandV2) =>
                            a.name.localeCompare(b.name),
                          ) // Sort sub-brands alphabetically
                          .map((subBrand: VehicleSubBrandV2) => (
                            <div
                              key={subBrand.id}
                              className="border-b border-gray-100 last:border-b-0"
                            >
                              {/* Sub-Brand Header */}
                              <div className="flex items-center justify-between p-4 pl-12 bg-white hover:bg-gray-50 transition-colors">
                                <div className="flex items-center space-x-3">
                                  <button
                                    onClick={() => toggleSubBrand(subBrand.id)}
                                    className="flex items-center justify-center w-6 h-6 rounded hover:bg-gray-200 transition-colors"
                                    disabled={!subBrand.models?.length}
                                  >
                                    {subBrand.models &&
                                      subBrand.models.length > 0 &&
                                      (expandedSubBrands.has(subBrand.id) ? (
                                        <ChevronDown className="h-4 w-4" />
                                      ) : (
                                        <ChevronRight className="h-4 w-4" />
                                      ))}
                                  </button>
                                  <Tag className="h-5 w-5 text-purple-600" />
                                  <span className="font-medium text-gray-900">
                                    {highlightText(subBrand.name, searchQuery)}
                                  </span>
                                  {!subBrand.isActive && (
                                    <Badge variant="warning">Inactive</Badge>
                                  )}
                                  <Badge variant="outline">
                                    {subBrand.models?.length || 0} model
                                    {(subBrand.models?.length || 0) !== 1
                                      ? "s"
                                      : ""}
                                  </Badge>
                                </div>

                                <CompanyAdminOnly>
                                  <div className="flex items-center space-x-2">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => onCreateModel(subBrand)}
                                      className="text-green-600 hover:text-green-700 hover:bg-green-50"
                                      title="Add model"
                                    >
                                      <Plus className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => onEditSubBrand(subBrand)}
                                      className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                      title="Edit sub-brand"
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => onDeleteSubBrand(subBrand)}
                                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                      title="Delete sub-brand"
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </CompanyAdminOnly>
                              </div>

                              {/* Models Level */}
                              {expandedSubBrands.has(subBrand.id) &&
                                subBrand.models &&
                                subBrand.models.length > 0 && (
                                  <div className="bg-gray-25">
                                    {subBrand.models
                                      .sort(
                                        (
                                          a: VehicleModelV2,
                                          b: VehicleModelV2,
                                        ) => a.name.localeCompare(b.name),
                                      ) // Sort models alphabetically
                                      .map((model: VehicleModelV2) => (
                                        <div
                                          key={model.id}
                                          className="flex items-center justify-between p-3 pl-20 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 transition-colors"
                                        >
                                          <div className="flex items-center space-x-3">
                                            <Car className="h-4 w-4 text-green-600" />
                                            <span className="text-gray-900">
                                              {highlightText(
                                                model.name,
                                                searchQuery,
                                              )}
                                            </span>
                                            {!model.isActive && (
                                              <Badge variant="warning">
                                                Inactive
                                              </Badge>
                                            )}
                                            <Badge
                                              variant="secondary"
                                              className="text-xs"
                                            >
                                              {/* TODO: Show actual year count when available */}
                                              0 years
                                            </Badge>
                                          </div>

                                          <div className="flex items-center space-x-2">
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() =>
                                                onManageModelYears(model)
                                              }
                                              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                              title="Manage year associations"
                                            >
                                              <Calendar className="h-3 w-3" />
                                            </Button>

                                            <CompanyAdminOnly>
                                              <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() =>
                                                  onEditModel(model)
                                                }
                                                className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                                title="Edit model"
                                              >
                                                <Edit className="h-3 w-3" />
                                              </Button>
                                              <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() =>
                                                  onDeleteModel(model)
                                                }
                                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                                title="Delete model"
                                              >
                                                <Trash2 className="h-3 w-3" />
                                              </Button>
                                            </CompanyAdminOnly>
                                          </div>
                                        </div>
                                      ))}
                                  </div>
                                )}

                              {/* Empty state for sub-brand with no models */}
                              {expandedSubBrands.has(subBrand.id) &&
                                (!subBrand.models ||
                                  subBrand.models.length === 0) && (
                                  <div className="p-6 pl-20 text-center text-gray-500">
                                    <Car className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                    <p className="text-sm">
                                      No models found for this sub-brand
                                    </p>
                                    <CompanyAdminOnly>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => onCreateModel(subBrand)}
                                        className="mt-2"
                                      >
                                        <Plus className="h-4 w-4 mr-2" />
                                        Add Model
                                      </Button>
                                    </CompanyAdminOnly>
                                  </div>
                                )}
                            </div>
                          ))}
                      </div>
                    )}

                  {/* Empty state for brand with no sub-brands */}
                  {expandedBrands.has(brand.id) &&
                    (!brand.subBrands || brand.subBrands.length === 0) && (
                      <div className="p-6 text-center text-gray-500 border-t border-gray-200">
                        <Tag className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm">
                          No sub-brands found for this brand
                        </p>
                        <CompanyAdminOnly>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onCreateSubBrand(brand)}
                            className="mt-2"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Add Sub-Brand
                          </Button>
                        </CompanyAdminOnly>
                      </div>
                    )}
                </div>
              ))}
          </div>
        </div>
      );
    },
  );
