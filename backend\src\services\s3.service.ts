import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

import { ValidationError } from '../types/error.types.js';
import { env } from '../utils/env-validation.js';
import { Logger } from '../utils/logger.js';

export interface PresignedUploadUrlRequest {
  fileName: string;
  fileSize: number;
  mimeType: string;
  tenantId: string;
  documentId: string;
}

export interface PresignedUploadUrlResponse {
  uploadUrl: string;
  s3Key: string;
  expiresIn: number;
}

export interface PresignedDownloadUrlRequest {
  s3Key: string;
  fileName?: string;
}

export interface PresignedDownloadUrlResponse {
  downloadUrl: string;
  expiresIn: number;
}

/**
 * Service for managing AWS S3 operations for document storage
 * Handles presigned URL generation for secure file uploads and downloads
 */
export class S3Service {
  private s3Client: S3Client;
  private bucketName: string;
  private logger: Logger;

  // URL expiration times (in seconds)
  private readonly UPLOAD_URL_EXPIRATION = 15 * 60; // 15 minutes
  private readonly DOWNLOAD_URL_EXPIRATION = 60 * 60; // 1 hour

  // File validation limits (in bytes)
  private readonly MAX_FILE_SIZES = {
    'application/pdf': 50 * 1024 * 1024, // 50MB for PDFs
    'application/msword': 25 * 1024 * 1024, // 25MB for DOC
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      25 * 1024 * 1024, // 25MB for DOCX
    'application/vnd.ms-excel': 25 * 1024 * 1024, // 25MB for XLS
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
      25 * 1024 * 1024, // 25MB for XLSX
    'text/html': 10 * 1024 * 1024, // 10MB for HTML
    'text/csv': 10 * 1024 * 1024, // 10MB for CSV
    'image/jpeg': 10 * 1024 * 1024, // 10MB for JPEG
    'image/png': 10 * 1024 * 1024, // 10MB for PNG
  };

  private readonly ALLOWED_MIME_TYPES = Object.keys(this.MAX_FILE_SIZES);

  constructor(logger: Logger) {
    this.logger = logger;
    this.bucketName = env.AWS_S3_BUCKET_NAME;

    // Initialize S3 client with environment credentials
    this.s3Client = new S3Client({
      region: env.AWS_REGION,
      credentials: {
        accessKeyId: env.AWS_ACCESS_KEY_ID,
        secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
      },
    });

    this.logger.info('S3Service initialized', {
      region: env.AWS_REGION,
      bucket: this.bucketName,
    });
  }

  /**
   * Generate a presigned URL for file upload
   */
  async generatePresignedUploadUrl(
    request: PresignedUploadUrlRequest
  ): Promise<PresignedUploadUrlResponse> {
    this.logOperation('generatePresignedUploadUrl', {
      fileName: request.fileName,
      fileSize: request.fileSize,
      mimeType: request.mimeType,
      tenantId: request.tenantId,
      documentId: request.documentId,
    });

    try {
      // Validate file type and size
      this.validateFile(request.fileName, request.fileSize, request.mimeType);

      // Generate S3 key with tenant isolation
      const s3Key = this.generateS3Key(
        request.tenantId,
        request.documentId,
        request.fileName
      );

      // Create the PutObject command
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key,
        ContentType: request.mimeType,
        ContentLength: request.fileSize,
        // Add metadata for tracking
        Metadata: {
          tenantId: request.tenantId,
          documentId: request.documentId,
          originalFileName: request.fileName,
        },
      });

      // Generate presigned URL
      const uploadUrl = await getSignedUrl(this.s3Client, command, {
        expiresIn: this.UPLOAD_URL_EXPIRATION,
      });

      this.logger.info('Presigned upload URL generated successfully', {
        s3Key,
        expiresIn: this.UPLOAD_URL_EXPIRATION,
        tenantId: request.tenantId,
      });

      return {
        uploadUrl,
        s3Key,
        expiresIn: this.UPLOAD_URL_EXPIRATION,
      };
    } catch (error) {
      this.logError(
        'generatePresignedUploadUrl',
        error as Error,
        request as unknown as Record<string, unknown>
      );
      throw error;
    }
  }

  /**
   * Generate a presigned URL for file download
   */
  async generatePresignedDownloadUrl(
    request: PresignedDownloadUrlRequest
  ): Promise<PresignedDownloadUrlResponse> {
    this.logOperation('generatePresignedDownloadUrl', {
      s3Key: request.s3Key,
      fileName: request.fileName,
    });

    try {
      // Validate S3 key format for security
      this.validateS3Key(request.s3Key);

      // Create the GetObject command
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: request.s3Key,
        // Set response headers for download
        ResponseContentDisposition: request.fileName
          ? `attachment; filename="${request.fileName}"`
          : undefined,
      });

      // Generate presigned URL
      const downloadUrl = await getSignedUrl(this.s3Client, command, {
        expiresIn: this.DOWNLOAD_URL_EXPIRATION,
      });

      this.logger.info('Presigned download URL generated successfully', {
        s3Key: request.s3Key,
        expiresIn: this.DOWNLOAD_URL_EXPIRATION,
      });

      return {
        downloadUrl,
        expiresIn: this.DOWNLOAD_URL_EXPIRATION,
      };
    } catch (error) {
      this.logError(
        'generatePresignedDownloadUrl',
        error as Error,
        request as unknown as Record<string, unknown>
      );
      throw error;
    }
  }

  /**
   * Test S3 connectivity
   */
  async testConnectivity(): Promise<{ healthy: boolean; error?: string }> {
    this.logOperation('testConnectivity');

    try {
      // Try to list objects in the bucket (with limit 1 to minimize impact)
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: 'health-check-test-key-that-should-not-exist',
      });

      // We expect this to fail with NoSuchKey, which means S3 is accessible
      await getSignedUrl(this.s3Client, command, { expiresIn: 60 });

      this.logger.info('S3 connectivity test successful');
      return { healthy: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logError('testConnectivity', error as Error);

      // If it's a credentials or network error, S3 is not accessible
      // If it's a NoSuchKey error, S3 is accessible but the key doesn't exist (expected)
      if (
        errorMessage.includes('NoSuchKey') ||
        errorMessage.includes('AccessDenied')
      ) {
        return { healthy: true };
      }

      return { healthy: false, error: errorMessage };
    }
  }

  // ===== PRIVATE HELPER METHODS =====

  /**
   * Generate S3 key with tenant isolation
   */
  private generateS3Key(
    tenantId: string,
    documentId: string,
    fileName: string
  ): string {
    // Format: tenant-{tenantId}/{documentId}-{filename}
    const sanitizedFileName = this.sanitizeFileName(fileName);
    return `tenant-${tenantId}/${documentId}-${sanitizedFileName}`;
  }

  /**
   * Sanitize filename for S3 key
   */
  private sanitizeFileName(fileName: string): string {
    // Remove or replace characters that could cause issues in S3 keys
    return fileName
      .replace(/[^a-zA-Z0-9._-]/g, '_') // Replace special chars with underscore
      .replace(/_{2,}/g, '_') // Replace multiple underscores with single
      .replace(/^_+|_+$/g, ''); // Remove leading/trailing underscores
  }

  /**
   * Validate file type, size, and name
   */
  private validateFile(
    fileName: string,
    fileSize: number,
    mimeType: string
  ): void {
    // Validate file name
    if (!fileName || fileName.trim().length === 0) {
      throw new ValidationError('File name cannot be empty');
    }

    // Validate MIME type
    if (!this.ALLOWED_MIME_TYPES.includes(mimeType)) {
      throw new ValidationError(
        `File type '${mimeType}' is not allowed. Allowed types: ${this.ALLOWED_MIME_TYPES.join(', ')}`
      );
    }

    // Validate file size
    const maxSize =
      this.MAX_FILE_SIZES[mimeType as keyof typeof this.MAX_FILE_SIZES];
    if (fileSize > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024));
      throw new ValidationError(
        `File size ${Math.round(fileSize / (1024 * 1024))}MB exceeds maximum allowed size of ${maxSizeMB}MB for ${mimeType}`
      );
    }

    // Validate minimum file size (prevent empty files)
    if (fileSize <= 0) {
      throw new ValidationError('File size must be greater than 0');
    }
  }

  /**
   * Validate S3 key format for security
   */
  private validateS3Key(s3Key: string): void {
    // Ensure the key follows our tenant isolation pattern
    // Format: tenant-{tenantId}/{documentId}-{filename}
    if (
      !s3Key.match(/^tenant-[a-zA-Z0-9_-]+\/[a-zA-Z0-9_-]+-[a-zA-Z0-9._-]+$/)
    ) {
      throw new ValidationError('Invalid S3 key format');
    }
  }

  /**
   * Log service operation with context
   */
  private logOperation(
    operation: string,
    context: Record<string, unknown> = {}
  ): void {
    this.logger.info(`S3Service: ${operation}`, context);
  }

  /**
   * Log service error with context
   */
  private logError(
    operation: string,
    error: Error,
    context: Record<string, unknown> = {}
  ): void {
    this.logger.error(`S3Service: ${operation} failed`, {
      error: error.message,
      stack: error.stack,
      ...context,
    });
  }
}
