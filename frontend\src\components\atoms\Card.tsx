import React from "react";
import { clsx } from "clsx";

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "elevated" | "interactive";
  children: React.ReactNode;
}

const variantClasses = {
  default:
    "bg-white border border-gray-200 rounded-2xl shadow-sm transition-all duration-300 hover:shadow-md",
  elevated:
    "bg-white border border-gray-100 rounded-2xl shadow-md hover:shadow-lg hover:border-gray-200 transition-all duration-300",
  interactive:
    "bg-white border border-gray-200 rounded-2xl shadow-sm hover:shadow-lg hover:scale-[1.01] hover:border-primary-200 transition-all duration-300 cursor-pointer active:scale-[0.99]",
};

export const Card: React.FC<CardProps> = ({
  variant = "default",
  className,
  children,
  ...props
}) => {
  return (
    <div className={clsx(variantClasses[variant], className)} {...props}>
      {children}
    </div>
  );
};

export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  subtitle?: string;
  children?: React.ReactNode;
}

export const CardHeader: React.FC<CardHeaderProps> = ({
  title,
  subtitle,
  className,
  children,
  ...props
}) => {
  return (
    <div
      className={clsx(
        "border-b border-gray-100 px-6 py-5 bg-gray-50/50",
        className,
      )}
      {...props}
    >
      <h3 className="text-lg font-semibold text-gray-900 tracking-tight">
        {title}
      </h3>
      {subtitle && <p className="text-sm text-gray-600 mt-1">{subtitle}</p>}
      {children}
    </div>
  );
};

export interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export const CardContent: React.FC<CardContentProps> = ({
  className,
  children,
  ...props
}) => {
  return (
    <div className={clsx("p-6 space-y-4", className)} {...props}>
      {children}
    </div>
  );
};
