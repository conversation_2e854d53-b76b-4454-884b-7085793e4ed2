/**
 * Design System Spacing Tokens
 * Based on 8px grid system with Salient-inspired proportions
 *
 * Provides consistent spacing for layouts, components,
 * and responsive design across all platforms
 */

export const spacing = {
  // Base spacing scale (8px grid)
  0: "0px",
  px: "1px",
  0.5: "0.125rem", // 2px
  1: "0.25rem", // 4px
  1.5: "0.375rem", // 6px
  2: "0.5rem", // 8px
  2.5: "0.625rem", // 10px
  3: "0.75rem", // 12px
  3.5: "0.875rem", // 14px
  4: "1rem", // 16px
  5: "1.25rem", // 20px
  6: "1.5rem", // 24px
  7: "1.75rem", // 28px
  8: "2rem", // 32px
  9: "2.25rem", // 36px
  10: "2.5rem", // 40px
  11: "2.75rem", // 44px
  12: "3rem", // 48px
  14: "3.5rem", // 56px
  16: "4rem", // 64px
  20: "5rem", // 80px
  24: "6rem", // 96px
  28: "7rem", // 112px
  32: "8rem", // 128px
  36: "9rem", // 144px
  40: "10rem", // 160px
  44: "11rem", // 176px
  48: "12rem", // 192px
  52: "13rem", // 208px
  56: "14rem", // 224px
  60: "15rem", // 240px
  64: "16rem", // 256px
  72: "18rem", // 288px
  80: "20rem", // 320px
  96: "24rem", // 384px

  // Semantic spacing (Salient-inspired)
  semantic: {
    // Component spacing
    componentXS: "0.25rem", // 4px - Tight spacing within components
    componentSM: "0.5rem", // 8px - Standard component padding
    componentMD: "1rem", // 16px - Comfortable component spacing
    componentLG: "1.5rem", // 24px - Generous component spacing
    componentXL: "2rem", // 32px - Extra spacious components

    // Layout spacing
    layoutXS: "1rem", // 16px - Tight layout spacing
    layoutSM: "1.5rem", // 24px - Standard layout spacing
    layoutMD: "2rem", // 32px - Comfortable layout spacing
    layoutLG: "3rem", // 48px - Generous layout spacing
    layoutXL: "4rem", // 64px - Extra spacious layouts
    layoutXXL: "6rem", // 96px - Maximum layout spacing

    // Section spacing (Salient-inspired)
    sectionSM: "5rem", // 80px - Small section spacing
    sectionMD: "6rem", // 96px - Standard section spacing
    sectionLG: "8rem", // 128px - Large section spacing
    sectionXL: "10rem", // 160px - Extra large section spacing

    // Container spacing
    containerSM: "1rem", // 16px - Mobile container padding
    containerMD: "1.5rem", // 24px - Tablet container padding
    containerLG: "2rem", // 32px - Desktop container padding
  },

  // Responsive spacing breakpoints
  responsive: {
    // Mobile-first spacing
    mobile: {
      section: "3rem", // 48px - Mobile section spacing
      container: "1rem", // 16px - Mobile container padding
      component: "1rem", // 16px - Mobile component spacing
    },
    // Tablet spacing
    tablet: {
      section: "4rem", // 64px - Tablet section spacing
      container: "1.5rem", // 24px - Tablet container padding
      component: "1.5rem", // 24px - Tablet component spacing
    },
    // Desktop spacing
    desktop: {
      section: "6rem", // 96px - Desktop section spacing
      container: "2rem", // 32px - Desktop container padding
      component: "2rem", // 32px - Desktop component spacing
    },
    // Large desktop spacing
    large: {
      section: "8rem", // 128px - Large desktop section spacing
      container: "2rem", // 32px - Large desktop container padding
      component: "2rem", // 32px - Large desktop component spacing
    },
  },

  // Touch targets (for React Native compatibility)
  touch: {
    minimum: "2.75rem", // 44px - Minimum touch target
    comfortable: "3rem", // 48px - Comfortable touch target
    generous: "3.5rem", // 56px - Generous touch target
  },
} as const;

// Type definitions
export type SpacingToken = keyof typeof spacing;
export type SemanticSpacing = keyof typeof spacing.semantic;
export type ResponsiveSpacing = keyof typeof spacing.responsive;

// Utility function for accessing spacing values
export const getSpacing = (path: string): string => {
  const keys = path.split(".");
  let value: unknown = spacing;

  for (const key of keys) {
    if (typeof value === "object" && value !== null && key in value) {
      value = (value as Record<string, unknown>)[key];
    } else {
      throw new Error(`Spacing token "${path}" not found`);
    }
  }

  if (typeof value !== "string") {
    throw new Error(`Spacing token "${path}" is not a string`);
  }

  return value;
};

// Common spacing utilities
export const spacingUtils = {
  // Get responsive spacing for sections
  getSectionSpacing: (
    breakpoint: "mobile" | "tablet" | "desktop" | "large" = "desktop",
  ) => {
    return spacing.responsive[breakpoint].section;
  },

  // Get container padding for breakpoint
  getContainerPadding: (
    breakpoint: "mobile" | "tablet" | "desktop" | "large" = "desktop",
  ) => {
    return spacing.responsive[breakpoint].container;
  },

  // Get component spacing for breakpoint
  getComponentSpacing: (
    breakpoint: "mobile" | "tablet" | "desktop" | "large" = "desktop",
  ) => {
    return spacing.responsive[breakpoint].component;
  },
};

// Export individual scales for convenience
export const { semantic, responsive, touch } = spacing;
