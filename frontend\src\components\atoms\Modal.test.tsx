import { render, screen, cleanup } from "@testing-library/react";
import { Modal } from "./Modal";

describe("Modal", () => {
  afterEach(() => {
    cleanup();
    // Clean up any modal elements that might be left in document.body
    const modalElements = document.body.querySelectorAll('[style*="z-index"]');
    modalElements.forEach(element => element.remove());
  });
  it("should render with proper viewport positioning classes", () => {
    render(
      <Modal isOpen={true} onClose={() => {}} title="Test Modal">
        <div>Modal content</div>
      </Modal>
    );

    // Modal is rendered via portal to document.body, so find it there
    const modalContainer = document.body.querySelector('[style*="z-index"]') as HTMLElement;
    expect(modalContainer).toHaveClass("fixed", "inset-0", "flex", "items-center", "justify-center");

    // Check that the backdrop has fixed positioning for full viewport coverage
    const backdrop = modalContainer.querySelector("div");
    expect(backdrop).toHaveClass("fixed", "inset-0", "bg-black", "bg-opacity-50");
  });

  it("should render modal content with proper z-index stacking", () => {
    render(
      <Modal isOpen={true} onClose={() => {}} title="Test Modal" zIndex={100}>
        <div>Modal content</div>
      </Modal>
    );

    // Modal is rendered via portal to document.body, so find it there
    const modalContainer = document.body.querySelector('[style*="z-index: 101"]') as HTMLElement;
    expect(modalContainer).toHaveStyle("z-index: 101"); // zIndex + 1

    const backdrop = modalContainer.querySelector("div:first-child");
    expect(backdrop).toHaveStyle("z-index: 100"); // base zIndex

    const modalContent = modalContainer.querySelector("div:nth-child(2)"); // Second div is the modal content
    expect(modalContent).toHaveStyle("z-index: 102"); // zIndex + 2
  });

  it("should not render when isOpen is false", () => {
    render(
      <Modal isOpen={false} onClose={() => {}} title="Test Modal">
        <div>Modal content</div>
      </Modal>
    );

    expect(screen.queryByText("Test Modal")).not.toBeInTheDocument();
  });
});
