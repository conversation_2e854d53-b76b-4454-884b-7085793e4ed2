import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import { StatDisplay } from "./StatDisplay";
import { Users, DollarSign } from "lucide-react";

describe("StatDisplay", () => {
  const defaultProps = {
    label: "Total Users",
    value: "12,345",
  };

  describe("Rendering", () => {
    it("should render stat display with label and value", () => {
      render(<StatDisplay {...defaultProps} />);

      expect(screen.getByText("TOTAL USERS")).toBeInTheDocument();
      expect(screen.getByText("12,345")).toBeInTheDocument();
    });

    it("should render with custom className", () => {
      render(<StatDisplay {...defaultProps} className="custom-stat" />);

      const container = screen.getByText("TOTAL USERS").closest(".custom-stat");
      expect(container).toBeInTheDocument();
    });

    it("should render with icon", () => {
      render(
        <StatDisplay
          {...defaultProps}
          icon={<Users data-testid="users-icon" />}
        />,
      );

      expect(screen.getByTestId("users-icon")).toBeInTheDocument();
    });

    it("should render with value prefix and suffix", () => {
      render(
        <StatDisplay
          {...defaultProps}
          value="45.2"
          valuePrefix="$"
          valueSuffix="K"
        />,
      );

      expect(screen.getByText("$45.2K")).toBeInTheDocument();
    });

    it("should uppercase the label", () => {
      render(<StatDisplay label="total revenue" value="100" />);

      expect(screen.getByText("TOTAL REVENUE")).toBeInTheDocument();
    });
  });

  describe("Variants", () => {
    it("should render default variant with white background", () => {
      render(<StatDisplay {...defaultProps} variant="default" />);

      const container = screen.getByTestId("stat-display-container");
      expect(container).toHaveClass("bg-white");
    });

    it("should render card variant with shadow and border", () => {
      render(<StatDisplay {...defaultProps} variant="card" />);

      const container = screen.getByTestId("stat-display-container");
      expect(container).toHaveClass(
        "bg-white",
        "shadow-xs",
        "hover:shadow-sm",
        "rounded-lg",
        "border",
      );
    });

    it("should render minimal variant with transparent background", () => {
      render(<StatDisplay {...defaultProps} variant="minimal" />);

      const container = screen.getByTestId("stat-display-container");
      expect(container).toHaveClass("bg-transparent");
    });

    it("should render highlighted variant with gradient background", () => {
      render(<StatDisplay {...defaultProps} variant="highlighted" />);

      const container = screen.getByTestId("stat-display-container");
      expect(container).toHaveClass(
        "bg-gradient-to-br",
        "from-primary-50",
        "to-primary-100/50",
      );
    });
  });

  describe("Sizes", () => {
    it("should render small size with appropriate classes", () => {
      render(<StatDisplay {...defaultProps} size="sm" />);

      const container = screen.getByTestId("stat-display-container");
      const label = screen.getByText("TOTAL USERS");
      const value = screen.getByText("12,345");

      expect(container).toHaveClass("p-3");
      expect(label).toHaveClass("text-xs");
      expect(value).toHaveClass("text-lg");
    });

    it("should render medium size with appropriate classes (default)", () => {
      render(<StatDisplay {...defaultProps} size="md" />);

      const container = screen.getByTestId("stat-display-container");
      const label = screen.getByText("TOTAL USERS");
      const value = screen.getByText("12,345");

      expect(container).toHaveClass("p-4");
      expect(label).toHaveClass("text-sm");
      expect(value).toHaveClass("text-2xl");
    });

    it("should render large size with appropriate classes", () => {
      render(<StatDisplay {...defaultProps} size="lg" />);

      const container = screen.getByTestId("stat-display-container");
      const label = screen.getByText("TOTAL USERS");
      const value = screen.getByText("12,345");

      expect(container).toHaveClass("p-6");
      expect(label).toHaveClass("text-base");
      expect(value).toHaveClass("text-3xl");
    });

    it("should render extra large size with appropriate classes", () => {
      render(<StatDisplay {...defaultProps} size="xl" />);

      const container = screen.getByTestId("stat-display-container");
      const label = screen.getByText("TOTAL USERS");
      const value = screen.getByText("12,345");

      expect(container).toHaveClass("p-8");
      expect(label).toHaveClass("text-lg");
      expect(value).toHaveClass("text-4xl");
    });
  });

  describe("Themes", () => {
    it("should render default theme with gray colors", () => {
      render(<StatDisplay {...defaultProps} theme="default" />);

      const label = screen.getByText("TOTAL USERS");
      const value = screen.getByText("12,345");

      expect(label).toHaveClass("text-gray-600");
      expect(value).toHaveClass("text-gray-900");
    });

    it("should render primary theme with primary colors", () => {
      render(<StatDisplay {...defaultProps} theme="primary" />);

      const label = screen.getByText("TOTAL USERS");
      const value = screen.getByText("12,345");

      expect(label).toHaveClass("text-primary-700");
      expect(value).toHaveClass("text-primary-900");
    });

    it("should render success theme with green colors", () => {
      render(<StatDisplay {...defaultProps} theme="success" />);

      const label = screen.getByText("TOTAL USERS");
      const value = screen.getByText("12,345");

      expect(label).toHaveClass("text-green-700");
      expect(value).toHaveClass("text-green-900");
    });

    it("should render warning theme with yellow colors", () => {
      render(<StatDisplay {...defaultProps} theme="warning" />);

      const label = screen.getByText("TOTAL USERS");
      const value = screen.getByText("12,345");

      expect(label).toHaveClass("text-yellow-700");
      expect(value).toHaveClass("text-yellow-900");
    });

    it("should render error theme with red colors", () => {
      render(<StatDisplay {...defaultProps} theme="error" />);

      const label = screen.getByText("TOTAL USERS");
      const value = screen.getByText("12,345");

      expect(label).toHaveClass("text-red-700");
      expect(value).toHaveClass("text-red-900");
    });
  });

  describe("Orientation", () => {
    it("should render vertical orientation by default", () => {
      render(<StatDisplay {...defaultProps} />);

      const container = screen.getByTestId("stat-display-container");
      expect(container).toHaveClass("flex-col", "text-center");
    });

    it("should render horizontal orientation", () => {
      render(<StatDisplay {...defaultProps} orientation="horizontal" />);

      const container = screen.getByTestId("stat-display-container");
      expect(container).toHaveClass(
        "flex-row",
        "items-center",
        "justify-between",
      );
    });

    it("should position icon correctly in horizontal orientation", () => {
      render(
        <StatDisplay
          {...defaultProps}
          orientation="horizontal"
          icon={<Users data-testid="icon" />}
        />,
      );

      const iconContainer =
        screen.getByTestId("icon").parentElement?.parentElement;
      expect(iconContainer).toHaveClass("flex-shrink-0");
    });
  });

  describe("Change/Trend Display", () => {
    it("should render positive change with up trend", () => {
      const change = {
        value: "+12%",
        trend: "up" as const,
        period: "vs last month",
      };

      render(<StatDisplay {...defaultProps} change={change} />);

      expect(screen.getByText("+12%")).toBeInTheDocument();
      expect(screen.getByText("vs last month")).toBeInTheDocument();

      const changeText = screen.getByText("+12%");
      expect(changeText).toHaveClass("text-green-600");
    });

    it("should render negative change with down trend", () => {
      const change = {
        value: "-5%",
        trend: "down" as const,
        period: "vs last month",
      };

      render(<StatDisplay {...defaultProps} change={change} />);

      expect(screen.getByText("-5%")).toBeInTheDocument();

      const changeText = screen.getByText("-5%");
      expect(changeText).toHaveClass("text-red-600");
    });

    it("should render neutral change", () => {
      const change = {
        value: "0%",
        trend: "neutral" as const,
      };

      render(<StatDisplay {...defaultProps} change={change} />);

      expect(screen.getByText("0%")).toBeInTheDocument();

      const changeText = screen.getByText("0%");
      expect(changeText).toHaveClass("text-gray-600");
    });

    it("should format percentage changes correctly", () => {
      const change = {
        value: 15,
        trend: "up" as const,
        percentage: true,
      };

      render(<StatDisplay {...defaultProps} change={change} />);

      expect(screen.getByText("+15%")).toBeInTheDocument();
    });

    it("should render trend icons by default", () => {
      const change = {
        value: "+12%",
        trend: "up" as const,
      };

      render(<StatDisplay {...defaultProps} change={change} />);

      // Check for trending up icon (lucide-react TrendingUp)
      const trendIcon = document.querySelector("svg");
      expect(trendIcon).toBeInTheDocument();
    });

    it("should hide trend icons when showTrendIcon is false", () => {
      const change = {
        value: "+12%",
        trend: "up" as const,
      };

      render(
        <StatDisplay {...defaultProps} change={change} showTrendIcon={false} />,
      );

      // Should still show the change text but no icon
      expect(screen.getByText("+12%")).toBeInTheDocument();
      const trendIcon = document.querySelector("svg");
      expect(trendIcon).toBeNull();
    });
  });

  describe("Icon Display", () => {
    it("should render icon with themed background", () => {
      render(
        <StatDisplay
          {...defaultProps}
          icon={<DollarSign data-testid="dollar-icon" />}
          theme="success"
        />,
      );

      const iconContainer =
        screen.getByTestId("dollar-icon").parentElement?.parentElement;
      expect(iconContainer).toHaveClass("bg-green-100", "text-green-600");
    });

    it("should size icon container based on size prop", () => {
      render(
        <StatDisplay
          {...defaultProps}
          icon={<Users data-testid="icon" />}
          size="lg"
        />,
      );

      const iconContainer =
        screen.getByTestId("icon").parentElement?.parentElement;
      expect(iconContainer).toHaveClass("h-12", "w-12");
    });
  });

  describe("Accessibility", () => {
    it("should render semantic content structure", () => {
      render(<StatDisplay {...defaultProps} />);

      expect(screen.getByText("TOTAL USERS")).toBeInTheDocument();
      expect(screen.getByText("12,345")).toBeInTheDocument();
    });

    it("should support custom attributes", () => {
      render(
        <StatDisplay
          {...defaultProps}
          data-testid="custom-stat"
          aria-label="User statistics"
        />,
      );

      const container = screen.getByTestId("custom-stat");
      expect(container).toHaveAttribute("aria-label", "User statistics");
    });
  });
});
