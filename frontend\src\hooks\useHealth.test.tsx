import { describe, it, expect, beforeEach } from "vitest";
import { renderHook, waitFor } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import React from "react";
import { useHealth } from "../../src/hooks/useHealth";
import {
  createMockHealthService,
  setupMockHealthService,
  testData,
  type MockHealthService,
} from "../__tests__/helpers/service-factory.helper";

// Test wrapper with React Query provider
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: 3, // Enable retries for testing
        gcTime: 0,
        retryDelay: 0, // No delay for faster tests
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe("useHealth", () => {
  let mockHealthService: MockHealthService;

  beforeEach(() => {
    mockHealthService = createMockHealthService();
  });

  describe("Successful Health Check", () => {
    it("should return health data when service call succeeds", async () => {
      // Arrange - Setup mock to return success
      setupMockHealthService(mockHealthService, "success");

      // Act - Render hook with injected service
      const { result } = renderHook(() => useHealth(mockHealthService), {
        wrapper: createWrapper(),
      });

      // Assert - Verify initial loading state
      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
      expect(result.current.error).toBeNull();

      // Wait for query to complete
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Assert - Verify successful data fetch
      expect(result.current.data).toEqual(testData.healthResponse);
      expect(result.current.error).toBeNull();
      expect(result.current.isSuccess).toBe(true);
      expect(mockHealthService.checkHealth).toHaveBeenCalledTimes(1);
    });

    it("should use custom refetch interval when provided", async () => {
      // Arrange
      setupMockHealthService(mockHealthService, "success");
      const customInterval = 30000;

      // Act
      const { result } = renderHook(
        () => useHealth(mockHealthService, customInterval),
        { wrapper: createWrapper() },
      );

      // Wait for initial load
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Assert - Verify the hook was called with correct parameters
      expect(result.current.data).toEqual(testData.healthResponse);
    });

    it("should provide refetch functionality", async () => {
      // Arrange
      setupMockHealthService(mockHealthService, "success");

      // Act
      const { result } = renderHook(() => useHealth(mockHealthService), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Reset mock call count
      mockHealthService.checkHealth.mockClear();

      // Trigger refetch
      result.current.refetch();

      // Assert - Verify refetch calls service again
      await waitFor(() => {
        expect(mockHealthService.checkHealth).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe("Error Handling", () => {
    it("should handle service errors gracefully", async () => {
      // Arrange - Setup mock to throw error
      setupMockHealthService(mockHealthService, "error");

      // Act
      const { result } = renderHook(() => useHealth(mockHealthService), {
        wrapper: createWrapper(),
      });

      // Wait for query to complete (including retries)
      await waitFor(
        () => {
          expect(result.current.isError).toBe(true);
        },
        { timeout: 3000 },
      );

      // Assert - Verify error state
      expect(result.current.error).toBeTruthy();
      expect(result.current.data).toBeUndefined();
      expect(result.current.isLoading).toBe(false);
      expect(mockHealthService.checkHealth).toHaveBeenCalledTimes(4); // initial + 3 retries
    });

    it("should retry failed requests according to configuration", async () => {
      // Arrange - Setup mock to fail
      setupMockHealthService(mockHealthService, "error");

      // Act
      const { result } = renderHook(() => useHealth(mockHealthService), {
        wrapper: createWrapper(),
      });

      // Wait for all retries to complete
      await waitFor(
        () => {
          expect(result.current.isError).toBe(true);
        },
        { timeout: 3000 },
      );

      // Assert - Verify retry attempts (initial + 3 retries = 4 total)
      expect(mockHealthService.checkHealth).toHaveBeenCalledTimes(4);
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe("Loading States", () => {
    it("should show loading state during initial fetch", () => {
      // Arrange - Setup mock that never resolves
      setupMockHealthService(mockHealthService, "loading");

      // Act
      const { result } = renderHook(() => useHealth(mockHealthService), {
        wrapper: createWrapper(),
      });

      // Assert - Verify loading state
      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
      expect(result.current.error).toBeNull();
    });
  });

  describe("Service Dependency Injection", () => {
    it("should call the injected health service", async () => {
      // Arrange
      setupMockHealthService(mockHealthService, "success");

      // Act
      const { result } = renderHook(() => useHealth(mockHealthService), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Assert - Verify the correct service was called
      expect(mockHealthService.checkHealth).toHaveBeenCalledWith();
      expect(mockHealthService.checkHealth).toHaveBeenCalledTimes(1);
    });

    it("should work with different service implementations", async () => {
      // Arrange - Create a different mock service
      const alternativeService = createMockHealthService();
      const alternativeResponse = {
        data: {
          status: "ok",
          environment: "staging",
          database: "connected",
        },
        timestamp: "2024-01-02T00:00:00.000Z",
      };
      alternativeService.checkHealth.mockResolvedValue(alternativeResponse);

      // Act
      const { result } = renderHook(() => useHealth(alternativeService), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Assert - Verify the alternative service was used
      expect(result.current.data).toEqual(alternativeResponse);
      expect(alternativeService.checkHealth).toHaveBeenCalledTimes(1);
      expect(mockHealthService.checkHealth).not.toHaveBeenCalled();
    });
  });
});
