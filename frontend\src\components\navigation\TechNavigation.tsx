import React from "react";
import { Link, useLocation } from "react-router-dom";
import { Home, QrCode, ClipboardList, Zap } from "lucide-react";

interface NavItem {
  path: string;
  icon: React.ReactNode;
  label: string;
}

const navItems: NavItem[] = [
  {
    path: "/tech",
    icon: <Home className="h-6 w-6" />,
    label: "Home",
  },
  {
    path: "/tech/scan",
    icon: <QrCode className="h-6 w-6" />,
    label: "Scan",
  },
  {
    path: "/tech/work-orders",
    icon: <ClipboardList className="h-6 w-6" />,
    label: "Orders",
  },
  {
    path: "/tech/quick-actions",
    icon: <Zap className="h-6 w-6" />,
    label: "Actions",
  },
];

export const TechNavigation: React.FC = () => {
  const location = useLocation();

  const isActiveLink = (path: string) => {
    if (path === "/tech") {
      return location.pathname === "/tech";
    }
    return location.pathname.startsWith(path);
  };

  return (
    <nav className="bg-white/95 backdrop-blur-sm border-t border-primary-100/50 sticky bottom-0 z-50 shadow-lg">
      <div className="grid grid-cols-4 h-18">
        {navItems.map((item) => {
          const isActive = isActiveLink(item.path);

          return (
            <Link
              key={item.path}
              to={item.path}
              className={`
                flex flex-col items-center justify-center space-y-2
                min-h-[56px] transition-all duration-200 relative
                ${
                  isActive
                    ? "text-primary-700 bg-gradient-to-t from-primary-50 to-primary-25"
                    : "text-gray-600 hover:text-primary-600 hover:bg-primary-50/50"
                }
              `}
            >
              {/* Active indicator */}
              {isActive && (
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full" />
              )}

              <div
                className={`flex-shrink-0 p-1 rounded-lg transition-all duration-200 ${
                  isActive
                    ? "bg-primary-100/60 shadow-sm"
                    : "hover:bg-primary-100/40"
                }`}
              >
                {item.icon}
              </div>
              <span
                className={`text-xs font-semibold leading-none ${
                  isActive ? "text-primary-800" : ""
                }`}
              >
                {item.label}
              </span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
};
