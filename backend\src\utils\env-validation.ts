import path from 'path';

import dotenv from 'dotenv';
import { z } from 'zod';

// Load environment variables before validation
// Use process.cwd() for cross-platform compatibility (works in both ESM and CommonJS contexts)
dotenv.config({ path: path.resolve(process.cwd(), '.env') });
dotenv.config({
  path: path.resolve(process.cwd(), '.env.local'),
  override: true,
});

const envSchema = z.object({
  NODE_ENV: z
    .enum(['development', 'production', 'test'])
    .default('development'),
  PORT: z
    .string()
    .default('8080')
    .transform((val) => {
      const port = parseInt(val, 10);
      if (isNaN(port) || port < 1 || port > 65535) {
        throw new Error(
          `Invalid PORT value: ${val}. Must be a number between 1 and 65535.`
        );
      }
      return port;
    }),
  DATABASE_URL: z.string().min(1, 'DATABASE_URL is required'),
  TEST_DATABASE_URL: z.string().optional(),
  CLERK_SECRET_KEY: z.string().min(1, 'CLERK_SECRET_KEY is required'),
  CLERK_PUBLISHABLE_KEY: z.string().min(1, 'CLERK_PUBLISHABLE_KEY is required'),
  CORS_ORIGIN: z
    .string()
    .default('http://localhost:5173')
    .transform((val) => {
      // Clean up any trailing equals or other invalid characters from each URL
      const cleaned = val
        .split(/[;,]/)
        .map((url) => url.trim().replace(/=+$/, ''))
        .filter((url) => url.length > 0)
        .join(',');

      if (!cleaned) {
        throw new Error('CORS_ORIGIN cannot be empty after cleaning');
      }
      return cleaned;
    }),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  REDIS_URL: z.string().optional(),
  FRONTEND_URL: z
    .string()
    .url('FRONTEND_URL must be a valid URL')
    .default('http://localhost:5173')
    .transform((val) => {
      // Remove trailing slash for consistency
      return val.replace(/\/$/, '');
    }),
  // AWS S3 Configuration
  AWS_REGION: z.string().min(1, 'AWS_REGION is required for S3 operations'),
  AWS_S3_BUCKET_NAME: z
    .string()
    .min(1, 'AWS_S3_BUCKET_NAME is required for S3 operations'),
  AWS_ACCESS_KEY_ID: z
    .string()
    .min(1, 'AWS_ACCESS_KEY_ID is required for S3 operations'),
  AWS_SECRET_ACCESS_KEY: z
    .string()
    .min(1, 'AWS_SECRET_ACCESS_KEY is required for S3 operations'),
});

export type EnvConfig = z.infer<typeof envSchema>;

export function validateEnvironment(): EnvConfig {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors
        .map((err) => err.path.join('.'))
        .join(', ');
      throw new Error(
        `Environment validation failed. Missing or invalid variables: ${missingVars}`
      );
    }
    throw error;
  }
}

// Validate environment on module load
export const env = validateEnvironment();
