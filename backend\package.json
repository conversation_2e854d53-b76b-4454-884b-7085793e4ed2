{"name": "tech-notes-backend", "version": "1.0.0", "description": "Tech Notes Backend API", "type": "module", "main": "dist/index.js", "scripts": {"dev": "tsx src/index.ts", "start": "yarn run db:migrate && yarn run db:seed:prod && node dist/index.js", "start:dev": "node dist/index.js", "build": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON> --noEmit<PERSON>n<PERSON><PERSON>r false", "db:migrate": "prisma migrate deploy", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "format": "prettier --write src", "check": "yarn run test && yarn run lint && yarn run format && yarn run build", "clean": "rm -rf dist", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "tsx prisma/seed.ts", "db:seed:prod": "echo 'Production seeding handled by migrations - no additional seeding required'", "seed:dev:roles": "tsx --no-cache scripts/seed-dev-roles.ts", "get-clerk-ids": "tsx scripts/get-clerk-ids.ts", "db:studio": "prisma studio", "list:test-dbs": "tsx scripts/list-test-databases.ts", "cleanup:test-dbs": "tsx scripts/cleanup-test-databases.ts", "cleanup:test-dbs:dry": "tsx scripts/cleanup-test-databases.ts --dry-run", "cleanup:test-dbs:force": "tsx scripts/cleanup-test-databases.ts --force"}, "dependencies": {"@aws-sdk/client-s3": "^3.850.0", "@aws-sdk/s3-request-presigner": "^3.850.0", "@clerk/clerk-sdk-node": "^4.13.14", "@prisma/client": "^5.6.0", "@types/cors": "^2.8.19", "@types/helmet": "^0.0.48", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "helmet": "^8.1.0", "prisma": "^5.6.0", "redis": "^4.6.0", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/express": "^5.0.3", "@types/jest": "^29.5.8", "@types/pg": "^8.10.9", "@types/redis": "^4.0.11", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "pg": "^8.11.3", "prettier": "^3.1.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsx": "^4.20.3"}, "keywords": ["express", "typescript", "prisma", "clerk", "multi-tenant"], "author": "Tech Notes Team", "license": "MIT"}