import React, { useState, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import {
  <PERSON>,
  Card<PERSON>eader,
  Card<PERSON><PERSON>nt,
  <PERSON><PERSON>,
  LoadingSpinner,
  Alert,
  Input,
  CompanyAdminOnly,
} from "../../components";
import { useTypedApi } from "../../services/api-client";
import { VehicleHierarchyTree } from "./components/VehicleHierarchyTree";
import { VehicleHierarchyFilters } from "./components/VehicleHierarchyFilters";
import { CreateYearModal } from "./components/CreateYearModal";
import { CreateMakeModal } from "./components/CreateMakeModal";
import { CreateModelModal } from "./components/CreateModelModal";
import { BulkOperationsModal } from "./components/BulkOperationsModal";
import { YearsManagementModal } from "./components/YearsManagementModal";
import { MakesManagementModal } from "./components/MakesManagementModal";
import { ModelsManagementModal } from "./components/ModelsManagementModal";
import { ModelYearAssociationModal } from "./components/ModelYearAssociationModal";
import type {
  VehicleYear,
  VehicleMake,
  VehicleModel,
} from "../../services/api-client";
import {
  Calendar,
  Car,
  Settings,
  Upload,
  Search,
  Filter,
  Download,
} from "lucide-react";

export const VehicleHierarchyPage: React.FC = () => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  // Modal states
  const [isCreateYearModalOpen, setIsCreateYearModalOpen] = useState(false);
  const [isCreateMakeModalOpen, setIsCreateMakeModalOpen] = useState(false);
  const [isCreateModelModalOpen, setIsCreateModelModalOpen] = useState(false);
  const [isBulkOperationsModalOpen, setIsBulkOperationsModalOpen] =
    useState(false);
  const [isYearsManagementModalOpen, setIsYearsManagementModalOpen] =
    useState(false);
  const [isMakesManagementModalOpen, setIsMakesManagementModalOpen] =
    useState(false);
  const [isModelsManagementModalOpen, setIsModelsManagementModalOpen] =
    useState(false);
  const [isModelYearAssociationModalOpen, setIsModelYearAssociationModalOpen] =
    useState(false);
  const [selectedMakeForModel, setSelectedMakeForModel] =
    useState<VehicleMake | null>(null);
  const [selectedModelForYearAssociation, setSelectedModelForYearAssociation] =
    useState<VehicleModel | null>(null);

  // Search and filter states
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedYearFilter, setSelectedYearFilter] = useState<string>("");
  const [selectedMakeFilter, setSelectedMakeFilter] = useState<string>("");
  const [showFilters, setShowFilters] = useState(false);

  // Fetch hierarchy data
  const {
    data: hierarchyResponse,
    isLoading: isLoadingHierarchy,
    error: hierarchyError,
    refetch: refetchHierarchy,
  } = useQuery({
    queryKey: ["vehicle-hierarchy-full"],
    queryFn: () => api.vehicleHierarchy.getFullHierarchy(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Fetch individual data for stats
  const { data: yearsResponse, isLoading: isLoadingYears } = useQuery({
    queryKey: ["vehicle-years"],
    queryFn: () => api.vehicleHierarchy.getYears(),
    staleTime: 2 * 60 * 1000,
  });

  const { data: makesResponse, isLoading: isLoadingMakes } = useQuery({
    queryKey: ["vehicle-makes"],
    queryFn: () => api.vehicleHierarchy.getMakes(),
    staleTime: 2 * 60 * 1000,
  });

  // Delete mutations
  const deleteYearMutation = useMutation({
    mutationFn: (yearId: string) => api.vehicleHierarchy.deleteYear(yearId),
    onSuccess: () => {
      toast.success("Year deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-years"] });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete year");
    },
  });

  const deleteMakeMutation = useMutation({
    mutationFn: (makeId: string) => api.vehicleHierarchy.deleteMake(makeId),
    onSuccess: () => {
      toast.success("Make deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-makes"] });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete make");
    },
  });

  const deleteModelMutation = useMutation({
    mutationFn: (modelId: string) => api.vehicleHierarchy.deleteModel(modelId),
    onSuccess: () => {
      toast.success("Model deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete model");
    },
  });

  // Event handlers
  const handleCreateModel = (make: VehicleMake) => {
    setSelectedMakeForModel(make);
    setIsCreateModelModalOpen(true);
  };

  const handleManageModelYears = (model: VehicleModel) => {
    setSelectedModelForYearAssociation(model);
    setIsModelYearAssociationModalOpen(true);
  };

  const handleDeleteYear = async (year: VehicleYear) => {
    if (
      window.confirm(
        `Are you sure you want to delete year ${year.year}? This will also remove all associated model-year relationships.`,
      )
    ) {
      deleteYearMutation.mutate(year.id);
    }
  };

  const handleDeleteMake = async (make: VehicleMake) => {
    if (
      window.confirm(
        `Are you sure you want to delete make "${make.name}"? This will also delete all associated models.`,
      )
    ) {
      deleteMakeMutation.mutate(make.id);
    }
  };

  const handleDeleteModel = async (model: VehicleModel) => {
    if (
      window.confirm(`Are you sure you want to delete model "${model.name}"?`)
    ) {
      deleteModelMutation.mutate(model.id);
    }
  };

  const handleModalClose = () => {
    setIsCreateYearModalOpen(false);
    setIsCreateMakeModalOpen(false);
    setIsCreateModelModalOpen(false);
    setIsBulkOperationsModalOpen(false);
    setIsYearsManagementModalOpen(false);
    setIsMakesManagementModalOpen(false);
    setIsModelsManagementModalOpen(false);
    setIsModelYearAssociationModalOpen(false);
    setIsYearsManagementModalOpen(false);
    setIsMakesManagementModalOpen(false);
    setIsModelsManagementModalOpen(false);
    setSelectedMakeForModel(null);
    setSelectedModelForYearAssociation(null);
  };

  // Filter and search logic
  const filteredHierarchy = useMemo(() => {
    if (!hierarchyResponse?.data) return null;

    const { years } = hierarchyResponse.data;

    // Apply filters
    let filteredYears = years;

    // Year filter
    if (selectedYearFilter) {
      filteredYears = filteredYears.filter(
        (year) => year.id === selectedYearFilter,
      );
    }

    // Search and make filter
    if (searchQuery || selectedMakeFilter) {
      filteredYears = filteredYears
        .map((year) => ({
          ...year,
          makes: year.makes
            .filter((make) => {
              // Make filter
              if (selectedMakeFilter && make.id !== selectedMakeFilter) {
                return false;
              }

              // Search filter
              if (searchQuery) {
                const query = searchQuery.toLowerCase();
                const makeMatches = make.name.toLowerCase().includes(query);
                const modelMatches = make.models.some((model) =>
                  model.name.toLowerCase().includes(query),
                );
                return makeMatches || modelMatches;
              }

              return true;
            })
            .map((make) => ({
              ...make,
              models: searchQuery
                ? make.models.filter(
                    (model) =>
                      model.name
                        .toLowerCase()
                        .includes(searchQuery.toLowerCase()) ||
                      make.name
                        .toLowerCase()
                        .includes(searchQuery.toLowerCase()),
                  )
                : make.models,
            })),
        }))
        .filter((year) => year.makes.length > 0);
    }

    return { years: filteredYears };
  }, [
    hierarchyResponse?.data,
    searchQuery,
    selectedYearFilter,
    selectedMakeFilter,
  ]);

  const clearFilters = () => {
    setSearchQuery("");
    setSelectedYearFilter("");
    setSelectedMakeFilter("");
  };

  // Export functionality
  const exportToCSV = useMutation({
    mutationFn: () => api.vehicleHierarchy.exportToCSV(),
    onSuccess: (csvData) => {
      const blob = new Blob([csvData], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");

      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute(
          "download",
          `vehicle-hierarchy-${new Date().toISOString().split("T")[0]}.csv`,
        );
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

      toast.success("Vehicle hierarchy exported to CSV");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to export CSV");
    },
  });

  const exportToJSON = useMutation({
    mutationFn: () => api.vehicleHierarchy.exportToJSON(),
    onSuccess: (blob) => {
      const link = document.createElement("a");

      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute(
          "download",
          `vehicle-hierarchy-${new Date().toISOString().split("T")[0]}.json`,
        );
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

      toast.success("Vehicle hierarchy exported to JSON");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to export JSON");
    },
  });

  // Calculate stats
  const totalYears = yearsResponse?.data.length || 0;
  const totalMakes = makesResponse?.data.length || 0;
  const totalModels =
    hierarchyResponse?.data.years.reduce(
      (total, year) =>
        total +
        year.makes.reduce(
          (makeTotal, make) => makeTotal + make.models.length,
          0,
        ),
      0,
    ) || 0;

  const isLoading = isLoadingHierarchy || isLoadingYears || isLoadingMakes;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (hierarchyError) {
    return (
      <Alert variant="error" className="m-6">
        <p>Error loading vehicle hierarchy: {hierarchyError.message}</p>
        <Button onClick={() => refetchHierarchy()} className="mt-2">
          Retry
        </Button>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Vehicle Hierarchy
          </h1>
          <p className="text-gray-600 mt-1">
            Manage years, makes, and models for your vehicle specifications
          </p>
        </div>

        <div className="flex items-center space-x-3">
          {/* Export buttons - available to all users */}
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={() => exportToCSV.mutate()}
              disabled={exportToCSV.isPending}
              className="flex items-center space-x-2"
            >
              {exportToCSV.isPending ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
              ) : (
                <Download className="h-4 w-4" />
              )}
              <span>Export CSV</span>
            </Button>

            <Button
              variant="outline"
              onClick={() => exportToJSON.mutate()}
              disabled={exportToJSON.isPending}
              className="flex items-center space-x-2"
            >
              {exportToJSON.isPending ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
              ) : (
                <Download className="h-4 w-4" />
              )}
              <span>Export JSON</span>
            </Button>
          </div>

          <CompanyAdminOnly>
            <div className="flex items-center space-x-3 border-l border-gray-300 pl-3">
              <Button
                variant="outline"
                onClick={() => setIsBulkOperationsModalOpen(true)}
                className="flex items-center space-x-2"
              >
                <Upload className="h-4 w-4" />
                <span>Bulk Operations</span>
              </Button>

              <div className="flex items-center space-x-2">
                <Button
                  onClick={() => setIsCreateYearModalOpen(true)}
                  className="flex items-center space-x-2"
                >
                  <Calendar className="h-4 w-4" />
                  <span>Add Year</span>
                </Button>

                <Button
                  onClick={() => setIsCreateMakeModalOpen(true)}
                  className="flex items-center space-x-2"
                >
                  <Car className="h-4 w-4" />
                  <span>Add Make</span>
                </Button>
              </div>
            </div>
          </CompanyAdminOnly>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card
          className="cursor-pointer hover:shadow-lg transition-shadow duration-200"
          onClick={() => setIsYearsManagementModalOpen(true)}
        >
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Years</p>
                <p className="text-2xl font-bold text-gray-900">{totalYears}</p>
                <p className="text-xs text-gray-500 mt-1">Click to manage</p>
              </div>
              <Calendar className="h-8 w-8 text-primary-600" />
            </div>
          </CardContent>
        </Card>

        <Card
          className="cursor-pointer hover:shadow-lg transition-shadow duration-200"
          onClick={() => setIsMakesManagementModalOpen(true)}
        >
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Makes</p>
                <p className="text-2xl font-bold text-gray-900">{totalMakes}</p>
                <p className="text-xs text-gray-500 mt-1">Click to manage</p>
              </div>
              <Car className="h-8 w-8 text-primary-600" />
            </div>
          </CardContent>
        </Card>

        <Card
          className="cursor-pointer hover:shadow-lg transition-shadow duration-200"
          onClick={() => setIsModelsManagementModalOpen(true)}
        >
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Models
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {totalModels}
                </p>
                <p className="text-xs text-gray-500 mt-1">Click to manage</p>
              </div>
              <Settings className="h-8 w-8 text-primary-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Search Bar */}
            <div className="flex items-center space-x-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search makes and models..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2"
              >
                <Filter className="h-4 w-4" />
                <span>Filters</span>
              </Button>
              {(searchQuery || selectedYearFilter || selectedMakeFilter) && (
                <Button
                  variant="outline"
                  onClick={clearFilters}
                  className="text-gray-600"
                >
                  Clear
                </Button>
              )}
            </div>

            {/* Filter Controls */}
            {showFilters && (
              <VehicleHierarchyFilters
                years={yearsResponse?.data || []}
                makes={makesResponse?.data || []}
                selectedYearFilter={selectedYearFilter}
                selectedMakeFilter={selectedMakeFilter}
                onYearFilterChange={setSelectedYearFilter}
                onMakeFilterChange={setSelectedMakeFilter}
              />
            )}
          </div>
        </CardContent>
      </Card>

      {/* Hierarchy Tree */}
      <Card>
        <CardHeader
          title="Vehicle Hierarchy Tree"
          subtitle={
            searchQuery || selectedYearFilter || selectedMakeFilter
              ? "Filtered results - use search and filters above to refine"
              : "Expand and collapse to navigate the hierarchy"
          }
        />
        <CardContent>
          {filteredHierarchy && (
            <VehicleHierarchyTree
              hierarchy={filteredHierarchy}
              onCreateModel={handleCreateModel}
              onManageModelYears={handleManageModelYears}
              onDeleteYear={handleDeleteYear}
              onDeleteMake={handleDeleteMake}
              onDeleteModel={handleDeleteModel}
              searchQuery={searchQuery}
            />
          )}
        </CardContent>
      </Card>

      {/* Modals */}
      <CreateYearModal
        isOpen={isCreateYearModalOpen}
        onClose={handleModalClose}
      />

      <CreateMakeModal
        isOpen={isCreateMakeModalOpen}
        onClose={handleModalClose}
      />

      <CreateModelModal
        isOpen={isCreateModelModalOpen}
        onClose={handleModalClose}
        selectedMake={selectedMakeForModel}
      />

      <BulkOperationsModal
        isOpen={isBulkOperationsModalOpen}
        onClose={handleModalClose}
      />

      <YearsManagementModal
        isOpen={isYearsManagementModalOpen}
        onClose={handleModalClose}
      />

      <MakesManagementModal
        isOpen={isMakesManagementModalOpen}
        onClose={handleModalClose}
      />

      <ModelsManagementModal
        isOpen={isModelsManagementModalOpen}
        onClose={handleModalClose}
        onManageModelYears={handleManageModelYears}
      />

      <ModelYearAssociationModal
        isOpen={isModelYearAssociationModalOpen}
        onClose={handleModalClose}
        model={selectedModelForYearAssociation}
      />

      <YearsManagementModal
        isOpen={isYearsManagementModalOpen}
        onClose={handleModalClose}
      />

      <MakesManagementModal
        isOpen={isMakesManagementModalOpen}
        onClose={handleModalClose}
      />

      <ModelsManagementModal
        isOpen={isModelsManagementModalOpen}
        onClose={handleModalClose}
        onManageModelYears={handleManageModelYears}
      />
    </div>
  );
};
