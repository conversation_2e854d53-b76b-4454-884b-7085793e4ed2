# Docker Deployment Implementation Guide

Essential Docker patterns for multi-stage builds, optimization, and Render deployment.

---

## 🏗️ Docker Architecture

### Multi-Stage Build Strategy

- **Builder stage**: Install dependencies and build application
- **Runtime stage**: Copy only production files for minimal image size

### Container Structure

- **Backend**: Node.js Alpine + Express (port 8080)
- **Frontend**: Node.js build → Nginx serve (port 8080)

---

## 🔧 Backend Docker Implementation

### Critical Requirements

```dockerfile
# Build from project root (monorepo context)
docker build -f backend/Dockerfile .

# Critical: Copy generated files to match compiled import paths
COPY --from=builder /app/backend/src/generated ./dist/generated
# NOT ./generated or ./src/generated
```

**Critical**: Ensure import paths match compiled JavaScript structure. TypeScript `src/services/file.ts` compiles to `dist/services/file.js`, so runtime resolves `../generated/prisma` as `dist/generated/prisma`.

---

## 🎨 Frontend Docker Implementation

### Essential Configuration

```dockerfile
# Environment variables must be passed as build args
ARG VITE_API_URL
ARG VITE_CLERK_PUBLISHABLE_KEY
ARG VITE_STRIPE_PUBLISHABLE_KEY

# Alpine Linux requires specific Rollup binary
RUN cd frontend && yarn add @rollup/rollup-linux-x64-musl --dev
```

### Nginx Configuration

```nginx
# frontend/nginx.conf
server {
    listen 8080;
    root /usr/share/nginx/html;

    # SPA routing
    location / { try_files $uri $uri/ /index.html; }

    # Cache static assets (1 year), not HTML
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

---

## 🚀 Build Optimization

### Layer Caching Strategy

```dockerfile
# 1. Package files first (changes less frequently)
COPY package.json yarn.lock ./

# 2. Install dependencies (cached if packages unchanged)
RUN yarn install --frozen-lockfile

# 3. Source code last (changes most frequently)
COPY backend/src/ ./backend/src/
```

### Production Optimizations

- **Alpine Linux**: Smaller images (`node:20-alpine`)
- **Production deps only**: `yarn install --production --frozen-lockfile`
- **Multi-stage**: Build tools not in production image

---

## 🧪 Local Testing Implementation

### Testing Your Docker Builds

Verify your containers work correctly before deployment:

```bash
# Build and test backend
docker build -f backend/Dockerfile -t saas-backend .
docker run --rm -p 8080:8080 saas-backend

# Build and test frontend (with required env vars)
docker build -f frontend/Dockerfile \
  --build-arg VITE_API_URL=http://localhost:8080 \
  --build-arg VITE_CLERK_PUBLISHABLE_KEY=pk_test_... \
  -t saas-frontend .
docker run --rm -p 3000:8080 saas-frontend

# Validate import paths work
docker run --rm saas-backend node -e "require('./dist/generated/prisma')"
```

---

## ☁️ Render Deployment Implementation

### Configuring Render Services

Set up your Render services with these optimal settings in render.yaml:

```yaml
# Backend service configuration
- type: web
  name: tech-notes-backend
  runtime: docker
  plan: starter # Can upgrade to standard/pro
  dockerfilePath: ./infrastructure/docker/backend/Dockerfile
  dockerContext: .
  healthCheckPath: /health

# Frontend service configuration
- type: web
  name: tech-notes-frontend
  runtime: docker
  plan: starter
  dockerfilePath: ./infrastructure/docker/frontend/Dockerfile
  dockerContext: .
  healthCheckPath: /health
```

### Setting Up Environment Variables

Configure environment variables in render.yaml and Render dashboard:

```yaml
# In render.yaml (non-sensitive)
envVars:
  - key: NODE_ENV
    value: production
  - key: PORT
    value: 8080
# In Render dashboard (sensitive)
# CLERK_SECRET_KEY, STRIPE_SECRET_KEY, etc.
```

---

## 📚 Quick Reference

### Essential Docker Commands

Use these commands for building and testing:

```bash
# Build from project root (monorepo context)
docker build -f backend/Dockerfile -t backend .
docker build -f frontend/Dockerfile -t frontend .

# Test locally
docker run --rm -p 8080:8080 backend
docker run --rm -p 3000:8080 frontend
```

### Critical Implementation Patterns

Remember these essential patterns:

```dockerfile
# Backend: Copy generated files to match compiled import paths
COPY --from=builder /app/backend/src/generated ./dist/generated

# Frontend: Environment variables as build args
ARG VITE_API_URL
ENV VITE_API_URL=$VITE_API_URL
```

### Troubleshooting Common Issues

Avoid these frequent deployment problems:

- **Import errors**: Ensure generated files copied to `./dist/generated` (not `./generated`)
- **Build context**: Always build from project root, not subdirectory
- **Frontend env vars**: Must pass as `--build-arg` during build
- **Alpine compatibility**: Use `@rollup/rollup-linux-x64-musl` for frontend builds
- **Render build failures**: Check dockerfilePath and dockerContext in render.yaml
- **Health check failures**: Ensure `/health` endpoints respond correctly

---

**Remember**: Always ensure Docker import paths match your compiled JavaScript structure. Test your containers locally before deploying to Render.
