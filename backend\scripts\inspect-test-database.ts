#!/usr/bin/env ts-node

import { config as loadDotenv } from 'dotenv';
import path from 'path';
import { getTestDatabaseManager } from '../src/tests/helpers/test-database-manager';
import { spawn } from 'child_process';
import readline from 'readline';

/**
 * <PERSON>ript to create a test database, seed it, and allow inspection with Prisma Studio
 * This mimics the Jest test setup but pauses for manual inspection
 */
async function inspectTestDatabase() {
  console.log('🧪 Creating ephemeral test database for inspection...');

  try {
    // Load environment variables (same as Jest global setup)
    loadDotenv({ path: path.resolve(__dirname, '../../.env') });
    loadDotenv({
      path: path.resolve(__dirname, '../../.env.local'),
      override: true,
    });

    // Set test environment
    process.env.NODE_ENV = 'test';

    // Validate required environment variables
    const requiredEnvVars = ['TEST_DATABASE_ADMIN_URL'] as const;
    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        throw new Error(`Missing required environment variable: ${envVar}`);
      }
    }

    // Create test database manager and initialize database
    const testDbManager = getTestDatabaseManager();
    const config = await testDbManager.createTestDatabase();

    // Set the DATABASE_URL for Prisma Studio
    process.env.DATABASE_URL = config.connectionUrl;

    console.log('✅ Test database created and seeded!');
    console.log(`📊 Database name: ${config.databaseName}`);
    console.log(`🔗 Connection URL: ${config.connectionUrl}`);

    // Ask user what they want to do
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    console.log('\n🎨 What would you like to do?');
    console.log('1. Open Prisma Studio (recommended)');
    console.log('2. Just show connection details');
    console.log('3. Run tests then pause');

    const answer = await new Promise<string>((resolve) => {
      rl.question('Enter your choice (1-3): ', resolve);
    });

    rl.close();

    if (answer === '1' || answer === '') {
      // Launch Prisma Studio
      console.log('\n🎨 Starting Prisma Studio...');
      console.log(
        '📊 Studio will open in your browser at http://localhost:5555'
      );
      console.log(
        "🛑 Press Ctrl+C here when you're done to clean up the database"
      );

      const studioProcess = spawn('npx', ['prisma', 'studio'], {
        env: {
          ...process.env,
          DATABASE_URL: config.connectionUrl,
        },
        stdio: 'inherit',
      });

      // Handle process termination
      const cleanup = async () => {
        console.log('\n🛑 Stopping Prisma Studio...');
        studioProcess.kill('SIGINT');
        await cleanupDatabase(testDbManager);
        process.exit(0);
      };

      process.on('SIGINT', cleanup);
      process.on('SIGTERM', cleanup);

      // Wait for studio process to exit
      studioProcess.on('exit', async (code) => {
        console.log(`\n📊 Prisma Studio exited with code ${code}`);
        await cleanupDatabase(testDbManager);
        process.exit(code || 0);
      });
    } else if (answer === '2') {
      // Just show details and wait
      console.log('\n📋 Database ready for inspection:');
      console.log(`   Database: ${config.databaseName}`);
      console.log(`   URL: ${config.connectionUrl}`);
      console.log(
        '\n💡 You can connect to this database with any PostgreSQL client'
      );
      console.log('🛑 Press Ctrl+C when done to clean up');

      // Wait for user interrupt
      await new Promise<void>((resolve) => {
        process.on('SIGINT', () => {
          console.log('\n🛑 Cleaning up...');
          resolve();
        });
        process.on('SIGTERM', () => {
          console.log('\n🛑 Cleaning up...');
          resolve();
        });
      });

      await cleanupDatabase(testDbManager);
    } else if (answer === '3') {
      // Run tests first, then pause
      console.log('\n🧪 Running tests against the database...');

      try {
        const testProcess = spawn('npm', ['test'], {
          env: {
            ...process.env,
            DATABASE_URL: config.connectionUrl,
          },
          stdio: 'inherit',
        });

        await new Promise<void>((resolve, reject) => {
          testProcess.on('exit', (code) => {
            if (code === 0) {
              console.log('\n✅ Tests completed successfully!');
              resolve();
            } else {
              console.log(`\n❌ Tests failed with code ${code}`);
              resolve(); // Don't reject, still allow inspection
            }
          });
        });

        console.log(
          '\n🎨 Tests complete! Database is still available for inspection.'
        );
        console.log('💡 You can now run Prisma Studio in another terminal:');
        console.log(
          `   DATABASE_URL="${config.connectionUrl}" npx prisma studio`
        );
        console.log('🛑 Press Ctrl+C here when done to clean up');

        // Wait for user interrupt
        await new Promise<void>((resolve) => {
          process.on('SIGINT', () => {
            console.log('\n🛑 Cleaning up...');
            resolve();
          });
        });

        await cleanupDatabase(testDbManager);
      } catch (error) {
        console.error('❌ Error running tests:', error);
        await cleanupDatabase(testDbManager);
        process.exit(1);
      }
    } else {
      console.log('❌ Invalid choice. Cleaning up...');
      await cleanupDatabase(testDbManager);
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Failed to create test database:', error);
    process.exit(1);
  }
}

async function cleanupDatabase(testDbManager: any) {
  try {
    console.log('\n🧹 Cleaning up test database...');
    await testDbManager.destroyTestDatabase();
    console.log('✅ Test database cleaned up successfully');
  } catch (error) {
    console.error('❌ Failed to clean up test database:', error);
    console.error('💡 You may need to run: npm run cleanup:test-dbs');
  }
}

// Run the inspection
inspectTestDatabase().catch(console.error);
