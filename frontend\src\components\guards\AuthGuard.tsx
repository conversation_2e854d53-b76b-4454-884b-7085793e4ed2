import React from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "../../hooks/useAuth";
import { Button } from "../atoms/Button";

interface AuthGuardProps {
  children: React.ReactNode;
}

/**
 * AuthGuard component that enforces complete authentication requirements:
 * 1. Valid Clerk token (handled by SignedIn/SignedOut in App.tsx)
 * 2. Backend authentication confirmation
 * 3. Database connectivity
 * 4. User onboarding completion
 */
export const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const {
    isLoading,
    isAuthenticated,
    isServiceUnavailable,
    needsOnboarding,
    statusError,
    signOut,
  } = useAuth();

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Verifying authentication...</p>
        </div>
      </div>
    );
  }

  // Show service unavailable error (database connectivity issues)
  if (isServiceUnavailable) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="bg-red-100 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <svg
              className="w-8 h-8 text-red-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Service Temporarily Unavailable
          </h2>
          <p className="text-gray-600 mb-4">
            The authentication service is currently unavailable due to database
            connectivity issues. Please try again in a few moments.
          </p>
          <Button onClick={() => window.location.reload()} variant="primary">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  // Show authentication error
  if (statusError && !isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="bg-yellow-100 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <svg
              className="w-8 h-8 text-yellow-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Authentication Error
          </h2>
          <p className="text-gray-600 mb-4">
            There was an error verifying your authentication. Please try signing
            in again.
          </p>
          <Button
            onClick={async () => {
              try {
                await signOut();
                // After sign out, user will be redirected by Clerk/App routing
              } catch (error) {
                console.error("Error signing out:", error);
                // Fallback to manual redirect if sign out fails
                window.location.href = "/";
              }
            }}
            variant="primary"
          >
            Sign Out and Return Home
          </Button>
        </div>
      </div>
    );
  }

  // Redirect to home if not authenticated (shouldn't happen due to SignedIn guard, but safety check)
  if (!isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  // Show invitation acceptance if needed (replaces public onboarding)
  if (needsOnboarding) {
    // Check if we're on the accept-invitation page with a valid invitation
    const currentPath = window.location.pathname;
    const searchParams = new URLSearchParams(window.location.search);
    const hasClerkTicket = searchParams.has("__clerk_ticket");
    const hasInvitationId = searchParams.has("invitation");

    // If we're on accept-invitation page with proper parameters, show the component
    if (
      currentPath === "/accept-invitation" &&
      (hasClerkTicket || hasInvitationId)
    ) {
      // Import InvitationAcceptance dynamically to avoid circular dependencies
      const InvitationAcceptance = React.lazy(() =>
        import("../organisms/InvitationAcceptance").then((module) => ({
          default: module.InvitationAcceptance,
        })),
      );

      return (
        <React.Suspense
          fallback={
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          }
        >
          <InvitationAcceptance />
        </React.Suspense>
      );
    }

    // Otherwise, show account setup error (user authenticated with Clerk but not in database)
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Account Setup Required
          </h2>
          <p className="text-gray-600 mb-6">
            Your account exists but setup is incomplete. This usually happens if
            you were invited but the invitation process wasn't completed
            properly.
          </p>
          <div className="space-y-3">
            <Button
              onClick={async () => {
                try {
                  await signOut();
                  // After sign out, user will be redirected by Clerk/App routing
                } catch (error) {
                  console.error("Error signing out:", error);
                  // Fallback to manual redirect if sign out fails
                  window.location.href = "/";
                }
              }}
              variant="primary"
            >
              Sign Out and Return Home
            </Button>
            <p className="text-sm text-gray-500">
              If you continue to have issues, please contact your administrator
              for assistance.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // All checks passed - render the protected content
  return <>{children}</>;
};
