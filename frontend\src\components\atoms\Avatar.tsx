import React from "react";
import { clsx } from "clsx";

export interface AvatarProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  src?: string;
  alt?: string;
  initials?: string;
  variant?: "primary" | "gray";
}

const sizeClasses = {
  xs: "h-6 w-6 text-xs",
  sm: "h-8 w-8 text-sm",
  md: "h-10 w-10 text-sm",
  lg: "h-12 w-12 text-base",
  xl: "h-16 w-16 text-lg",
};

const variantClasses = {
  primary: "bg-primary-600 text-white",
  gray: "bg-gray-600 text-white",
};

export const Avatar: React.FC<AvatarProps> = ({
  size = "md",
  src,
  alt,
  initials,
  variant = "primary",
  className,
  ...props
}) => {
  if (src) {
    return (
      <div
        className={clsx(
          "rounded-full overflow-hidden flex-shrink-0",
          sizeClasses[size],
          className,
        )}
        {...props}
      >
        <img
          src={src}
          alt={alt || "Avatar"}
          className="h-full w-full object-cover"
        />
      </div>
    );
  }

  return (
    <div
      className={clsx(
        "rounded-full flex items-center justify-center font-medium flex-shrink-0",
        sizeClasses[size],
        variantClasses[variant],
        className,
      )}
      {...props}
    >
      {initials || "?"}
    </div>
  );
};
