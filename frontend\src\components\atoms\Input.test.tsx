import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import { Input } from "./Input";

describe("Input", () => {
  describe("Rendering", () => {
    it("should render input with default styling", () => {
      render(<Input placeholder="Enter text" />);

      const input = screen.getByPlaceholderText("Enter text");
      expect(input).toBeInTheDocument();
      expect(input).toHaveClass(
        "w-full",
        "px-4",
        "py-3",
        "border",
        "border-gray-300",
      );
      expect(input).toHaveClass("rounded-xl", "bg-white", "shadow-xs");
      expect(input).toHaveClass(
        "focus:outline-none",
        "focus:ring-2",
        "focus:ring-primary-500/20",
      );
    });

    it("should render input with custom className", () => {
      render(<Input className="custom-class" placeholder="Custom input" />);

      const input = screen.getByPlaceholderText("Custom input");
      expect(input).toHaveClass("custom-class");
    });
  });

  describe("Label", () => {
    it("should render input with label", () => {
      render(<Input label="Email Address" placeholder="Enter email" />);

      const label = screen.getByText("Email Address");
      const input = screen.getByPlaceholderText("Enter email");

      expect(label).toBeInTheDocument();
      expect(label).toHaveClass(
        "text-sm",
        "font-semibold",
        "text-gray-800",
        "tracking-tight",
      );
      expect(label).toHaveAttribute("for", input.id);
    });

    it("should render input without label when not provided", () => {
      render(<Input placeholder="No label" />);

      const input = screen.getByPlaceholderText("No label");
      expect(input).toBeInTheDocument();
      expect(screen.queryByRole("label")).not.toBeInTheDocument();
    });
  });

  describe("Icon", () => {
    it("should render input with icon", () => {
      const icon = <span data-testid="search-icon">🔍</span>;
      render(<Input icon={icon} placeholder="Search" />);

      const iconElement = screen.getByTestId("search-icon");
      const input = screen.getByPlaceholderText("Search");

      expect(iconElement).toBeInTheDocument();
      expect(input).toHaveClass("pl-11"); // Adjusted padding for icon
    });

    it("should render input without icon padding when no icon", () => {
      render(<Input placeholder="No icon" />);

      const input = screen.getByPlaceholderText("No icon");
      expect(input).not.toHaveClass("pl-11");
    });

    it("should style icon container correctly", () => {
      const icon = <span data-testid="icon">📧</span>;
      render(<Input icon={icon} placeholder="With icon" />);

      const iconElement = screen.getByTestId("icon");
      const iconContainer = iconElement.parentElement;

      expect(iconContainer).toHaveClass(
        "absolute",
        "inset-y-0",
        "left-0",
        "pl-4",
      );
      expect(iconContainer).toHaveClass(
        "flex",
        "items-center",
        "pointer-events-none",
        "text-gray-500",
      );
    });
  });

  describe("Error State", () => {
    it("should render input with error styling", () => {
      render(
        <Input error="This field is required" placeholder="Error input" />,
      );

      const input = screen.getByPlaceholderText("Error input");
      const errorMessage = screen.getByText("This field is required");

      expect(input).toHaveClass(
        "border-error-300",
        "focus:border-error-500",
        "focus:ring-error-500/20",
      );
      expect(errorMessage).toBeInTheDocument();
      expect(errorMessage).toHaveClass(
        "mt-2",
        "text-sm",
        "text-error-600",
        "font-medium",
      );
    });

    it("should render input without error styling when no error", () => {
      render(<Input placeholder="No error" />);

      const input = screen.getByPlaceholderText("No error");
      expect(input).not.toHaveClass("border-error-300");
      expect(screen.queryByRole("alert")).not.toBeInTheDocument();
    });
  });

  describe("Enhanced Styling", () => {
    it("should have enhanced focus and hover states", () => {
      render(<Input placeholder="Enhanced input" />);

      const input = screen.getByPlaceholderText("Enhanced input");
      expect(input).toHaveClass("hover:border-gray-400");
      expect(input).toHaveClass("transition-all", "duration-300");
      expect(input).toHaveClass("font-medium");
    });

    it("should have enhanced placeholder styling", () => {
      render(<Input placeholder="Enhanced placeholder" />);

      const input = screen.getByPlaceholderText("Enhanced placeholder");
      expect(input).toHaveClass("placeholder-gray-500");
    });
  });

  describe("Props", () => {
    it("should pass through HTML input props", () => {
      const handleChange = vi.fn();
      render(
        <Input
          type="email"
          value="<EMAIL>"
          onChange={handleChange}
          placeholder="Email input"
        />,
      );

      const input = screen.getByPlaceholderText(
        "Email input",
      ) as HTMLInputElement;
      expect(input).toHaveAttribute("type", "email");
      expect(input.value).toBe("<EMAIL>");
    });

    it("should generate unique id when not provided", () => {
      render(<Input placeholder="Auto ID" />);

      const input = screen.getByPlaceholderText("Auto ID");
      expect(input).toHaveAttribute("id");
      expect(input.id).toMatch(/^input-/);
    });

    it("should use provided id", () => {
      render(<Input id="custom-id" placeholder="Custom ID" />);

      const input = screen.getByPlaceholderText("Custom ID");
      expect(input).toHaveAttribute("id", "custom-id");
    });
  });

  describe("Complete Input with All Features", () => {
    it("should render complete input with label, icon, and error", () => {
      const icon = <span data-testid="email-icon">📧</span>;
      render(
        <Input
          label="Email Address"
          icon={icon}
          error="Please enter a valid email"
          placeholder="Enter your email"
          value="invalid-email"
        />,
      );

      const label = screen.getByText("Email Address");
      const input = screen.getByPlaceholderText("Enter your email");
      const icon_element = screen.getByTestId("email-icon");
      const error = screen.getByText("Please enter a valid email");

      expect(label).toBeInTheDocument();
      expect(input).toBeInTheDocument();
      expect(icon_element).toBeInTheDocument();
      expect(error).toBeInTheDocument();

      // Verify error styling is applied
      expect(input).toHaveClass("border-error-300");
    });
  });
});
