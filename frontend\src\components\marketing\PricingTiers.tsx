import React from "react";
import { PricingTier } from "./PricingTier";
import type { PricingTier as PricingTierType } from "../../types/pricing.types";

interface PricingTiersProps {
  tiers: PricingTierType[];
}

export const PricingTiers: React.FC<PricingTiersProps> = ({ tiers }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {tiers.map((tier) => (
        <PricingTier key={tier.name} tier={tier} />
      ))}
    </div>
  );
};
