import { clerkClient } from '@clerk/clerk-sdk-node';
import { Request, Response, NextFunction } from 'express';

import { createClerkAuthMiddleware } from './auth.middleware.js';
// Removed unused error imports

// Mock Prisma Client for this unit test
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    $connect: jest.fn(),
    $disconnect: jest.fn(),
    $transaction: jest.fn(),
    $queryRaw: jest.fn(),
    user: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
    },
    tenant: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
    },
  })),
}));

// Mock Clerk SDK
jest.mock('@clerk/clerk-sdk-node', () => ({
  clerkClient: {
    verifyToken: jest.fn(),
  },
}));

const mockClerkClient = clerkClient as jest.Mocked<typeof clerkClient>;

// Test data
const testData = {
  user: {
    id: 'test-user-id',
    clerkId: 'test-clerk-id',
    tenantId: 'test-tenant-id',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    imageUrl: 'https://example.com/image.jpg',
    isActive: true,
    createdAt: new Date('2025-06-20T13:28:24.806Z'),
    updatedAt: new Date('2025-06-20T13:28:24.806Z'),
  },
};

describe('Auth Middleware', () => {
  let clerkAuthMiddleware: ReturnType<typeof createClerkAuthMiddleware>;
  let mockServices: any;
  let req: Partial<Request>;
  let res: Partial<Response>;
  let next: NextFunction;

  beforeEach(() => {
    // Create mocks directly in test to avoid import/caching issues
    mockServices = {
      userService: {
        getUsersByTenant: jest.fn(),
        getUserById: jest.fn(),
        getUserByClerkId: jest.fn(),
        getUserByClerkIdWithRoles: jest.fn(),
        createUser: jest.fn(),
        updateUser: jest.fn(),
        deleteUser: jest.fn(),
        deactivateUser: jest.fn(),
        activateUser: jest.fn(),
        syncUserFromClerk: jest.fn(),
        createUserWithProfile: jest.fn(),
      },
      prismaService: {
        prisma: {
          user: {
            findMany: jest.fn(),
            findUnique: jest.fn(),
            findFirst: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            deleteMany: jest.fn(),
          },
          tenant: {
            findMany: jest.fn(),
            findUnique: jest.fn(),
            findFirst: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            deleteMany: jest.fn(),
          },
          $connect: jest.fn(),
          $disconnect: jest.fn(),
          $transaction: jest.fn(),
          $queryRaw: jest.fn(),
        },
        connect: jest.fn(),
        disconnect: jest.fn(),
        healthCheck: jest.fn().mockResolvedValue(true),
        authHealthCheck: jest.fn().mockResolvedValue({ healthy: true }),
      },
      authCacheService: {
        get: jest.fn(),
        set: jest.fn(),
        invalidate: jest.fn(),
        disconnect: jest.fn(),
      },
      permissionService: {
        getUserPermissions: jest.fn().mockResolvedValue([]),
      },
      userEngagementService: {
        updateLastLogin: jest.fn().mockResolvedValue(undefined),
        updateLastActivity: jest.fn().mockResolvedValue(undefined),
        getEngagementMetrics: jest.fn(),
        getInactiveUsers: jest.fn(),
      },
      logger: {
        error: jest.fn(),
        warn: jest.fn(),
        info: jest.fn(),
        debug: jest.fn(),
        verbose: jest.fn(),
        silly: jest.fn(),
        log: jest.fn(),
        query: jest.fn(),
      },
    };

    clerkAuthMiddleware = createClerkAuthMiddleware({
      userService: mockServices.userService as any,
      prismaService: mockServices.prismaService as any,
      authCacheService: mockServices.authCacheService as any,
      permissionService: mockServices.permissionService as any,
      userEngagementService: mockServices.userEngagementService as any,
      logger: mockServices.logger as any,
    });

    req = {
      headers: {},
      path: '/test',
      method: 'GET',
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    next = jest.fn();

    jest.clearAllMocks();
  });

  describe('Database Connectivity Security Tests', () => {
    it('should return 503 when database health check fails', async () => {
      // Arrange
      req.headers!.authorization = 'Bearer valid-token';
      mockClerkClient.verifyToken.mockResolvedValue({
        sub: 'clerk-123',
      } as any);
      mockServices.authCacheService.get.mockResolvedValue(null); // Cache miss
      mockServices.prismaService.authHealthCheck.mockResolvedValue({
        healthy: false,
        error: 'Connection refused',
      });

      // Act
      await clerkAuthMiddleware()(req as any, res as any, next);

      // Assert
      expect(res.status).toHaveBeenCalledWith(503);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Service Unavailable',
        message: 'Authentication service requires database connectivity',
        statusCode: 503,
        timestamp: expect.any(String),
      });
      expect(next).not.toHaveBeenCalled();
      expect(
        mockServices.userService.getUserByClerkIdWithRoles
      ).not.toHaveBeenCalled();
    });

    it('should return 503 when database health check throws error', async () => {
      // Arrange
      req.headers!.authorization = 'Bearer valid-token';
      mockClerkClient.verifyToken.mockResolvedValue({
        sub: 'clerk-123',
      } as any);
      mockServices.authCacheService.get.mockResolvedValue(null); // Cache miss
      mockServices.prismaService.authHealthCheck.mockRejectedValue(
        new Error('ECONNREFUSED: Connection refused')
      );

      // Act
      await clerkAuthMiddleware()(req as any, res as any, next);

      // Assert
      expect(res.status).toHaveBeenCalledWith(503);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Service Unavailable',
        message: 'Authentication service requires database connectivity',
        statusCode: 503,
        timestamp: expect.any(String),
      });
      expect(next).not.toHaveBeenCalled();
    });

    it('should proceed when database is healthy and user exists', async () => {
      // Arrange
      req.headers!.authorization = 'Bearer valid-token';
      mockClerkClient.verifyToken.mockResolvedValue({
        sub: 'clerk-123',
      } as any);
      mockServices.authCacheService.get.mockResolvedValue(null); // Cache miss
      mockServices.prismaService.authHealthCheck.mockResolvedValue({
        healthy: true,
      });
      mockServices.userService.getUserByClerkIdWithRoles.mockResolvedValue({
        ...testData.user,
        userRoles: [],
      });
      mockServices.permissionService.getUserPermissions.mockResolvedValue([]);

      // Act
      try {
        await clerkAuthMiddleware()(req as any, res as any, next);
      } catch (error) {
        console.error('Middleware threw error:', error);
        throw error;
      }

      // Assert
      expect(mockServices.prismaService.authHealthCheck).toHaveBeenCalled();
      expect(
        mockServices.userService.getUserByClerkIdWithRoles
      ).toHaveBeenCalledWith('clerk-123');
      expect(
        mockServices.permissionService.getUserPermissions
      ).toHaveBeenCalled();
      expect((req as any).user).toEqual({
        id: testData.user.id,
        clerkId: testData.user.clerkId,
        tenantId: testData.user.tenantId,
        email: testData.user.email,
        firstName: testData.user.firstName,
        lastName: testData.user.lastName,
        imageUrl: testData.user.imageUrl,
        isActive: testData.user.isActive,
        createdAt: testData.user.createdAt,
        updatedAt: testData.user.updatedAt,
        roles: [],
        permissions: [],
        canBypassTenantScope: false,
      });
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    it('should use cached auth context and skip database health check', async () => {
      // Arrange
      const cachedAuthContext = {
        id: testData.user.id,
        clerkId: testData.user.clerkId,
        tenantId: testData.user.tenantId,
        email: testData.user.email,
        firstName: testData.user.firstName,
        lastName: testData.user.lastName,
        imageUrl: testData.user.imageUrl,
        isActive: testData.user.isActive,
        createdAt: testData.user.createdAt,
        updatedAt: testData.user.updatedAt,
        roles: [],
        permissions: [],
        canBypassTenantScope: false,
      };

      req.headers!.authorization = 'Bearer valid-token';
      mockClerkClient.verifyToken.mockResolvedValue({
        sub: 'clerk-123',
      } as any);
      mockServices.authCacheService.get.mockResolvedValue(cachedAuthContext);

      // Act
      await clerkAuthMiddleware()(req as any, res as any, next);

      // Assert
      expect(mockServices.prismaService.authHealthCheck).not.toHaveBeenCalled();
      expect(
        mockServices.userService.getUserByClerkIdWithRoles
      ).not.toHaveBeenCalled();
      expect((req as any).user).toEqual(cachedAuthContext);
      expect(next).toHaveBeenCalled();
    });
  });

  describe('Authentication Token Validation', () => {
    it('should return 401 when no authorization header', async () => {
      // Act
      await clerkAuthMiddleware()(req as any, res as any, next);

      // Assert
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Unauthorized',
        message: 'Authentication token is required',
        statusCode: 401,
        timestamp: expect.any(String),
      });
      expect(next).not.toHaveBeenCalled();
    });

    it('should return 401 when authorization header is malformed', async () => {
      // Arrange
      req.headers!.authorization = 'InvalidFormat token';

      // Act
      await clerkAuthMiddleware()(req as any, res as any, next);

      // Assert
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Unauthorized',
        message: 'Authentication token is required',
        statusCode: 401,
        timestamp: expect.any(String),
      });
    });

    it('should return 401 when Clerk token verification fails', async () => {
      // Arrange
      req.headers!.authorization = 'Bearer invalid-token';
      mockClerkClient.verifyToken.mockResolvedValue(undefined as any);

      // Act
      await clerkAuthMiddleware()(req as any, res as any, next);

      // Assert
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Unauthorized',
        message: 'Invalid authentication token',
        statusCode: 401,
        timestamp: expect.any(String),
      });
    });

    it('should return 404 when user not found in database', async () => {
      // Arrange
      req.headers!.authorization = 'Bearer valid-token';
      mockClerkClient.verifyToken.mockResolvedValue({
        sub: 'clerk-123',
      } as any);
      mockServices.authCacheService.get.mockResolvedValue(null);
      mockServices.prismaService.authHealthCheck.mockResolvedValue({
        healthy: true,
      });
      mockServices.userService.getUserByClerkIdWithRoles.mockResolvedValue(
        null
      );

      // Act
      await clerkAuthMiddleware()(req as any, res as any, next);

      // Assert
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'User Not Found',
        message: 'User not found. Please complete onboarding.',
        statusCode: 404,
        timestamp: expect.any(String),
      });
    });
  });

  describe('Optional Authentication', () => {
    it('should proceed without authentication when required=false and no token', async () => {
      // Act
      await clerkAuthMiddleware({ required: false })(
        req as any,
        res as any,
        next
      );

      // Assert
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
      expect((req as any).user).toBeUndefined();
    });

    it('should still validate token when provided even with required=false', async () => {
      // Arrange
      req.headers!.authorization = 'Bearer invalid-token';
      mockClerkClient.verifyToken.mockResolvedValue(undefined as any);

      // Act
      await clerkAuthMiddleware({ required: false })(
        req as any,
        res as any,
        next
      );

      // Assert
      expect(res.status).toHaveBeenCalledWith(401);
      expect(next).not.toHaveBeenCalled();
    });
  });

  describe('User Deactivation Security', () => {
    it('should block deactivated user from database lookup', async () => {
      // Arrange
      const deactivatedUser = {
        ...testData.user,
        isActive: false,
        userRoles: [],
      };
      req.headers!.authorization = 'Bearer valid-token';
      mockClerkClient.verifyToken.mockResolvedValue({
        sub: 'clerk-123',
      } as any);
      mockServices.authCacheService.get.mockResolvedValue(null); // Cache miss
      mockServices.prismaService.authHealthCheck.mockResolvedValue({
        healthy: true,
      });
      mockServices.userService.getUserByClerkIdWithRoles.mockResolvedValue(
        deactivatedUser
      );

      // Act
      await clerkAuthMiddleware()(req as any, res as any, next);

      // Assert
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Unauthorized',
        message: 'Account has been deactivated. Please contact support.',
        statusCode: 401,
        timestamp: expect.any(String),
      });
      expect(next).not.toHaveBeenCalled();
      expect((req as any).user).toBeUndefined();
    });

    it('should block deactivated user from cache and clear cache', async () => {
      // Arrange
      const deactivatedCachedContext = {
        id: testData.user.id,
        clerkId: testData.user.clerkId,
        tenantId: testData.user.tenantId,
        email: testData.user.email,
        firstName: testData.user.firstName,
        lastName: testData.user.lastName,
        imageUrl: testData.user.imageUrl,
        isActive: false, // Deactivated user in cache
        createdAt: testData.user.createdAt,
        updatedAt: testData.user.updatedAt,
        roles: [],
        permissions: [],
        canBypassTenantScope: false,
      };

      req.headers!.authorization = 'Bearer valid-token';
      mockClerkClient.verifyToken.mockResolvedValue({
        sub: 'clerk-123',
      } as any);
      mockServices.authCacheService.get.mockResolvedValue(
        deactivatedCachedContext
      );

      // Act
      await clerkAuthMiddleware()(req as any, res as any, next);

      // Assert
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Unauthorized',
        message: 'Account has been deactivated. Please contact support.',
        statusCode: 401,
        timestamp: expect.any(String),
      });
      expect(mockServices.authCacheService.invalidate).toHaveBeenCalledWith(
        'auth:user:clerk-123'
      );
      expect(next).not.toHaveBeenCalled();
      expect((req as any).user).toBeUndefined();
    });

    it('should allow active user to proceed normally', async () => {
      // Arrange
      const activeUser = { ...testData.user, isActive: true, userRoles: [] };
      req.headers!.authorization = 'Bearer valid-token';
      mockClerkClient.verifyToken.mockResolvedValue({
        sub: 'clerk-123',
      } as any);
      mockServices.authCacheService.get.mockResolvedValue(null); // Cache miss
      mockServices.prismaService.authHealthCheck.mockResolvedValue({
        healthy: true,
      });
      mockServices.userService.getUserByClerkIdWithRoles.mockResolvedValue(
        activeUser
      );
      mockServices.permissionService.getUserPermissions.mockResolvedValue([]);

      // Act
      await clerkAuthMiddleware()(req as any, res as any, next);

      // Assert
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
      expect((req as any).user).toEqual({
        id: activeUser.id,
        clerkId: activeUser.clerkId,
        tenantId: activeUser.tenantId,
        email: activeUser.email,
        firstName: activeUser.firstName,
        lastName: activeUser.lastName,
        imageUrl: activeUser.imageUrl,
        isActive: activeUser.isActive,
        createdAt: activeUser.createdAt,
        updatedAt: activeUser.updatedAt,
        roles: [],
        permissions: [],
        canBypassTenantScope: false,
      });
      expect(mockServices.authCacheService.set).toHaveBeenCalledWith(
        'auth:user:clerk-123',
        expect.objectContaining({ isActive: true }),
        300
      );
    });

    it('should allow active cached user to proceed normally', async () => {
      // Arrange
      const activeCachedContext = {
        id: testData.user.id,
        clerkId: testData.user.clerkId,
        tenantId: testData.user.tenantId,
        email: testData.user.email,
        firstName: testData.user.firstName,
        lastName: testData.user.lastName,
        imageUrl: testData.user.imageUrl,
        isActive: true, // Active user in cache
        createdAt: testData.user.createdAt,
        updatedAt: testData.user.updatedAt,
        roles: [],
        permissions: [],
        canBypassTenantScope: false,
      };

      req.headers!.authorization = 'Bearer valid-token';
      mockClerkClient.verifyToken.mockResolvedValue({
        sub: 'clerk-123',
      } as any);
      mockServices.authCacheService.get.mockResolvedValue(activeCachedContext);

      // Act
      await clerkAuthMiddleware()(req as any, res as any, next);

      // Assert
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
      expect((req as any).user).toEqual(activeCachedContext);
      expect(mockServices.authCacheService.invalidate).not.toHaveBeenCalled();
      expect(mockServices.prismaService.authHealthCheck).not.toHaveBeenCalled();
      expect(
        mockServices.userService.getUserByClerkIdWithRoles
      ).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle database connectivity errors in user lookup', async () => {
      // Arrange
      req.headers!.authorization = 'Bearer valid-token';
      mockClerkClient.verifyToken.mockResolvedValue({
        sub: 'clerk-123',
      } as any);
      mockServices.authCacheService.get.mockResolvedValue(null);
      mockServices.prismaService.authHealthCheck.mockResolvedValue({
        healthy: true,
      });
      mockServices.userService.getUserByClerkIdWithRoles.mockRejectedValue(
        new Error('connect ECONNREFUSED 127.0.0.1:5432')
      );

      // Act
      await clerkAuthMiddleware()(req as any, res as any, next);

      // Assert
      expect(res.status).toHaveBeenCalledWith(503);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Service Unavailable',
        message: 'Authentication service requires database connectivity',
        statusCode: 503,
        timestamp: expect.any(String),
      });
    });

    it('should handle unexpected errors gracefully', async () => {
      // Arrange
      req.headers!.authorization = 'Bearer valid-token';
      mockClerkClient.verifyToken.mockRejectedValue(
        new Error('Unexpected error')
      );

      // Act
      await clerkAuthMiddleware()(req as any, res as any, next);

      // Assert
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Internal Server Error',
        message: 'Authentication service error',
        statusCode: 500,
        timestamp: expect.any(String),
      });
    });
  });
});
