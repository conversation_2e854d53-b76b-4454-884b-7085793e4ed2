export default {
  preset: 'ts-jest/presets/default-esm',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  testMatch: ['<rootDir>/src/**/*.test.ts'],
  testPathIgnorePatterns: [
    '/node_modules/',
    '<rootDir>/../frontend/',
    '/frontend/',
    '.*\\.tsx$',
    '.*\\.jsx$',
    '<rootDir>/src/__tests__/setup.ts',
  ],
  extensionsToTreatAsEsm: ['.ts'],
  transform: {
    '^.+\\.ts$': [
      'ts-jest',
      {
        useESM: true,
        tsconfig: {
          module: 'ESNext',
        },
      },
    ],
  },
  transformIgnorePatterns: ['node_modules/(?!(@tech-notes/shared))'],
  moduleNameMapper: {
    '^@tech-notes/shared$': '<rootDir>/../shared/src/index.ts',
    '^(\\.{1,2}/.*)\\.js$': '$1', // Map relative .js imports to their .ts equivalents for ESM compatibility
  },
  collectCoverageFrom: ['src/**/*.ts', '!src/**/*.d.ts', '!src/index.ts'],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/src/__tests__/setup.ts'],
  testTimeout: 10000, // Reduced timeout for faster unit tests
  // Configure concise output similar to frontend
  reporters: ['default'],
  silent: false, // Keep console.log from tests, but reduce setup noise
  verbose: false, // Reduce verbose test descriptions
  // Reduce noise in test output
  collectCoverage: false, // Only collect coverage when explicitly requested
  // Removed: globalSetup, globalTeardown, maxWorkers restriction
};
