#!/usr/bin/env ts-node

import { Client } from 'pg';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from root directory
dotenv.config({ path: path.resolve(__dirname, '../../.env') });
dotenv.config({
  path: path.resolve(__dirname, '../../.env.local'),
  override: true,
});

async function checkDatabaseSettings() {
  const adminUrl = process.env.TEST_DATABASE_ADMIN_URL;

  if (!adminUrl) {
    console.error(
      '❌ TEST_DATABASE_ADMIN_URL environment variable is required'
    );
    process.exit(1);
  }

  const client = new Client({
    connectionString: adminUrl,
    ssl: {
      rejectUnauthorized: false,
    },
  });

  try {
    console.log('🔗 Connecting to database...');
    await client.connect();

    console.log('\n⚙️  PostgreSQL Connection & Timeout Settings:');
    console.log('─'.repeat(60));

    // Check key timeout settings
    const timeoutSettings = [
      'idle_in_transaction_session_timeout',
      'statement_timeout',
      'lock_timeout',
      'tcp_keepalives_idle',
      'tcp_keepalives_interval',
      'tcp_keepalives_count',
    ];

    for (const setting of timeoutSettings) {
      try {
        const result = await client.query(`SHOW ${setting}`);
        const value = result.rows[0][setting];
        console.log(`${setting.padEnd(35)}: ${value}`);
      } catch (error) {
        console.log(`${setting.padEnd(35)}: Unable to query`);
      }
    }

    console.log('\n🔍 Current Active Connections to Test Databases:');
    console.log('─'.repeat(80));

    // Get detailed connection info
    const connectionsResult = await client.query(`
      SELECT 
        datname,
        pid,
        usename,
        application_name,
        client_addr,
        backend_start,
        query_start,
        state_change,
        state,
        EXTRACT(EPOCH FROM (NOW() - backend_start))::int as connection_age_seconds,
        EXTRACT(EPOCH FROM (NOW() - state_change))::int as state_age_seconds
      FROM pg_stat_activity
      WHERE datname LIKE 'tech_notes_test_%'
      AND pid <> pg_backend_pid()
      ORDER BY backend_start
    `);

    if (connectionsResult.rows.length === 0) {
      console.log('✨ No active connections to test databases');
    } else {
      console.log(
        `${'Database'.padEnd(25)} ${'PID'.padEnd(8)} ${'State'.padEnd(10)} ${'Age (min)'.padEnd(12)} ${'State Age (min)'}`
      );
      console.log('─'.repeat(80));

      connectionsResult.rows.forEach((row) => {
        const ageMinutes = Math.floor(row.connection_age_seconds / 60);
        const stateAgeMinutes = Math.floor(row.state_age_seconds / 60);
        const dbShort =
          row.datname.length > 24
            ? row.datname.substring(0, 21) + '...'
            : row.datname;

        console.log(
          `${dbShort.padEnd(25)} ${String(row.pid).padEnd(8)} ${(row.state || 'null').padEnd(10)} ${String(ageMinutes).padEnd(12)} ${stateAgeMinutes}`
        );
      });

      console.log('\n💡 Connection Analysis:');
      const oldestConnection = Math.max(
        ...connectionsResult.rows.map((r) => r.connection_age_seconds)
      );
      const oldestMinutes = Math.floor(oldestConnection / 60);
      const oldestHours = Math.floor(oldestMinutes / 60);

      if (oldestHours > 0) {
        console.log(
          `   Oldest connection: ${oldestHours}h ${oldestMinutes % 60}m ago`
        );
      } else {
        console.log(`   Oldest connection: ${oldestMinutes}m ago`);
      }
    }

    console.log('\n🔄 Render Service Restart Impact:');
    console.log('─'.repeat(50));
    console.log(
      '• Restarting your backend service will NOT affect these connections'
    );
    console.log(
      '• These are direct database connections, not through your app'
    );
    console.log(
      '• Only PostgreSQL service restart would drop them (Render controls this)'
    );
    console.log(
      '• Connections will timeout based on PostgreSQL settings above'
    );
  } catch (error) {
    console.error('❌ Failed to check database settings:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run the check
checkDatabaseSettings().catch(console.error);
