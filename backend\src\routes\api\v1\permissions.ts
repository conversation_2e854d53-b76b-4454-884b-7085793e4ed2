import { PermissionResource, PermissionAction } from '@prisma/client';
import { Router, NextFunction, Request, Response } from 'express';
import { Logger } from 'winston';

import { MiddlewareFactory } from '../../../middleware/middleware-factory.js';
import { PermissionService } from '../../../services/permission.service.js';
import {
  CreatePermissionData,
  PermissionCheck,
} from '../../../types/rbac.types.js';
import { getRequestUser } from '../../../utils/request-types.js';

interface ServiceDependencies {
  permissionService: PermissionService;
  middlewareFactory: MiddlewareFactory;
  logger: Logger;
}

export function createPermissionsRouter(
  dependencies: ServiceDependencies
): Router {
  const { permissionService, middlewareFactory, logger } = dependencies;
  const router = Router();

  /**
   * GET /api/v1/permissions
   * Get all permissions (System Admin only)
   */
  router.get(
    '/',
    ...middlewareFactory.createAuthWithSystemAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const permissions = await permissionService.getAllPermissions(
          getRequestUser(req)!
        );

        res.json({
          data: permissions,
          meta: {
            count: permissions.length,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch all permissions', {
          error,
          userId: getRequestUser(req)?.id,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/permissions
   * Create a new permission (System Admin only)
   */
  router.post(
    '/',
    ...middlewareFactory.createAuthWithSystemAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const permissionData: CreatePermissionData = req.body;

        // Basic validation
        if (
          !permissionData.name ||
          !permissionData.description ||
          !permissionData.resource ||
          !permissionData.action
        ) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'Name, description, resource, and action are required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        // Validate enum values
        if (
          !Object.values(PermissionResource).includes(permissionData.resource)
        ) {
          return res.status(400).json({
            error: 'Bad Request',
            message: `Invalid resource. Must be one of: ${Object.values(PermissionResource).join(', ')}`,
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        if (!Object.values(PermissionAction).includes(permissionData.action)) {
          return res.status(400).json({
            error: 'Bad Request',
            message: `Invalid action. Must be one of: ${Object.values(PermissionAction).join(', ')}`,
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        const permission = await permissionService.createPermission(
          permissionData,
          getRequestUser(req)!
        );

        res.status(201).json({
          data: permission,
          message: 'Permission created successfully',
        });
      } catch (error) {
        logger.error('Failed to create permission', {
          error,
          permissionData: req.body,
          userId: getRequestUser(req)?.id,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/permissions/check
   * Check if current user has specific permission(s)
   */
  router.post(
    '/check',
    middlewareFactory.createAuth(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { permission, permissions } = req.body;

        if (!permission && !permissions) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'Either "permission" or "permissions" array is required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        if (permission) {
          // Single permission check
          const { resource, action, context } = permission;

          if (!resource || !action) {
            return res.status(400).json({
              error: 'Bad Request',
              message: 'Permission must have resource and action',
              statusCode: 400,
              timestamp: new Date().toISOString(),
            });
          }

          const hasPermission = await permissionService.hasPermission(
            getRequestUser(req)!.id,
            { resource, action, context },
            getRequestUser(req)!
          );

          res.json({
            data: {
              permission: `${resource}:${action}`,
              hasPermission,
              context,
            },
          });
        } else {
          // Multiple permissions check
          if (!Array.isArray(permissions)) {
            return res.status(400).json({
              error: 'Bad Request',
              message: 'Permissions must be an array',
              statusCode: 400,
              timestamp: new Date().toISOString(),
            });
          }

          const permissionChecks: PermissionCheck[] = permissions.map(
            (p: PermissionCheck) => ({
              resource: p.resource,
              action: p.action,
              context: p.context,
            })
          );

          const results = await permissionService.hasPermissions(
            getRequestUser(req)!.id,
            permissionChecks,
            getRequestUser(req)!
          );

          res.json({
            data: {
              permissions: results,
              userId: getRequestUser(req)!.id,
            },
          });
        }
      } catch (error) {
        logger.error('Failed to check permissions', {
          error,
          checkData: req.body,
          userId: getRequestUser(req)?.id,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/permissions/user/:userId
   * Get all permissions for a specific user
   */
  router.get(
    '/user/:userId',
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { userId } = req.params;

        if (!userId) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'User ID is required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        const permissions = await permissionService.getUserPermissions(
          userId,
          getRequestUser(req)!
        );

        res.json({
          data: permissions,
          meta: {
            count: permissions.length,
            userId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch user permissions', {
          error,
          targetUserId: req.params.userId,
          userId: getRequestUser(req)?.id,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/permissions/me
   * Get current user's permissions
   */
  router.get(
    '/me',
    middlewareFactory.createAuth(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const permissions = await permissionService.getUserPermissions(
          getRequestUser(req)!.id,
          getRequestUser(req)!
        );

        res.json({
          data: permissions,
          meta: {
            count: permissions.length,
            userId: getRequestUser(req)!.id,
            canBypassTenantScope:
              getRequestUser(req)!.canBypassTenantScope || false,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch current user permissions', {
          error,
          userId: getRequestUser(req)?.id,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/permissions/check-user
   * Check if a specific user has permission(s) (Admin only)
   */
  router.post(
    '/check-user',
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { userId, permission, permissions } = req.body;

        if (!userId) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'User ID is required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        if (!permission && !permissions) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'Either "permission" or "permissions" array is required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        if (permission) {
          // Single permission check
          const { resource, action, context } = permission;

          const hasPermission = await permissionService.hasPermission(
            userId,
            { resource, action, context },
            getRequestUser(req)!
          );

          res.json({
            data: {
              userId,
              permission: `${resource}:${action}`,
              hasPermission,
              context,
            },
          });
        } else {
          // Multiple permissions check
          const permissionChecks: PermissionCheck[] = permissions.map(
            (p: PermissionCheck) => ({
              resource: p.resource,
              action: p.action,
              context: p.context,
            })
          );

          const results = await permissionService.hasPermissions(
            userId,
            permissionChecks,
            getRequestUser(req)!
          );

          res.json({
            data: {
              userId,
              permissions: results,
            },
          });
        }
      } catch (error) {
        logger.error('Failed to check user permissions', {
          error,
          checkData: req.body,
          userId: getRequestUser(req)?.id,
        });
        next(error);
      }
    }
  );

  return router;
}
