module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint', 'prettier', 'import'],
  extends: ['eslint:recommended', 'prettier'],
  rules: {
    'prettier/prettier': 'error',
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'warn',

    // ESM Import Extension Rules
    'import/extensions': [
      'error',
      'ignorePackages',
      {
        'js': 'always',
        'ts': 'never',
        'tsx': 'never'
      }
    ],

    // Ensure consistent import order and grouping
    'import/order': [
      'error',
      {
        'groups': [
          'builtin',
          'external',
          'internal',
          'parent',
          'sibling',
          'index'
        ],
        'newlines-between': 'always',
        'alphabetize': {
          'order': 'asc',
          'caseInsensitive': true
        }
      }
    ],

    // Prevent importing from files without extensions in ESM
    // Note: Disabled during development because TypeScript source files don't have .js extensions
    // The important rule is 'import/extensions' which enforces .js extensions in source code
    'import/no-unresolved': 'off'
  },
  settings: {
    'import/resolver': {
      'node': {
        'extensions': ['.js', '.ts', '.tsx']
      }
    },
    'import/extensions': ['.js', '.ts', '.tsx'],
    'import/parsers': {
      '@typescript-eslint/parser': ['.ts', '.tsx']
    }
  },
  env: {
    node: true,
    jest: true,
    es2020: true
  },
};
