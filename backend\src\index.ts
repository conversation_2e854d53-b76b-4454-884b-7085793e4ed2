// Import modules - environment loading happens in env-validation.ts
import { fileURLToPath } from 'url';

import cors from 'cors';
import express from 'express';
import helmet from 'helmet';

import { createClerkAuthMiddleware } from './middleware/auth.middleware.js';
import {
  createError<PERSON>and<PERSON>,
  createNotFoundHandler,
} from './middleware/error-handler.js';
import { createMiddlewareFactory } from './middleware/middleware-factory.js';
import { createAppRoutes } from './routes/index.js';
import { AuthCacheService } from './services/auth-cache.service.js';
import { ClerkInvitationService } from './services/clerk-invitation.service.js';
import { DocumentService } from './services/document.service.js';
import { InvitationService } from './services/invitation.service.js';
import { OnboardingService } from './services/onboarding.service.js';
import { PermissionService } from './services/permission.service.js';
import { PrismaService } from './services/prisma.service.js';
import { RoleService } from './services/role.service.js';
import { S3Service } from './services/s3.service.js';
import { TenantService } from './services/tenant.service.js';
import { UserEngagementService } from './services/user-engagement.service.js';
import { UserService } from './services/user.service.js';
import { VehicleHierarchyV2Service } from './services/vehicle-hierarchy-v2.service.js';
import {
  VehicleBrandV3Service,
  VehicleSubBrandV3Service,
  VehicleModelV3Service,
  VehicleYearV3Service,
  VehicleModelYearV3Service,
  VehicleHierarchyV3CoordinatorService,
} from './services/vehicle-hierarchy-v3/index.js';
import { VehicleHierarchyService } from './services/vehicle-hierarchy.service.js';
import { env } from './utils/env-validation.js';
import { logger } from './utils/logger.js';

async function createApp(): Promise<express.Application> {
  const app = express();

  // Security middleware
  app.use(helmet());

  // Configure CORS to handle multiple origins (semicolon-separated to avoid gcloud parsing issues)
  const corsOrigins = env.CORS_ORIGIN.split(/[;,]/)
    .map((origin) => origin.trim())
    .filter((origin) => origin.length > 0);

  // Debug logging for CORS configuration
  logger.info('CORS configuration', {
    rawCorsOrigin: env.CORS_ORIGIN,
    parsedOrigins: corsOrigins,
    finalOrigin: corsOrigins.length === 1 ? corsOrigins[0] : corsOrigins,
  });

  app.use(
    cors({
      origin: corsOrigins.length === 1 ? corsOrigins[0] : corsOrigins,
      credentials: true,
    })
  );

  // Basic middleware
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // Initialize services with dependency injection
  const prismaService = PrismaService.getInstance(logger);
  const userService = new UserService(prismaService, logger);
  const tenantService = new TenantService(prismaService, logger);
  const onboardingService = new OnboardingService(
    tenantService,
    userService,
    logger
  );
  const authCacheService = new AuthCacheService(logger, env.REDIS_URL);

  // Initialize RBAC services
  const roleService = new RoleService(prismaService, logger);
  const permissionService = new PermissionService(prismaService, logger);

  // Initialize engagement tracking service
  const userEngagementService = new UserEngagementService(
    prismaService,
    logger
  );

  // Initialize vehicle hierarchy services
  const vehicleHierarchyService = new VehicleHierarchyService(
    prismaService,
    logger
  );
  const vehicleHierarchyV2Service = new VehicleHierarchyV2Service(
    prismaService,
    logger
  );

  // Initialize V3 vehicle hierarchy services (refactored)
  const vehicleBrandV3Service = new VehicleBrandV3Service(
    prismaService,
    logger
  );
  const vehicleSubBrandV3Service = new VehicleSubBrandV3Service(
    prismaService,
    logger,
    vehicleBrandV3Service
  );
  const vehicleModelV3Service = new VehicleModelV3Service(
    prismaService,
    logger,
    vehicleSubBrandV3Service
  );
  const vehicleYearV3Service = new VehicleYearV3Service(prismaService, logger);
  const vehicleModelYearV3Service = new VehicleModelYearV3Service(
    prismaService,
    logger,
    vehicleModelV3Service,
    vehicleYearV3Service
  );
  const vehicleHierarchyV3CoordinatorService =
    new VehicleHierarchyV3CoordinatorService(
      vehicleBrandV3Service,
      vehicleSubBrandV3Service,
      vehicleModelV3Service,
      vehicleYearV3Service,
      vehicleModelYearV3Service,
      prismaService,
      logger
    );

  // Maintain backward compatibility
  const vehicleHierarchyV3Service = vehicleHierarchyV3CoordinatorService;

  // Initialize invitation services
  const clerkInvitationService = new ClerkInvitationService(logger);
  const invitationService = new InvitationService(
    prismaService,
    logger,
    clerkInvitationService,
    userService,
    tenantService,
    roleService
  );

  // Initialize document storage services
  const s3Service = new S3Service(logger);
  const documentService = new DocumentService(prismaService, logger, s3Service);

  // Attempt database connection in background - don't block server startup
  prismaService.connect().catch((error) => {
    logger.warn(
      'Database connection failed during startup, will retry on first request',
      {
        error:
          error instanceof Error
            ? {
                name: error.name,
                message: error.message,
                stack: error.stack,
              }
            : error,
      }
    );
    // Continue startup even if database connection fails
    // The health check endpoint will show database status
  });

  // Create middleware factories
  const clerkAuth = createClerkAuthMiddleware({
    userService,
    prismaService,
    permissionService,
    userEngagementService,
    authCacheService,
    logger,
  });

  const middlewareFactory = createMiddlewareFactory({
    userService,
    permissionService,
    userEngagementService,
    prismaService,
    authCacheService,
    logger,
  });

  const errorHandler = createErrorHandler({ logger });
  const notFoundHandler = createNotFoundHandler({ logger });

  // Service dependencies for route factories
  const serviceDependencies = {
    prismaService,
    userService,
    tenantService,
    onboardingService,
    invitationService,
    roleService,
    permissionService,
    userEngagementService,
    vehicleHierarchyService,
    vehicleHierarchyV2Service,
    vehicleHierarchyV3Service,
    documentService,
    s3Service,
    middlewareFactory,
    clerkAuth,
    logger,
  };

  // Mount all routes using factory pattern
  app.use('/', createAppRoutes(serviceDependencies));

  // Error handling middleware (must be last)
  app.use(notFoundHandler);
  app.use(errorHandler);

  return app;
}

async function startServer() {
  try {
    console.log('=== BACKEND STARTUP DEBUG ===');
    console.log('Node version:', process.version);
    console.log('Platform:', process.platform);
    console.log('Environment variables check:');
    console.log('- NODE_ENV:', process.env.NODE_ENV);
    console.log('- PORT:', process.env.PORT);
    console.log('- DATABASE_URL exists:', !!process.env.DATABASE_URL);
    console.log('- CLERK_SECRET_KEY exists:', !!process.env.CLERK_SECRET_KEY);
    console.log('- CORS_ORIGIN:', process.env.CORS_ORIGIN);

    logger.info('Starting Tech Notes Backend...', {
      environment: env.NODE_ENV,
      port: env.PORT,
      nodeVersion: process.version,
      platform: process.platform,
    });

    // Validate critical environment variables
    console.log('About to validate environment variables...');
    try {
      logger.info('Environment validation passed', {
        hasDatabase: !!env.DATABASE_URL,
        hasClerkSecret: !!env.CLERK_SECRET_KEY,
        corsOrigin: env.CORS_ORIGIN,
      });
      console.log('Environment validation successful');
    } catch (envError) {
      console.error('Environment validation failed:', envError);
      throw envError;
    }

    console.log('About to create app...');
    const app = await createApp();
    console.log('App created successfully');

    console.log(`About to start server on port ${env.PORT}...`);
    const server = app.listen(env.PORT, '0.0.0.0', () => {
      logger.info('Server started successfully', {
        port: env.PORT,
        host: '0.0.0.0',
        environment: env.NODE_ENV,
        pid: process.pid,
      });
    });

    // Handle server startup errors
    server.on('error', (error: Error & { code?: string }) => {
      if (error.code === 'EADDRINUSE') {
        logger.error(`Port ${env.PORT} is already in use`, {
          error,
        });
      } else if (error.code === 'EACCES') {
        logger.error(`Permission denied to bind to port ${env.PORT}`, {
          error,
        });
      } else {
        logger.error('Server startup error', {
          error,
        });
      }
      process.exit(1);
    });

    // Graceful shutdown
    process.on('SIGTERM', async () => {
      logger.info('SIGTERM received, shutting down gracefully');
      server.close(async () => {
        const prismaService = PrismaService.getInstance(logger);
        await prismaService.disconnect();
        process.exit(0);
      });
    });

    process.on('SIGINT', async () => {
      logger.info('SIGINT received, shutting down gracefully');
      server.close(async () => {
        const prismaService = PrismaService.getInstance(logger);
        await prismaService.disconnect();
        process.exit(0);
      });
    });
  } catch (error) {
    logger.error('Failed to start server', {
      error:
        error instanceof Error
          ? {
              name: error.name,
              message: error.message,
              stack: error.stack,
            }
          : error,
    });
    console.error('Detailed error:', error);
    process.exit(1);
  }
}

// Start server if this file is run directly
const currentFile = fileURLToPath(import.meta.url);
const runFile = process.argv[1];

if (currentFile === runFile) {
  startServer();
}

export { createApp };
