import { clerkClient } from '@clerk/clerk-sdk-node';
import express from 'express';
import request from 'supertest';

import { testData } from '../../../__tests__/simple-mocks.js';

import { createAuthRouter } from './auth.js';

// Mock Prisma Client for this unit test
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    $connect: jest.fn(),
    $disconnect: jest.fn(),
    $transaction: jest.fn(),
    $queryRaw: jest.fn(),
    user: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
    },
    tenant: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
    },
  })),
}));

// Mock Clerk SDK
jest.mock('@clerk/clerk-sdk-node', () => ({
  clerkClient: {
    verifyToken: jest.fn(),
    users: {
      getUser: jest.fn(),
    },
  },
}));

const mockClerkClient = clerkClient as jest.Mocked<typeof clerkClient>;

describe('Auth Routes', () => {
  // Helper function to create a test app with specific mocks
  function createTestAppWithMocks(clerkAuthMock?: any) {
    const mockServices = {
      userService: {
        getUsersByTenant: jest.fn(),
        getUserById: jest.fn(),
        getUserByClerkId: jest.fn(),
        createUser: jest.fn(),
        updateUser: jest.fn(),
        deleteUser: jest.fn(),
        deactivateUser: jest.fn(),
        activateUser: jest.fn(),
        syncUserFromClerk: jest.fn(),
        createUserWithProfile: jest.fn(),
      },
      tenantService: {
        getAllTenants: jest.fn(),
        getTenantById: jest.fn(),
        getTenantBySlug: jest.fn(),
        createTenant: jest.fn(),
        updateTenant: jest.fn(),
        deleteTenant: jest.fn(),
        getTenantWithStats: jest.fn(),
        isSlugAvailable: jest.fn(),
      },
      onboardingService: {
        createTenantAndUser: jest.fn(),
        isSlugAvailable: jest.fn(),
      },
      invitationService: {
        createInvitation: jest.fn(),
        getInvitationByToken: jest.fn(),
        acceptInvitation: jest.fn(),
        revokeInvitation: jest.fn(),
        getInvitationsByTenant: jest.fn(),
      },
      prismaService: {
        prisma: {
          user: {
            findMany: jest.fn(),
            findUnique: jest.fn(),
            findFirst: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            deleteMany: jest.fn(),
          },
          tenant: {
            findMany: jest.fn(),
            findUnique: jest.fn(),
            findFirst: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            deleteMany: jest.fn(),
          },
          $connect: jest.fn(),
          $disconnect: jest.fn(),
          $transaction: jest.fn(),
          $queryRaw: jest.fn(),
        },
        connect: jest.fn(),
        disconnect: jest.fn(),
        healthCheck: jest.fn().mockResolvedValue(true),
        authHealthCheck: jest.fn().mockResolvedValue({ healthy: true }),
      },
      authCacheService: {
        get: jest.fn(),
        set: jest.fn(),
        invalidate: jest.fn(),
        disconnect: jest.fn(),
      },
      logger: {
        error: jest.fn(),
        warn: jest.fn(),
        info: jest.fn(),
        debug: jest.fn(),
        verbose: jest.fn(),
        silly: jest.fn(),
        log: jest.fn(),
        query: jest.fn(),
      },
      clerkAuth:
        clerkAuthMock ||
        jest.fn().mockImplementation(() => {
          return (req: any, res: any, next: any) => next();
        }),
    } as any;

    const authRouter = createAuthRouter({
      userService: mockServices.userService as any,
      tenantService: mockServices.tenantService as any,
      onboardingService: mockServices.onboardingService as any,
      invitationService: mockServices.invitationService as any,
      prismaService: mockServices.prismaService as any,
      clerkAuth: mockServices.clerkAuth as any,
      logger: mockServices.logger as any,
    });

    const app = express();
    app.use(express.json());
    app.use('/api/v1/auth', authRouter);

    return { app, mockServices };
  }

  describe('GET /api/v1/auth/status', () => {
    describe('Database Connectivity Security Tests', () => {
      it('should return 503 when database health check fails', async () => {
        // Arrange
        const { app, mockServices } = createTestAppWithMocks();
        mockClerkClient.verifyToken.mockResolvedValue({
          sub: 'clerk-123',
        } as any);
        mockServices.prismaService.authHealthCheck.mockResolvedValue({
          healthy: false,
          error: 'Connection refused',
        });

        // Act
        const response = await request(app)
          .get('/api/v1/auth/status')
          .set('Authorization', 'Bearer valid-token')
          .expect(503);

        // Assert
        expect(response.body).toMatchObject({
          error: 'Service Unavailable',
          message: 'Authentication service requires database connectivity',
          statusCode: 503,
        });
        expect(response.body.timestamp).toBeDefined();
        expect(
          mockServices.userService.getUserByClerkId
        ).not.toHaveBeenCalled();
      });

      it('should return 503 when database health check throws error', async () => {
        // Arrange
        const { app, mockServices } = createTestAppWithMocks();
        mockClerkClient.verifyToken.mockResolvedValue({
          sub: 'clerk-123',
        } as any);
        mockServices.prismaService.authHealthCheck.mockRejectedValue(
          new Error('ECONNREFUSED: Connection refused')
        );

        // Act
        const response = await request(app)
          .get('/api/v1/auth/status')
          .set('Authorization', 'Bearer valid-token')
          .expect(503);

        // Assert
        expect(response.body).toMatchObject({
          error: 'Service Unavailable',
          message: 'Authentication service requires database connectivity',
          statusCode: 503,
        });
      });

      it('should return auth status when database is healthy', async () => {
        // Arrange
        const { app, mockServices } = createTestAppWithMocks();
        mockClerkClient.verifyToken.mockResolvedValue({
          sub: 'clerk-123',
        } as any);
        mockServices.prismaService.authHealthCheck.mockResolvedValue({
          healthy: true,
        });
        mockServices.userService.getUserByClerkId.mockResolvedValue(
          testData.user
        );

        // Act
        const response = await request(app)
          .get('/api/v1/auth/status')
          .set('Authorization', 'Bearer valid-token')
          .expect(200);

        // Assert
        expect(response.body).toMatchObject({
          data: {
            authenticated: true,
            onboarded: true,
            user: {
              id: testData.user.id,
              tenantId: testData.user.tenantId,
              email: testData.user.email,
              firstName: testData.user.firstName,
              lastName: testData.user.lastName,
            },
          },
        });
        expect(response.body.timestamp).toBeDefined();
      });
    });

    describe('Authentication Token Validation', () => {
      it('should return unauthenticated status when no token provided', async () => {
        // Arrange
        const { app } = createTestAppWithMocks();

        // Act
        const response = await request(app)
          .get('/api/v1/auth/status')
          .expect(200);

        // Assert
        expect(response.body).toMatchObject({
          data: {
            authenticated: false,
            onboarded: false,
          },
        });
        expect(response.body.timestamp).toBeDefined();
      });

      it('should return unauthenticated status when token is malformed', async () => {
        // Arrange
        const { app } = createTestAppWithMocks();

        // Act
        const response = await request(app)
          .get('/api/v1/auth/status')
          .set('Authorization', 'InvalidFormat token')
          .expect(200);

        // Assert
        expect(response.body).toMatchObject({
          data: {
            authenticated: false,
            onboarded: false,
          },
        });
      });

      it('should return unauthenticated status when Clerk token verification fails', async () => {
        // Arrange
        const { app } = createTestAppWithMocks();
        mockClerkClient.verifyToken.mockResolvedValue(undefined as any);

        // Act
        const response = await request(app)
          .get('/api/v1/auth/status')
          .set('Authorization', 'Bearer invalid-token')
          .expect(200);

        // Assert
        expect(response.body).toMatchObject({
          data: {
            authenticated: false,
            onboarded: false,
          },
        });
      });

      it('should return authenticated but not onboarded when user not found', async () => {
        // Arrange
        const { app, mockServices } = createTestAppWithMocks();
        mockClerkClient.verifyToken.mockResolvedValue({
          sub: 'clerk-123',
        } as any);
        mockServices.prismaService.authHealthCheck.mockResolvedValue({
          healthy: true,
        });
        mockServices.userService.getUserByClerkId.mockResolvedValue(null);

        // Act
        const response = await request(app)
          .get('/api/v1/auth/status')
          .set('Authorization', 'Bearer valid-token')
          .expect(200);

        // Assert
        expect(response.body).toMatchObject({
          data: {
            authenticated: true,
            onboarded: false,
            user: null,
          },
        });
      });
    });

    describe('Error Handling', () => {
      it('should return 503 when user lookup fails due to database connectivity', async () => {
        // Arrange
        const { app, mockServices } = createTestAppWithMocks();
        mockClerkClient.verifyToken.mockResolvedValue({
          sub: 'clerk-123',
        } as any);
        mockServices.prismaService.authHealthCheck.mockResolvedValue({
          healthy: true,
        });
        mockServices.userService.getUserByClerkId.mockRejectedValue(
          new Error('connect ECONNREFUSED 127.0.0.1:5432')
        );

        // Act
        const response = await request(app)
          .get('/api/v1/auth/status')
          .set('Authorization', 'Bearer valid-token')
          .expect(503);

        // Assert
        expect(response.body).toMatchObject({
          error: 'Service Unavailable',
          message: 'Authentication service requires database connectivity',
          statusCode: 503,
        });
      });

      it('should return 500 for unexpected errors', async () => {
        // Arrange
        const { app } = createTestAppWithMocks();
        mockClerkClient.verifyToken.mockRejectedValue(
          new Error('Unexpected error')
        );

        // Act
        const response = await request(app)
          .get('/api/v1/auth/status')
          .set('Authorization', 'Bearer valid-token')
          .expect(500);

        // Assert
        expect(response.body).toMatchObject({
          error: 'Internal Server Error',
          message: 'Failed to check authentication status',
          statusCode: 500,
        });
      });
    });
  });

  describe('GET /api/v1/auth/me', () => {
    it('should require authentication', async () => {
      // Arrange - Create app with auth middleware that returns 401
      const clerkAuthMock = jest.fn().mockImplementation(() => {
        return (req: any, res: any) => {
          res.status(401).json({
            error: 'Unauthorized',
            message: 'Authentication token is required',
            statusCode: 401,
            timestamp: new Date().toISOString(),
          });
        };
      });

      const { app } = createTestAppWithMocks(clerkAuthMock);

      // Act
      const response = await request(app).get('/api/v1/auth/me').expect(401);

      // Assert
      expect(response.body).toMatchObject({
        error: 'Unauthorized',
        message: 'Authentication token is required',
        statusCode: 401,
      });
    });

    it('should return user profile when authenticated', async () => {
      // Arrange - Create app with auth middleware that sets req.user
      const clerkAuthMock = jest.fn().mockImplementation(() => {
        return (req: any, res: any, next: any) => {
          req.user = {
            id: testData.user.id,
            clerkId: testData.user.clerkId,
            tenantId: testData.user.tenantId,
            email: testData.user.email,
            firstName: testData.user.firstName,
            lastName: testData.user.lastName,
            imageUrl: testData.user.imageUrl,
            isActive: testData.user.isActive,
            createdAt: testData.user.createdAt,
            updatedAt: testData.user.updatedAt,
          };
          next();
        };
      });

      const { app } = createTestAppWithMocks(clerkAuthMock);

      // Act
      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', 'Bearer valid-token')
        .expect(200);

      // Assert
      expect(response.body).toMatchObject({
        data: {
          id: testData.user.id,
          tenantId: testData.user.tenantId,
          email: testData.user.email,
          firstName: testData.user.firstName,
          lastName: testData.user.lastName,
        },
      });
      expect(response.body.timestamp).toBeDefined();
    });

    it('should return 503 when database is unavailable', async () => {
      // Arrange - Create app with auth middleware that returns 503
      const clerkAuthMock = jest.fn().mockImplementation(() => {
        return (req: any, res: any) => {
          res.status(503).json({
            error: 'Service Unavailable',
            message: 'Authentication service requires database connectivity',
            statusCode: 503,
            timestamp: new Date().toISOString(),
          });
        };
      });

      const { app } = createTestAppWithMocks(clerkAuthMock);

      // Act
      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', 'Bearer valid-token')
        .expect(503);

      // Assert
      expect(response.body).toMatchObject({
        error: 'Service Unavailable',
        message: 'Authentication service requires database connectivity',
        statusCode: 503,
      });
    });
  });

  describe('POST /api/v1/auth/onboard', () => {
    it('should require authentication', async () => {
      // Arrange - Create app with auth middleware that returns 401
      const clerkAuthMock = jest.fn().mockImplementation(() => {
        return (req: any, res: any) => {
          res.status(401).json({
            error: 'Unauthorized',
            message: 'Authentication token is required',
            statusCode: 401,
            timestamp: new Date().toISOString(),
          });
        };
      });

      const { app } = createTestAppWithMocks(clerkAuthMock);

      // Act
      const response = await request(app)
        .post('/api/v1/auth/onboard')
        .send({ tenantName: 'Test Tenant' })
        .expect(401);

      // Assert
      expect(response.body).toMatchObject({
        error: 'Unauthorized',
        message: 'Authentication token is required',
        statusCode: 401,
      });
    });

    it('should return 500 when database is unavailable during onboarding', async () => {
      // Arrange - Create app with services that will fail during onboarding
      // Note: /onboard route doesn't use clerkAuth middleware, it handles auth manually
      const { app, mockServices } = createTestAppWithMocks();

      // Mock Clerk to return valid session
      mockClerkClient.verifyToken.mockResolvedValue({
        sub: 'clerk-123',
      } as any);
      (
        mockClerkClient.users.getUser as jest.MockedFunction<any>
      ).mockResolvedValue({
        emailAddresses: [{ emailAddress: '<EMAIL>' }],
        firstName: 'Test',
        lastName: 'User',
        imageUrl: 'https://example.com/image.jpg',
      } as any);

      // Mock database failure during user lookup
      mockServices.userService.getUserByClerkId.mockRejectedValue(
        new Error('connect ECONNREFUSED 127.0.0.1:5432')
      );

      // Act
      const response = await request(app)
        .post('/api/v1/auth/onboard')
        .set('Authorization', 'Bearer valid-token')
        .send({
          tenantName: 'Test Tenant',
          tenantSlug: 'test-tenant',
          firstName: 'Test',
          lastName: 'User',
        })
        .expect(500);

      // Assert
      expect(response.body).toMatchObject({
        error: 'Internal Server Error',
        statusCode: 500,
      });
    });
  });
});
