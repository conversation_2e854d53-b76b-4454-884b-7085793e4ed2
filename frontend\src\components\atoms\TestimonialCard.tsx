import React from "react";
import { clsx } from "clsx";
import { Card } from "./Card";
import { Quote } from "lucide-react";

export interface TestimonialCardProps
  extends React.HTMLAttributes<HTMLDivElement> {
  /** Testimonial quote text */
  quote: string;
  /** Author information */
  author: {
    name: string;
    title?: string;
    company?: string;
    avatar?: string;
  };
  /** Visual variant */
  variant?: "default" | "elevated" | "bordered" | "minimal";
  /** Size variant */
  size?: "sm" | "md" | "lg";
  /** Layout style */
  layout?: "vertical" | "horizontal";
  /** Whether to show quote icon */
  showQuoteIcon?: boolean;
  /** Quote icon position */
  quoteIconPosition?: "top" | "inline";
  /** Rating (1-5 stars) */
  rating?: number;
  /** Whether the card is interactive */
  interactive?: boolean;
}

const sizeClasses = {
  sm: {
    container: "p-4",
    quote: "text-sm",
    authorName: "text-sm font-semibold",
    authorTitle: "text-xs",
    avatar: "h-8 w-8",
    quoteIcon: "h-4 w-4",
  },
  md: {
    container: "p-6",
    quote: "text-base",
    authorName: "text-base font-semibold",
    authorTitle: "text-sm",
    avatar: "h-10 w-10",
    quoteIcon: "h-5 w-5",
  },
  lg: {
    container: "p-8",
    quote: "text-lg",
    authorName: "text-lg font-semibold",
    authorTitle: "text-base",
    avatar: "h-12 w-12",
    quoteIcon: "h-6 w-6",
  },
} as const;

const variantClasses = {
  default: "bg-white shadow-xs hover:shadow-sm",
  elevated: "bg-white shadow-sm hover:shadow-base",
  bordered:
    "bg-white border-2 border-gray-200 hover:border-primary-300 shadow-xs",
  minimal: "bg-transparent",
} as const;

const layoutClasses = {
  vertical: "text-center",
  horizontal: "text-left",
} as const;

export const TestimonialCard: React.FC<TestimonialCardProps> = ({
  quote,
  author,
  variant = "default",
  size = "md",
  layout = "vertical",
  showQuoteIcon = true,
  quoteIconPosition = "top",
  rating,
  interactive = false,
  className,
  ...props
}) => {
  const sizeConfig = sizeClasses[size];

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center gap-1 mb-4">
        {[1, 2, 3, 4, 5].map((star) => (
          <svg
            key={star}
            className={clsx(
              "h-4 w-4",
              star <= rating ? "text-yellow-400 fill-current" : "text-gray-300",
            )}
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    );
  };

  const renderQuoteIcon = () => {
    if (!showQuoteIcon) return null;

    return (
      <Quote
        className={clsx(
          sizeConfig.quoteIcon,
          "text-primary-400",
          quoteIconPosition === "top" && "mb-4",
          quoteIconPosition === "inline" && "inline mr-2 flex-shrink-0",
        )}
      />
    );
  };

  const renderAvatar = () => {
    if (author.avatar) {
      return (
        <img
          src={author.avatar}
          alt={`${author.name} avatar`}
          className={clsx(
            sizeConfig.avatar,
            "rounded-full object-cover border-2 border-gray-200",
          )}
        />
      );
    }

    // Fallback to initials
    const initials = author.name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);

    return (
      <div
        className={clsx(
          sizeConfig.avatar,
          "rounded-full bg-primary-100 text-primary-600 flex items-center justify-center font-semibold text-sm",
        )}
      >
        {initials}
      </div>
    );
  };

  const renderAuthorInfo = () => (
    <div
      className={clsx(
        "flex items-center gap-3",
        layout === "vertical" && "justify-center",
      )}
    >
      {renderAvatar()}
      <div className={clsx(layout === "vertical" && "text-center")}>
        <div className={clsx(sizeConfig.authorName, "text-gray-900")}>
          {author.name}
        </div>
        {(author.title || author.company) && (
          <div className={clsx(sizeConfig.authorTitle, "text-gray-600")}>
            {author.title}
            {author.title && author.company && ", "}
            {author.company}
          </div>
        )}
      </div>
    </div>
  );

  const cardContent = (
    <>
      {rating && renderStars(rating)}

      {showQuoteIcon && quoteIconPosition === "top" && (
        <div className={clsx(layout === "vertical" && "flex justify-center")}>
          {renderQuoteIcon()}
        </div>
      )}

      <blockquote
        className={clsx(
          sizeConfig.quote,
          "text-gray-700 leading-relaxed mb-6 relative",
        )}
      >
        {showQuoteIcon && quoteIconPosition === "inline" && renderQuoteIcon()}"
        {quote}"
      </blockquote>

      {renderAuthorInfo()}
    </>
  );

  return (
    <Card
      className={clsx(
        sizeConfig.container,
        layoutClasses[layout],
        variantClasses[variant],
        interactive && "cursor-pointer transition-all duration-200",
        interactive && "hover:scale-[1.02] active:scale-[0.98]",
        className,
      )}
      data-testid="testimonial-card-container"
      {...props}
    >
      {cardContent}
    </Card>
  );
};
