import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { BackendAuthContext } from '@tech-notes/shared';

import { ConflictError, NotFoundError } from '../types/error.types.js';

import { VehicleHierarchyV3Service } from './vehicle-hierarchy-v3.service.js';

describe('VehicleHierarchyV3Service', () => {
  let vehicleHierarchyV3Service: VehicleHierarchyV3Service;
  let mockServices: any;
  let mockAuthContext: BackendAuthContext;

  beforeEach(() => {
    // Create mocks directly in test following project patterns
    mockServices = {
      prismaService: {
        prisma: {
          tenant: {
            findUnique: jest.fn().mockResolvedValue({ id: 'test-tenant-id' }),
          },
          user: {
            findFirst: jest.fn().mockResolvedValue({ id: 'test-user-id' }),
          },
          vehicleBrandV3: {
            findMany: jest.fn(),
            findFirst: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            aggregate: jest.fn(),
          },
          vehicleSubBrandV3: {
            findMany: jest.fn(),
            findFirst: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            aggregate: jest.fn(),
          },
          vehicleYearV3: {
            findMany: jest.fn(),
            findFirst: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            aggregate: jest.fn(),
          },
          vehicleModelYearV3: {
            findMany: jest.fn(),
            findFirst: jest.fn(),
            create: jest.fn(),
            delete: jest.fn(),
            deleteMany: jest.fn(),
          },
          $transaction: jest.fn(),
        },
        healthCheck: jest.fn().mockResolvedValue(true),
      },
      logger: {
        error: jest.fn(),
        warn: jest.fn(),
        info: jest.fn(),
        debug: jest.fn(),
        verbose: jest.fn(),
        silly: jest.fn(),
        log: jest.fn(),
        query: jest.fn(),
      },
    };

    vehicleHierarchyV3Service = new VehicleHierarchyV3Service(
      mockServices.prismaService as any,
      mockServices.logger as any
    );

    // Mock auth context for tests
    mockAuthContext = {
      id: 'test-user-id',
      clerkId: 'test-clerk-id',
      tenantId: 'test-tenant-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      imageUrl: null,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      canBypassTenantScope: false, // Regular user for most tests
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getBrandsByTenant', () => {
    it('should return brands for a tenant', async () => {
      // Arrange
      const expectedBrands = [
        {
          id: 'brand-1',
          name: 'Ford',
          tenantId: 'test-tenant-id',
          isActive: true,
          displayOrder: 1,
          deletedAt: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'brand-2',
          name: 'Toyota',
          tenantId: 'test-tenant-id',
          isActive: true,
          displayOrder: 2,
          deletedAt: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockServices.prismaService.prisma.vehicleBrandV3.findMany.mockResolvedValue(
        expectedBrands
      );

      // Act
      const brands = await vehicleHierarchyV3Service.getBrandsByTenant(
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(brands).toEqual(expectedBrands);
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.findMany
      ).toHaveBeenCalledWith({
        where: {
          tenantId: 'test-tenant-id',
          deletedAt: null,
        },
        orderBy: [{ displayOrder: 'asc' }, { name: 'asc' }],
      });
    });

    it('should filter out deleted brands', async () => {
      // Arrange
      const activeBrands = [
        {
          id: 'brand-1',
          name: 'Ford',
          tenantId: 'test-tenant-id',
          isActive: true,
          displayOrder: 1,
          deletedAt: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockServices.prismaService.prisma.vehicleBrandV3.findMany.mockResolvedValue(
        activeBrands
      );

      // Act
      const brands = await vehicleHierarchyV3Service.getBrandsByTenant(
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(brands).toEqual(activeBrands);
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.findMany
      ).toHaveBeenCalledWith({
        where: {
          tenantId: 'test-tenant-id',
          deletedAt: null, // Should filter out deleted brands
        },
        orderBy: [{ displayOrder: 'asc' }, { name: 'asc' }],
      });
    });
  });

  describe('getBrandById', () => {
    it('should return brand by ID when found', async () => {
      // Arrange
      const expectedBrand = {
        id: 'brand-1',
        name: 'Ford',
        tenantId: 'test-tenant-id',
        isActive: true,
        displayOrder: 1,
        deletedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        expectedBrand
      );

      // Act
      const brand = await vehicleHierarchyV3Service.getBrandById(
        'brand-1',
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(brand).toEqual(expectedBrand);
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.findFirst
      ).toHaveBeenCalledWith({
        where: {
          id: 'brand-1',
          tenantId: 'test-tenant-id',
          deletedAt: null,
        },
      });
    });

    it('should return null when brand not found', async () => {
      // Arrange
      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        null
      );

      // Act
      const brand = await vehicleHierarchyV3Service.getBrandById(
        'non-existent-id',
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(brand).toBeNull();
    });
  });

  describe('createBrand', () => {
    it('should create a new brand with correct display order', async () => {
      // Arrange
      const brandData = { name: 'Ford' };
      const expectedBrand = {
        id: 'brand-1',
        name: 'Ford',
        tenantId: 'test-tenant-id',
        isActive: true,
        displayOrder: 3, // Next after existing brands
        deletedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock aggregate to return max display order of 2
      mockServices.prismaService.prisma.vehicleBrandV3.aggregate.mockResolvedValue(
        {
          _max: { displayOrder: 2 },
        }
      );
      mockServices.prismaService.prisma.vehicleBrandV3.create.mockResolvedValue(
        expectedBrand
      );

      // Act
      const brand = await vehicleHierarchyV3Service.createBrand(
        brandData,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(brand).toEqual(expectedBrand);
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.aggregate
      ).toHaveBeenCalledWith({
        where: {
          tenantId: 'test-tenant-id',
          deletedAt: null,
        },
        _max: {
          displayOrder: true,
        },
      });
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.create
      ).toHaveBeenCalledWith({
        data: {
          name: 'Ford',
          tenantId: 'test-tenant-id',
          displayOrder: 3,
        },
      });
    });

    it('should create first brand with display order 1', async () => {
      // Arrange
      const brandData = { name: 'Ford' };
      const expectedBrand = {
        id: 'brand-1',
        name: 'Ford',
        tenantId: 'test-tenant-id',
        isActive: true,
        displayOrder: 1,
        deletedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock aggregate to return null (no existing brands)
      mockServices.prismaService.prisma.vehicleBrandV3.aggregate.mockResolvedValue(
        {
          _max: { displayOrder: null },
        }
      );
      mockServices.prismaService.prisma.vehicleBrandV3.create.mockResolvedValue(
        expectedBrand
      );

      // Act
      const brand = await vehicleHierarchyV3Service.createBrand(
        brandData,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(brand).toEqual(expectedBrand);
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.create
      ).toHaveBeenCalledWith({
        data: {
          name: 'Ford',
          tenantId: 'test-tenant-id',
          displayOrder: 1, // First brand gets order 1
        },
      });
    });

    it('should throw ConflictError for duplicate brand name', async () => {
      // Arrange
      const brandData = { name: 'Ford' };

      // Mock aggregate for display order
      mockServices.prismaService.prisma.vehicleBrandV3.aggregate.mockResolvedValue(
        {
          _max: { displayOrder: 1 },
        }
      );

      // Create a proper PrismaClientKnownRequestError for unique constraint violation
      const error = new PrismaClientKnownRequestError(
        'Unique constraint violation',
        {
          code: 'P2002',
          clientVersion: '5.0.0',
        }
      );

      mockServices.prismaService.prisma.vehicleBrandV3.create.mockRejectedValue(
        error
      );

      // Act & Assert
      await expect(
        vehicleHierarchyV3Service.createBrand(
          brandData,
          'test-tenant-id',
          mockAuthContext
        )
      ).rejects.toThrow(ConflictError);
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.create
      ).toHaveBeenCalledWith({
        data: {
          name: 'Ford',
          tenantId: 'test-tenant-id',
          displayOrder: 2,
        },
      });
    });

    it('should trim brand name before creating', async () => {
      // Arrange
      const brandData = { name: '  Ford  ' };
      const expectedBrand = {
        id: 'brand-1',
        name: 'Ford',
        tenantId: 'test-tenant-id',
        isActive: true,
        displayOrder: 1,
        deletedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockServices.prismaService.prisma.vehicleBrandV3.aggregate.mockResolvedValue(
        {
          _max: { displayOrder: null },
        }
      );
      mockServices.prismaService.prisma.vehicleBrandV3.create.mockResolvedValue(
        expectedBrand
      );

      // Act
      await vehicleHierarchyV3Service.createBrand(
        brandData,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.create
      ).toHaveBeenCalledWith({
        data: {
          name: 'Ford', // Should be trimmed
          tenantId: 'test-tenant-id',
          displayOrder: 1,
        },
      });
    });
  });

  describe('updateBrand', () => {
    const existingBrand = {
      id: 'brand-1',
      name: 'Ford',
      tenantId: 'test-tenant-id',
      isActive: true,
      displayOrder: 1,
      deletedAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should update brand name', async () => {
      // Arrange
      const updateData = { name: 'Ford Motor Company' };
      const updatedBrand = { ...existingBrand, name: 'Ford Motor Company' };

      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        existingBrand
      );
      mockServices.prismaService.prisma.vehicleBrandV3.update.mockResolvedValue(
        updatedBrand
      );

      // Act
      const brand = await vehicleHierarchyV3Service.updateBrand(
        'brand-1',
        updateData,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(brand).toEqual(updatedBrand);
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.findFirst
      ).toHaveBeenCalledWith({
        where: {
          id: 'brand-1',
          tenantId: 'test-tenant-id',
          deletedAt: null,
        },
      });
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.update
      ).toHaveBeenCalledWith({
        where: { id: 'brand-1' },
        data: { name: 'Ford Motor Company' },
      });
    });

    it('should update isActive status', async () => {
      // Arrange
      const updateData = { isActive: false };
      const updatedBrand = { ...existingBrand, isActive: false };

      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        existingBrand
      );
      mockServices.prismaService.prisma.vehicleBrandV3.update.mockResolvedValue(
        updatedBrand
      );

      // Act
      const brand = await vehicleHierarchyV3Service.updateBrand(
        'brand-1',
        updateData,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(brand).toEqual(updatedBrand);
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.update
      ).toHaveBeenCalledWith({
        where: { id: 'brand-1' },
        data: { isActive: false },
      });
    });

    it('should update display order', async () => {
      // Arrange
      const updateData = { displayOrder: 5 };
      const updatedBrand = { ...existingBrand, displayOrder: 5 };

      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        existingBrand
      );
      mockServices.prismaService.prisma.vehicleBrandV3.update.mockResolvedValue(
        updatedBrand
      );

      // Act
      const brand = await vehicleHierarchyV3Service.updateBrand(
        'brand-1',
        updateData,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(brand).toEqual(updatedBrand);
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.update
      ).toHaveBeenCalledWith({
        where: { id: 'brand-1' },
        data: { displayOrder: 5 },
      });
    });

    it('should update multiple fields at once', async () => {
      // Arrange
      const updateData = {
        name: 'Ford Motor Company',
        isActive: false,
        displayOrder: 3,
      };
      const updatedBrand = {
        ...existingBrand,
        name: 'Ford Motor Company',
        isActive: false,
        displayOrder: 3,
      };

      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        existingBrand
      );
      mockServices.prismaService.prisma.vehicleBrandV3.update.mockResolvedValue(
        updatedBrand
      );

      // Act
      const brand = await vehicleHierarchyV3Service.updateBrand(
        'brand-1',
        updateData,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(brand).toEqual(updatedBrand);
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.update
      ).toHaveBeenCalledWith({
        where: { id: 'brand-1' },
        data: {
          name: 'Ford Motor Company',
          isActive: false,
          displayOrder: 3,
        },
      });
    });

    it('should throw NotFoundError when brand does not exist', async () => {
      // Arrange
      const updateData = { name: 'Updated Name' };
      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        null
      );

      // Act & Assert
      await expect(
        vehicleHierarchyV3Service.updateBrand(
          'non-existent-id',
          updateData,
          'test-tenant-id',
          mockAuthContext
        )
      ).rejects.toThrow(NotFoundError);
    });

    it('should throw ConflictError for duplicate name', async () => {
      // Arrange
      const updateData = { name: 'Toyota' };
      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        existingBrand
      );

      const error = new PrismaClientKnownRequestError(
        'Unique constraint violation',
        {
          code: 'P2002',
          clientVersion: '5.0.0',
        }
      );
      mockServices.prismaService.prisma.vehicleBrandV3.update.mockRejectedValue(
        error
      );

      // Act & Assert
      await expect(
        vehicleHierarchyV3Service.updateBrand(
          'brand-1',
          updateData,
          'test-tenant-id',
          mockAuthContext
        )
      ).rejects.toThrow(ConflictError);
    });

    it('should trim name before updating', async () => {
      // Arrange
      const updateData = { name: '  Ford Motor Company  ' };
      const updatedBrand = { ...existingBrand, name: 'Ford Motor Company' };

      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        existingBrand
      );
      mockServices.prismaService.prisma.vehicleBrandV3.update.mockResolvedValue(
        updatedBrand
      );

      // Act
      await vehicleHierarchyV3Service.updateBrand(
        'brand-1',
        updateData,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.update
      ).toHaveBeenCalledWith({
        where: { id: 'brand-1' },
        data: { name: 'Ford Motor Company' }, // Should be trimmed
      });
    });
  });

  describe('deleteBrand', () => {
    const existingBrand = {
      id: 'brand-1',
      name: 'Ford',
      tenantId: 'test-tenant-id',
      isActive: true,
      displayOrder: 1,
      deletedAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should soft delete a brand', async () => {
      // Arrange
      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        existingBrand
      );
      mockServices.prismaService.prisma.vehicleBrandV3.update.mockResolvedValue(
        {
          ...existingBrand,
          deletedAt: new Date(),
          isActive: false,
        }
      );

      // Act
      await vehicleHierarchyV3Service.deleteBrand(
        'brand-1',
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.findFirst
      ).toHaveBeenCalledWith({
        where: {
          id: 'brand-1',
          tenantId: 'test-tenant-id',
          deletedAt: null,
        },
      });
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.update
      ).toHaveBeenCalledWith({
        where: { id: 'brand-1' },
        data: {
          deletedAt: expect.any(Date),
          isActive: false,
        },
      });
    });

    it('should throw NotFoundError when brand does not exist', async () => {
      // Arrange
      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        null
      );

      // Act & Assert
      await expect(
        vehicleHierarchyV3Service.deleteBrand(
          'non-existent-id',
          'test-tenant-id',
          mockAuthContext
        )
      ).rejects.toThrow(NotFoundError);
    });
  });

  describe('reorderBrands', () => {
    it('should reorder brands using transaction', async () => {
      // Arrange
      const brandOrders = [
        { id: 'brand-1', displayOrder: 2 },
        { id: 'brand-2', displayOrder: 1 },
      ];

      const mockTransaction = jest.fn().mockImplementation(async (callback) => {
        const mockTx = {
          vehicleBrandV3: {
            update: jest.fn().mockResolvedValue({}),
          },
        };
        return await callback(mockTx);
      });

      mockServices.prismaService.prisma.$transaction = mockTransaction;

      // Act
      await vehicleHierarchyV3Service.reorderBrands(
        brandOrders,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(mockTransaction).toHaveBeenCalledWith(expect.any(Function));
    });
  });

  // ===== SUB-BRAND TESTS =====

  describe('getSubBrandsByBrand', () => {
    it('should return sub-brands for a specific brand', async () => {
      // Arrange
      const brandId = 'brand-1';
      const expectedSubBrands = [
        {
          id: 'sub-brand-1',
          name: 'F-150',
          brandId: 'brand-1',
          tenantId: 'test-tenant-id',
          isActive: true,
          displayOrder: 1,
          deletedAt: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'sub-brand-2',
          name: 'Mustang',
          brandId: 'brand-1',
          tenantId: 'test-tenant-id',
          isActive: true,
          displayOrder: 2,
          deletedAt: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      // Mock brand validation
      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        {
          id: brandId,
        }
      );
      mockServices.prismaService.prisma.vehicleSubBrandV3.findMany.mockResolvedValue(
        expectedSubBrands
      );

      // Act
      const subBrands = await vehicleHierarchyV3Service.getSubBrandsByBrand(
        brandId,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(subBrands).toEqual(expectedSubBrands);
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.findFirst
      ).toHaveBeenCalledWith({
        where: { id: brandId, tenantId: 'test-tenant-id', deletedAt: null },
        select: { id: true },
      });
      expect(
        mockServices.prismaService.prisma.vehicleSubBrandV3.findMany
      ).toHaveBeenCalledWith({
        where: { brandId, tenantId: 'test-tenant-id', deletedAt: null },
        orderBy: { displayOrder: 'asc' },
      });
    });

    it('should throw NotFoundError if brand does not exist', async () => {
      // Arrange
      const brandId = 'non-existent-brand';
      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        null
      );

      // Act & Assert
      await expect(
        vehicleHierarchyV3Service.getSubBrandsByBrand(
          brandId,
          'test-tenant-id',
          mockAuthContext
        )
      ).rejects.toThrow(NotFoundError);
    });
  });

  describe('getSubBrandById', () => {
    it('should return a sub-brand by ID', async () => {
      // Arrange
      const subBrandId = 'sub-brand-1';
      const expectedSubBrand = {
        id: subBrandId,
        name: 'F-150',
        brandId: 'brand-1',
        tenantId: 'test-tenant-id',
        isActive: true,
        displayOrder: 1,
        deletedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockServices.prismaService.prisma.vehicleSubBrandV3.findFirst.mockResolvedValue(
        expectedSubBrand
      );

      // Act
      const subBrand = await vehicleHierarchyV3Service.getSubBrandById(
        subBrandId,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(subBrand).toEqual(expectedSubBrand);
      expect(
        mockServices.prismaService.prisma.vehicleSubBrandV3.findFirst
      ).toHaveBeenCalledWith({
        where: { id: subBrandId, tenantId: 'test-tenant-id', deletedAt: null },
      });
    });

    it('should return null if sub-brand does not exist', async () => {
      // Arrange
      const subBrandId = 'non-existent-sub-brand';
      mockServices.prismaService.prisma.vehicleSubBrandV3.findFirst.mockResolvedValue(
        null
      );

      // Act
      const subBrand = await vehicleHierarchyV3Service.getSubBrandById(
        subBrandId,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(subBrand).toBeNull();
    });
  });

  describe('createSubBrand', () => {
    it('should create a new sub-brand with correct display order', async () => {
      // Arrange
      const subBrandData = { name: 'F-150', brandId: 'brand-1' };
      const expectedSubBrand = {
        id: 'sub-brand-1',
        name: 'F-150',
        brandId: 'brand-1',
        tenantId: 'test-tenant-id',
        isActive: true,
        displayOrder: 3, // Next after existing sub-brands
        deletedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock brand validation
      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        {
          id: 'brand-1',
        }
      );
      // Mock aggregate to return max display order of 2
      mockServices.prismaService.prisma.vehicleSubBrandV3.aggregate.mockResolvedValue(
        {
          _max: { displayOrder: 2 },
        }
      );
      mockServices.prismaService.prisma.vehicleSubBrandV3.create.mockResolvedValue(
        expectedSubBrand
      );

      // Act
      const subBrand = await vehicleHierarchyV3Service.createSubBrand(
        subBrandData,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(subBrand).toEqual(expectedSubBrand);
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.findFirst
      ).toHaveBeenCalledWith({
        where: { id: 'brand-1', tenantId: 'test-tenant-id', deletedAt: null },
        select: { id: true },
      });
      expect(
        mockServices.prismaService.prisma.vehicleSubBrandV3.aggregate
      ).toHaveBeenCalledWith({
        where: {
          brandId: 'brand-1',
          tenantId: 'test-tenant-id',
          deletedAt: null,
        },
        _max: { displayOrder: true },
      });
      expect(
        mockServices.prismaService.prisma.vehicleSubBrandV3.create
      ).toHaveBeenCalledWith({
        data: {
          name: 'F-150',
          brandId: 'brand-1',
          tenantId: 'test-tenant-id',
          displayOrder: 3,
        },
      });
    });

    it('should create first sub-brand with display order 1', async () => {
      // Arrange
      const subBrandData = { name: 'F-150', brandId: 'brand-1' };
      const expectedSubBrand = {
        id: 'sub-brand-1',
        name: 'F-150',
        brandId: 'brand-1',
        tenantId: 'test-tenant-id',
        isActive: true,
        displayOrder: 1,
        deletedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock brand validation
      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        {
          id: 'brand-1',
        }
      );
      // Mock aggregate to return null (no existing sub-brands)
      mockServices.prismaService.prisma.vehicleSubBrandV3.aggregate.mockResolvedValue(
        {
          _max: { displayOrder: null },
        }
      );
      mockServices.prismaService.prisma.vehicleSubBrandV3.create.mockResolvedValue(
        expectedSubBrand
      );

      // Act
      const subBrand = await vehicleHierarchyV3Service.createSubBrand(
        subBrandData,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(subBrand).toEqual(expectedSubBrand);
      expect(
        mockServices.prismaService.prisma.vehicleSubBrandV3.create
      ).toHaveBeenCalledWith({
        data: {
          name: 'F-150',
          brandId: 'brand-1',
          tenantId: 'test-tenant-id',
          displayOrder: 1,
        },
      });
    });

    it('should throw NotFoundError if brand does not exist', async () => {
      // Arrange
      const subBrandData = { name: 'F-150', brandId: 'non-existent-brand' };
      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        null
      );

      // Act & Assert
      await expect(
        vehicleHierarchyV3Service.createSubBrand(
          subBrandData,
          'test-tenant-id',
          mockAuthContext
        )
      ).rejects.toThrow(NotFoundError);
    });

    it('should throw ConflictError for duplicate sub-brand name within brand', async () => {
      // Arrange
      const subBrandData = { name: 'F-150', brandId: 'brand-1' };

      // Mock brand validation
      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        {
          id: 'brand-1',
        }
      );
      mockServices.prismaService.prisma.vehicleSubBrandV3.aggregate.mockResolvedValue(
        {
          _max: { displayOrder: 1 },
        }
      );

      const duplicateError = new PrismaClientKnownRequestError(
        'Unique constraint failed',
        { code: 'P2002', clientVersion: '5.0.0' }
      );
      mockServices.prismaService.prisma.vehicleSubBrandV3.create.mockRejectedValue(
        duplicateError
      );

      // Act & Assert
      await expect(
        vehicleHierarchyV3Service.createSubBrand(
          subBrandData,
          'test-tenant-id',
          mockAuthContext
        )
      ).rejects.toThrow(ConflictError);
    });
  });

  describe('updateSubBrand', () => {
    it('should update a sub-brand successfully', async () => {
      // Arrange
      const subBrandId = 'sub-brand-1';
      const updateData = { name: 'F-150 Updated', isActive: false };
      const expectedSubBrand = {
        id: subBrandId,
        name: 'F-150 Updated',
        brandId: 'brand-1',
        tenantId: 'test-tenant-id',
        isActive: false,
        displayOrder: 1,
        deletedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockServices.prismaService.prisma.vehicleSubBrandV3.update.mockResolvedValue(
        expectedSubBrand
      );

      // Act
      const subBrand = await vehicleHierarchyV3Service.updateSubBrand(
        subBrandId,
        updateData,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(subBrand).toEqual(expectedSubBrand);
      expect(
        mockServices.prismaService.prisma.vehicleSubBrandV3.update
      ).toHaveBeenCalledWith({
        where: { id: subBrandId, tenantId: 'test-tenant-id', deletedAt: null },
        data: { name: 'F-150 Updated', isActive: false },
      });
    });

    it('should throw NotFoundError if sub-brand does not exist', async () => {
      // Arrange
      const subBrandId = 'non-existent-sub-brand';
      const updateData = { name: 'Updated Name' };

      const notFoundError = new PrismaClientKnownRequestError(
        'Record not found',
        { code: 'P2025', clientVersion: '5.0.0' }
      );
      mockServices.prismaService.prisma.vehicleSubBrandV3.update.mockRejectedValue(
        notFoundError
      );

      // Act & Assert
      await expect(
        vehicleHierarchyV3Service.updateSubBrand(
          subBrandId,
          updateData,
          'test-tenant-id',
          mockAuthContext
        )
      ).rejects.toThrow(NotFoundError);
    });
  });

  describe('deleteSubBrand', () => {
    it('should soft delete a sub-brand successfully', async () => {
      // Arrange
      const subBrandId = 'sub-brand-1';
      const expectedResult = {
        id: subBrandId,
        deletedAt: new Date(),
        isActive: false,
      };

      mockServices.prismaService.prisma.vehicleSubBrandV3.update.mockResolvedValue(
        expectedResult
      );

      // Act
      await vehicleHierarchyV3Service.deleteSubBrand(
        subBrandId,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(
        mockServices.prismaService.prisma.vehicleSubBrandV3.update
      ).toHaveBeenCalledWith({
        where: { id: subBrandId, tenantId: 'test-tenant-id', deletedAt: null },
        data: { deletedAt: expect.any(Date), isActive: false },
      });
    });

    it('should throw NotFoundError if sub-brand does not exist', async () => {
      // Arrange
      const subBrandId = 'non-existent-sub-brand';

      const notFoundError = new PrismaClientKnownRequestError(
        'Record not found',
        { code: 'P2025', clientVersion: '5.0.0' }
      );
      mockServices.prismaService.prisma.vehicleSubBrandV3.update.mockRejectedValue(
        notFoundError
      );

      // Act & Assert
      await expect(
        vehicleHierarchyV3Service.deleteSubBrand(
          subBrandId,
          'test-tenant-id',
          mockAuthContext
        )
      ).rejects.toThrow(NotFoundError);
    });
  });

  describe('reorderSubBrands', () => {
    it('should reorder sub-brands within a brand successfully', async () => {
      // Arrange
      const brandId = 'brand-1';
      const subBrandOrders = [
        { id: 'sub-brand-1', displayOrder: 2 },
        { id: 'sub-brand-2', displayOrder: 1 },
      ];

      // Mock brand validation
      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        {
          id: brandId,
        }
      );

      const mockTx = {
        vehicleSubBrandV3: {
          update: jest.fn().mockResolvedValue({}),
        },
      };

      const mockTransaction = jest.fn().mockImplementation(async (callback) => {
        return await callback(mockTx);
      });

      mockServices.prismaService.prisma.$transaction = mockTransaction;

      // Act
      await vehicleHierarchyV3Service.reorderSubBrands(
        brandId,
        subBrandOrders,
        'test-tenant-id',
        mockAuthContext
      );

      // Assert
      expect(
        mockServices.prismaService.prisma.vehicleBrandV3.findFirst
      ).toHaveBeenCalledWith({
        where: { id: brandId, tenantId: 'test-tenant-id', deletedAt: null },
        select: { id: true },
      });
      expect(mockTransaction).toHaveBeenCalledWith(expect.any(Function));
      expect(mockTx.vehicleSubBrandV3.update).toHaveBeenCalledTimes(2);
    });

    it('should throw NotFoundError if brand does not exist', async () => {
      // Arrange
      const brandId = 'non-existent-brand';
      const subBrandOrders = [{ id: 'sub-brand-1', displayOrder: 1 }];

      mockServices.prismaService.prisma.vehicleBrandV3.findFirst.mockResolvedValue(
        null
      );

      // Act & Assert
      await expect(
        vehicleHierarchyV3Service.reorderSubBrands(
          brandId,
          subBrandOrders,
          'test-tenant-id',
          mockAuthContext
        )
      ).rejects.toThrow(NotFoundError);
    });
  });
});
