// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// RBAC Enums
enum RoleType {
  SYSTEM_ADMIN
  COMPANY_ADMIN
  COMPANY_TECH
}

enum PermissionResource {
  USER
  TENANT
  SYSTEM
  DATA
  DOCUMENT
}

enum PermissionAction {
  READ
  WRITE
  DELETE
  MANAGE
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  EXPIRED
  REVOKED
}

model Tenant {
  id        String   @id @default(cuid())
  name      String
  slug      String   @unique
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  users            User[]
  userRoles        UserRole[]
  invitations      Invitation[]

  // V3 Vehicle Hierarchy Relations
  vehicleBrandsV3     VehicleBrandV3[]
  vehicleSubBrandsV3  VehicleSubBrandV3[]
  vehicleModelsV3     VehicleModelV3[]
  vehicleYearsV3      VehicleYearV3[]
  vehicleModelYearsV3 VehicleModelYearV3[]
  // Document Relations
  documents           Document[]

  @@map("tenants")
}

model User {
  id        String   @id @default(cuid())
  clerkId   String   @unique
  tenantId  String
  email     String
  firstName String?
  lastName  String?
  imageUrl  String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // User engagement tracking
  lastLoginAt    DateTime?
  lastActivityAt DateTime?

  // Relations
  tenant        Tenant     @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  userRoles     UserRole[]
  assignedRoles UserRole[] @relation("AssignedRoles")
  createdInvitations Invitation[] @relation("CreatedInvitations")
  usedInvitation Invitation? @relation("UsedInvitation")
  createdDocuments Document[] @relation("CreatedDocuments")

  // Indexes for performance
  @@index([tenantId])
  @@index([clerkId])
  @@index([tenantId, email])
  @@index([tenantId, isActive])
  @@index([tenantId, lastLoginAt])    // For engagement queries
  @@index([tenantId, lastActivityAt]) // For engagement queries
  @@map("users")
}

model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  type        RoleType
  description String
  isSystemRole Boolean @default(false) // true for System Admin
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  userRoles   UserRole[]
  permissions RolePermission[]
  invitations Invitation[]

  @@map("roles")
}

model Permission {
  id          String             @id @default(cuid())
  name        String             @unique
  description String
  resource    PermissionResource
  action      PermissionAction
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt

  // Relations
  roles       RolePermission[]

  @@map("permissions")
}

model UserRole {
  id         String   @id @default(cuid())
  userId     String
  roleId     String
  tenantId   String?  // MUST be null for system-level roles
  assignedBy String?  // userId who assigned this role
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  role       Role     @relation(fields: [roleId], references: [id], onDelete: Cascade)
  tenant     Tenant?  @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  assignedByUser User? @relation("AssignedRoles", fields: [assignedBy], references: [id])

  // Constraints and Security
  @@unique([userId, roleId, tenantId])
  @@index([userId])
  @@index([tenantId])
  @@index([roleId])

  // Note: Critical security validation (System roles MUST have null tenantId)
  // will be enforced at the service layer due to Prisma limitations

  @@map("user_roles")
}

model RolePermission {
  roleId       String
  permissionId String

  role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@id([roleId, permissionId])
  @@map("role_permissions")
}

model Invitation {
  id          String    @id @default(cuid())
  clerkInvitationId String @unique // Clerk's invitation ID
  email       String
  tenantId    String
  roleId      String    // Role to assign when invitation is accepted
  createdBy   String    // User ID who created the invitation
  usedBy      String?   @unique // User ID who used the invitation (unique for one-to-one)
  usedAt      DateTime? // When the invitation was used
  expiresAt   DateTime  // When the invitation expires
  status      InvitationStatus @default(PENDING)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  tenant      Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  role        Role      @relation(fields: [roleId], references: [id], onDelete: Cascade)
  creator     User      @relation("CreatedInvitations", fields: [createdBy], references: [id])
  user        User?     @relation("UsedInvitation", fields: [usedBy], references: [id])

  // Indexes for performance
  @@index([tenantId])
  @@index([email])
  @@index([status])
  @@index([createdBy])
  @@index([expiresAt])
  @@map("invitations")
}

// Document Storage Model
model Document {
  id          String   @id @default(cuid())
  tenantId    String
  fileName    String
  originalName String
  fileSize    Int
  mimeType    String
  s3Key       String   @unique
  title       String?
  description String?
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  deletedAt   DateTime?

  // Relations
  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  creator     User     @relation("CreatedDocuments", fields: [createdBy], references: [id])

  // Indexes for performance
  @@index([tenantId])
  @@index([createdBy])
  @@index([s3Key])
  @@index([tenantId, createdAt])
  @@index([tenantId, fileName])
  @@index([deletedAt])
  @@map("documents")
}



// ===== VEHICLE HIERARCHY V3 MODELS =====
// 4-Level Hierarchy: Brand → Sub-Brand → Model ↔ Year (many-to-many)
// Incremental implementation: Brand first, then Sub-Brand, Model, Year associations

model VehicleBrandV3 {
  id           String    @id @default(cuid())
  name         String    @db.VarChar(500) // Max 500 characters
  tenantId     String
  isActive     Boolean   @default(true)
  displayOrder Int       // Tenant-scoped ordering (handled in service layer)
  deletedAt    DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relations
  tenant       Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  subBrands    VehicleSubBrandV3[]

  // Constraints
  @@unique([name, tenantId]) // Unique brand name per tenant
  @@index([tenantId])
  @@index([name])
  @@index([isActive])
  @@index([deletedAt])       // For soft delete queries
  @@index([tenantId, displayOrder]) // Tenant-scoped ordering
  @@map("vehicle_brands_v3")
}

model VehicleSubBrandV3 {
  id           String    @id @default(cuid())
  name         String    @db.VarChar(500) // Max 500 characters
  brandId      String
  tenantId     String
  isActive     Boolean   @default(true)
  displayOrder Int       // Brand-scoped ordering (handled in service layer)
  deletedAt    DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relations
  brand        VehicleBrandV3 @relation(fields: [brandId], references: [id], onDelete: Cascade)
  tenant       Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  models       VehicleModelV3[]

  // Constraints
  @@unique([name, brandId, tenantId]) // Unique sub-brand name per brand per tenant
  @@index([tenantId])
  @@index([brandId])
  @@index([name])
  @@index([isActive])
  @@index([deletedAt])       // For soft delete queries
  @@index([brandId, displayOrder]) // Brand-scoped ordering
  @@index([tenantId, brandId]) // Tenant and brand filtering
  @@map("vehicle_sub_brands_v3")
}

model VehicleModelV3 {
  id           String    @id @default(cuid())
  name         String    @db.VarChar(500) // Max 500 characters
  subBrandId   String
  tenantId     String
  isActive     Boolean   @default(true)
  displayOrder Int       // Sub-brand-scoped ordering (handled in service layer)
  deletedAt    DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relations
  subBrand     VehicleSubBrandV3    @relation(fields: [subBrandId], references: [id], onDelete: Cascade)
  tenant       Tenant               @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  modelYears   VehicleModelYearV3[]

  // Constraints
  @@unique([name, subBrandId, tenantId]) // Unique model name per sub-brand per tenant
  @@index([tenantId])
  @@index([subBrandId])
  @@index([name])
  @@index([isActive])
  @@index([deletedAt])       // For soft delete queries
  @@index([subBrandId, displayOrder]) // Sub-brand-scoped ordering
  @@index([tenantId, subBrandId]) // Tenant and sub-brand filtering
  @@map("vehicle_models_v3")
}

model VehicleYearV3 {
  id           String    @id @default(cuid())
  name         String    @db.VarChar(500) // Year value (e.g., "2020", "2021")
  tenantId     String
  isActive     Boolean   @default(true)
  displayOrder Int
  deletedAt    DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relations
  tenant       Tenant                @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  modelYears   VehicleModelYearV3[]

  // Constraints
  @@unique([name, tenantId]) // Unique year name per tenant
  @@index([tenantId])
  @@index([name])
  @@index([isActive])
  @@index([deletedAt])       // For soft delete queries
  @@index([tenantId, displayOrder]) // Tenant-scoped ordering
  @@map("vehicle_years_v3")
}

model VehicleModelYearV3 {
  id        String   @id @default(cuid())
  modelId   String
  yearId    String
  tenantId  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  model  VehicleModelV3 @relation(fields: [modelId], references: [id], onDelete: Cascade)
  year   VehicleYearV3  @relation(fields: [yearId], references: [id], onDelete: Cascade)
  tenant Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Constraints
  @@unique([modelId, yearId, tenantId]) // Prevent duplicate model-year associations per tenant
  @@index([tenantId])
  @@index([modelId])
  @@index([yearId])
  @@index([tenantId, modelId]) // Tenant and model filtering
  @@index([tenantId, yearId])  // Tenant and year filtering
  @@map("vehicle_model_years_v3")
}

