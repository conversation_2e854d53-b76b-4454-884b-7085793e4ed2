import React, { useState } from "react";
import { <PERSON>ton, Badge, CompanyAdminOnly } from "../../../components";
import type {
  VehicleHierarchyData,
  VehicleYear,
  VehicleMake,
  VehicleModel,
} from "../../../services/api-client";
import {
  ChevronDown,
  ChevronRight,
  Calendar,
  Car,
  Settings,
  Plus,
  Trash2,
} from "lucide-react";

interface VehicleHierarchyTreeProps {
  hierarchy: VehicleHierarchyData;
  onCreateModel: (make: VehicleMake) => void;
  onManageModelYears: (model: VehicleModel) => void;
  onDeleteYear: (year: VehicleYear) => void;
  onDeleteMake: (make: VehicleMake) => void;
  onDeleteModel: (model: VehicleModel) => void;
  searchQuery?: string;
}

export const VehicleHierarchyTree: React.FC<VehicleHierarchyTreeProps> =
  React.memo(
    ({
      hierarchy,
      onCreateModel,
      onManageModelYears,
      onDeleteYear,
      onDeleteMake,
      onDeleteModel,
      searchQuery = "",
    }) => {
      const [expandedYears, setExpandedYears] = useState<Set<string>>(
        new Set(),
      );
      const [expandedMakes, setExpandedMakes] = useState<Set<string>>(
        new Set(),
      );

      const toggleYear = (yearId: string) => {
        const newExpanded = new Set(expandedYears);
        if (newExpanded.has(yearId)) {
          newExpanded.delete(yearId);
        } else {
          newExpanded.add(yearId);
        }
        setExpandedYears(newExpanded);
      };

      const toggleMake = (makeId: string) => {
        const newExpanded = new Set(expandedMakes);
        if (newExpanded.has(makeId)) {
          newExpanded.delete(makeId);
        } else {
          newExpanded.add(makeId);
        }
        setExpandedMakes(newExpanded);
      };

      const expandAll = () => {
        const allYearIds = new Set(hierarchy.years.map((year) => year.id));
        const allMakeIds = new Set(
          hierarchy.years.flatMap((year) => year.makes.map((make) => make.id)),
        );
        setExpandedYears(allYearIds);
        setExpandedMakes(allMakeIds);
      };

      const collapseAll = () => {
        setExpandedYears(new Set());
        setExpandedMakes(new Set());
      };

      // Search highlighting utility
      const highlightText = (text: string, query: string) => {
        if (!query.trim()) return text;

        const regex = new RegExp(
          `(${query.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
          "gi",
        );
        const parts = text.split(regex);

        return parts.map((part, index) =>
          regex.test(part) ? (
            <mark key={index} className="bg-yellow-200 px-1 rounded">
              {part}
            </mark>
          ) : (
            part
          ),
        );
      };

      if (!hierarchy.years.length) {
        return (
          <div className="text-center py-12">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No Years Found
            </h3>
            <p className="text-gray-600 mb-4">
              Start by creating your first year to build the vehicle hierarchy.
            </p>
          </div>
        );
      }

      return (
        <div className="space-y-4">
          {/* Tree Controls */}
          <div className="flex items-center justify-between border-b border-gray-200 pb-4">
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={expandAll}>
                Expand All
              </Button>
              <Button variant="outline" size="sm" onClick={collapseAll}>
                Collapse All
              </Button>
            </div>
            <div className="text-sm text-gray-600">
              {hierarchy.years.length} years,{" "}
              {hierarchy.years.reduce(
                (total, year) => total + year.makes.length,
                0,
              )}{" "}
              makes,{" "}
              {hierarchy.years.reduce(
                (total, year) =>
                  total +
                  year.makes.reduce(
                    (makeTotal, make) => makeTotal + make.models.length,
                    0,
                  ),
                0,
              )}{" "}
              models
            </div>
          </div>

          {/* Tree Structure */}
          <div className="space-y-2">
            {hierarchy.years
              .sort((a, b) => b.year - a.year) // Sort years descending
              .map((year) => (
                <div
                  key={year.id}
                  className="border border-gray-200 rounded-lg"
                >
                  {/* Year Level */}
                  <div className="flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors">
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => toggleYear(year.id)}
                        className="flex items-center justify-center w-6 h-6 rounded hover:bg-gray-200 transition-colors"
                        disabled={!year.makes.length}
                      >
                        {year.makes.length > 0 &&
                          (expandedYears.has(year.id) ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          ))}
                      </button>
                      <Calendar className="h-5 w-5 text-primary-600" />
                      <span className="font-semibold text-gray-900">
                        {year.year}
                      </span>
                      <Badge variant="secondary">
                        {year.makes.length} make
                        {year.makes.length !== 1 ? "s" : ""}
                      </Badge>
                    </div>

                    <CompanyAdminOnly>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDeleteYear(year)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CompanyAdminOnly>
                  </div>

                  {/* Makes Level */}
                  {expandedYears.has(year.id) && year.makes.length > 0 && (
                    <div className="border-t border-gray-200">
                      {year.makes
                        .sort((a, b) => a.name.localeCompare(b.name)) // Sort makes alphabetically
                        .map((make) => (
                          <div
                            key={make.id}
                            className="border-b border-gray-100 last:border-b-0"
                          >
                            {/* Make Header */}
                            <div className="flex items-center justify-between p-4 pl-12 bg-white hover:bg-gray-50 transition-colors">
                              <div className="flex items-center space-x-3">
                                <button
                                  onClick={() => toggleMake(make.id)}
                                  className="flex items-center justify-center w-6 h-6 rounded hover:bg-gray-200 transition-colors"
                                  disabled={!make.models.length}
                                >
                                  {make.models.length > 0 &&
                                    (expandedMakes.has(make.id) ? (
                                      <ChevronDown className="h-4 w-4" />
                                    ) : (
                                      <ChevronRight className="h-4 w-4" />
                                    ))}
                                </button>
                                <Car className="h-5 w-5 text-blue-600" />
                                <span className="font-medium text-gray-900">
                                  {highlightText(make.name, searchQuery)}
                                </span>
                                <Badge variant="outline">
                                  {make.models.length} model
                                  {make.models.length !== 1 ? "s" : ""}
                                </Badge>
                              </div>

                              <CompanyAdminOnly>
                                <div className="flex items-center space-x-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => onCreateModel(make)}
                                    className="text-green-600 hover:text-green-700 hover:bg-green-50"
                                  >
                                    <Plus className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => onDeleteMake(make)}
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </CompanyAdminOnly>
                            </div>

                            {/* Models Level */}
                            {expandedMakes.has(make.id) &&
                              make.models.length > 0 && (
                                <div className="bg-gray-25">
                                  {make.models
                                    .sort((a, b) =>
                                      a.name.localeCompare(b.name),
                                    ) // Sort models alphabetically
                                    .map((model) => (
                                      <div
                                        key={model.id}
                                        className="flex items-center justify-between p-3 pl-20 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 transition-colors"
                                      >
                                        <div className="flex items-center space-x-3">
                                          <Settings className="h-4 w-4 text-green-600" />
                                          <span className="text-gray-900">
                                            {highlightText(
                                              model.name,
                                              searchQuery,
                                            )}
                                          </span>
                                          {!model.isActive && (
                                            <Badge variant="warning">
                                              Inactive
                                            </Badge>
                                          )}
                                          <Badge
                                            variant="secondary"
                                            className="text-xs"
                                          >
                                            {model.years?.length || 0} year
                                            {(model.years?.length || 0) !== 1
                                              ? "s"
                                              : ""}
                                          </Badge>
                                        </div>

                                        <div className="flex items-center space-x-2">
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() =>
                                              onManageModelYears(model)
                                            }
                                            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                            title="Manage year associations"
                                          >
                                            <Calendar className="h-3 w-3" />
                                          </Button>

                                          <CompanyAdminOnly>
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() =>
                                                onDeleteModel(model)
                                              }
                                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                            >
                                              <Trash2 className="h-3 w-3" />
                                            </Button>
                                          </CompanyAdminOnly>
                                        </div>
                                      </div>
                                    ))}
                                </div>
                              )}

                            {/* Empty state for make with no models */}
                            {expandedMakes.has(make.id) &&
                              make.models.length === 0 && (
                                <div className="p-6 pl-20 text-center text-gray-500">
                                  <Settings className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                  <p className="text-sm">
                                    No models found for this make
                                  </p>
                                  <CompanyAdminOnly>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => onCreateModel(make)}
                                      className="mt-2"
                                    >
                                      <Plus className="h-4 w-4 mr-2" />
                                      Add Model
                                    </Button>
                                  </CompanyAdminOnly>
                                </div>
                              )}
                          </div>
                        ))}
                    </div>
                  )}

                  {/* Empty state for year with no makes */}
                  {expandedYears.has(year.id) && year.makes.length === 0 && (
                    <div className="p-6 text-center text-gray-500 border-t border-gray-200">
                      <Car className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm">No makes found for this year</p>
                    </div>
                  )}
                </div>
              ))}
          </div>
        </div>
      );
    },
  );
