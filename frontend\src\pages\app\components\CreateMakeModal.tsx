import React, { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { Modal, Button, Input, FormField, Alert } from "../../../components";
import { useTypedApi } from "../../../services/api-client";
import { Car } from "lucide-react";

interface CreateMakeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const CreateMakeModal: React.FC<CreateMakeModalProps> = ({
  isOpen,
  onClose,
}) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [name, setName] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});

  const createMakeMutation = useMutation({
    mutationFn: (makeData: { name: string }) =>
      api.vehicleHierarchy.createMake(makeData),
    onSuccess: () => {
      toast.success("Make created successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-makes"] });
      handleClose();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create make");
    },
  });

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = "Make name is required";
    } else if (name.trim().length < 2) {
      newErrors.name = "Make name must be at least 2 characters";
    } else if (name.trim().length > 100) {
      newErrors.name = "Make name must be less than 100 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    createMakeMutation.mutate({ name: name.trim() });
  };

  const handleClose = () => {
    setName("");
    setErrors({});
    onClose();
  };

  // Common RV/Travel Trailer makes for quick selection
  const suggestedMakes = [
    "Rockwood GeoPro",
    "Forest River Cherokee",
    "Keystone Passport",
    "Jayco Jay Flight",
    "Grand Design Imagine",
    "Coachmen Catalina",
    "Dutchmen Aspen Trail",
    "KZ Sportsmen",
    "Palomino Puma",
    "Winnebago Micro Minnie",
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Create New Make"
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex items-center space-x-3 text-gray-600 mb-4">
          <Car className="h-5 w-5" />
          <span>Add a new vehicle make to the hierarchy</span>
        </div>

        <FormField label="Make Name" error={errors.name} required>
          <Input
            type="text"
            placeholder="e.g., Rockwood GeoPro"
            value={name}
            onChange={(e) => setName(e.target.value)}
            error={errors.name}
            maxLength={100}
          />
        </FormField>

        {/* Quick Select Buttons */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Popular RV Makes
          </label>
          <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
            {suggestedMakes.map((suggestedMake) => (
              <Button
                key={suggestedMake}
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setName(suggestedMake)}
                className="text-xs text-left justify-start"
              >
                {suggestedMake}
              </Button>
            ))}
          </div>
          <p className="text-xs text-gray-500">
            Click any make above to use it, or type your own
          </p>
        </div>

        {createMakeMutation.error && (
          <Alert variant="error">
            {createMakeMutation.error.message || "Failed to create make"}
          </Alert>
        )}

        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={createMakeMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={createMakeMutation.isPending}
            className="flex items-center space-x-2"
          >
            {createMakeMutation.isPending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Creating...</span>
              </>
            ) : (
              <>
                <Car className="h-4 w-4" />
                <span>Create Make</span>
              </>
            )}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
