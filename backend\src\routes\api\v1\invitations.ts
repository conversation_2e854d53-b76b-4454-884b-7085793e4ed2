import { RoleType } from '@prisma/client';
import { Router, Response, Request, NextFunction } from 'express';
import { z } from 'zod';

import { MiddlewareFactory } from '../../../middleware/middleware-factory.js';
import { CommonPermissions } from '../../../middleware/middleware-factory.js';
import { InvitationService } from '../../../services/invitation.service.js';
import { PrismaService } from '../../../services/prisma.service.js';
import {
  CreateTenantWithAdminData,
  InviteUserData,
} from '../../../types/invitation.types.js';
import { Logger } from '../../../utils/logger.js';
import { getRequestUser } from '../../../utils/request-types.js';

interface ServiceDependencies {
  invitationService: InvitationService;
  middlewareFactory: MiddlewareFactory;
  prismaService: PrismaService;
  logger: Logger;
}

// Validation schemas
const createTenantWithAdminSchema = z.object({
  tenantName: z.string().min(1, 'Tenant name is required'),
  tenantSlug: z
    .string()
    .min(1, 'Tenant slug is required')
    .regex(
      /^[a-z0-9-]+$/,
      'Tenant slug must contain only lowercase letters, numbers, and hyphens'
    ),
  adminEmail: z.string().email('Valid email is required'),
  adminFirstName: z.string().optional(),
  adminLastName: z.string().optional(),
});

const inviteUserSchema = z.object({
  email: z.string().email('Valid email is required'),
  roleType: z.nativeEnum(RoleType),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
});

export function createInvitationsRouter(
  dependencies: ServiceDependencies
): Router {
  const { invitationService, middlewareFactory, prismaService, logger } =
    dependencies;
  const router = Router();

  /**
   * POST /api/v1/invitations/tenant-with-admin
   * Create new tenant with initial Company Admin (System Admin only)
   */
  router.post(
    '/tenant-with-admin',
    ...middlewareFactory.createAuthWithSystemAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const data = createTenantWithAdminSchema.parse(req.body);
        const user = getRequestUser(req);

        const result = await invitationService.createTenantWithAdmin(
          data as CreateTenantWithAdminData,
          user!
        );

        logger.info('Tenant created with admin invitation', {
          tenantId: result.tenant.id,
          tenantName: result.tenant.name,
          adminEmail: data.adminEmail,
          invitationId: result.invitation.id,
          createdBy: user!.id,
        });

        res.status(201).json({
          data: {
            tenant: result.tenant,
            invitation: result.invitation,
          },
          message: 'Tenant created and admin invitation sent successfully',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return res.status(400).json({
            error: 'Bad Request',
            message: error.errors.map((e) => e.message).join(', '),
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        logger.error('Failed to create tenant with admin', {
          error,
          requestData: req.body,
          userId: getRequestUser(req)?.id,
        });

        // Let the error handler middleware handle the error instead of crashing
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/invitations/invite-user
   * Invite user to existing tenant (Company Admin only)
   */
  router.post(
    '/invite-user',
    ...middlewareFactory.createAuthWithPermission(
      CommonPermissions.USER_MANAGE
    ),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const data = inviteUserSchema.parse(req.body);
        const user = getRequestUser(req);

        const invitation = await invitationService.inviteUserToTenant(
          data as InviteUserData,
          user!
        );

        logger.info('User invitation created', {
          invitationId: invitation.id,
          email: data.email,
          roleType: data.roleType,
          tenantId: user!.tenantId,
          createdBy: user!.id,
        });

        res.status(201).json({
          data: invitation,
          message: 'User invitation sent successfully',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return res.status(400).json({
            error: 'Bad Request',
            message: error.errors.map((e) => e.message).join(', '),
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        logger.error('Failed to create user invitation', {
          error,
          requestData: req.body,
          userId: getRequestUser(req)?.id,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/invitations/:id/details
   * Get invitation details by ID (public endpoint for debugging)
   */
  router.get(
    '/:id/details',
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { id } = req.params;

        const invitation = await prismaService.prisma.invitation.findUnique({
          where: { id },
          include: {
            tenant: { select: { id: true, name: true, slug: true } },
            role: { select: { id: true, name: true, type: true } },
          },
        });

        if (!invitation) {
          return res.status(404).json({
            error: 'Not Found',
            message: 'Invitation not found',
            statusCode: 404,
            timestamp: new Date().toISOString(),
          });
        }

        res.json({
          data: {
            id: invitation.id,
            email: invitation.email,
            status: invitation.status,
            expiresAt: invitation.expiresAt,
            createdAt: invitation.createdAt,
            tenant: invitation.tenant,
            role: invitation.role,
          },
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        logger.error('Failed to fetch invitation details', {
          error,
          invitationId: req.params.id,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/invitations
   * Get invitations for current tenant (Company Admin only)
   */
  router.get(
    '/',
    ...middlewareFactory.createAuthWithPermission(CommonPermissions.USER_READ),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const user = getRequestUser(req);
        const invitations = await invitationService.getInvitationsByTenant(
          user!.tenantId,
          user!
        );

        res.json({
          data: invitations,
          meta: {
            count: invitations.length,
            tenantId: user!.tenantId,
          },
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        logger.error('Failed to fetch invitations', {
          error,
          userId: getRequestUser(req)?.id,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * DELETE /api/v1/invitations/:id
   * Revoke an invitation (Company Admin only)
   */
  router.delete(
    '/:id',
    ...middlewareFactory.createAuthWithPermission(
      CommonPermissions.USER_MANAGE
    ),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { id } = req.params;
        const user = getRequestUser(req);

        await invitationService.revokeInvitation(id, user!);

        logger.info('Invitation revoked', {
          invitationId: id,
          revokedBy: user!.id,
          tenantId: user!.tenantId,
        });

        res.json({
          message: 'Invitation revoked successfully',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        logger.error('Failed to revoke invitation', {
          error,
          invitationId: req.params.id,
          userId: getRequestUser(req)?.id,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  return router;
}
