# Auto-generated test environment variables
# Created by global-setup.js at 2025-07-12T13:44:17.490Z
NODE_ENV=test
DATABASE_URL=postgresql://test_user:test_password@localhost:5433/tech_notes_test
TEST_DATABASE_URL=postgresql://test_user:test_password@localhost:5433/tech_notes_test
# Mock Clerk configuration for tests
CLERK_SECRET_KEY=sk_test_mock_key_for_tests
CLERK_PUBLISHABLE_KEY=pk_test_mock_key_for_tests
# Other required environment variables
LOG_LEVEL=error
CORS_ORIGIN=http://localhost:5173
FRONTEND_URL=http://localhost:5173
