/**
 * Model specific CRUD hook that extends shared patterns with cascading Brand → Sub-Brand selection
 * Handles the complexity of dual selection while reusing shared validation and error handling
 */

import React, { useState, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import type { VehicleModelV3, VehicleSubBrandV3 } from "../services/api-client";
import type { ModelConfig } from "../components/vehicle-hierarchy-v3/configs/entity-configs";
import { validateEntityName, sanitizeEntityName } from "./utils/entity-validation.utils";

export interface ModelCRUDReturn {
  // Data state
  models: VehicleModelV3[];
  subBrands: VehicleSubBrandV3[];
  sortedModels: VehicleModelV3[];
  isLoading: boolean;
  error: string | null;

  // Form state
  newModelName: string;
  selectedSubBrandId: string;
  editingModel: VehicleModelV3 | null;
  editModelName: string;
  editModelActive: boolean;
  editSelectedSubBrandId: string;
  deleteConfirmModel: VehicleModelV3 | null;

  // Operation state
  isPending: boolean;
  currentOperation: 'create' | 'update' | 'delete' | null;

  // State setters
  setNewModelName: (name: string) => void;
  setSelectedSubBrandId: (subBrandId: string) => void;
  setEditingModel: (model: VehicleModelV3 | null) => void;
  setEditModelName: (name: string) => void;
  setEditModelActive: (active: boolean) => void;
  setEditSelectedSubBrandId: (subBrandId: string) => void;
  setDeleteConfirmModel: (model: VehicleModelV3 | null) => void;

  // Action handlers
  handleCreate: () => void;
  handleEdit: (model: VehicleModelV3) => void;
  handleUpdate: () => void;
  handleDelete: (model: VehicleModelV3) => void;
  confirmDelete: () => void;
  handleClose: () => void;

  // Validation
  validateName: (name: string) => string | null;
}

/**
 * Model CRUD hook that extends the base patterns with cascading Brand → Sub-Brand selection
 */
export const useModelCRUD = (
  config: ModelConfig,
  isOpen: boolean
): ModelCRUDReturn => {
  const queryClient = useQueryClient();

  // ===== STATE MANAGEMENT =====
  const [newModelName, setNewModelName] = useState("");
  const [selectedSubBrandId, setSelectedSubBrandId] = useState("");
  const [editingModel, setEditingModel] = useState<VehicleModelV3 | null>(null);
  const [editModelName, setEditModelName] = useState("");
  const [editModelActive, setEditModelActive] = useState(true);
  const [editSelectedSubBrandId, setEditSelectedSubBrandId] = useState("");
  const [deleteConfirmModel, setDeleteConfirmModel] = useState<VehicleModelV3 | null>(null);
  const [currentOperation, setCurrentOperation] = useState<'create' | 'update' | 'delete' | null>(null);
  const [error, setError] = useState<string | null>(null);

  // ===== DATA FETCHING =====
  // Fetch all models with brand and sub-brand information
  const {
    data: modelsResponse,
    isLoading: isLoadingModels,
    error: modelsError,
  } = useQuery({
    queryKey: [config.queryKey],
    queryFn: config.apiMethods.getAllModels,
    enabled: isOpen,
  });

  // Fetch all sub-brands with brand information
  const {
    data: subBrandsResponse,
    isLoading: isLoadingSubBrands,
    error: subBrandsError,
  } = useQuery({
    queryKey: ["vehicle-sub-brands-with-brands-v3"],
    queryFn: config.apiMethods.getAllSubBrandsWithBrands,
    enabled: isOpen,
  });

  const models = useMemo(() => modelsResponse?.data || [], [modelsResponse?.data]);
  const subBrands = useMemo(() => subBrandsResponse?.data || [], [subBrandsResponse?.data]);

  const isLoading = isLoadingModels || isLoadingSubBrands;
  const queryError = modelsError || subBrandsError;

  // Handle query errors
  React.useEffect(() => {
    if (queryError) {
      const errorMessage = queryError instanceof Error ? queryError.message : 'Failed to fetch data';
      setError(errorMessage);
      handleError(queryError instanceof Error ? queryError : new Error(errorMessage), 'fetch');
    }
  }, [queryError]);

  // Sort models by brand name, then sub-brand name, then model name
  const sortedModels = useMemo(() => {
    return [...models].sort((a, b) => {
      const brandA = a.subBrand?.brand?.name || "";
      const brandB = b.subBrand?.brand?.name || "";
      if (brandA !== brandB) {
        return brandA.localeCompare(brandB);
      }

      const subBrandA = a.subBrand?.name || "";
      const subBrandB = b.subBrand?.name || "";
      if (subBrandA !== subBrandB) {
        return subBrandA.localeCompare(subBrandB);
      }

      return a.name.localeCompare(b.name);
    });
  }, [models]);

  // ===== MUTATIONS =====
  const createMutation = useMutation({
    mutationFn: (data: { subBrandId: string; name: string }) => {
      setCurrentOperation('create');
      // Find the selected sub-brand to get its brand ID
      const selectedSubBrand = subBrands.find(sb => sb.id === data.subBrandId);
      const brandId = selectedSubBrand?.brand?.id || "";
      return config.apiMethods.createModel(brandId, data.subBrandId, { name: sanitizeEntityName(data.name) });
    },
    onSuccess: () => {
      handleSuccess(`${config.entityName} created successfully`);
      queryClient.invalidateQueries({ queryKey: [config.queryKey] });
      setNewModelName("");
      setSelectedSubBrandId("");
      setCurrentOperation(null);
    },
    onError: (error: Error) => {
      handleError(error, 'create');
      setCurrentOperation(null);
    },
  });

  const updateMutation = useMutation({
    mutationFn: (data: { subBrandId: string; modelId: string; name: string; isActive: boolean; newSubBrandId?: string }) => {
      setCurrentOperation('update');
      // Find the selected sub-brand to get its brand ID
      const selectedSubBrand = subBrands.find(sb => sb.id === data.subBrandId);
      const brandId = selectedSubBrand?.brand?.id || "";
      return config.apiMethods.updateModel(brandId, data.subBrandId, data.modelId, {
        name: sanitizeEntityName(data.name),
        subBrandId: data.newSubBrandId, // Send the new sub-brand ID if it's being changed
        isActive: data.isActive,
      });
    },
    onSuccess: () => {
      handleSuccess(`${config.entityName} updated successfully`);
      queryClient.invalidateQueries({ queryKey: [config.queryKey] });
      setEditingModel(null);
      setEditModelName("");
      setEditModelActive(true);
      setEditSelectedSubBrandId("");
      setCurrentOperation(null);
    },
    onError: (error: Error) => {
      handleError(error, 'update');
      setCurrentOperation(null);
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (data: { subBrandId: string; modelId: string }) => {
      setCurrentOperation('delete');
      // Find the sub-brand to get its brand ID
      const subBrand = subBrands.find(sb => sb.id === data.subBrandId);
      const brandId = subBrand?.brand?.id || "";
      return config.apiMethods.deleteModel(brandId, data.subBrandId, data.modelId);
    },
    onSuccess: () => {
      handleSuccess(`${config.entityName} deleted successfully`);
      queryClient.invalidateQueries({ queryKey: [config.queryKey] });
      setDeleteConfirmModel(null);
      setCurrentOperation(null);
    },
    onError: (error: Error) => {
      handleError(error, 'delete');
      setCurrentOperation(null);
    },
  });

  // ===== COMPUTED STATE =====
  const isPending = createMutation.isPending || updateMutation.isPending || deleteMutation.isPending;

  // ===== UTILITY FUNCTIONS =====
  const validateName = (name: string): string | null => {
    return validateEntityName(name, config.validation);
  };

  const handleSuccess = (message: string) => {
    toast.success(message);
    setError(null);
  };

  const handleError = (error: Error, operation: string) => {
    const message = error.message || `Failed to ${operation} ${config.entityName.toLowerCase()}`;
    toast.error(message);
    setError(message);
  };

  // ===== ACTION HANDLERS =====
  const handleCreate = () => {
    const nameError = validateName(newModelName);
    if (nameError) {
      toast.error(nameError);
      return;
    }
    if (!selectedSubBrandId) {
      toast.error("Please select a sub-brand");
      return;
    }
    createMutation.mutate({
      subBrandId: selectedSubBrandId,
      name: newModelName.trim()
    });
  };

  const handleEdit = (model: VehicleModelV3) => {
    setEditingModel(model);
    setEditModelName(model.name);
    setEditModelActive(model.isActive);
    setEditSelectedSubBrandId(model.subBrandId);
  };

  const handleUpdate = () => {
    if (!editingModel) return;

    const nameError = validateName(editModelName);
    if (nameError) {
      toast.error(nameError);
      return;
    }
    if (!editSelectedSubBrandId) {
      toast.error("Please select a sub-brand");
      return;
    }

    updateMutation.mutate({
      subBrandId: editingModel.subBrandId, // Original sub-brand ID for the API path
      modelId: editingModel.id,
      name: editModelName.trim(),
      isActive: editModelActive,
      newSubBrandId: editSelectedSubBrandId !== editingModel.subBrandId ? editSelectedSubBrandId : undefined, // Only send if changed
    });
  };

  const handleDelete = (model: VehicleModelV3) => {
    setDeleteConfirmModel(model);
  };

  const confirmDelete = () => {
    if (!deleteConfirmModel) return;
    deleteMutation.mutate({
      subBrandId: deleteConfirmModel.subBrandId,
      modelId: deleteConfirmModel.id,
    });
  };

  const handleClose = () => {
    if (isPending) return;
    setNewModelName("");
    setSelectedSubBrandId("");
    setEditingModel(null);
    setEditModelName("");
    setEditModelActive(true);
    setEditSelectedSubBrandId("");
    setDeleteConfirmModel(null);
    setError(null);
    setCurrentOperation(null);
  };

  // ===== RETURN INTERFACE =====
  return {
    // Data state
    models,
    subBrands,
    sortedModels,
    isLoading,
    error,

    // Form state
    newModelName,
    selectedSubBrandId,
    editingModel,
    editModelName,
    editModelActive,
    editSelectedSubBrandId,
    deleteConfirmModel,

    // Operation state
    isPending,
    currentOperation,

    // State setters
    setNewModelName,
    setSelectedSubBrandId,
    setEditingModel,
    setEditModelName,
    setEditModelActive,
    setEditSelectedSubBrandId,
    setDeleteConfirmModel,

    // Action handlers
    handleCreate,
    handleEdit,
    handleUpdate,
    handleDelete,
    confirmDelete,
    handleClose,

    // Validation
    validateName,
  };
};
