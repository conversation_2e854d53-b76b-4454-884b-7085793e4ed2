import { useUser, useAuth as useClerkA<PERSON>, useClerk } from "@clerk/clerk-react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  useAuthenticatedApi,
  type IAuthService,
} from "../services/auth-api.service";

export interface UseAuthOptions {
  authService?: IAuthService;
}

export interface UseAuthReturn {
  // Authentication state
  isLoading: boolean | undefined;
  isAuthenticated: boolean;
  isOnboarded: boolean;
  needsOnboarding: boolean;
  isServiceUnavailable: boolean;

  // User data
  clerkUser: any; // eslint-disable-line @typescript-eslint/no-explicit-any -- Using any to handle version conflicts between @clerk/types versions
  userProfile: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  authStatus: any; // eslint-disable-line @typescript-eslint/no-explicit-any

  // Actions
  signOut: () => Promise<void>;
  signIn: () => void;
  refreshAuth: () => void;
  getToken: () => Promise<string | null>;

  // API service
  api: IAuthService;

  // Errors
  statusError: Error | null;
  profileError: Error | null;
}

/**
 * Consolidated auth hook that wraps Clerk and provides app-specific auth logic
 * Single source of truth for all authentication state
 * Supports dependency injection for testing
 */
export function useAuth(options: UseAuthOptions = {}): UseAuthReturn {
  const { isSignedIn, user: clerkUser, isLoaded } = useUser();
  const { signOut, getToken } = useClerkAuth();
  const { redirectToSignIn } = useClerk();
  const queryClient = useQueryClient();

  // Always call the hook (React Hooks rules), then use injected service if provided
  const defaultAuthApi = useAuthenticatedApi();
  const authApi = options.authService ?? defaultAuthApi;

  // Query auth status from our backend
  const {
    data: authStatus,
    isLoading: isCheckingStatus,
    error: statusError,
    refetch: refetchStatus,
  } = useQuery({
    queryKey: ["auth-status"],
    queryFn: () => authApi.checkAuthStatus(),
    enabled: isSignedIn && isLoaded,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    throwOnError: false, // Ensure errors are captured in the error state
  });

  // Calculate derived state first
  // Service is unavailable if:
  // 1. Auth status explicitly says so, OR
  // 2. Auth status check failed with 503 error
  const isServiceUnavailable =
    authStatus?.serviceUnavailable ||
    (statusError &&
      (("statusCode" in statusError &&
        (statusError as Error & { statusCode: number }).statusCode === 503) ||
        (statusError.message &&
          (statusError.message.includes("503") ||
            statusError.message.includes("Service Unavailable"))) ||
        (statusError as Error & { cause?: { statusCode: number } }).cause
          ?.statusCode === 503 ||
        (statusError.name === "Error" &&
          statusError.message === "Service Unavailable"))) ||
    false;

  // Critical: User is only authenticated if:
  // 1. They have a valid Clerk token (isSignedIn)
  // 2. Backend confirms authentication (authStatus?.authenticated)
  // 3. Service is available (database connectivity)
  const isAuthenticated = Boolean(
    isSignedIn && authStatus?.authenticated && !isServiceUnavailable,
  );
  const isOnboarded = Boolean(authStatus?.onboarded && !isServiceUnavailable);
  const needsOnboarding = isAuthenticated && !isOnboarded;

  // Don't load profile if service is unavailable or not authenticated
  // Only load profile when: signed in, loaded, auth status resolved, authenticated, onboarded, and service available
  const shouldLoadProfile =
    isSignedIn &&
    isLoaded &&
    !isCheckingStatus &&
    authStatus?.authenticated === true &&
    authStatus?.onboarded === true &&
    !isServiceUnavailable;

  // Query current user profile from our backend
  const {
    data: userProfile,
    isLoading: isLoadingProfile,
    error: profileError,
    refetch: refetchProfile,
  } = useQuery({
    queryKey: ["user-profile"],
    queryFn: async () => {
      const result = await authApi.getCurrentUser();
      return result || null; // Ensure we always return a value
    },
    enabled: shouldLoadProfile,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    throwOnError: false, // Ensure errors are captured in the error state
  });

  const isLoading =
    !isLoaded || isCheckingStatus || (shouldLoadProfile && isLoadingProfile);

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error("Sign out error:", error);
    }
  };

  const handleSignIn = () => {
    redirectToSignIn();
  };

  const refreshAuth = () => {
    // Invalidate all auth-related queries to force fresh data
    queryClient.invalidateQueries({ queryKey: ["auth-status"] });
    queryClient.invalidateQueries({ queryKey: ["user-profile"] });
    // Also refetch directly
    refetchStatus();
    refetchProfile();
  };

  return {
    // Authentication state
    isLoading,
    isAuthenticated,
    isOnboarded,
    needsOnboarding,
    isServiceUnavailable,

    // User data
    clerkUser,
    userProfile,
    authStatus,

    // Actions
    signOut: handleSignOut,
    signIn: handleSignIn,
    refreshAuth,
    getToken,

    // API service
    api: authApi,

    // Errors
    statusError,
    profileError,
  };
}
