import React from "react";
import { clsx } from "clsx";
import { X } from "lucide-react";

export interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?:
    | "default"
    | "primary"
    | "secondary"
    | "outline"
    | "success"
    | "warning"
    | "error"
    | "admin"
    | "user"
    | "tech";
  size?: "sm" | "md";
  removable?: boolean;
  onRemove?: () => void;
  icon?: React.ReactNode;
  children: React.ReactNode;
}

const variantClasses = {
  default: "bg-gray-100 text-gray-800 border border-gray-200 shadow-xs",
  primary:
    "bg-gradient-to-r from-primary-50 to-primary-100 text-primary-800 border border-primary-200 shadow-xs",
  secondary: "bg-gray-50 text-gray-700 border border-gray-200 shadow-xs",
  outline:
    "border-2 border-gray-300 text-gray-700 bg-white shadow-xs hover:border-gray-400 transition-colors duration-200",
  success:
    "bg-gradient-to-r from-success-50 to-success-100 text-success-800 border border-success-200 shadow-xs",
  warning:
    "bg-gradient-to-r from-warning-50 to-warning-100 text-warning-800 border border-warning-200 shadow-xs",
  error:
    "bg-gradient-to-r from-error-50 to-error-100 text-error-800 border border-error-200 shadow-xs",
  // Semantic variants for role types with enhanced styling
  admin:
    "bg-gradient-to-r from-error-50 to-error-100 text-error-800 border border-error-200 shadow-xs ring-1 ring-error-200", // System Admin - red for high privilege
  user: "bg-gradient-to-r from-primary-50 to-primary-100 text-primary-800 border border-primary-200 shadow-xs ring-1 ring-primary-200", // Company Admin - blue for management
  tech: "bg-gradient-to-r from-purple-50 to-purple-100 text-purple-800 border border-purple-200 shadow-xs ring-1 ring-purple-200", // Company Tech - purple for technical
};

const sizeClasses = {
  sm: "px-2.5 py-1 text-xs font-semibold tracking-wide",
  md: "px-3 py-1.5 text-sm font-semibold tracking-wide",
};

export const Badge: React.FC<BadgeProps> = ({
  variant = "default",
  size = "md",
  removable = false,
  onRemove,
  icon,
  className,
  children,
  ...props
}) => {
  return (
    <span
      className={clsx(
        "inline-flex items-center rounded-full transition-all duration-200",
        variantClasses[variant],
        sizeClasses[size],
        className,
      )}
      {...props}
    >
      {icon && <span className="mr-1.5 -ml-0.5">{icon}</span>}
      {children}
      {removable && onRemove && (
        <button
          onClick={onRemove}
          className="ml-1.5 -mr-0.5 h-4 w-4 text-current hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-current focus:ring-opacity-20 rounded-full transition-all duration-200"
          aria-label="Remove"
        >
          <X className="h-full w-full" />
        </button>
      )}
    </span>
  );
};
