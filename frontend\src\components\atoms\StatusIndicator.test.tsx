import { describe, it, expect } from "vitest";
import { render, screen } from "../../__tests__/helpers/test-utils";
import StatusIndicator from "./StatusIndicator";
import { createMockProps } from "../../__tests__/helpers/mock-props.helper";

describe("StatusIndicator", () => {
  describe("Rendering", () => {
    it("should render with ok status", () => {
      const props = createMockProps.statusIndicator({ status: "ok" });
      render(<StatusIndicator {...props} />);

      const indicator = screen.getByRole("status");
      expect(indicator).toBeInTheDocument();
      expect(indicator).toHaveAttribute("aria-label", "Status: ok");
      expect(indicator).toHaveClass("bg-green-500");
    });

    it("should render with error status", () => {
      const props = createMockProps.statusIndicator({ status: "error" });
      render(<StatusIndicator {...props} />);

      const indicator = screen.getByRole("status");
      expect(indicator).toHaveAttribute("aria-label", "Status: error");
      expect(indicator).toHaveClass("bg-red-500");
    });

    it("should render with loading status", () => {
      const props = createMockProps.statusIndicator({ status: "loading" });
      render(<StatusIndicator {...props} />);

      const indicator = screen.getByRole("status");
      expect(indicator).toHaveAttribute("aria-label", "Status: loading");
      expect(indicator).toHaveClass("bg-yellow-500", "animate-pulse");
    });
  });

  describe("Size Variants", () => {
    it("should render small size correctly", () => {
      const props = createMockProps.statusIndicator({ size: "sm" });
      render(<StatusIndicator {...props} />);

      const indicator = screen.getByRole("status");
      expect(indicator).toHaveClass("h-2", "w-2");
    });

    it("should render medium size correctly (default)", () => {
      const props = createMockProps.statusIndicator({ size: "md" });
      render(<StatusIndicator {...props} />);

      const indicator = screen.getByRole("status");
      expect(indicator).toHaveClass("h-3", "w-3");
    });

    it("should render large size correctly", () => {
      const props = createMockProps.statusIndicator({ size: "lg" });
      render(<StatusIndicator {...props} />);

      const indicator = screen.getByRole("status");
      expect(indicator).toHaveClass("h-4", "w-4");
    });

    it("should default to medium size when size prop is not provided", () => {
      render(<StatusIndicator status="ok" />);

      const indicator = screen.getByRole("status");
      expect(indicator).toHaveClass("h-3", "w-3");
    });
  });

  describe("Custom Styling", () => {
    it("should apply custom className", () => {
      const props = createMockProps.statusIndicator({
        className: "custom-class border-2",
      });
      render(<StatusIndicator {...props} />);

      const indicator = screen.getByRole("status");
      expect(indicator).toHaveClass("custom-class", "border-2");
    });

    it("should maintain base classes with custom className", () => {
      const props = createMockProps.statusIndicator({
        status: "ok",
        size: "lg",
        className: "custom-class",
      });
      render(<StatusIndicator {...props} />);

      const indicator = screen.getByRole("status");
      expect(indicator).toHaveClass(
        "rounded-full",
        "h-4",
        "w-4",
        "bg-green-500",
        "custom-class",
      );
    });
  });

  describe("Accessibility", () => {
    it("should have proper ARIA attributes", () => {
      const props = createMockProps.statusIndicator({ status: "error" });
      render(<StatusIndicator {...props} />);

      const indicator = screen.getByRole("status");
      expect(indicator).toHaveAttribute("role", "status");
      expect(indicator).toHaveAttribute("aria-label", "Status: error");
    });

    it("should be accessible by screen readers for all status types", () => {
      const statuses = ["ok", "error", "loading"] as const;

      statuses.forEach((status) => {
        const { unmount } = render(<StatusIndicator status={status} />);

        const indicator = screen.getByRole("status");
        expect(indicator).toHaveAttribute("aria-label", `Status: ${status}`);

        unmount();
      });
    });
  });
});
