import { Router, NextFunction, Request, Response } from 'express';
import { Logger } from 'winston';

import { MiddlewareFactory } from '../../../middleware/middleware-factory.js';
import { PermissionService } from '../../../services/permission.service.js';
import { RoleService } from '../../../services/role.service.js';
import { CreateRoleData, AssignRoleData } from '../../../types/rbac.types.js';
import { getRequestUser } from '../../../utils/request-types.js';

interface ServiceDependencies {
  roleService: RoleService;
  permissionService: PermissionService;
  middlewareFactory: MiddlewareFactory;
  logger: Logger;
}

export function createRolesRouter(dependencies: ServiceDependencies): Router {
  const { roleService, permissionService, middlewareFactory, logger } =
    dependencies;
  const router = Router();

  /**
   * GET /api/v1/roles
   * Get all roles (System Admin only)
   */
  router.get(
    '/',
    ...middlewareFactory.createAuthWithSystemAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const roles = await roleService.getAllRoles(getRequestUser(req)!);

        res.json({
          data: roles,
          meta: {
            count: roles.length,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch all roles', {
          error,
          userId: getRequestUser(req)?.id,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/roles/tenant/:tenantId
   * Get roles available for assignment within a tenant
   */
  router.get(
    '/tenant/:tenantId',
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = req.params;

        if (!tenantId) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'Tenant ID is required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        const roles = await roleService.getRolesForTenant(
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: roles,
          meta: {
            count: roles.length,
            tenantId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch tenant roles', {
          error,
          tenantId: req.params.tenantId,
          userId: getRequestUser(req)?.id,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/roles
   * Create a new role (System Admin only)
   */
  router.post(
    '/',
    ...middlewareFactory.createAuthWithSystemAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const roleData: CreateRoleData = req.body;

        // Basic validation
        if (!roleData.name || !roleData.type || !roleData.description) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'Name, type, and description are required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        const role = await roleService.createRole(
          roleData,
          getRequestUser(req)!
        );

        res.status(201).json({
          data: role,
          message: 'Role created successfully',
        });
      } catch (error) {
        logger.error('Failed to create role', {
          error,
          roleData: req.body,
          userId: getRequestUser(req)?.id,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/roles/assign
   * Assign a role to a user
   */
  router.post(
    '/assign',
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const assignmentData: AssignRoleData = {
          ...req.body,
          assignedBy: getRequestUser(req)!.id,
        };

        // Basic validation
        if (!assignmentData.userId || !assignmentData.roleId) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'User ID and Role ID are required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        const userRole = await roleService.assignRole(
          assignmentData,
          getRequestUser(req)!
        );

        res.status(201).json({
          data: userRole,
          message: 'Role assigned successfully',
        });
      } catch (error) {
        logger.error('Failed to assign role', {
          error,
          assignmentData: req.body,
          userId: getRequestUser(req)?.id,
        });
        next(error);
      }
    }
  );

  /**
   * DELETE /api/v1/roles/assign
   * Remove a role from a user
   */
  router.delete(
    '/assign',
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { userId, roleId, tenantId } = req.body;

        // Basic validation
        if (!userId || !roleId) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'User ID and Role ID are required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        await roleService.removeRole(
          userId,
          roleId,
          tenantId || null,
          getRequestUser(req)!
        );

        res.json({
          message: 'Role removed successfully',
        });
      } catch (error) {
        logger.error('Failed to remove role', {
          error,
          removalData: req.body,
          userId: getRequestUser(req)?.id,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/roles/user/:userId
   * Get all roles for a specific user
   */
  router.get(
    '/user/:userId',
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { userId } = req.params;

        if (!userId) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'User ID is required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        const userRoles = await roleService.getUserRoles(
          userId,
          getRequestUser(req)!
        );

        res.json({
          data: userRoles,
          meta: {
            count: userRoles.length,
            userId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch user roles', {
          error,
          targetUserId: req.params.userId,
          userId: getRequestUser(req)?.id,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/roles/:roleId/permissions
   * Get permissions for a specific role
   */
  router.get(
    '/:roleId/permissions',
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { roleId } = req.params;

        if (!roleId) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'Role ID is required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        const permissions = await permissionService.getRolePermissions(
          roleId,
          getRequestUser(req)!
        );

        res.json({
          data: permissions,
          meta: {
            count: permissions.length,
            roleId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch role permissions', {
          error,
          roleId: req.params.roleId,
          userId: getRequestUser(req)?.id,
        });
        next(error);
      }
    }
  );

  return router;
}
