import { Tenant } from '@prisma/client';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { BackendAuthContext } from '@tech-notes/shared';

import {
  NotFoundError,
  ConflictError,
  ValidationError,
} from '../types/error.types.js';
import { Logger } from '../utils/logger.js';

import { BaseTenantService } from './base-tenant.service.js';
import { PrismaService } from './prisma.service.js';

export interface CreateTenantData {
  name: string;
  slug: string;
  isActive?: boolean;
}

export interface UpdateTenantData {
  name?: string;
  slug?: string;
  isActive?: boolean;
}

/**
 * Service for managing tenant operations
 * Extends BaseTenantService for consistent patterns
 */
export class TenantService extends BaseTenantService {
  constructor(prismaService: PrismaService, logger: Logger) {
    super(prismaService, logger);
  }

  /**
   * Get all tenants (admin operation - no tenant scoping)
   */
  async getAllTenants(): Promise<Tenant[]> {
    this.logOperation('getAllTenants');

    try {
      return await this.prisma.tenant.findMany({
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      this.logError('getAllTenants', error as Error);
      throw error;
    }
  }

  /**
   * Get tenant by ID with auth context
   */
  async getTenantById(
    tenantId: string,
    BackendAuthContext: BackendAuthContext
  ): Promise<Tenant | null> {
    this.logOperation('getTenantById', {
      tenantId,
      userId: BackendAuthContext.id,
    });

    try {
      return await this.prisma.tenant.findUnique({
        where: { id: tenantId },
      });
    } catch (error) {
      this.logError('getTenantById', error as Error, { tenantId });
      throw error;
    }
  }

  /**
   * Get tenant by slug
   */
  async getTenantBySlug(slug: string): Promise<Tenant | null> {
    this.logOperation('getTenantBySlug', { slug });

    try {
      return await this.prisma.tenant.findUnique({
        where: { slug },
      });
    } catch (error) {
      this.logError('getTenantBySlug', error as Error, { slug });
      throw error;
    }
  }

  /**
   * Create a new tenant (System Admin only)
   */
  async createTenant(
    tenantData: CreateTenantData,
    BackendAuthContext: BackendAuthContext
  ): Promise<Tenant> {
    this.logOperation('createTenant', {
      name: tenantData.name,
      slug: tenantData.slug,
      createdBy: BackendAuthContext.id,
    });

    // Validate slug format
    if (!/^[a-z0-9-]+$/.test(tenantData.slug)) {
      throw new ValidationError(
        'Tenant slug must contain only lowercase letters, numbers, and hyphens'
      );
    }

    try {
      return await this.prisma.tenant.create({
        data: tenantData,
      });
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictError('Tenant with this slug already exists');
        }
      }
      this.logError(
        'createTenant',
        error as Error,
        tenantData as unknown as Record<string, unknown>
      );
      throw error;
    }
  }

  /**
   * Update tenant data
   */
  async updateTenant(
    tenantId: string,
    tenantData: UpdateTenantData,
    BackendAuthContext: BackendAuthContext
  ): Promise<Tenant> {
    this.logOperation('updateTenant', {
      tenantId,
      updatedBy: BackendAuthContext.id,
    });

    // Validate slug format if provided
    if (tenantData.slug && !/^[a-z0-9-]+$/.test(tenantData.slug)) {
      throw new ValidationError(
        'Tenant slug must contain only lowercase letters, numbers, and hyphens'
      );
    }

    try {
      const tenant = await this.prisma.tenant.findUnique({
        where: { id: tenantId },
      });

      if (!tenant) {
        throw new NotFoundError('Tenant not found');
      }

      return await this.prisma.tenant.update({
        where: { id: tenantId },
        data: tenantData,
      });
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictError('Tenant with this slug already exists');
        }
      }
      this.logError('updateTenant', error as Error, { tenantId });
      throw error;
    }
  }

  /**
   * Delete tenant (System Admin only)
   * Note: This will cascade delete all users in the tenant
   */
  async deleteTenant(
    tenantId: string,
    BackendAuthContext: BackendAuthContext
  ): Promise<void> {
    this.logOperation('deleteTenant', {
      tenantId,
      deletedBy: BackendAuthContext.id,
    });

    try {
      const tenant = await this.prisma.tenant.findUnique({
        where: { id: tenantId },
        include: { _count: { select: { users: true } } },
      });

      if (!tenant) {
        throw new NotFoundError('Tenant not found');
      }

      // Log warning if tenant has users
      if (tenant._count.users > 0) {
        this.logger.warn('Deleting tenant with users', {
          tenantId,
          userCount: tenant._count.users,
          deletedBy: BackendAuthContext.id,
        });
      }

      await this.prisma.tenant.delete({
        where: { id: tenantId },
      });

      this.logger.info('Tenant deleted successfully', {
        tenantId,
        deletedBy: BackendAuthContext.id,
      });
    } catch (error) {
      this.logError('deleteTenant', error as Error, { tenantId });
      throw error;
    }
  }

  /**
   * Get tenant with user count
   */
  async getTenantWithStats(
    tenantId: string
  ): Promise<(Tenant & { userCount: number }) | null> {
    this.logOperation('getTenantWithStats', { tenantId });

    try {
      const tenant = await this.prisma.tenant.findUnique({
        where: { id: tenantId },
        include: { _count: { select: { users: true } } },
      });

      if (!tenant) {
        return null;
      }

      return {
        ...tenant,
        userCount: tenant._count.users,
      };
    } catch (error) {
      this.logError('getTenantWithStats', error as Error, { tenantId });
      throw error;
    }
  }

  /**
   * Check if tenant slug is available
   */
  async isSlugAvailable(
    slug: string,
    excludeTenantId?: string
  ): Promise<boolean> {
    this.logOperation('isSlugAvailable', { slug, excludeTenantId });

    try {
      const existingTenant = await this.prisma.tenant.findUnique({
        where: { slug },
        select: { id: true },
      });

      if (!existingTenant) {
        return true;
      }

      // If excluding a specific tenant (for updates), check if it's the same tenant
      return excludeTenantId ? existingTenant.id === excludeTenantId : false;
    } catch (error) {
      this.logError('isSlugAvailable', error as Error, {
        slug,
        excludeTenantId,
      });
      throw error;
    }
  }
}
