/**
 * Design System Tokens - Central Export
 *
 * Provides unified access to all design tokens for consistent
 * theming across the application
 */

import {
  colors,
  getColor,
  primary,
  gray,
  semantic,
  gradients,
  surface,
  border,
  text,
} from "./colors";
import type { ColorScale, SemanticColor, ColorToken } from "./colors";

import {
  typography,
  getTypography,
  fontFamily,
  fontSize,
  fontWeight,
  lineHeight,
  letterSpacing,
  textStyles,
} from "./typography";
import type { FontSize, FontWeight, TextStyle } from "./typography";

import {
  spacing,
  getSpacing,
  semantic as spacingSemantic,
  responsive,
  touch,
  spacingUtils,
} from "./spacing";
import type {
  SpacingToken,
  SemanticSpacing,
  ResponsiveSpacing,
} from "./spacing";

import {
  shadows,
  getShadow,
  semantic as shadowSemantic,
  colored,
  glow,
  focus,
  shadowUtils,
} from "./shadows";
import type { ShadowToken, SemanticShadow, ColoredShadow } from "./shadows";

// Re-export everything
export {
  colors,
  getColor,
  primary,
  gray,
  semantic,
  gradients,
  surface,
  border,
  text,
};
export type { ColorScale, SemanticColor, ColorToken };

export {
  typography,
  getTypography,
  fontFamily,
  fontSize,
  fontWeight,
  lineHeight,
  letterSpacing,
  textStyles,
};
export type { FontSize, FontWeight, TextStyle };

export {
  spacing,
  getSpacing,
  spacingSemantic,
  responsive,
  touch,
  spacingUtils,
};
export type { SpacingToken, SemanticSpacing, ResponsiveSpacing };

export {
  shadows,
  getShadow,
  shadowSemantic,
  colored,
  glow,
  focus,
  shadowUtils,
};
export type { ShadowToken, SemanticShadow, ColoredShadow };

// Combined design system object for easy access
export const designSystem = {
  colors,
  typography,
  spacing,
  shadows,
} as const;

// Utility functions for common design system operations
export const designSystemUtils = {
  // Get theme values by path
  getToken: (
    category: "colors" | "typography" | "spacing" | "shadows",
    path: string,
  ): unknown => {
    switch (category) {
      case "colors":
        return getColor(path);
      case "typography":
        return getTypography(path);
      case "spacing":
        return getSpacing(path);
      case "shadows":
        return getShadow(path);
      default:
        throw new Error(`Unknown design system category: ${category}`);
    }
  },

  // Validate token existence
  hasToken: (
    category: "colors" | "typography" | "spacing" | "shadows",
    path: string,
  ): boolean => {
    try {
      designSystemUtils.getToken(category, path);
      return true;
    } catch {
      return false;
    }
  },

  // Get responsive values
  getResponsiveSpacing: (
    size: "mobile" | "tablet" | "desktop" | "large" = "desktop",
  ) => ({
    section: spacing.responsive[size].section,
    container: spacing.responsive[size].container,
    component: spacing.responsive[size].component,
  }),
};

// Type definitions for the complete design system
export type DesignSystemCategory = keyof typeof designSystem;
export type DesignSystemToken =
  | ColorToken
  | FontSize
  | FontWeight
  | SpacingToken
  | ShadowToken;
