/**
 * Shared validation utilities for entity CRUD operations
 * Provides consistent validation logic across all entity types
 */

import type { ValidationConfig, BaseEntity } from "../types/entity-crud.types";

/**
 * Validates entity name according to configuration rules
 */
export const validateEntityName = (
  name: string,
  config: ValidationConfig
): string | null => {
  const trimmedName = name.trim();
  
  if (!trimmedName) {
    return config.requiredMessage;
  }
  
  if (trimmedName.length > config.maxLength) {
    return config.maxLengthMessage;
  }
  
  return null;
};

/**
 * Validates if entity name is unique within a list
 */
export const validateEntityNameUniqueness = <T extends BaseEntity>(
  name: string,
  entities: T[],
  excludeId?: string
): string | null => {
  const trimmedName = name.trim().toLowerCase();
  
  const duplicate = entities.find(
    entity => 
      entity.name.toLowerCase() === trimmedName && 
      entity.id !== excludeId
  );
  
  if (duplicate) {
    return `An entity with the name "${name}" already exists`;
  }
  
  return null;
};

/**
 * Validates entity update data to determine if changes were made
 */
export const validateEntityChanges = <T extends BaseEntity>(
  originalEntity: T,
  newName: string,
  newActive: boolean
): { hasChanges: boolean; changes: Partial<T> } => {
  const changes: Partial<T> = {};
  let hasChanges = false;
  
  const trimmedName = newName.trim();
  if (trimmedName !== originalEntity.name) {
    changes.name = trimmedName as T['name'];
    hasChanges = true;
  }
  
  if (newActive !== originalEntity.isActive) {
    changes.isActive = newActive as T['isActive'];
    hasChanges = true;
  }
  
  return { hasChanges, changes };
};

/**
 * Sanitizes entity name input
 */
export const sanitizeEntityName = (name: string): string => {
  return name.trim().replace(/\s+/g, ' '); // Replace multiple spaces with single space
};

/**
 * Formats entity name for display
 */
export const formatEntityNameForDisplay = (name: string): string => {
  return sanitizeEntityName(name);
};

/**
 * Common validation configuration presets
 */
export const ValidationPresets = {
  STANDARD: {
    maxLength: 500,
    requiredMessage: "Please enter a name",
    maxLengthMessage: "Name must be 500 characters or less",
  },
  
  SHORT: {
    maxLength: 100,
    requiredMessage: "Please enter a name",
    maxLengthMessage: "Name must be 100 characters or less",
  },
  
  LONG: {
    maxLength: 1000,
    requiredMessage: "Please enter a name",
    maxLengthMessage: "Name must be 1000 characters or less",
  },
} as const;

/**
 * Creates entity-specific validation configuration
 */
export const createValidationConfig = (
  entityName: string,
  maxLength: number = 500
): ValidationConfig => ({
  maxLength,
  requiredMessage: `Please enter a ${entityName.toLowerCase()} name`,
  maxLengthMessage: `${entityName} name must be ${maxLength} characters or less`,
});

/**
 * Validation result type for comprehensive validation
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Comprehensive entity validation
 */
export const validateEntity = <T extends BaseEntity>(
  name: string,
  config: ValidationConfig,
  entities: T[],
  excludeId?: string
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Name validation
  const nameError = validateEntityName(name, config);
  if (nameError) {
    errors.push(nameError);
  }
  
  // Uniqueness validation (only if name is valid)
  if (!nameError) {
    const uniquenessError = validateEntityNameUniqueness(name, entities, excludeId);
    if (uniquenessError) {
      errors.push(uniquenessError);
    }
  }
  
  // Add warnings for potential issues
  const trimmedName = name.trim();
  if (trimmedName.length > config.maxLength * 0.8) {
    warnings.push(`Name is getting long (${trimmedName.length}/${config.maxLength} characters)`);
  }
  
  if (/^\d+$/.test(trimmedName)) {
    warnings.push("Name contains only numbers - consider adding descriptive text");
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};
