import React from "react";
import StatusIndicator from "../atoms/StatusIndicator";
import { <PERSON>, CardHeader, CardContent } from "../atoms/Card";
import { Button } from "../atoms/Button";
import { Badge } from "../atoms/Badge";
import { formatDateTime } from "@tech-notes/shared";
import type { HealthCardProps } from "../../types/api.types";

const HealthCard: React.FC<HealthCardProps> = ({
  status,
  environment,
  database,
  timestamp,
  onRefresh,
}) => {
  const formatTimestamp = (ts?: string) => {
    if (!ts) return "Unknown";
    return formatDateTime(ts);
  };

  const getStatusBadge = () => {
    switch (status) {
      case "ok":
        return <Badge variant="success">Healthy</Badge>;
      case "error":
        return <Badge variant="error">Error</Badge>;
      default:
        return <Badge variant="warning">Loading...</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader title="System Health">
        <div className="flex items-center space-x-2 mt-2">
          <StatusIndicator status={status} size="lg" />
          {getStatusBadge()}
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-3">
          {environment && (
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Environment:</span>
              <span className="text-sm font-medium text-gray-900">
                {environment}
              </span>
            </div>
          )}

          {database && (
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Database:</span>
              <Badge variant={database === "connected" ? "success" : "error"}>
                {database}
              </Badge>
            </div>
          )}

          <div className="flex justify-between">
            <span className="text-sm text-gray-600">Last Updated:</span>
            <span className="text-sm font-medium text-gray-900">
              {formatTimestamp(timestamp)}
            </span>
          </div>
        </div>

        {onRefresh && (
          <div className="mt-6">
            <Button onClick={onRefresh} className="w-full">
              Refresh
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default HealthCard;
