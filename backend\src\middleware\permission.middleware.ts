import { PermissionResource, PermissionAction, RoleType } from '@prisma/client';
import { Response, NextFunction, Request } from 'express';

import { PermissionService } from '../services/permission.service.js';
import { ForbiddenError } from '../types/error.types.js';
import { Logger } from '../utils/logger.js';
import { getRequestUser } from '../utils/request-types.js';

export interface PermissionMiddlewareDependencies {
  permissionService: PermissionService;
  logger: Logger;
}

export interface PermissionRequirement {
  resource: PermissionResource;
  action: PermissionAction;
  context?: {
    tenantId?: string;
  };
}

/**
 * Factory function to create permission checking middleware
 */
export function createPermissionMiddleware(
  dependencies: PermissionMiddlewareDependencies
) {
  const { permissionService, logger } = dependencies;

  return function requirePermission(
    requirement: PermissionRequirement | PermissionRequirement[]
  ) {
    return async (
      req: Request,
      res: Response,
      next: NextFunction
    ): Promise<void> => {
      try {
        if (!getRequestUser(req)) {
          logger.warn('Permission check attempted without authentication', {
            path: req.path,
            method: req.method,
          });
          res.status(401).json({
            error: 'Unauthorized',
            message: 'Authentication required',
            statusCode: 401,
            timestamp: new Date().toISOString(),
          });
          return;
        }

        const requirements = Array.isArray(requirement)
          ? requirement
          : [requirement];
        const BackendAuthContext = getRequestUser(req);

        // System Admins automatically pass all permission checks
        if (BackendAuthContext.canBypassTenantScope) {
          logger.debug('Permission granted for System Admin', {
            userId: BackendAuthContext.id,
            requirements: requirements.map((r) => `${r.resource}:${r.action}`),
          });
          return next();
        }

        // Check each permission requirement
        for (const permReq of requirements) {
          const hasPermission = await permissionService.hasPermission(
            BackendAuthContext.id,
            {
              resource: permReq.resource,
              action: permReq.action,
              context: permReq.context || {
                tenantId: BackendAuthContext.tenantId,
              },
            },
            BackendAuthContext
          );

          if (!hasPermission) {
            logger.warn('Permission denied', {
              userId: BackendAuthContext.id,
              tenantId: BackendAuthContext.tenantId,
              permission: `${permReq.resource}:${permReq.action}`,
              context: permReq.context,
              path: req.path,
              method: req.method,
            });

            res.status(403).json({
              error: 'Forbidden',
              message: `Insufficient permissions: ${permReq.resource}:${permReq.action}`,
              statusCode: 403,
              timestamp: new Date().toISOString(),
            });
            return;
          }
        }

        logger.debug('All permissions granted', {
          userId: BackendAuthContext.id,
          permissions: requirements.map(
            (permReq) => `${permReq.resource}:${permReq.action}`
          ),
        });

        next();
      } catch (error) {
        logger.error('Permission check failed', {
          error: error instanceof Error ? error.message : 'Unknown error',
          path: req.path,
          method: req.method,
          userId: getRequestUser(req)?.id,
        });

        if (error instanceof ForbiddenError) {
          res.status(403).json({
            error: 'Forbidden',
            message: error.message,
            statusCode: 403,
            timestamp: new Date().toISOString(),
          });
          return;
        }

        res.status(500).json({
          error: 'Internal Server Error',
          message: 'Permission check service error',
          statusCode: 500,
          timestamp: new Date().toISOString(),
        });
      }
    };
  };
}

/**
 * Factory function to create role checking middleware
 */
export function createRoleMiddleware(
  dependencies: PermissionMiddlewareDependencies
) {
  const { logger } = dependencies;

  return function requireRole(roleTypes: RoleType | RoleType[]) {
    return async (
      req: Request,
      res: Response,
      next: NextFunction
    ): Promise<void> => {
      try {
        if (!getRequestUser(req)) {
          logger.warn('Role check attempted without authentication', {
            path: req.path,
            method: req.method,
          });
          res.status(401).json({
            error: 'Unauthorized',
            message: 'Authentication required',
            statusCode: 401,
            timestamp: new Date().toISOString(),
          });
          return;
        }

        const requiredRoles = Array.isArray(roleTypes)
          ? roleTypes
          : [roleTypes];
        const BackendAuthContext = getRequestUser(req);

        // Check if user has any of the required roles
        const userRoleTypes =
          BackendAuthContext.roles?.map((r) => r.role.type) || [];
        const hasRequiredRole = requiredRoles.some((requiredRole) =>
          userRoleTypes.includes(requiredRole)
        );

        if (!hasRequiredRole) {
          logger.warn('Role requirement not met', {
            userId: BackendAuthContext.id,
            tenantId: BackendAuthContext.tenantId,
            userRoles: userRoleTypes,
            requiredRoles,
            path: req.path,
            method: req.method,
          });

          res.status(403).json({
            error: 'Forbidden',
            message: `Required role not found: ${requiredRoles.join(' or ')}`,
            statusCode: 403,
            timestamp: new Date().toISOString(),
          });
          return;
        }

        logger.debug('Role requirement satisfied', {
          userId: BackendAuthContext.id,
          userRoles: userRoleTypes,
          requiredRoles,
        });

        next();
      } catch (error) {
        logger.error('Role check failed', {
          error: error instanceof Error ? error.message : 'Unknown error',
          path: req.path,
          method: req.method,
          userId: getRequestUser(req)?.id,
        });

        res.status(500).json({
          error: 'Internal Server Error',
          message: 'Role check service error',
          statusCode: 500,
          timestamp: new Date().toISOString(),
        });
      }
    };
  };
}

/**
 * Convenience middleware for System Admin only access
 */
export function createSystemAdminMiddleware(
  dependencies: PermissionMiddlewareDependencies
) {
  const { logger } = dependencies;

  return async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      if (!getRequestUser(req)) {
        logger.warn('System Admin check attempted without authentication', {
          path: req.path,
          method: req.method,
        });
        res.status(401).json({
          error: 'Unauthorized',
          message: 'Authentication required',
          statusCode: 401,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      if (!getRequestUser(req).canBypassTenantScope) {
        logger.warn('System Admin access denied', {
          userId: getRequestUser(req).id,
          tenantId: getRequestUser(req).tenantId,
          path: req.path,
          method: req.method,
        });

        res.status(403).json({
          error: 'Forbidden',
          message: 'System Administrator access required',
          statusCode: 403,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      logger.debug('System Admin access granted', {
        userId: getRequestUser(req).id,
      });

      next();
    } catch (error) {
      logger.error('System Admin check failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        path: req.path,
        method: req.method,
        userId: getRequestUser(req)?.id,
      });

      res.status(500).json({
        error: 'Internal Server Error',
        message: 'System Admin check service error',
        statusCode: 500,
        timestamp: new Date().toISOString(),
      });
    }
  };
}

/**
 * Convenience middleware for Company Admin access within tenant
 */
export function createCompanyAdminMiddleware(
  dependencies: PermissionMiddlewareDependencies
) {
  const roleMiddleware = createRoleMiddleware(dependencies);
  return roleMiddleware([RoleType.SYSTEM_ADMIN, RoleType.COMPANY_ADMIN]);
}

/**
 * Convenience middleware for any Company role (Admin or Tech)
 */
export function createCompanyUserMiddleware(
  dependencies: PermissionMiddlewareDependencies
) {
  const roleMiddleware = createRoleMiddleware(dependencies);
  return roleMiddleware([
    RoleType.SYSTEM_ADMIN,
    RoleType.COMPANY_ADMIN,
    RoleType.COMPANY_TECH,
  ]);
}
