import { describe, it, expect, beforeEach } from '@jest/globals';
import { BackendAuthContext } from '@tech-notes/shared';

import {
  createMockPrismaService,
  createMockLogger,
  testData,
  MockPrismaService,
  MockLogger,
} from '../__tests__/simple-mocks.js';
import { ForbiddenError } from '../types/error.types.js';

import { RoleService } from './role.service.js';

describe('RoleService', () => {
  let roleService: RoleService;
  let mockPrisma: MockPrismaService;
  let mockLogger: MockLogger;
  let systemAdminContext: BackendAuthContext;
  let companyAdminContext: BackendAuthContext;

  beforeEach(() => {
    // Create simple, direct mocks
    mockPrisma = createMockPrismaService();
    mockLogger = createMockLogger();

    roleService = new RoleService(mockPrisma as any, mockLogger as any);

    // Mock auth contexts for tests
    systemAdminContext = {
      ...testData.BackendAuthContext,
      canBypassTenantScope: true, // System admin
    };

    companyAdminContext = {
      ...testData.BackendAuthContext,
      canBypassTenantScope: false, // Company admin
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getAllRoles', () => {
    it('should return all roles for system admin', async () => {
      // Arrange
      const mockRoles = [
        { id: '1', name: 'System Admin', isSystemRole: true },
        { id: '2', name: 'Company Admin', isSystemRole: false },
      ];
      mockPrisma.prisma.role.findMany.mockResolvedValue(mockRoles);

      // Act
      const result = await roleService.getAllRoles(systemAdminContext);

      // Assert
      expect(result).toEqual(mockRoles);
      expect(mockPrisma.prisma.role.findMany).toHaveBeenCalledWith({
        orderBy: [{ isSystemRole: 'desc' }, { name: 'asc' }],
      });
    });

    it('should throw ForbiddenError for non-system admin', async () => {
      // Act & Assert
      await expect(
        roleService.getAllRoles(companyAdminContext)
      ).rejects.toThrow(ForbiddenError);
      expect(mockPrisma.prisma.role.findMany).not.toHaveBeenCalled();
    });
  });

  describe('createRole', () => {
    it('should create role for system admin', async () => {
      // Arrange
      const roleData = {
        name: 'Test Role',
        type: 'COMPANY_ADMIN' as const,
        description: 'Test role description',
        isSystemRole: false,
      };
      const createdRole = { id: '1', ...roleData };

      mockPrisma.prisma.role.create.mockResolvedValue(createdRole);

      // Act
      const result = await roleService.createRole(roleData, systemAdminContext);

      // Assert
      expect(result).toEqual(createdRole);
      expect(mockPrisma.prisma.role.create).toHaveBeenCalledWith({
        data: roleData,
      });
    });

    it('should throw ForbiddenError for non-system admin', async () => {
      // Arrange
      const roleData = {
        name: 'Test Role',
        type: 'COMPANY_ADMIN' as const,
        description: 'Test role description',
      };

      // Act & Assert
      await expect(
        roleService.createRole(roleData, companyAdminContext)
      ).rejects.toThrow(ForbiddenError);
      expect(mockPrisma.prisma.role.create).not.toHaveBeenCalled();
    });
  });
});
