import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen } from "@testing-library/react";
import { BrowserRouter } from "react-router-dom";
import { ClerkProvider } from "@clerk/clerk-react";
import { TechLayout } from "../../src/layouts/TechLayout";
import { useAuth } from "../../src/hooks/useAuth";
import { type IAuthService } from "../../src/services/auth-api.service";

// Mock the hooks
vi.mock("../../src/hooks/useAuth");
vi.mock("../../src/components/navigation/TechNavigation", () => ({
  TechNavigation: () => (
    <div data-testid="tech-navigation">Tech Navigation</div>
  ),
}));

// Mock Clerk components
vi.mock("@clerk/clerk-react", () => ({
  ClerkProvider: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  UserButton: () => <div data-testid="user-button">User But<PERSON></div>,
}));

const mockUseAuth = vi.mocked(useAuth);

const mockApiService: IAuthService = {
  checkAuthStatus: vi.fn(),
  getCurrentUser: vi.fn(),
  completeOnboarding: vi.fn(),
  acceptInvitation: vi.fn(),
  makeRequest: vi.fn(),
  getToken: vi.fn(),
};

const renderTechLayout = () => {
  return render(
    <BrowserRouter>
      <ClerkProvider publishableKey="test">
        <TechLayout />
      </ClerkProvider>
    </BrowserRouter>,
  );
};

describe("TechLayout", () => {
  beforeEach(() => {
    mockUseAuth.mockReturnValue({
      userProfile: {
        id: "test-user-id",
        clerkId: "test-clerk-id",
        email: "<EMAIL>",
        firstName: "John",
        lastName: "Doe",
        tenantId: "test-tenant-id",
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        roles: [],
        permissions: [],
        canBypassTenantScope: false,
      },
      isLoading: false,
      isAuthenticated: true,
      isOnboarded: true,
      needsOnboarding: false,
      isServiceUnavailable: false,
      clerkUser: null,
      authStatus: undefined,
      signOut: vi.fn(),
      signIn: vi.fn(),
      refreshAuth: vi.fn(),
      getToken: vi.fn(),
      api: mockApiService,
      statusError: null,
      profileError: null,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("renders the tech layout with header and navigation", () => {
    renderTechLayout();

    // Check for header elements
    expect(screen.getByText("Tech Notes")).toBeInTheDocument();
    expect(screen.getByTestId("user-button")).toBeInTheDocument();

    // Check for navigation
    expect(screen.getByTestId("tech-navigation")).toBeInTheDocument();
  });

  it("displays user first name when available", () => {
    renderTechLayout();

    expect(screen.getByText("John")).toBeInTheDocument();
  });

  it("hides user name on small screens but shows on larger screens", () => {
    renderTechLayout();

    const userName = screen.getByText("John");
    const userNameContainer = userName.closest(".hidden.sm\\:block");
    expect(userNameContainer).toBeInTheDocument();
  });

  it("renders with mobile-first responsive design", () => {
    renderTechLayout();

    const container = screen.getByRole("banner"); // header element
    expect(container).toHaveClass("sticky", "top-0", "z-50");

    // Check for mobile-optimized padding (updated to py-4)
    const headerContent = container.querySelector(".px-4.py-4");
    expect(headerContent).toBeInTheDocument();
  });

  it("has proper layout structure for mobile", () => {
    renderTechLayout();

    // Check main content area
    const main = screen.getByRole("main");
    expect(main).toHaveClass("flex-1", "overflow-auto");

    // Check for mobile padding
    const contentDiv = main.querySelector(".px-4.py-6");
    expect(contentDiv).toBeInTheDocument();
  });

  it("handles missing user profile gracefully", () => {
    mockUseAuth.mockReturnValue({
      userProfile: null,
      isLoading: false,
      isAuthenticated: false,
      isOnboarded: false,
      needsOnboarding: false,
      isServiceUnavailable: false,
      clerkUser: null,
      authStatus: undefined,
      signOut: vi.fn(),
      signIn: vi.fn(),
      refreshAuth: vi.fn(),
      getToken: vi.fn(),
      api: mockApiService,
      statusError: null,
      profileError: null,
    });

    renderTechLayout();

    expect(screen.getByText("Tech Notes")).toBeInTheDocument();
    expect(screen.getByTestId("user-button")).toBeInTheDocument();
    expect(screen.queryByText("John")).not.toBeInTheDocument();
  });

  it("handles user without first name", () => {
    mockUseAuth.mockReturnValue({
      userProfile: {
        id: "test-user-id",
        clerkId: "test-clerk-id",
        email: "<EMAIL>",
        firstName: null,
        lastName: "Doe",
        tenantId: "test-tenant-id",
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        roles: [],
        permissions: [],
        canBypassTenantScope: false,
      },
      isLoading: false,
      isAuthenticated: true,
      isOnboarded: true,
      needsOnboarding: false,
      isServiceUnavailable: false,
      clerkUser: null,
      authStatus: undefined,
      signOut: vi.fn(),
      signIn: vi.fn(),
      refreshAuth: vi.fn(),
      getToken: vi.fn(),
      api: mockApiService,
      statusError: null,
      profileError: null,
    });

    renderTechLayout();

    expect(screen.getByText("Tech Notes")).toBeInTheDocument();
    expect(screen.queryByText("John")).not.toBeInTheDocument();
  });

  it("has proper accessibility structure", () => {
    renderTechLayout();

    // Check for semantic HTML structure
    expect(screen.getByRole("banner")).toBeInTheDocument(); // header
    expect(screen.getByRole("main")).toBeInTheDocument(); // main content

    // Check for proper heading hierarchy
    const heading = screen.getByRole("heading", { level: 1 });
    expect(heading).toHaveTextContent("Tech Notes");
  });
});
