import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import { Card, CardHeader, CardContent } from "./Card";

describe("Card", () => {
  describe("Rendering", () => {
    it("should render card with default variant", () => {
      render(<Card>Card content</Card>);

      const card = screen.getByText("Card content");
      expect(card).toBeInTheDocument();
      expect(card).toHaveClass(
        "bg-white",
        "border",
        "border-gray-200",
        "rounded-2xl",
      );
      expect(card).toHaveClass("shadow-sm", "transition-all", "duration-300");
    });

    it("should render card with custom className", () => {
      render(<Card className="custom-class">Card content</Card>);

      const card = screen.getByText("Card content");
      expect(card).toHaveClass("custom-class");
    });
  });

  describe("Variants", () => {
    it("should render default variant with basic styling", () => {
      render(<Card variant="default">Default card</Card>);

      const card = screen.getByText("Default card");
      expect(card).toHaveClass("shadow-sm", "hover:shadow-md");
      expect(card).toHaveClass("border-gray-200");
    });

    it("should render elevated variant with enhanced shadow", () => {
      render(<Card variant="elevated">Elevated card</Card>);

      const card = screen.getByText("Elevated card");
      expect(card).toHaveClass("shadow-md", "hover:shadow-lg");
      expect(card).toHaveClass("border-gray-100", "hover:border-gray-200");
    });

    it("should render interactive variant with hover effects", () => {
      render(<Card variant="interactive">Interactive card</Card>);

      const card = screen.getByText("Interactive card");
      expect(card).toHaveClass("cursor-pointer");
      expect(card).toHaveClass("hover:scale-[1.01]", "active:scale-[0.99]");
      expect(card).toHaveClass("hover:border-primary-200");
    });
  });

  describe("Props", () => {
    it("should pass through HTML div props", () => {
      const handleClick = vi.fn();
      render(
        <Card onClick={handleClick} data-testid="test-card">
          Clickable card
        </Card>,
      );

      const card = screen.getByTestId("test-card");
      card.click();
      expect(handleClick).toHaveBeenCalledOnce();
    });
  });
});

describe("CardHeader", () => {
  describe("Rendering", () => {
    it("should render header with title", () => {
      render(<CardHeader title="Card Title" />);

      const title = screen.getByText("Card Title");
      expect(title).toBeInTheDocument();
      expect(title).toHaveClass(
        "text-lg",
        "font-semibold",
        "text-gray-900",
        "tracking-tight",
      );
    });

    it("should render header with title and subtitle", () => {
      render(<CardHeader title="Card Title" subtitle="Card subtitle" />);

      const title = screen.getByText("Card Title");
      const subtitle = screen.getByText("Card subtitle");

      expect(title).toBeInTheDocument();
      expect(subtitle).toBeInTheDocument();
      expect(subtitle).toHaveClass("text-sm", "text-gray-600", "mt-1");
    });

    it("should render header with children", () => {
      render(
        <CardHeader title="Card Title">
          <button>Action</button>
        </CardHeader>,
      );

      const title = screen.getByText("Card Title");
      const button = screen.getByRole("button", { name: "Action" });

      expect(title).toBeInTheDocument();
      expect(button).toBeInTheDocument();
    });

    it("should have enhanced header styling", () => {
      render(<CardHeader title="Enhanced Header" />);

      const header = screen.getByText("Enhanced Header").parentElement;
      expect(header).toHaveClass("border-b", "border-gray-100");
      expect(header).toHaveClass("px-6", "py-5", "bg-gray-50/50");
    });
  });
});

describe("CardContent", () => {
  describe("Rendering", () => {
    it("should render content with children", () => {
      render(
        <CardContent>
          <p>Card content text</p>
        </CardContent>,
      );

      const content = screen.getByText("Card content text");
      expect(content).toBeInTheDocument();
    });

    it("should have enhanced content styling", () => {
      render(
        <CardContent>
          <p>Content with spacing</p>
        </CardContent>,
      );

      const content = screen.getByText("Content with spacing").parentElement;
      expect(content).toHaveClass("p-6", "space-y-4");
    });

    it("should render with custom className", () => {
      render(
        <CardContent className="custom-content">
          <p>Custom content</p>
        </CardContent>,
      );

      const content = screen.getByText("Custom content").parentElement;
      expect(content).toHaveClass("custom-content");
    });
  });
});

describe("Card Composition", () => {
  it("should render complete card with header and content", () => {
    render(
      <Card variant="elevated">
        <CardHeader title="Complete Card" subtitle="With all parts" />
        <CardContent>
          <p>This is the card content</p>
        </CardContent>
      </Card>,
    );

    const title = screen.getByText("Complete Card");
    const subtitle = screen.getByText("With all parts");
    const content = screen.getByText("This is the card content");

    expect(title).toBeInTheDocument();
    expect(subtitle).toBeInTheDocument();
    expect(content).toBeInTheDocument();
  });
});
