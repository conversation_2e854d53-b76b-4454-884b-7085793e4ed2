import React from "react";
import { Link } from "react-router-dom";
import { useAuth } from "../../hooks/useAuth";
import { Card, CardContent } from "../../components";
import {
  QrCode,
  ClipboardList,
  Zap,
  User,
  Clock,
  CheckCircle,
} from "lucide-react";

interface QuickActionCard {
  title: string;
  description: string;
  icon: React.ReactNode;
  path: string;
  color: string;
}

const quickActions: QuickActionCard[] = [
  {
    title: "Scan VIN/QR",
    description: "Scan vehicle codes",
    icon: <QrCode className="h-8 w-8" />,
    path: "/tech/scan",
    color: "bg-blue-500",
  },
  {
    title: "Work Orders",
    description: "View active orders",
    icon: <ClipboardList className="h-8 w-8" />,
    path: "/tech/work-orders",
    color: "bg-green-500",
  },
  {
    title: "Quick Actions",
    description: "Common tasks",
    icon: <Zap className="h-8 w-8" />,
    path: "/tech/quick-actions",
    color: "bg-orange-500",
  },
];

export const TechDashboard: React.FC = () => {
  const { userProfile } = useAuth();

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="text-center py-4">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Welcome{userProfile?.firstName ? `, ${userProfile.firstName}` : ""}!
        </h1>
        <p className="text-gray-600">Ready to get to work?</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex flex-col items-center space-y-2">
              <Clock className="h-6 w-6 text-blue-500" />
              <div className="text-sm font-medium text-gray-900">Today</div>
              <div className="text-xs text-gray-500">0 orders</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex flex-col items-center space-y-2">
              <CheckCircle className="h-6 w-6 text-green-500" />
              <div className="text-sm font-medium text-gray-900">Complete</div>
              <div className="text-xs text-gray-500">0 orders</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex flex-col items-center space-y-2">
              <User className="h-6 w-6 text-purple-500" />
              <div className="text-sm font-medium text-gray-900">Status</div>
              <div className="text-xs text-gray-500">Active</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Action Cards */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900 px-1">
          Quick Actions
        </h2>

        <div className="space-y-3">
          {quickActions.map((action) => (
            <Link key={action.path} to={action.path} className="block">
              <Card className="hover:shadow-md transition-shadow duration-200">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    {/* Icon */}
                    <div
                      className={`${action.color} text-white p-3 rounded-lg flex-shrink-0`}
                    >
                      {action.icon}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {action.title}
                      </h3>
                      <p className="text-gray-600 text-sm">
                        {action.description}
                      </p>
                    </div>

                    {/* Arrow indicator */}
                    <div className="flex-shrink-0">
                      <svg
                        className="h-5 w-5 text-gray-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900 px-1">
          Recent Activity
        </h2>

        <Card>
          <CardContent className="p-6">
            <div className="text-center py-8">
              <ClipboardList className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 text-sm">No recent activity</p>
              <p className="text-gray-400 text-xs mt-1">
                Your completed work will appear here
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
