/**
 * Design System Shadow Tokens
 * Inspired by Salient template elevation patterns
 *
 * Provides consistent elevation system for cards, modals,
 * dropdowns, and interactive elements
 */

export const shadows = {
  // Base shadow scale (Salient-inspired)
  none: "none",

  // Subtle shadows for cards and surfaces
  xs: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
  sm: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",

  // Standard shadows for elevated elements
  base: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
  md: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",

  // Prominent shadows for modals and dropdowns
  lg: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
  xl: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",

  // Maximum elevation for overlays
  "2xl": "0 25px 50px -12px rgba(0, 0, 0, 0.25)",

  // Inner shadows for inputs and pressed states
  inner: "inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)",

  // Semantic shadows (Salient-inspired)
  semantic: {
    // Card shadows
    cardResting:
      "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
    cardHover:
      "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
    cardActive: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",

    // Button shadows
    buttonResting: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
    buttonHover:
      "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
    buttonActive: "inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)",

    // Modal and overlay shadows
    modal: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
    dropdown:
      "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
    tooltip:
      "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",

    // Form element shadows
    input: "inset 0 1px 2px 0 rgba(0, 0, 0, 0.05)",
    inputFocus:
      "0 0 0 3px rgba(59, 130, 246, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.05)",

    // Navigation shadows
    navbar: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
    sidebar:
      "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
  },

  // Colored shadows for brand elements
  colored: {
    primary: "0 4px 14px 0 rgba(59, 130, 246, 0.15)",
    primaryHover: "0 8px 25px 0 rgba(59, 130, 246, 0.2)",
    success: "0 4px 14px 0 rgba(16, 185, 129, 0.15)",
    warning: "0 4px 14px 0 rgba(245, 158, 11, 0.15)",
    error: "0 4px 14px 0 rgba(239, 68, 68, 0.15)",
  },

  // Glow effects for interactive elements
  glow: {
    primary: "0 0 20px rgba(59, 130, 246, 0.3)",
    success: "0 0 20px rgba(16, 185, 129, 0.3)",
    warning: "0 0 20px rgba(245, 158, 11, 0.3)",
    error: "0 0 20px rgba(239, 68, 68, 0.3)",
  },

  // Focus rings (accessibility)
  focus: {
    default: "0 0 0 3px rgba(59, 130, 246, 0.1)",
    error: "0 0 0 3px rgba(239, 68, 68, 0.1)",
    success: "0 0 0 3px rgba(16, 185, 129, 0.1)",
  },
} as const;

// Type definitions
export type ShadowToken = keyof typeof shadows;
export type SemanticShadow = keyof typeof shadows.semantic;
export type ColoredShadow = keyof typeof shadows.colored;

// Utility function for accessing shadow values
export const getShadow = (path: string): string => {
  const keys = path.split(".");
  let value: unknown = shadows;

  for (const key of keys) {
    if (typeof value === "object" && value !== null && key in value) {
      value = (value as Record<string, unknown>)[key];
    } else {
      throw new Error(`Shadow token "${path}" not found`);
    }
  }

  if (typeof value !== "string") {
    throw new Error(`Shadow token "${path}" is not a string`);
  }

  return value;
};

// Shadow utilities for common patterns
export const shadowUtils = {
  // Get elevation shadow based on level (0-5)
  getElevation: (level: 0 | 1 | 2 | 3 | 4 | 5): string => {
    const elevationMap = {
      0: shadows.none,
      1: shadows.xs,
      2: shadows.sm,
      3: shadows.base,
      4: shadows.lg,
      5: shadows.xl,
    };
    return elevationMap[level];
  },

  // Get interactive shadow states
  getInteractiveShadows: (type: "card" | "button" = "card") => ({
    resting:
      shadows.semantic[`${type}Resting` as keyof typeof shadows.semantic],
    hover: shadows.semantic[`${type}Hover` as keyof typeof shadows.semantic],
    active: shadows.semantic[`${type}Active` as keyof typeof shadows.semantic],
  }),

  // Combine multiple shadows
  combine: (...shadowValues: string[]): string => {
    return shadowValues.filter((shadow) => shadow !== "none").join(", ");
  },
};

// Export individual scales for convenience
export const { semantic, colored, glow, focus } = shadows;
