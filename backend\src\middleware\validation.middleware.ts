import { Request, Response, NextFunction } from 'express';
import { z, ZodSchema } from 'zod';

import { ValidationError } from '../types/error.types.js';
import { Logger } from '../utils/logger.js';

export interface ValidationMiddlewareDependencies {
  logger: Logger;
}

/**
 * Factory function to create request validation middleware
 */
export function createValidationMiddleware(
  dependencies: ValidationMiddlewareDependencies
) {
  const { logger } = dependencies;

  return function validate(schema: {
    body?: ZodSchema;
    query?: ZodSchema;
    params?: ZodSchema;
  }) {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        // Validate request body
        if (schema.body) {
          req.body = schema.body.parse(req.body);
        }

        // Validate query parameters
        if (schema.query) {
          req.query = schema.query.parse(req.query);
        }

        // Validate route parameters
        if (schema.params) {
          req.params = schema.params.parse(req.params);
        }

        logger.debug('Request validation successful', {
          method: req.method,
          path: req.path,
          hasBody: !!schema.body,
          hasQuery: !!schema.query,
          hasParams: !!schema.params,
        });

        next();
      } catch (error) {
        if (error instanceof z.ZodError) {
          const validationErrors = error.errors.map((err) => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code,
          }));

          logger.warn('Request validation failed', {
            method: req.method,
            path: req.path,
            errors: validationErrors,
            body: req.body,
            query: req.query,
            params: req.params,
          });

          const errorMessage = `Validation failed: ${validationErrors
            .map((err) => `${err.field} - ${err.message}`)
            .join(', ')}`;

          next(new ValidationError(errorMessage));
          return;
        }

        logger.error('Unexpected validation error', {
          error: error instanceof Error ? error.message : 'Unknown error',
          method: req.method,
          path: req.path,
        });

        next(new ValidationError('Request validation failed'));
      }
    };
  };
}

// Common validation schemas
export const commonSchemas = {
  // ID parameter validation
  idParam: z.object({
    id: z.string().cuid('Invalid ID format'),
  }),

  // Pagination query validation
  pagination: z.object({
    page: z.string().transform(Number).pipe(z.number().min(1)).optional(),
    limit: z
      .string()
      .transform(Number)
      .pipe(z.number().min(1).max(100))
      .optional(),
  }),

  // Tenant ID validation
  tenantId: z.object({
    tenantId: z.string().cuid('Invalid tenant ID format'),
  }),
};
