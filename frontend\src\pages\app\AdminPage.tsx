import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";

import {
  <PERSON>,
  <PERSON>H<PERSON>er,
  CardContent,
  Alert,
  Badge,
  <PERSON>ton,
  StatCard,
  Modal,
  LoadingSpinner,
  SystemAdminOnly,
} from "../../components";
import { useAuth } from "../../hooks/useAuth";
import { useTypedApi } from "../../services/api-client";
import type { User } from "../../services/api-client";
import {
  Users,
  Building2,
  Shield,
  Activity,
  TrendingUp,
  Eye,
} from "lucide-react";

// Quick Action Card Component
interface QuickActionCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  onClick: () => void;
  disabled?: boolean;
}

const QuickActionCard: React.FC<QuickActionCardProps> = ({
  title,
  description,
  icon,
  onClick,
  disabled = false,
}) => (
  <Card
    className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 bg-white/80 border-gray-200/60 ${disabled ? "opacity-50" : ""}`}
  >
    <CardContent className="p-6">
      <Button
        variant="ghost"
        onClick={onClick}
        disabled={disabled}
        className="w-full h-auto p-0 flex flex-col items-center space-y-4 group"
      >
        <div className="p-4 bg-gradient-to-br from-primary-50 to-primary-100 rounded-xl group-hover:from-primary-100 group-hover:to-primary-200 transition-all duration-200">
          {icon}
        </div>
        <div className="text-center">
          <h3 className="font-semibold text-gray-900 group-hover:text-primary-700 transition-colors">
            {title}
          </h3>
          <p className="text-sm text-gray-600 mt-1 leading-relaxed">
            {description}
          </p>
        </div>
      </Button>
    </CardContent>
  </Card>
);

// User Detail Modal Component
interface UserDetailModalProps {
  user: User | null;
  isOpen: boolean;
  onClose: () => void;
}

const UserDetailModal: React.FC<UserDetailModalProps> = ({
  user,
  isOpen,
  onClose,
}) => {
  if (!user) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`User Details: ${user.firstName} ${user.lastName}`}
      size="lg"
    >
      <div className="space-y-6">
        {/* Basic Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Basic Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Full Name
              </label>
              <p className="mt-1 text-sm text-gray-900">
                {user.firstName} {user.lastName}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <p className="mt-1 text-sm text-gray-900">{user.email}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Tenant ID
              </label>
              <code className="mt-1 text-xs bg-gray-100 px-2 py-1 rounded block">
                {user.tenantId}
              </code>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <div className="mt-1">
                <Badge variant={user.isActive ? "success" : "warning"}>
                  {user.isActive ? "Active" : "Inactive"}
                </Badge>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Created
              </label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(user.createdAt).toLocaleDateString()}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Last Updated
              </label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(user.updatedAt).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>

        {/* Roles */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Assigned Roles
          </h3>
          <div className="flex flex-wrap gap-2">
            {user.userRoles && user.userRoles.length > 0 ? (
              user.userRoles.map((userRole) => (
                <Badge key={userRole.id} variant="primary">
                  {userRole.role.name}
                </Badge>
              ))
            ) : (
              <p className="text-sm text-gray-500">No roles assigned</p>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button onClick={() => (window.location.href = "/app/users")}>
            Manage User
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export const AdminPage: React.FC = () => {
  const { userProfile, isLoading: authLoading } = useAuth();
  const api = useTypedApi();

  // Modal states
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isUserDetailModalOpen, setIsUserDetailModalOpen] = useState(false);

  // Fetch system-wide data
  const {
    data: usersResponse,
    isLoading: isLoadingUsers,
    error: usersError,
  } = useQuery({
    queryKey: ["admin-users"],
    queryFn: () => api.users.getAll(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const {
    data: tenantsResponse,
    isLoading: isLoadingTenants,
    error: tenantsError,
  } = useQuery({
    queryKey: ["admin-tenants"],
    queryFn: () => api.tenants.getAll(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const {
    data: rolesResponse,
    isLoading: isLoadingRoles,
    error: rolesError,
  } = useQuery({
    queryKey: ["admin-roles"],
    queryFn: () => api.roles.getAll(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Calculate statistics
  const totalUsers = usersResponse?.meta.count || 0;
  const activeUsers =
    usersResponse?.data.filter((user) => user.isActive).length || 0;
  const totalTenants = tenantsResponse?.data.length || 0;
  const activeTenants =
    tenantsResponse?.data.filter((tenant) => tenant.isActive).length || 0;
  const totalRoles = rolesResponse?.data.length || 0;
  const systemRoles =
    rolesResponse?.data.filter((role) => role.isSystemRole).length || 0;

  // Event handlers
  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setIsUserDetailModalOpen(true);
  };

  const handleCloseUserDetail = () => {
    setIsUserDetailModalOpen(false);
    setSelectedUser(null);
  };

  // Quick actions
  const quickActions = [
    {
      title: "Manage Tenants",
      description: "Create and configure tenants",
      icon: <Building2 className="h-6 w-6 text-primary-600" />,
      onClick: () => (window.location.href = "/app/settings"),
    },
    {
      title: "Manage Users",
      description: "Cross-tenant user management",
      icon: <Users className="h-6 w-6 text-primary-600" />,
      onClick: () => (window.location.href = "/app/users"),
    },
    {
      title: "Role Management",
      description: "System roles and permissions",
      icon: <Shield className="h-6 w-6 text-primary-600" />,
      onClick: () => (window.location.href = "/app/roles"),
    },
    {
      title: "System Health",
      description: "Monitor system status",
      icon: <Activity className="h-6 w-6 text-primary-600" />,
      onClick: () => (window.location.href = "/app/health"),
    },
  ];

  if (authLoading) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-gray-900">
          System Administration
        </h1>
        <LoadingSpinner />
      </div>
    );
  }

  const isLoading = isLoadingUsers || isLoadingTenants || isLoadingRoles;
  const hasErrors = usersError || tenantsError || rolesError;

  return (
    <SystemAdminOnly>
      <div className="space-y-8">
        {/* Enhanced Page Header */}
        <div className="text-center bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl p-8 border border-purple-100/50">
          <div className="max-w-2xl mx-auto">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">
              System Administration
            </h1>
            <p className="mt-4 text-lg text-gray-600 leading-relaxed">
              Comprehensive system management and monitoring dashboard
            </p>
            <div className="mt-4 inline-flex items-center px-3 py-1 rounded-full bg-white/60 border border-purple-200/60">
              <Shield className="h-4 w-4 text-purple-500 mr-2" />
              <span className="text-sm font-medium text-gray-700">
                System Admin Access
              </span>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {hasErrors && (
          <Alert variant="error">
            <p>
              Some data could not be loaded. Please refresh the page or check
              your permissions.
            </p>
          </Alert>
        )}

        {/* Enhanced System Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Users"
            value={isLoading ? "--" : totalUsers.toString()}
            change={{
              value: isLoading ? "Loading..." : `${activeUsers} active`,
              trend: activeUsers > 0 ? "up" : "neutral",
              period: "",
            }}
            icon={<Users className="h-6 w-6 text-primary-600" />}
          />
          <StatCard
            title="Total Tenants"
            value={isLoading ? "--" : totalTenants.toString()}
            change={{
              value: isLoading ? "Loading..." : `${activeTenants} active`,
              trend: activeTenants > 0 ? "up" : "neutral",
              period: "",
            }}
            icon={<Building2 className="h-6 w-6 text-primary-600" />}
          />
          <StatCard
            title="System Roles"
            value={isLoading ? "--" : totalRoles.toString()}
            change={{
              value: isLoading ? "Loading..." : `${systemRoles} system`,
              trend: "neutral",
              period: "",
            }}
            icon={<Shield className="h-6 w-6 text-primary-600" />}
          />
          <StatCard
            title="System Health"
            value="Online"
            change={{ value: "Operational", trend: "up", period: "" }}
            icon={<TrendingUp className="h-6 w-6 text-primary-600" />}
          />
        </div>

        {/* Enhanced Quick Actions */}
        <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-6 border border-gray-200/60 shadow-sm">
          <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <Activity className="h-5 w-5 text-primary-600 mr-2" />
            Quick Actions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <QuickActionCard
                key={index}
                title={action.title}
                description={action.description}
                icon={action.icon}
                onClick={action.onClick}
              />
            ))}
          </div>
        </div>

        {/* Recent Users Overview */}
        {!isLoadingUsers && usersResponse && (
          <Card>
            <CardHeader title="Recent Users" />
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-600">
                    Latest user registrations across all tenants
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => (window.location.href = "/app/users")}
                  >
                    View All Users
                  </Button>
                </div>

                <div className="space-y-3">
                  {usersResponse.data.slice(0, 5).map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                          <Users className="h-4 w-4 text-primary-600" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">
                            {user.firstName} {user.lastName}
                          </h3>
                          <p className="text-sm text-gray-500">{user.email}</p>
                        </div>
                        <Badge variant={user.isActive ? "success" : "warning"}>
                          {user.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewUser(user)}
                          className="flex items-center space-x-1"
                        >
                          <Eye className="h-4 w-4" />
                          <span>View</span>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Tenant Overview */}
        {!isLoadingTenants && tenantsResponse && (
          <Card>
            <CardHeader title="Tenant Overview" />
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-600">
                    All tenants in the system
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => (window.location.href = "/app/settings")}
                  >
                    Manage Tenants
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {tenantsResponse.data.map((tenant) => (
                    <div
                      key={tenant.id}
                      className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium text-gray-900">
                          {tenant.name}
                        </h3>
                        <Badge
                          variant={tenant.isActive ? "success" : "warning"}
                        >
                          {tenant.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-500 mb-2">
                        {tenant.slug}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-400">
                        <span>
                          Created:{" "}
                          {new Date(tenant.createdAt).toLocaleDateString()}
                        </span>
                        <span>
                          Users:{" "}
                          {usersResponse?.data.filter(
                            (u) => u.tenantId === tenant.id,
                          ).length || 0}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* System Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Current Administrator */}
          <Card>
            <CardHeader title="Current Administrator" />
            <CardContent>
              {userProfile ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">
                      Name:
                    </span>
                    <span className="text-sm text-gray-900">
                      {userProfile.firstName} {userProfile.lastName}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">
                      Email:
                    </span>
                    <span className="text-sm text-gray-900">
                      {userProfile.email}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">
                      System Admin:
                    </span>
                    <Badge variant="admin">Yes</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">
                      Tenant:
                    </span>
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                      {userProfile.tenantId.slice(-8)}
                    </code>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-gray-500">
                  Administrator information not available
                </p>
              )}
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader title="System Status" />
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">
                    API Status:
                  </span>
                  <Badge variant="success">Online</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">
                    Database:
                  </span>
                  <Badge variant="success">Connected</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">
                    Authentication:
                  </span>
                  <Badge variant="success">Active</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">
                    Last Updated:
                  </span>
                  <span className="text-sm text-gray-900">
                    {new Date().toLocaleTimeString()}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* User Detail Modal */}
        <UserDetailModal
          user={selectedUser}
          isOpen={isUserDetailModalOpen}
          onClose={handleCloseUserDetail}
        />
      </div>
    </SystemAdminOnly>
  );
};
