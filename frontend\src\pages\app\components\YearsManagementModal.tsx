import React, { useState, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
  <PERSON>ge,
  <PERSON>ert,
  <PERSON><PERSON><PERSON><PERSON>ner,
  FormField,
  CompanyAdminOnly,
} from "../../../components";
import { useTypedApi } from "../../../services/api-client";
import type { VehicleYear } from "../../../services/api-client";
import { Calendar, Plus, Trash2, Search, AlertTriangle } from "lucide-react";

interface YearsManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const YearsManagementModal: React.FC<YearsManagementModalProps> = ({
  isOpen,
  onClose,
}) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [searchQuery, setSearchQuery] = useState("");
  const [isCreateMode, setIsCreateMode] = useState(false);

  const [newYear, setNewYear] = useState("");
  const [deleteConfirmYear, setDeleteConfirmYear] =
    useState<VehicleYear | null>(null);

  // Fetch years
  const { data: yearsResponse, isLoading } = useQuery({
    queryKey: ["vehicle-years"],
    queryFn: () => api.vehicleHierarchy.getYears(),
    enabled: isOpen,
  });

  // Fetch hierarchy for cascade warnings
  const { data: hierarchyResponse } = useQuery({
    queryKey: ["vehicle-hierarchy-full"],
    queryFn: () => api.vehicleHierarchy.getFullHierarchy(),
    enabled: isOpen,
  });

  // Create year mutation
  const createYearMutation = useMutation({
    mutationFn: (year: number) => api.vehicleHierarchy.createYear({ year }),
    onSuccess: () => {
      toast.success("Year created successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-years"] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      setIsCreateMode(false);
      setNewYear("");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create year");
    },
  });

  // Delete year mutation
  const deleteYearMutation = useMutation({
    mutationFn: (yearId: string) => api.vehicleHierarchy.deleteYear(yearId),
    onSuccess: () => {
      toast.success("Year deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-years"] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      setDeleteConfirmYear(null);
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete year");
    },
  });

  // Filter years based on search
  const filteredYears = useMemo(() => {
    if (!yearsResponse?.data) return [];

    return yearsResponse.data
      .filter((year) => year.year.toString().includes(searchQuery))
      .sort((a, b) => b.year - a.year);
  }, [yearsResponse?.data, searchQuery]);

  // Get year statistics
  const getYearStats = (year: VehicleYear) => {
    if (!hierarchyResponse?.data) return { makes: 0, models: 0 };

    const yearData = hierarchyResponse.data.years.find((y) => y.id === year.id);
    if (!yearData) return { makes: 0, models: 0 };

    const makes = yearData.makes.length;
    const models = yearData.makes.reduce(
      (total, make) => total + make.models.length,
      0,
    );

    return { makes, models };
  };

  const handleCreateYear = () => {
    const yearNum = parseInt(newYear);
    if (isNaN(yearNum) || yearNum < 1900 || yearNum > 2050) {
      toast.error("Please enter a valid year between 1900 and 2050");
      return;
    }
    createYearMutation.mutate(yearNum);
  };

  const handleDeleteYear = (year: VehicleYear) => {
    const stats = getYearStats(year);
    if (stats.makes > 0 || stats.models > 0) {
      setDeleteConfirmYear(year);
    } else {
      deleteYearMutation.mutate(year.id);
    }
  };

  const confirmDelete = () => {
    if (deleteConfirmYear) {
      deleteYearMutation.mutate(deleteConfirmYear.id);
    }
  };

  const handleClose = () => {
    setSearchQuery("");
    setIsCreateMode(false);
    setNewYear("");
    setDeleteConfirmYear(null);
    onClose();
  };

  const isPending =
    createYearMutation.isPending || deleteYearMutation.isPending;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Manage Vehicle Years"
      size="lg"
    >
      <div className="space-y-6">
        {/* Header with search and add button */}
        <div className="flex items-center justify-between">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search years..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <CompanyAdminOnly>
            <Button
              onClick={() => setIsCreateMode(true)}
              className="ml-4 flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Add Year</span>
            </Button>
          </CompanyAdminOnly>
        </div>

        {/* Create year form */}
        {isCreateMode && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="text-lg font-medium text-blue-900 mb-3">
              Add New Year
            </h3>
            <div className="flex items-end space-x-3">
              <FormField label="Year" className="flex-1">
                <Input
                  type="number"
                  placeholder="e.g., 2024"
                  value={newYear}
                  onChange={(e) => setNewYear(e.target.value)}
                  min="1900"
                  max="2050"
                />
              </FormField>
              <Button
                onClick={handleCreateYear}
                disabled={isPending || !newYear}
                className="flex items-center space-x-2"
              >
                {createYearMutation.isPending ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Plus className="h-4 w-4" />
                )}
                <span>Create</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setIsCreateMode(false);
                  setNewYear("");
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        {/* Years list */}
        <div className="space-y-3">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner size="md" />
            </div>
          ) : filteredYears.length > 0 ? (
            filteredYears.map((year) => {
              const stats = getYearStats(year);
              return (
                <div
                  key={year.id}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-4">
                    <Calendar className="h-5 w-5 text-primary-600" />
                    <div>
                      <h3 className="font-medium text-gray-900">{year.year}</h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>
                          Created:{" "}
                          {new Date(year.createdAt).toLocaleDateString()}
                        </span>
                        <Badge variant="secondary">{stats.makes} makes</Badge>
                        <Badge variant="secondary">{stats.models} models</Badge>
                      </div>
                    </div>
                  </div>

                  <CompanyAdminOnly>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteYear(year)}
                        disabled={isPending}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CompanyAdminOnly>
                </div>
              );
            })
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Calendar className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p>No years found</p>
              {searchQuery && (
                <p className="text-sm">Try adjusting your search</p>
              )}
            </div>
          )}
        </div>

        {/* Delete confirmation */}
        {deleteConfirmYear && (
          <Alert variant="warning" className="border-orange-200 bg-orange-50">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
              <div className="flex-1">
                <h3 className="font-medium text-orange-900">
                  Confirm Deletion
                </h3>
                <p className="text-orange-800 mt-1">
                  Are you sure you want to delete year {deleteConfirmYear.year}?
                  {(() => {
                    const stats = getYearStats(deleteConfirmYear);
                    if (stats.makes > 0 || stats.models > 0) {
                      return (
                        <span className="block mt-2 font-medium">
                          ⚠️ This will also delete {stats.makes} make(s) and{" "}
                          {stats.models} model(s) associated with this year.
                        </span>
                      );
                    }
                    return null;
                  })()}
                </p>
                <div className="flex items-center space-x-3 mt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setDeleteConfirmYear(null)}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={confirmDelete}
                    disabled={isPending}
                    className="bg-red-600 text-white hover:bg-red-700"
                  >
                    {deleteYearMutation.isPending ? "Deleting..." : "Delete"}
                  </Button>
                </div>
              </div>
            </div>
          </Alert>
        )}

        {(createYearMutation.error || deleteYearMutation.error) && (
          <Alert variant="error">
            {createYearMutation.error?.message ||
              deleteYearMutation.error?.message ||
              "Operation failed"}
          </Alert>
        )}
      </div>
    </Modal>
  );
};
