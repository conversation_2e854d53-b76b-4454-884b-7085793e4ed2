import fs from "fs";
import path from "path";
import <PERSON> from "papaparse";

// Parse CSV and generate TypeScript data file
function parseCsvToTs() {
  try {
    // Read the CSV file
    const csvPath = path.join(process.cwd(), "public/data/features.csv");
    const csvContent = fs.readFileSync(csvPath, "utf8");

    // Parse CSV
    const parsed = Papa.parse(csvContent, {
      header: true,
      skipEmptyLines: true,
      transformHeader: (header) => header.trim(),
    });

    if (parsed.errors.length > 0) {
      console.warn("CSV parsing warnings:", parsed.errors);
    }

    // Filter out empty rows
    const features = parsed.data.filter((row) => {
      return (
        row &&
        Object.values(row).some((val) => val && val.toString().trim() !== "")
      );
    });

    // Transform the data
    const transformedFeatures = features.map((row) => ({
      groupName: row.groupName,
      featureName: row.featureName,
      featureDescription: row.featureDescription,
      basic:
        row.basic === "true" ? true : row.basic === "false" ? false : row.basic,
      plus:
        row.plus === "true" ? true : row.plus === "false" ? false : row.plus,
      pro: row.pro === "true" ? true : row.pro === "false" ? false : row.pro,
      enterprise:
        row.enterprise === "true"
          ? true
          : row.enterprise === "false"
            ? false
            : row.enterprise,
      includeOnCard: row.includeOnCard === "true",
      setupFee: row.setupFee === "true",
    }));

    // Group features by category
    const grouped = transformedFeatures.reduce((acc, feature) => {
      if (!acc[feature.groupName]) {
        acc[feature.groupName] = [];
      }

      acc[feature.groupName].push({
        name: feature.featureName,
        description: feature.featureDescription,
        basic: feature.basic,
        plus: feature.plus,
        pro: feature.pro,
        enterprise: feature.enterprise,
        includeOnCard: feature.includeOnCard,
        setupFee: feature.setupFee,
      });

      return acc;
    }, {});

    const featureGroups = Object.entries(grouped).map(
      ([groupName, features]) => ({
        groupName,
        features,
      }),
    );

    // Generate TypeScript file content
    const tsContent = `// Auto-generated from CSV data - DO NOT EDIT MANUALLY
// Source: public/data/features.csv

import type { FeatureGroup } from '../types/pricing.types';

export const FEATURE_GROUPS: FeatureGroup[] = ${JSON.stringify(featureGroups, null, 2)};

export const PRICING_CONFIG = {
  companyName: 'Tech Notes',
  tagline: 'An all-in-one tool for making sure your service technicians have the exact content they need right when they need it',
  pricingNote: 'All prices are per month per location',
  ctaHeadline: 'Ready to Get Started?',
  ctaDescription: 'Join thousands of teams already using Tech Notes to streamline their operations. Start your free trial today.',
  trialButtonText: 'Start Free Trial',
  demoButtonText: 'Schedule Demo'
};

export const PRICING_TIERS = [
  {
    name: 'Basic',
    price: '$7',
    period: 'month',
    description: 'Perfect for small teams getting started',
    icon: 'Users',
    popular: false,
    buttonText: 'Get Started',
    buttonVariant: 'primary' as const,
    order: 1
  },
  {
    name: 'Plus',
    price: '$15',
    period: 'month',
    description: 'Everything in Basic plus advanced features',
    icon: 'Zap',
    popular: false,
    buttonText: 'Get Started',
    buttonVariant: 'primary' as const,
    order: 2
  },
  {
    name: 'Pro',
    price: '$30',
    period: 'month',
    description: 'Everything in Plus plus automation',
    icon: 'Star',
    popular: true,
    buttonText: 'Get Started',
    buttonVariant: 'primary' as const,
    order: 3
  },
  {
    name: 'Enterprise',
    price: 'Custom',
    period: 'month',
    description: 'Everything in Pro plus enterprise features',
    icon: 'Shield',
    popular: false,
    buttonText: 'Contact Sales',
    buttonVariant: 'secondary' as const,
    order: 4
  }
];
`;

    // Write the TypeScript file
    const outputPath = path.join(process.cwd(), "src/data/pricing-data.ts");

    // Ensure the data directory exists
    const dataDir = path.dirname(outputPath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    fs.writeFileSync(outputPath, tsContent);

    console.log(`✅ Successfully generated pricing data from CSV`);
    console.log(
      `📊 Processed ${transformedFeatures.length} features in ${featureGroups.length} groups`,
    );
    console.log(`📝 Output: ${outputPath}`);
  } catch (error) {
    console.error("❌ Error parsing CSV:", error);
    process.exit(1);
  }
}

parseCsvToTs();
