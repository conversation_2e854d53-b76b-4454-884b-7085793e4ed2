import { useAuth as useC<PERSON>kAuth } from "@clerk/clerk-react";
import { env } from "../config/env";
import type { UserProfile } from "../types/auth.types";

export interface AuthStatus {
  authenticated: boolean;
  onboarded: boolean;
  user?: {
    id: string;
    tenantId: string;
    email: string;
    firstName?: string | null;
    lastName?: string | null;
  } | null;
}

export interface OnboardingData {
  tenantName: string;
  tenantSlug: string;
  firstName?: string;
  lastName?: string;
}

export interface OnboardingResponse {
  user: {
    id: string;
    clerkId: string;
    tenantId: string;
    email: string;
    firstName?: string | null;
    lastName?: string | null;
    imageUrl?: string | null;
    createdAt: string;
    updatedAt: string;
  };
  tenant: {
    id: string;
    name: string;
    slug: string;
    createdAt: string;
    updatedAt: string;
  };
}

export interface AcceptInvitationData {
  invitationToken: string;
  firstName?: string;
  lastName?: string;
  password: string;
}

export interface AcceptInvitationResponse {
  user: {
    id: string;
    clerkId: string;
    tenantId: string;
    email: string;
    firstName?: string | null;
    lastName?: string | null;
    imageUrl?: string | null;
    createdAt: string;
    updatedAt: string;
  };
  invitation: {
    id: string;
    email: string;
    status: string;
    role: {
      name: string;
      type: string;
    };
    tenant: {
      id: string;
      name: string;
      slug: string;
    };
  };
}

/**
 * Auth Service Interface for Dependency Injection
 * Enables clean testing and service substitution
 */
export interface IAuthService {
  checkAuthStatus(): Promise<AuthStatus & { serviceUnavailable?: boolean }>;
  getCurrentUser(): Promise<UserProfile | null>;
  completeOnboarding(data: OnboardingData): Promise<OnboardingResponse>;
  acceptInvitation(
    data: AcceptInvitationData,
  ): Promise<AcceptInvitationResponse>;
  makeRequest<T>(endpoint: string, options?: RequestInit): Promise<T>;
  getToken(): Promise<string | null>;
}

/**
 * Authenticated API service that automatically includes auth tokens
 * Extends the existing API patterns with authentication
 * Note: This is a low-level service that requires explicit token injection
 */
export class AuthApiService {
  private getApiUrl(): string {
    const apiUrl = env.apiUrl;

    if (apiUrl) {
      // Ensure the URL has https:// protocol and doesn't end with a slash
      let fullUrl = apiUrl;
      if (!fullUrl.startsWith("http://") && !fullUrl.startsWith("https://")) {
        fullUrl = `https://${fullUrl}`;
      }
      return fullUrl.replace(/\/$/, "");
    }

    // Fallback for development
    return "http://localhost:8081";
  }

  private async makeAuthenticatedRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    getToken: () => Promise<string | null>,
  ): Promise<T> {
    const baseUrl = this.getApiUrl();
    const url = `${baseUrl}${endpoint}`;

    const token = await getToken();

    const headers = {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({
        error: "Unknown Error",
        message: `Request failed with status ${response.status}`,
        statusCode: response.status,
      }));

      // Create a custom error that includes the status code
      const error = new Error(
        errorData.message || `Request failed with status ${response.status}`,
      ) as Error & { statusCode: number };
      error.statusCode = response.status;
      throw error;
    }

    return response.json();
  }

  async checkAuthStatus(
    getToken: () => Promise<string | null>,
  ): Promise<AuthStatus & { serviceUnavailable?: boolean }> {
    try {
      const response = await this.makeAuthenticatedRequest<{
        data: AuthStatus;
      }>("/api/v1/auth/status", { method: "GET" }, getToken);
      return response.data;
    } catch (error) {
      console.error("Failed to check auth status:", error);

      // Check if this is a service unavailable error (database down)
      if (
        error instanceof Error &&
        "statusCode" in error &&
        (error as Error & { statusCode: number }).statusCode === 503
      ) {
        console.warn(
          "Authentication service unavailable - database connectivity issue",
        );
        return {
          authenticated: false,
          onboarded: false,
          user: null,
          serviceUnavailable: true,
        };
      }

      // Check for 503 in error message as fallback
      if (
        error instanceof Error &&
        (error.message.includes("503") ||
          error.message.includes("Service Unavailable"))
      ) {
        console.warn(
          "Authentication service unavailable - detected from error message",
        );
        return {
          authenticated: false,
          onboarded: false,
          user: null,
          serviceUnavailable: true,
        };
      }

      return {
        authenticated: false,
        onboarded: false,
        user: null,
      };
    }
  }

  async getCurrentUser(
    getToken: () => Promise<string | null>,
  ): Promise<UserProfile | null> {
    const response = await this.makeAuthenticatedRequest<{ data: UserProfile }>(
      "/api/v1/auth/me",
      { method: "GET" },
      getToken,
    );
    return response.data;
  }

  async completeOnboarding(
    data: OnboardingData,
    getToken: () => Promise<string | null>,
  ): Promise<OnboardingResponse> {
    const response = await this.makeAuthenticatedRequest<{
      data: OnboardingResponse;
    }>(
      "/api/v1/auth/onboard",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      getToken,
    );
    return response.data;
  }

  async acceptInvitation(
    data: AcceptInvitationData,
    getToken: () => Promise<string | null>,
  ): Promise<AcceptInvitationResponse> {
    const response = await this.makeAuthenticatedRequest<{
      data: AcceptInvitationResponse;
    }>(
      "/api/v1/auth/accept-invitation",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      getToken,
    );
    return response.data;
  }

  async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    getToken: () => Promise<string | null>,
  ): Promise<T> {
    return this.makeAuthenticatedRequest<T>(endpoint, options, getToken);
  }
}

/**
 * Authenticated API service wrapper that implements IAuthService
 * Automatically handles token injection from Clerk
 */
export class AuthenticatedApiService implements IAuthService {
  private apiService: AuthApiService;
  private getTokenFn: () => Promise<string | null>;

  constructor(
    apiService: AuthApiService,
    getToken: () => Promise<string | null>,
  ) {
    this.apiService = apiService;
    this.getTokenFn = getToken;
  }

  async checkAuthStatus(): Promise<
    AuthStatus & { serviceUnavailable?: boolean }
  > {
    return this.apiService.checkAuthStatus(this.getTokenFn);
  }

  async getCurrentUser(): Promise<UserProfile | null> {
    return this.apiService.getCurrentUser(this.getTokenFn);
  }

  async completeOnboarding(data: OnboardingData): Promise<OnboardingResponse> {
    return this.apiService.completeOnboarding(data, this.getTokenFn);
  }

  async acceptInvitation(
    data: AcceptInvitationData,
  ): Promise<AcceptInvitationResponse> {
    return this.apiService.acceptInvitation(data, this.getTokenFn);
  }

  async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<T> {
    return this.apiService.makeRequest<T>(endpoint, options, this.getTokenFn);
  }

  getToken(): Promise<string | null> {
    return this.getTokenFn();
  }
}

/**
 * Hook to get an authenticated API service instance
 * Automatically handles token injection from Clerk
 */
export function useAuthenticatedApi(): IAuthService {
  const { getToken } = useClerkAuth();
  const apiService = new AuthApiService();

  return new AuthenticatedApiService(apiService, getToken);
}

/**
 * Default service instance for backward compatibility
 */
export const createDefaultAuthService = (): IAuthService => {
  // This will be used in production when no service is injected
  const apiService = new AuthApiService();
  // For default instance, we need to handle token differently
  // This is a fallback that shouldn't be used in normal operation
  const getToken = async () => null;
  return new AuthenticatedApiService(apiService, getToken);
};
