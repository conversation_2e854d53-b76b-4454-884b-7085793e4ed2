import type { HealthResponse } from "../types/api.types";

// Health Service Interface for DI
export interface IHealthService {
  checkHealth(): Promise<HealthResponse>;
}

// Production Health Service Implementation
export class HealthService implements IHealthService {
  private getApiUrl(): string {
    // In production (Render), use VITE_API_URL environment variable
    // In development, use relative URL (handled by Vite proxy)
    const apiUrl = import.meta.env.VITE_API_URL;

    console.log("Environment check:", {
      VITE_API_URL: apiUrl,
      NODE_ENV: import.meta.env.NODE_ENV,
      MODE: import.meta.env.MODE,
      PROD: import.meta.env.PROD,
    });

    if (apiUrl) {
      // Ensure the URL has https:// protocol and doesn't end with a slash
      let fullUrl = apiUrl;
      if (!fullUrl.startsWith("http://") && !fullUrl.startsWith("https://")) {
        fullUrl = `https://${fullUrl}`;
      }
      return fullUrl.replace(/\/$/, "");
    }

    // Fallback: if we're in production but no API URL is set,
    // try to construct it from the current hostname
    if (import.meta.env.PROD && typeof window !== "undefined") {
      const currentHost = window.location.hostname;
      if (currentHost.includes("tech-notes-frontend")) {
        const backendHost = currentHost.replace(
          "tech-notes-frontend",
          "tech-notes-backend",
        );
        console.log("Using fallback backend URL:", `https://${backendHost}`);
        return `https://${backendHost}`;
      }
    }

    // Fallback for development
    return "";
  }

  async checkHealth(): Promise<HealthResponse> {
    const baseUrl = this.getApiUrl();
    const url = `${baseUrl}/api/v1/health`;

    console.log("Making health check request to:", url);

    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Health check failed: ${response.status}`);
    }
    return response.json();
  }
}

// Default instance for backward compatibility
export const healthService = new HealthService();
