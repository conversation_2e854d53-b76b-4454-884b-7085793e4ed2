import { Document } from '@prisma/client';
import { BackendAuthContext } from '@tech-notes/shared';

import { NotFoundError, ValidationError } from '../types/error.types.js';
import { Logger } from '../utils/logger.js';

import { BaseTenantService } from './base-tenant.service.js';
import { PrismaService } from './prisma.service.js';
import { S3Service } from './s3.service.js';

// ===== INTERFACES =====

export interface CreateDocumentData {
  fileName: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
  title?: string;
  description?: string;
}

export interface CreateDocumentRequest extends CreateDocumentData {
  tenantId: string;
  createdBy: string;
}

export interface GetDocumentsOptions {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: 'createdAt' | 'fileName' | 'fileSize';
  sortOrder?: 'asc' | 'desc';
}

export interface GetDocumentsResponse {
  documents: Document[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface DocumentWithUploadUrl extends Document {
  uploadUrl: string;
  s3Key: string;
  expiresIn: number;
}

// ===== FILE VALIDATION CONSTANTS =====

export const ALLOWED_MIME_TYPES: Record<
  string,
  { maxSize: number; extensions: string[] }
> = {
  // PDF files
  'application/pdf': { maxSize: 50 * 1024 * 1024, extensions: ['.pdf'] }, // 50MB

  // Microsoft Office files
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': {
    maxSize: 25 * 1024 * 1024,
    extensions: ['.docx'],
  }, // 25MB
  'application/msword': { maxSize: 25 * 1024 * 1024, extensions: ['.doc'] }, // 25MB
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {
    maxSize: 25 * 1024 * 1024,
    extensions: ['.xlsx'],
  }, // 25MB
  'application/vnd.ms-excel': {
    maxSize: 25 * 1024 * 1024,
    extensions: ['.xls'],
  }, // 25MB

  // Web and data files
  'text/html': { maxSize: 10 * 1024 * 1024, extensions: ['.html', '.htm'] }, // 10MB
  'text/csv': { maxSize: 10 * 1024 * 1024, extensions: ['.csv'] }, // 10MB

  // Image files
  'image/jpeg': { maxSize: 10 * 1024 * 1024, extensions: ['.jpg', '.jpeg'] }, // 10MB
  'image/png': { maxSize: 10 * 1024 * 1024, extensions: ['.png'] }, // 10MB
};

/**
 * Service for managing document operations with S3 integration
 * Extends BaseTenantService for consistent tenant scoping patterns
 */
export class DocumentService extends BaseTenantService {
  private s3Service: S3Service;

  constructor(
    prismaService: PrismaService,
    logger: Logger,
    s3Service: S3Service
  ) {
    super(prismaService, logger);
    this.s3Service = s3Service;
  }

  // ===== PUBLIC METHODS =====

  /**
   * Create a new document with presigned upload URL
   */
  async createDocument(
    request: CreateDocumentRequest,
    BackendAuthContext?: BackendAuthContext
  ): Promise<DocumentWithUploadUrl> {
    this.logOperation('createDocument', {
      fileName: request.fileName,
      fileSize: request.fileSize,
      mimeType: request.mimeType,
      tenantId: request.tenantId,
      createdBy: request.createdBy,
    });

    // Validate file before processing
    this.validateFile(request.fileName, request.fileSize, request.mimeType);

    return await this.withTenantScopeForUser(
      request.tenantId,
      async () => {
        // Create document record in database
        const document = await this.prisma.document.create({
          data: {
            tenantId: request.tenantId,
            fileName: request.fileName,
            originalName: request.originalName,
            fileSize: request.fileSize,
            mimeType: request.mimeType,
            title: request.title,
            description: request.description,
            createdBy: request.createdBy,
            s3Key: '', // Will be updated after S3 key generation
          },
        });

        // Generate S3 presigned upload URL
        const uploadResponse = await this.s3Service.generatePresignedUploadUrl({
          fileName: request.fileName,
          fileSize: request.fileSize,
          mimeType: request.mimeType,
          tenantId: request.tenantId,
          documentId: document.id,
        });

        // Update document with S3 key
        const updatedDocument = await this.prisma.document.update({
          where: { id: document.id },
          data: { s3Key: uploadResponse.s3Key },
        });

        return {
          ...updatedDocument,
          uploadUrl: uploadResponse.uploadUrl,
          s3Key: uploadResponse.s3Key,
          expiresIn: uploadResponse.expiresIn,
        };
      },
      BackendAuthContext
    );
  }

  /**
   * Get documents for a tenant with pagination and filtering
   */
  async getDocuments(
    tenantId: string,
    options: GetDocumentsOptions = {},
    BackendAuthContext?: BackendAuthContext
  ): Promise<GetDocumentsResponse> {
    this.logOperation('getDocuments', { tenantId, options });

    const {
      page = 1,
      limit = 20,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = options;

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Build where clause
        const where: Record<string, unknown> = {
          tenantId,
          deletedAt: null, // Only active documents
        };

        if (search) {
          where.OR = [
            { fileName: { contains: search, mode: 'insensitive' } },
            { originalName: { contains: search, mode: 'insensitive' } },
            { title: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
          ];
        }

        // Get total count for pagination
        const total = await this.prisma.document.count({ where });

        // Get documents with pagination
        const documents = await this.prisma.document.findMany({
          where,
          orderBy: { [sortBy]: sortOrder },
          skip: (page - 1) * limit,
          take: limit,
          include: {
            creator: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        });

        return {
          documents,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
          },
        };
      },
      BackendAuthContext
    );
  }

  /**
   * Generate presigned download URL for a document
   */
  async generateDownloadUrl(
    documentId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<{ downloadUrl: string; expiresIn: number }> {
    this.logOperation('generateDownloadUrl', { documentId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Find document to ensure it exists and belongs to tenant
        const document = await this.prisma.document.findFirst({
          where: {
            id: documentId,
            tenantId,
            deletedAt: null,
          },
        });

        if (!document) {
          throw new NotFoundError('Document not found');
        }

        // Generate presigned download URL using S3 service
        const downloadResponse =
          await this.s3Service.generatePresignedDownloadUrl({
            s3Key: document.s3Key,
            fileName: document.originalName,
          });

        return {
          downloadUrl: downloadResponse.downloadUrl,
          expiresIn: downloadResponse.expiresIn,
        };
      },
      BackendAuthContext
    );
  }

  /**
   * Soft delete a document
   */
  async deleteDocument(
    documentId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<Document> {
    this.logOperation('deleteDocument', { documentId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Find document to ensure it exists and belongs to tenant
        const document = await this.prisma.document.findFirst({
          where: {
            id: documentId,
            tenantId,
            deletedAt: null,
          },
        });

        if (!document) {
          throw new NotFoundError('Document not found');
        }

        // Soft delete the document
        return await this.prisma.document.update({
          where: { id: documentId },
          data: { deletedAt: new Date() },
        });
      },
      BackendAuthContext
    );
  }

  // ===== PRIVATE HELPER METHODS =====

  /**
   * Validate file type, size, and name
   */
  private validateFile(
    fileName: string,
    fileSize: number,
    mimeType: string
  ): void {
    // Basic filename validation first
    if (!fileName || fileName.trim().length === 0) {
      throw new ValidationError('File name cannot be empty');
    }

    if (fileName.length > 255) {
      throw new ValidationError('File name cannot exceed 255 characters');
    }

    // Check if MIME type is allowed
    const allowedType = ALLOWED_MIME_TYPES[mimeType];
    if (!allowedType) {
      throw new ValidationError(
        `File type ${mimeType} is not allowed. Allowed types: ${Object.keys(
          ALLOWED_MIME_TYPES
        ).join(', ')}`
      );
    }

    // Check file size
    if (fileSize > allowedType.maxSize) {
      const maxSizeMB = Math.round(allowedType.maxSize / (1024 * 1024));
      throw new ValidationError(
        `File size ${Math.round(fileSize / (1024 * 1024))}MB exceeds maximum allowed size of ${maxSizeMB}MB for ${mimeType}`
      );
    }

    // Check file extension matches MIME type
    const fileExtension = fileName
      .toLowerCase()
      .substring(fileName.lastIndexOf('.'));
    if (!allowedType.extensions.includes(fileExtension)) {
      throw new ValidationError(
        `File extension ${fileExtension} does not match MIME type ${mimeType}. Expected: ${allowedType.extensions.join(
          ', '
        )}`
      );
    }
  }
}
