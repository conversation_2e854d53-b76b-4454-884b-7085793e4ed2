import { clerkClient } from '@clerk/clerk-sdk-node';
import { ClerkAuthOptions } from '@tech-notes/shared';
import { Request, Response, NextFunction } from 'express';

import {
  IAuthCacheService,
  AuthCacheService,
} from '../services/auth-cache.service.js';
import { PermissionService } from '../services/permission.service.js';
import { PrismaService } from '../services/prisma.service.js';
import { UserEngagementService } from '../services/user-engagement.service.js';
import { UserService } from '../services/user.service.js';
import {
  UnauthorizedError,
  NotFoundError,
  ServiceUnavailableError,
} from '../types/error.types.js';
import { Logger } from '../utils/logger.js';

export interface AuthMiddlewareDependencies {
  userService: UserService;
  prismaService: PrismaService;
  permissionService: PermissionService;
  userEngagementService: UserEngagementService;
  authCacheService: IAuthCacheService;
  logger: Logger;
}

/**
 * Factory function to create Clerk authentication middleware
 */
export function createClerkAuthMiddleware(
  dependencies: AuthMiddlewareDependencies
) {
  const {
    userService,
    prismaService,
    permissionService,
    userEngagementService,
    authCacheService,
    logger,
  } = dependencies;

  return function clerkAuth(options: ClerkAuthOptions = { required: true }) {
    return async (
      req: Request,
      res: Response,
      next: NextFunction
    ): Promise<void> => {
      try {
        const authHeader = req.headers.authorization;

        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          if (options.required) {
            throw new UnauthorizedError('Authentication token is required');
          }
          return next();
        }

        const token = authHeader.substring(7); // Remove 'Bearer ' prefix

        // Verify token with Clerk
        const session = await clerkClient.verifyToken(token);

        if (!session || !session.sub) {
          throw new UnauthorizedError('Invalid authentication token');
        }

        const clerkId = session.sub;

        const cacheKey = AuthCacheService.generateUserCacheKey(clerkId);

        // Try to get user from cache first
        let BackendAuthContext = await authCacheService.get(cacheKey);

        // Check if cached user is active
        if (BackendAuthContext && !BackendAuthContext.isActive) {
          logger.warn(
            'Deactivated user attempted to access application (cached)',
            {
              clerkId,
              userId: BackendAuthContext.id,
              email: BackendAuthContext.email,
            }
          );
          // Clear the cache for deactivated user
          await authCacheService.invalidate(cacheKey);
          throw new UnauthorizedError(
            'Account has been deactivated. Please contact support.'
          );
        }

        if (!BackendAuthContext) {
          // Cache miss - check database connectivity before user lookup
          const dbHealthResult = await prismaService.authHealthCheck();
          if (!dbHealthResult.healthy) {
            logger.error(
              'Database connectivity check failed during authentication',
              {
                error: dbHealthResult.error,
                clerkId,
              }
            );
            throw new ServiceUnavailableError(
              'Authentication service requires database connectivity'
            );
          }

          // Database is healthy, proceed with user lookup (with roles)
          const user = await userService.getUserByClerkIdWithRoles(clerkId);

          if (!user) {
            logger.warn('Authenticated user not found in database', {
              clerkId,
            });
            throw new NotFoundError(
              'User not found. Please complete onboarding.'
            );
          }

          // Check if user is active
          if (!user.isActive) {
            logger.warn('Deactivated user attempted to access application', {
              clerkId,
              userId: user.id,
              email: user.email,
            });
            throw new UnauthorizedError(
              'Account has been deactivated. Please contact support.'
            );
          }

          // Determine if user can bypass tenant scope (System Admin)
          const canBypassTenantScope = user.userRoles.some(
            (userRole) => userRole.role.isSystemRole
          );

          // Create temporary auth context for permission lookup
          const tempAuthContext = {
            id: user.id,
            clerkId: user.clerkId,
            tenantId: user.tenantId,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            imageUrl: user.imageUrl,
            isActive: user.isActive,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            roles: user.userRoles,
            permissions: [], // Will be populated below
            canBypassTenantScope,
          };

          // Get actual permissions from database via PermissionService
          const uniquePermissions = await permissionService.getUserPermissions(
            user.id,
            tempAuthContext
          );

          // Create auth context with RBAC data and cache it
          BackendAuthContext = {
            id: user.id,
            clerkId: user.clerkId,
            tenantId: user.tenantId,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            imageUrl: user.imageUrl,
            isActive: user.isActive,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            // RBAC Extensions
            roles: user.userRoles,
            permissions: uniquePermissions,
            canBypassTenantScope,
          };

          // Cache for 5 minutes
          await authCacheService.set(cacheKey, BackendAuthContext, 300);

          // Track login for new sessions (cache miss indicates new session)
          // Do this asynchronously to not block authentication
          userEngagementService
            .updateLastLogin(user.id, user.tenantId, BackendAuthContext)
            .catch((error) => {
              logger.error('Failed to track user login', {
                error: error instanceof Error ? error.message : 'Unknown error',
                userId: user.id,
                tenantId: user.tenantId,
                clerkId: user.clerkId,
              });
            });

          logger.debug('User login tracked', {
            userId: user.id,
            tenantId: user.tenantId,
            email: user.email,
          });
        }

        // Set user context on request
        (req as any).user = BackendAuthContext;

        logger.debug('User authenticated successfully', {
          userId: BackendAuthContext.id,
          tenantId: BackendAuthContext.tenantId,
          email: BackendAuthContext.email,
          roleCount: BackendAuthContext.roles?.length || 0,
          canBypassTenantScope:
            BackendAuthContext.canBypassTenantScope || false,
          cached: !!BackendAuthContext,
        });

        next();
      } catch (error) {
        logger.error('Authentication failed', {
          error: error instanceof Error ? error.message : 'Unknown error',
          path: req.path,
          method: req.method,
        });

        if (error instanceof UnauthorizedError) {
          res.status(401).json({
            error: 'Unauthorized',
            message: error.message,
            statusCode: 401,
            timestamp: new Date().toISOString(),
          });
          return;
        }

        if (error instanceof NotFoundError) {
          res.status(404).json({
            error: 'User Not Found',
            message: error.message,
            statusCode: 404,
            timestamp: new Date().toISOString(),
          });
          return;
        }

        if (error instanceof ServiceUnavailableError) {
          res.status(503).json({
            error: 'Service Unavailable',
            message: error.message,
            statusCode: 503,
            timestamp: new Date().toISOString(),
          });
          return;
        }

        // Check if this is a database connectivity error
        if (
          error instanceof Error &&
          (error.message.includes('connect') ||
            error.message.includes('ECONNREFUSED') ||
            error.message.includes('timeout') ||
            error.message.includes('database'))
        ) {
          res.status(503).json({
            error: 'Service Unavailable',
            message: 'Authentication service requires database connectivity',
            statusCode: 503,
            timestamp: new Date().toISOString(),
          });
          return;
        }

        res.status(500).json({
          error: 'Internal Server Error',
          message: 'Authentication service error',
          statusCode: 500,
          timestamp: new Date().toISOString(),
        });
      }
    };
  };
}

/**
 * Convenience middleware for required authentication
 */
export function createRequireAuthMiddleware(
  dependencies: AuthMiddlewareDependencies
) {
  const clerkAuth = createClerkAuthMiddleware(dependencies);
  return clerkAuth({ required: true });
}

/**
 * Convenience middleware for optional authentication
 */
export function createOptionalAuthMiddleware(
  dependencies: AuthMiddlewareDependencies
) {
  const clerkAuth = createClerkAuthMiddleware(dependencies);
  return clerkAuth({ required: false });
}
