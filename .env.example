# Root .env.example file with all configurations

# Development Environment
NODE_ENV=development

# Backend Configuration
PORT=8080
CORS_ORIGIN=http://localhost:5173,http://localhost:5174,http://localhost:4173
FRONTEND_URL=http://localhost:5173

# Database Configuration
# Local development (Docker)
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/tech_notes_dev"
DIRECT_URL="postgresql://postgres:postgres@localhost:5432/tech_notes_dev"

# Test database (Docker - separate port)
TEST_DATABASE_URL="postgresql://postgres:postgres@localhost:5433/tech_notes_test"

# Ephemeral test database admin connection (for creating/destroying test databases)
TEST_DATABASE_ADMIN_URL="postgresql://admin_user:<EMAIL>:5432/postgres"

# Authentication (Clerk)
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key_here
CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key_here

# Payments (Stripe)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# AWS S3 Configuration
AWS_REGION=us-east-2
AWS_S3_BUCKET_NAME=tech-notes-documents-dev
AWS_ACCESS_KEY_ID=your_aws_access_key_id_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key_here

# Frontend Configuration
VITE_API_URL=http://localhost:8080
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key_here
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here