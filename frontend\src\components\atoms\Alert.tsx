import React from "react";
import { clsx } from "clsx";
import { Info, CheckCircle, AlertTriangle, XCircle, X } from "lucide-react";

export interface AlertProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "info" | "success" | "warning" | "error";
  title?: string;
  dismissible?: boolean;
  onDismiss?: () => void;
  children: React.ReactNode;
}

const variantConfig = {
  info: {
    containerClass:
      "bg-gradient-to-r from-primary-50 to-primary-50/80 border-primary-200 shadow-xs ring-1 ring-primary-200/50",
    iconClass: "text-primary-600",
    titleClass: "text-primary-900 font-semibold tracking-tight",
    textClass: "text-primary-800",
    icon: Info,
  },
  success: {
    containerClass:
      "bg-gradient-to-r from-success-50 to-success-50/80 border-success-200 shadow-xs ring-1 ring-success-200/50",
    iconClass: "text-success-600",
    titleClass: "text-success-900 font-semibold tracking-tight",
    textClass: "text-success-800",
    icon: CheckCircle,
  },
  warning: {
    containerClass:
      "bg-gradient-to-r from-warning-50 to-warning-50/80 border-warning-200 shadow-xs ring-1 ring-warning-200/50",
    iconClass: "text-warning-600",
    titleClass: "text-warning-900 font-semibold tracking-tight",
    textClass: "text-warning-800",
    icon: AlertTriangle,
  },
  error: {
    containerClass:
      "bg-gradient-to-r from-error-50 to-error-50/80 border-error-200 shadow-xs ring-1 ring-error-200/50",
    iconClass: "text-error-600",
    titleClass: "text-error-900 font-semibold tracking-tight",
    textClass: "text-error-800",
    icon: XCircle,
  },
};

export const Alert: React.FC<AlertProps> = ({
  variant = "info",
  title,
  dismissible = false,
  onDismiss,
  className,
  children,
  ...props
}) => {
  const config = variantConfig[variant];
  const IconComponent = config.icon;

  return (
    <div
      className={clsx(
        "border rounded-xl p-5 flex items-start gap-4 transition-all duration-300",
        config.containerClass,
        className,
      )}
      {...props}
    >
      <IconComponent
        className={clsx("h-5 w-5 mt-0.5 flex-shrink-0", config.iconClass)}
      />
      <div className="flex-1 space-y-1">
        {title && (
          <h4 className={clsx("text-sm", config.titleClass)}>{title}</h4>
        )}
        <div className={clsx("text-sm leading-relaxed", config.textClass)}>
          {children}
        </div>
      </div>
      {dismissible && onDismiss && (
        <button
          onClick={onDismiss}
          className={clsx(
            "hover:opacity-75 focus:outline-none focus:ring-2 focus:ring-current focus:ring-opacity-20 rounded-md p-1 -m-1 transition-all duration-200",
            config.iconClass,
          )}
          aria-label="Dismiss"
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};
