import { VehicleYearV3 } from '@prisma/client';
import { BackendAuthContext } from '@tech-notes/shared';

import { NotFoundError } from '../../types/error.types.js';
import { Logger } from '../../utils/logger.js';
import { PrismaService } from '../prisma.service.js';

import { BaseVehicleHierarchyV3Service } from './base-vehicle-hierarchy-v3.service.js';

export interface CreateYearV3Data {
  name: string;
}

export interface UpdateYearV3Data {
  name?: string;
  isActive?: boolean;
  displayOrder?: number;
}

/**
 * Service for managing Vehicle Year V3 operations
 * Handles CRUD operations for years with tenant isolation
 */
export class VehicleYearV3Service extends BaseVehicleHierarchyV3Service {
  constructor(prismaService: PrismaService, logger: Logger) {
    super(prismaService, logger);
  }

  /**
   * Get all years for a tenant
   */
  async getYearsByTenant(
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleYearV3[]> {
    this.logOperation('getYearsByTenant', { tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleYearV3.findMany({
          where: this.getActiveEntityWhere({ tenantId }),
          orderBy: this.getStandardOrderBy(),
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Get a specific year by ID
   */
  async getYearById(
    yearId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleYearV3 | null> {
    this.logOperation('getYearById', { yearId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleYearV3.findFirst({
          where: this.getActiveEntityWhere({
            id: yearId,
            tenantId,
          }),
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Create a new year
   */
  async createYear(
    yearData: CreateYearV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleYearV3> {
    this.logOperation('createYear', {
      name: yearData.name,
      tenantId,
    });

    const trimmedName = this.validateAndTrimName(yearData.name, 'Year');

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Get next display order for this tenant
          const nextDisplayOrder = await this.getNextDisplayOrder(
            this.prisma.vehicleYearV3,
            { tenantId }
          );

          return await this.prisma.vehicleYearV3.create({
            data: {
              name: trimmedName,
              tenantId,
              displayOrder: nextDisplayOrder,
            },
          });
        } catch (error) {
          this.handleUniqueConstraintError(error, trimmedName, 'Year');
          this.logError('createYear', error as Error, {
            yearData,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Update a year
   */
  async updateYear(
    yearId: string,
    updateData: UpdateYearV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleYearV3> {
    this.logOperation('updateYear', { yearId, updateData, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Prepare update data
          const dataToUpdate: Record<string, unknown> = {};

          if (updateData.name !== undefined) {
            dataToUpdate.name = this.validateAndTrimName(
              updateData.name,
              'Year'
            );
          }

          if (updateData.isActive !== undefined) {
            dataToUpdate.isActive = updateData.isActive;
          }

          if (updateData.displayOrder !== undefined) {
            dataToUpdate.displayOrder = updateData.displayOrder;
          }

          return await this.prisma.vehicleYearV3.update({
            where: {
              id: yearId,
              tenantId,
              deletedAt: null,
            },
            data: dataToUpdate,
          });
        } catch (error) {
          this.handleNotFoundError(error, yearId, 'Year');
          this.handleUniqueConstraintError(
            error,
            updateData.name || '',
            'Year'
          );
          this.logError('updateYear', error as Error, {
            yearId,
            updateData,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Soft delete a year
   */
  async deleteYear(
    yearId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('deleteYear', { yearId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          await this.executeSoftDelete(this.prisma.vehicleYearV3, yearId, {
            tenantId,
          });
        } catch (error) {
          this.handleNotFoundError(error, yearId, 'Year');
          this.logError('deleteYear', error as Error, {
            yearId,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Reorder years within a tenant
   */
  async reorderYears(
    yearOrders: { id: string; displayOrder: number }[],
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('reorderYears', {
      yearOrders,
      tenantId,
    });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          await this.executeReorder('vehicleYearV3', yearOrders, {
            tenantId,
          });
        } catch (error) {
          this.logError('reorderYears', error as Error, {
            yearOrders,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Validate that a year exists and belongs to the tenant
   * Used by other services for relationship validation
   */
  async validateYearOwnership(yearId: string, tenantId: string): Promise<void> {
    const year = await this.prisma.vehicleYearV3.findFirst({
      where: this.getActiveEntityWhere({
        id: yearId,
        tenantId,
      }),
      select: { id: true },
    });

    if (!year) {
      throw new NotFoundError(`Year ${yearId} not found or not accessible`);
    }
  }
}
