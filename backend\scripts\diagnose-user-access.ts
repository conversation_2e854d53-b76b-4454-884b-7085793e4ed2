#!/usr/bin/env ts-node
/**
 * <PERSON><PERSON><PERSON> to diagnose user management access issues
 * Usage: npm run diagnose-user-access <clerk-user-id>
 *
 * This script will:
 * 1. Check if user exists in database
 * 2. Check user's roles and permissions
 * 3. Verify canBypassTenantScope logic
 * 4. Suggest fixes if issues are found
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function diagnoseUserAccess() {
  const clerkId = process.argv[2];

  if (!clerkId) {
    console.error('❌ Usage: npm run diagnose-user-access <clerk-user-id>');
    console.error(
      '   Example: npm run diagnose-user-access user_2abc123def456'
    );
    console.error('');
    console.error('   To find the Clerk user ID:');
    console.error('   1. Have the user log in to the application');
    console.error('   2. Check the users table in the database');
    console.error('   3. Use the clerkId from the user record');
    process.exit(1);
  }

  try {
    console.log('🔍 Diagnosing user access for Clerk ID:', clerkId);
    console.log('');

    // 1. Check if user exists
    console.log('👤 Step 1: Checking if user exists...');
    const user = await prisma.user.findUnique({
      where: { clerkId },
      include: {
        userRoles: {
          include: {
            role: true,
            tenant: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        },
        tenant: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    if (!user) {
      console.error('❌ User with Clerk ID', clerkId, 'not found.');
      console.error('   Make sure the user has logged in at least once.');
      process.exit(1);
    }

    console.log('✅ User found:');
    console.log(`   ID: ${user.id}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Name: ${user.firstName} ${user.lastName}`);
    console.log(`   Active: ${user.isActive}`);
    console.log(`   Tenant: ${user.tenant?.name} (${user.tenant?.slug})`);
    console.log('');

    // 2. Check user's roles
    console.log('🔐 Step 2: Checking user roles...');
    if (user.userRoles.length === 0) {
      console.log('❌ User has NO roles assigned!');
      console.log('   This is why they cannot access user management.');
      console.log('');
      console.log('🔧 Suggested fix:');
      console.log('   Run: npm run assign-system-admin', clerkId);
      process.exit(1);
    }

    console.log(`✅ User has ${user.userRoles.length} role(s):`);
    user.userRoles.forEach((userRole, index) => {
      console.log(
        `   ${index + 1}. ${userRole.role.name} (${userRole.role.type})`
      );
      console.log(`      System Role: ${userRole.role.isSystemRole}`);
      console.log(`      Tenant Scope: ${userRole.tenantId || 'Global'}`);
      if (userRole.tenant) {
        console.log(
          `      Tenant: ${userRole.tenant.name} (${userRole.tenant.slug})`
        );
      }
    });
    console.log('');

    // 3. Check canBypassTenantScope logic
    console.log('🌐 Step 3: Checking tenant scope bypass...');
    const canBypassTenantScope = user.userRoles.some(
      (userRole) => userRole.role.isSystemRole
    );

    if (canBypassTenantScope) {
      console.log('✅ User CAN bypass tenant scope (System Admin)');
      console.log('   This user should have access to user management.');
    } else {
      console.log('❌ User CANNOT bypass tenant scope');
      console.log(
        '   User needs System Admin role for global user management.'
      );
      console.log('');
      console.log('🔧 Suggested fix:');
      console.log('   Run: npm run assign-system-admin', clerkId);
    }
    console.log('');

    // 4. Check permissions
    console.log('🔑 Step 4: Checking permissions...');
    const permissions: string[] = [];

    user.userRoles.forEach((userRole) => {
      if (userRole.role.isSystemRole) {
        permissions.push(
          'system:manage',
          'tenant:manage',
          'user:manage',
          'data:read',
          'data:write'
        );
      } else if (userRole.role.type === 'COMPANY_ADMIN') {
        permissions.push(
          'tenant:read',
          'tenant:write',
          'user:manage',
          'data:read',
          'data:write'
        );
      } else if (userRole.role.type === 'COMPANY_TECH') {
        permissions.push('tenant:read', 'user:read', 'data:read', 'data:write');
      }
    });

    const uniquePermissions = [...new Set(permissions)];

    if (uniquePermissions.includes('user:manage')) {
      console.log('✅ User has user:manage permission');
    } else {
      console.log('❌ User does NOT have user:manage permission');
    }

    console.log('   All permissions:', uniquePermissions.join(', '));
    console.log('');

    // 5. Summary and recommendations
    console.log('📋 Summary:');
    if (canBypassTenantScope && uniquePermissions.includes('user:manage')) {
      console.log('✅ User should have access to user management');
      console.log('   If they still get permission errors, check:');
      console.log('   1. Frontend auth token is valid');
      console.log('   2. Backend auth middleware is working');
      console.log('   3. Permission middleware is configured correctly');
    } else {
      console.log('❌ User access issues found:');
      if (!canBypassTenantScope) {
        console.log('   - Missing System Admin role');
      }
      if (!uniquePermissions.includes('user:manage')) {
        console.log('   - Missing user:manage permission');
      }
      console.log('');
      console.log('🔧 To fix:');
      console.log('   Run: npm run assign-system-admin', clerkId);
    }

    // 6. Check system admin role exists
    console.log('');
    console.log('🔍 Step 5: Checking system admin role exists...');
    const systemAdminRole = await prisma.role.findFirst({
      where: { type: 'SYSTEM_ADMIN' },
    });

    if (systemAdminRole) {
      console.log('✅ System Admin role exists in database');
    } else {
      console.log('❌ System Admin role NOT found in database!');
      console.log('   Database migrations may not have been applied.');
      console.log('   Run: npx prisma migrate deploy');
    }
  } catch (error) {
    console.error('❌ Failed to diagnose user access:', error);
    process.exit(1);
  }
}

diagnoseUserAccess().finally(async () => {
  await prisma.$disconnect();
});
