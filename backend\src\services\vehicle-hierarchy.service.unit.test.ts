import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { BackendAuthContext } from '@tech-notes/shared';

import {
  NotFoundError,
  ConflictError,
  ValidationError,
} from '../types/error.types.js';
import { Logger } from '../utils/logger.js';

import { PrismaService } from './prisma.service.js';
import { VehicleHierarchyService } from './vehicle-hierarchy.service.js';

// Mock Prisma Client
const mockPrisma = {
  vehicleYear: {
    create: jest.fn(),
    findMany: jest.fn(),
    findFirst: jest.fn(),
    findUnique: jest.fn(),
    delete: jest.fn(),
  },
  vehicleMake: {
    create: jest.fn(),
    findMany: jest.fn(),
    findFirst: jest.fn(),
    delete: jest.fn(),
  },
  vehicleModel: {
    create: jest.fn(),
    findMany: jest.fn(),
    findFirst: jest.fn(),
    delete: jest.fn(),
  },
  vehicleModelYear: {
    create: jest.fn(),
    findFirst: jest.fn(),
    delete: jest.fn(),
  },
  tenant: {
    findUnique: jest.fn(),
  },
  user: {
    findFirst: jest.fn(),
  },
  $transaction: jest.fn(),
};

// Mock services
const mockPrismaService = {
  prisma: mockPrisma,
} as unknown as PrismaService;

const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  verbose: jest.fn(),
  silly: jest.fn(),
  log: jest.fn(),
  query: jest.fn(),
} as unknown as Logger;

describe('VehicleHierarchyService', () => {
  let service: VehicleHierarchyService;
  const testTenantId = 'test-tenant-id';
  const testAuthContext: BackendAuthContext = {
    id: 'test-user-id',
    clerkId: 'test-clerk-id',
    tenantId: testTenantId,
    email: '<EMAIL>',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    canBypassTenantScope: false,
  };

  beforeEach(() => {
    service = new VehicleHierarchyService(mockPrismaService, mockLogger);
    jest.clearAllMocks();

    // Default tenant validation mock
    mockPrisma.tenant.findUnique.mockResolvedValue({ id: testTenantId });
    // Default user validation mock
    mockPrisma.user.findFirst.mockResolvedValue({
      id: 'test-user-id',
      clerkId: 'test-clerk-id',
      tenantId: testTenantId,
    });
  });

  describe('Year Operations', () => {
    describe('createYear', () => {
      it('should create a new year successfully', async () => {
        const yearData = { year: 2024 };
        const expectedYear = {
          id: 'year-id',
          year: 2024,
          tenantId: testTenantId,
        };

        mockPrisma.vehicleYear.create.mockResolvedValue(expectedYear);

        const result = await service.createYear(
          yearData,
          testTenantId,
          testAuthContext
        );

        expect(mockPrisma.vehicleYear.create).toHaveBeenCalledWith({
          data: { year: 2024, tenantId: testTenantId },
        });
        expect(result).toEqual(expectedYear);
      });

      it('should throw ValidationError for invalid year range', async () => {
        const yearData = { year: 1800 };

        await expect(
          service.createYear(yearData, testTenantId, testAuthContext)
        ).rejects.toThrow(ValidationError);
        await expect(
          service.createYear(yearData, testTenantId, testAuthContext)
        ).rejects.toThrow('Year must be between 1900 and 2050');
      });

      it('should throw ConflictError for duplicate year', async () => {
        const yearData = { year: 2024 };
        const prismaError = new PrismaClientKnownRequestError(
          'Unique constraint violation',
          {
            code: 'P2002',
            clientVersion: '5.0.0',
          }
        );

        mockPrisma.vehicleYear.create.mockRejectedValue(prismaError);

        await expect(
          service.createYear(yearData, testTenantId, testAuthContext)
        ).rejects.toThrow(ConflictError);
      });
    });

    describe('getYearsByTenant', () => {
      it('should return years for tenant', async () => {
        const expectedYears = [
          { id: 'year1', year: 2024, tenantId: testTenantId },
          { id: 'year2', year: 2023, tenantId: testTenantId },
        ];

        mockPrisma.vehicleYear.findMany.mockResolvedValue(expectedYears);

        const result = await service.getYearsByTenant(
          testTenantId,
          testAuthContext
        );

        expect(mockPrisma.vehicleYear.findMany).toHaveBeenCalledWith({
          where: { tenantId: testTenantId },
          orderBy: { year: 'desc' },
        });
        expect(result).toEqual(expectedYears);
      });
    });

    describe('createYearRange', () => {
      it('should create multiple years in range', async () => {
        const expectedYears = [
          { id: 'year1', year: 2022, tenantId: testTenantId },
          { id: 'year2', year: 2023, tenantId: testTenantId },
          { id: 'year3', year: 2024, tenantId: testTenantId },
        ];

        // Mock the findMany call to check for existing years (return empty array = no existing years)
        mockPrisma.vehicleYear.findMany.mockResolvedValue([]);

        mockPrisma.$transaction.mockImplementation(async (callback) => {
          const mockTx = {
            vehicleYear: {
              create: jest
                .fn()
                .mockResolvedValueOnce(expectedYears[0])
                .mockResolvedValueOnce(expectedYears[1])
                .mockResolvedValueOnce(expectedYears[2]),
            },
          };
          return await callback(mockTx);
        });

        const result = await service.createYearRange(
          2022,
          2024,
          testTenantId,
          testAuthContext
        );

        expect(result).toHaveLength(3);
        expect(result).toEqual(expectedYears);
      });

      it('should throw ValidationError for invalid range', async () => {
        await expect(
          service.createYearRange(2024, 2022, testTenantId, testAuthContext)
        ).rejects.toThrow(ValidationError);
        await expect(
          service.createYearRange(2024, 2022, testTenantId, testAuthContext)
        ).rejects.toThrow('Start year must be less than or equal to end year');
      });

      it('should throw ValidationError for range too large', async () => {
        await expect(
          service.createYearRange(2000, 2100, testTenantId, testAuthContext)
        ).rejects.toThrow(ValidationError);
        await expect(
          service.createYearRange(2000, 2100, testTenantId, testAuthContext)
        ).rejects.toThrow('Year range cannot exceed 50 years');
      });
    });
  });

  describe('Make Operations', () => {
    describe('createMake', () => {
      it('should create a new make successfully', async () => {
        const makeData = { name: 'Rockwood GeoPro' };
        const expectedMake = {
          id: 'make-id',
          name: 'Rockwood GeoPro',
          tenantId: testTenantId,
        };

        mockPrisma.vehicleMake.create.mockResolvedValue(expectedMake);

        const result = await service.createMake(
          makeData,
          testTenantId,
          testAuthContext
        );

        expect(mockPrisma.vehicleMake.create).toHaveBeenCalledWith({
          data: { name: 'Rockwood GeoPro', tenantId: testTenantId },
        });
        expect(result).toEqual(expectedMake);
      });

      it('should throw ValidationError for empty name', async () => {
        const makeData = { name: '   ' };

        await expect(
          service.createMake(makeData, testTenantId, testAuthContext)
        ).rejects.toThrow(ValidationError);
        await expect(
          service.createMake(makeData, testTenantId, testAuthContext)
        ).rejects.toThrow('Make name cannot be empty');
      });

      it('should throw ConflictError for duplicate make', async () => {
        const makeData = { name: 'Rockwood GeoPro' };
        const prismaError = new PrismaClientKnownRequestError(
          'Unique constraint violation',
          {
            code: 'P2002',
            clientVersion: '5.0.0',
          }
        );

        mockPrisma.vehicleMake.create.mockRejectedValue(prismaError);

        await expect(
          service.createMake(makeData, testTenantId, testAuthContext)
        ).rejects.toThrow(ConflictError);
      });
    });

    describe('getMakesByTenant', () => {
      it('should return makes for tenant', async () => {
        const expectedMakes = [
          { id: 'make1', name: 'Forest River', tenantId: testTenantId },
          { id: 'make2', name: 'Rockwood GeoPro', tenantId: testTenantId },
        ];

        mockPrisma.vehicleMake.findMany.mockResolvedValue(expectedMakes);

        const result = await service.getMakesByTenant(
          testTenantId,
          testAuthContext
        );

        expect(mockPrisma.vehicleMake.findMany).toHaveBeenCalledWith({
          where: { tenantId: testTenantId },
          orderBy: { name: 'asc' },
        });
        expect(result).toEqual(expectedMakes);
      });
    });
  });

  describe('Model Operations', () => {
    describe('createModel', () => {
      it('should create a new model successfully', async () => {
        const modelData = { name: 'G20BHS', makeId: 'make-id' };
        const expectedModel = {
          id: 'model-id',
          name: 'G20BHS',
          makeId: 'make-id',
          tenantId: testTenantId,
          isActive: true,
        };

        // Mock make validation
        mockPrisma.vehicleMake.findFirst.mockResolvedValue({
          id: 'make-id',
          tenantId: testTenantId,
        });
        mockPrisma.vehicleModel.create.mockResolvedValue(expectedModel);

        const result = await service.createModel(
          modelData,
          testTenantId,
          testAuthContext
        );

        expect(mockPrisma.vehicleMake.findFirst).toHaveBeenCalledWith({
          where: { id: 'make-id', tenantId: testTenantId },
        });
        expect(mockPrisma.vehicleModel.create).toHaveBeenCalledWith({
          data: {
            name: 'G20BHS',
            makeId: 'make-id',
            tenantId: testTenantId,
            isActive: true,
          },
        });
        expect(result).toEqual(expectedModel);
      });

      it('should throw ValidationError for empty model name', async () => {
        const modelData = { name: '', makeId: 'make-id' };

        await expect(
          service.createModel(modelData, testTenantId, testAuthContext)
        ).rejects.toThrow(ValidationError);
        await expect(
          service.createModel(modelData, testTenantId, testAuthContext)
        ).rejects.toThrow('Model name cannot be empty');
      });

      it('should throw NotFoundError for invalid make', async () => {
        const modelData = { name: 'G20BHS', makeId: 'invalid-make-id' };

        mockPrisma.vehicleMake.findFirst.mockResolvedValue(null);

        await expect(
          service.createModel(modelData, testTenantId, testAuthContext)
        ).rejects.toThrow(NotFoundError);
        await expect(
          service.createModel(modelData, testTenantId, testAuthContext)
        ).rejects.toThrow('Make not found or does not belong to tenant');
      });
    });
  });

  describe('Tenant Isolation', () => {
    it('should validate tenant access for all operations', async () => {
      mockPrisma.tenant.findUnique.mockResolvedValue(null);

      await expect(
        service.getYearsByTenant('invalid-tenant', testAuthContext)
      ).rejects.toThrow(NotFoundError);

      expect(mockPrisma.tenant.findUnique).toHaveBeenCalledWith({
        where: { id: 'invalid-tenant' },
        select: { id: true },
      });
    });
  });
});
