import { useState, useEffect, useCallback } from "react";
import { useSearchParams } from "react-router-dom";
import {
  useUser,
  useSignUp,
  useAuth as useClerkAuth,
} from "@clerk/clerk-react";
import { Button } from "../atoms/Button";
import toast from "react-hot-toast";

interface InvitationFormData {
  firstName: string;
  lastName: string;
  username: string;
  password: string;
}

export function InvitationAcceptance() {
  const [searchParams] = useSearchParams();
  const clerkTicket = searchParams.get("__clerk_ticket"); // Clerk's invitation ticket
  const invitationId = searchParams.get("invitation"); // Our internal invitation ID
  const { isSignedIn, user: clerkUser } = useUser();
  const { isLoaded, signUp, setActive } = useSignUp();
  const { getToken } = useClerkAuth();
  const [formData, setFormData] = useState<InvitationFormData>({
    firstName: "",
    lastName: "",
    username: "",
    password: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Complete backend invitation acceptance after Clerk signup
  const completeBackendInvitation = useCallback(
    async (firstName: string, lastName: string) => {
      try {
        if (!invitationId) {
          console.warn(
            "No invitation ID found, skipping backend invitation acceptance",
          );
          return;
        }

        const response = await fetch(
          `${import.meta.env.VITE_API_URL}/api/v1/auth/accept-invitation`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${await getToken()}`,
            },
            body: JSON.stringify({
              invitationToken: invitationId,
              firstName,
              lastName,
            }),
          },
        );

        if (!response.ok) {
          const errorData = await response.json();

          // If the invitation was already accepted, that's actually success - don't show error
          if (
            errorData.message?.includes("already exists") ||
            errorData.message?.includes("already accepted")
          ) {
            console.log(
              "Backend invitation was already processed - this is expected",
            );
            return;
          }

          throw new Error(
            errorData.message || "Failed to complete backend invitation",
          );
        }

        console.log("Backend invitation acceptance completed successfully");
      } catch (error) {
        console.error("Failed to complete backend invitation:", error);

        // Check if this is an "already exists" error which is actually success
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        if (
          errorMessage.includes("already exists") ||
          errorMessage.includes("already accepted")
        ) {
          console.log(
            "User already exists - invitation was already processed successfully",
          );
          return;
        }

        // Don't throw here - we don't want to break the flow if backend fails
        // The user is already signed in with Clerk
        toast.error(
          "Account created but there was an issue completing setup. Please contact support if you experience any issues.",
        );
      }
    },
    [invitationId, getToken],
  );

  // Handle signed-in users visiting this page
  useEffect(() => {
    if (isSignedIn && clerkUser?.id && !isSubmitting) {
      // User is already signed in with Clerk
      // Check if they have a database record by trying to complete backend invitation
      // This handles the case where Clerk signup completed but backend user creation failed
      // Only run this if we're not currently submitting the form to avoid double-processing
      if (invitationId) {
        console.log(
          "User is signed in but visiting invitation page - attempting to complete backend setup",
        );
        completeBackendInvitation(
          clerkUser.firstName || "",
          clerkUser.lastName || "",
        )
          .then(() => {
            // After backend completion, redirect to app
            setTimeout(() => {
              window.location.href = "/app";
            }, 1000);
          })
          .catch(() => {
            // If backend completion fails, still redirect to app
            // The auth system will handle the missing user case
            setTimeout(() => {
              window.location.href = "/app";
            }, 1000);
          });
      } else {
        // No invitation ID, just redirect to app
        window.location.href = "/app";
      }
    }
  }, [
    isSignedIn,
    clerkUser,
    invitationId,
    completeBackendInvitation,
    isSubmitting,
  ]);

  // If Clerk is still loading, show loading state
  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // If there is no Clerk ticket, show error
  if (!clerkTicket) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Invalid Invitation
          </h2>
          <p className="text-gray-600 mb-6">
            This invitation link is invalid or has expired. Please contact your
            administrator for a new invitation.
          </p>

          <Button
            onClick={() => (window.location.href = "/")}
            variant="primary"
          >
            Return to Home
          </Button>
        </div>
      </div>
    );
  }

  // Handle form submission using Clerk's invitation flow
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isLoaded || !signUp) return;

    setIsSubmitting(true);

    try {
      // Create the sign-up with ticket, password, and username
      // Based on the missing requirements, Clerk needs username
      const signUpAttempt = await signUp.create({
        strategy: "ticket",
        ticket: clerkTicket,
        username: formData.username,
        password: formData.password,
      });

      // If the sign-up was completed, set the session to active
      if (signUpAttempt.status === "complete") {
        await setActive({ session: signUpAttempt.createdSessionId });

        // Complete backend invitation acceptance
        await completeBackendInvitation(formData.firstName, formData.lastName);

        toast.success(
          "Welcome! Your invitation has been accepted successfully.",
        );
        // Redirect to app after successful acceptance
        setTimeout(() => {
          window.location.href = "/app";
        }, 1500);
      } else if (signUpAttempt.status === "missing_requirements") {
        // Check what fields are missing and try to provide them
        console.log("Missing requirements:", signUpAttempt.missingFields);
        console.log("Unverified fields:", signUpAttempt.unverifiedFields);

        // Try to update with any remaining missing information
        // Avoid firstName/lastName due to parameter transformation issues
        try {
          // Only update if username is still missing
          const updateData: Record<string, unknown> = {};
          if (signUpAttempt.missingFields.includes("username")) {
            updateData.username = formData.username;
          }

          const updatedSignUp = await signUpAttempt.update(updateData);

          if (updatedSignUp.status === "complete") {
            await setActive({ session: updatedSignUp.createdSessionId });

            // Complete backend invitation acceptance
            await completeBackendInvitation(
              formData.firstName,
              formData.lastName,
            );

            toast.success(
              "Welcome! Your invitation has been accepted successfully.",
            );
            setTimeout(() => {
              window.location.href = "/app";
            }, 1500);
          } else {
            console.error(
              "Sign-up still not complete after update:",
              JSON.stringify(updatedSignUp, null, 2),
            );
            toast.error(
              "There was an issue completing your sign-up. Please try again.",
            );
          }
        } catch (updateErr: unknown) {
          console.error(
            "Error updating sign-up:",
            JSON.stringify(updateErr, null, 2),
          );
          // If update fails, still try to set the session active if we have one
          if (signUpAttempt.createdSessionId) {
            await setActive({ session: signUpAttempt.createdSessionId });

            // Complete backend invitation acceptance
            await completeBackendInvitation(
              formData.firstName,
              formData.lastName,
            );

            toast.success(
              "Welcome! Your invitation has been accepted successfully.",
            );
            setTimeout(() => {
              window.location.href = "/app";
            }, 1500);
          } else {
            const updateError = updateErr as {
              errors?: Array<{ message: string }>;
            };
            toast.error(
              updateError.errors?.[0]?.message ||
                "Failed to update sign-up information.",
            );
          }
        }
      } else {
        // If the status is not complete, check why. User may need to
        // complete further steps.
        console.error(
          "Sign-up not complete:",
          JSON.stringify(signUpAttempt, null, 2),
        );
        toast.error(
          "There was an issue completing your sign-up. Please try again.",
        );
      }
    } catch (err: unknown) {
      console.error(
        "Error during invitation acceptance:",
        JSON.stringify(err, null, 2),
      );
      const error = err as { errors?: Array<{ message: string }> };
      toast.error(
        error.errors?.[0]?.message ||
          "Failed to accept invitation. Please try again.",
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (
    field: keyof InvitationFormData,
    value: string,
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Basic form validation
  const isFormValid =
    formData.firstName.trim() &&
    formData.lastName.trim() &&
    formData.username.trim() &&
    formData.password.trim();

  return (
    <div
      className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8"
      data-testid="invitation-acceptance"
    >
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Accept Your Invitation
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Complete your profile to join the organization
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label
                htmlFor="firstName"
                className="block text-sm font-medium text-gray-700"
              >
                First Name
              </label>
              <div className="mt-1">
                <input
                  id="firstName"
                  name="firstName"
                  type="text"
                  required
                  value={formData.firstName}
                  onChange={(e) =>
                    handleInputChange("firstName", e.target.value)
                  }
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Your first name"
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="lastName"
                className="block text-sm font-medium text-gray-700"
              >
                Last Name
              </label>
              <div className="mt-1">
                <input
                  id="lastName"
                  name="lastName"
                  type="text"
                  required
                  value={formData.lastName}
                  onChange={(e) =>
                    handleInputChange("lastName", e.target.value)
                  }
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Your last name"
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="username"
                className="block text-sm font-medium text-gray-700"
              >
                Username
              </label>
              <div className="mt-1">
                <input
                  id="username"
                  name="username"
                  type="text"
                  required
                  value={formData.username}
                  onChange={(e) =>
                    handleInputChange("username", e.target.value)
                  }
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Choose a username"
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700"
              >
                Password
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  name="password"
                  type="password"
                  required
                  value={formData.password}
                  onChange={(e) =>
                    handleInputChange("password", e.target.value)
                  }
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Choose a password"
                />
              </div>
            </div>

            {/* CAPTCHA element for Clerk */}
            <div id="clerk-captcha" />

            <div>
              <Button
                type="submit"
                variant="primary"
                className="w-full flex justify-center py-2 px-4"
                disabled={isSubmitting || !isFormValid}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Accepting invitation...
                  </>
                ) : (
                  "Accept Invitation"
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
