#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to fix incorrect role assignments
 * - Remove System Admin <NAME_EMAIL> (should be Company Admin only)
 * - Verify <EMAIL> has correct System Admin role
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '../src/utils/logger';

const prisma = new PrismaClient();

async function fixRoleAssignments() {
  try {
    logger.info('🔧 Starting role assignment fix...');

    // 1. Remove System Admin <NAME_EMAIL>
    const companyAdminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' },
      include: {
        userRoles: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                type: true,
                isSystemRole: true,
              },
            },
          },
        },
      },
    });

    if (companyAdminUser) {
      logger.info(`👤 Found Company Admin user: ${companyAdminUser.email}`);

      // Find and remove System Admin role
      const systemAdminRoleAssignment = companyAdminUser.userRoles.find(
        (ur) => ur.role.type === 'SYSTEM_ADMIN'
      );

      if (systemAdminRoleAssignment) {
        logger.info('🗑️  Removing incorrect System Admin role assignment...');

        await prisma.userRole.delete({
          where: {
            id: systemAdminRoleAssignment.id,
          },
        });

        logger.info('✅ System Admin role removed from Company Admin user');
      } else {
        logger.info('ℹ️  No System Admin role found to remove');
      }

      // Verify remaining roles
      const updatedUser = await prisma.user.findFirst({
        where: { email: '<EMAIL>' },
        include: {
          userRoles: {
            include: {
              role: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                  isSystemRole: true,
                },
              },
            },
          },
        },
      });

      logger.info(
        `   Remaining roles: ${updatedUser?.userRoles.map((ur) => ur.role.name).join(', ')}`
      );
    }

    // 2. Verify <EMAIL> has System Admin role
    const systemAdminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' },
      include: {
        userRoles: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                type: true,
                isSystemRole: true,
              },
            },
          },
        },
      },
    });

    if (systemAdminUser) {
      logger.info(`👤 System Admin user: ${systemAdminUser.email}`);
      logger.info(
        `   Roles: ${systemAdminUser.userRoles.map((ur) => ur.role.name).join(', ')}`
      );

      const hasSystemAdminRole = systemAdminUser.userRoles.some(
        (ur) => ur.role.type === 'SYSTEM_ADMIN'
      );
      const canBypassTenantScope = systemAdminUser.userRoles.some(
        (ur) => ur.role.isSystemRole
      );

      logger.info(`   Has System Admin Role: ${hasSystemAdminRole}`);
      logger.info(`   Can Bypass Tenant Scope: ${canBypassTenantScope}`);

      if (hasSystemAdminRole) {
        logger.info('✅ System Admin user has correct permissions');
      } else {
        logger.warn('⚠️  System Admin user missing System Admin role!');
      }
    } else {
      logger.error('❌ System Admin user not found!');
    }

    // 3. Show final role summary
    logger.info('\n📊 Final Role Summary:');

    const allUsers = await prisma.user.findMany({
      include: {
        tenant: {
          select: {
            name: true,
          },
        },
        userRoles: {
          include: {
            role: {
              select: {
                name: true,
                type: true,
                isSystemRole: true,
              },
            },
          },
        },
      },
      orderBy: { email: 'asc' },
    });

    for (const user of allUsers) {
      const canBypassTenantScope = user.userRoles.some(
        (ur) => ur.role.isSystemRole
      );
      logger.info(`   ${user.email} (${user.tenant.name})`);
      logger.info(
        `     Roles: ${user.userRoles.map((ur) => ur.role.name).join(', ')}`
      );
      logger.info(`     Can Bypass Tenant Scope: ${canBypassTenantScope}`);
    }

    logger.info('\n🎯 Expected behavior:');
    logger.info('- <EMAIL>: System Admin (can see all users)');
    logger.info(
      '- <EMAIL>: Company Admin (can see only Douglas Tech users)'
    );
    logger.info('- <EMAIL>: System Admin (can see all users)');
  } catch (error) {
    logger.error('❌ Failed to fix role assignments:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
fixRoleAssignments().catch(console.error);
