import { Role, UserRole, Prisma } from '@prisma/client';
import { BackendAuthContext } from '@tech-notes/shared';

import {
  NotFoundError,
  ConflictError,
  ForbiddenError,
} from '../types/error.types.js';
import {
  CreateRoleData,
  AssignRoleData,
  UserRoleWithContext,
} from '../types/rbac.types.js';

import { BaseTenantService } from './base-tenant.service.js';

export class RoleService extends BaseTenantService {
  /**
   * Get all roles (system-wide operation, requires System Admin)
   */
  async getAllRoles(BackendAuthContext: BackendAuthContext): Promise<Role[]> {
    this.logOperation('getAllRoles', { userId: BackendAuthContext.id });

    if (!BackendAuthContext.canBypassTenantScope) {
      throw new ForbiddenError('Only System Admins can view all roles');
    }

    return await this.prisma.role.findMany({
      orderBy: [{ isSystemRole: 'desc' }, { name: 'asc' }],
    });
  }

  /**
   * Get roles available for assignment within a tenant
   */
  async getRolesForTenant(
    tenantId: string,
    BackendAuthContext: BackendAuthContext
  ): Promise<Role[]> {
    this.logOperation('getRolesForTenant', {
      tenantId,
      userId: BackendAuthContext.id,
    });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // System Admins can see all roles, others only see non-system roles
        const whereClause = BackendAuthContext.canBypassTenantScope
          ? {}
          : { isSystemRole: false };

        return await this.prisma.role.findMany({
          where: whereClause,
          orderBy: [{ isSystemRole: 'desc' }, { name: 'asc' }],
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Create a new role (System Admin only)
   */
  async createRole(
    roleData: CreateRoleData,
    BackendAuthContext: BackendAuthContext
  ): Promise<Role> {
    this.logOperation('createRole', {
      roleName: roleData.name,
      roleType: roleData.type,
      userId: BackendAuthContext.id,
    });

    if (!BackendAuthContext.canBypassTenantScope) {
      throw new ForbiddenError('Only System Admins can create roles');
    }

    try {
      return await this.transaction(async (tx) => {
        // Create the role
        const role = await tx.role.create({
          data: {
            name: roleData.name,
            type: roleData.type,
            description: roleData.description,
            isSystemRole: roleData.isSystemRole || false,
          },
        });

        // Assign permissions if provided
        if (roleData.permissionIds && roleData.permissionIds.length > 0) {
          await tx.rolePermission.createMany({
            data: roleData.permissionIds.map((permissionId) => ({
              roleId: role.id,
              permissionId,
            })),
          });
        }

        return role;
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictError(
            `Role with name '${roleData.name}' already exists`
          );
        }
      }
      this.logError(
        'createRole',
        error as Error,
        roleData as unknown as Record<string, unknown>
      );
      throw error;
    }
  }

  /**
   * Assign a role to a user
   */
  async assignRole(
    assignmentData: AssignRoleData,
    BackendAuthContext: BackendAuthContext
  ): Promise<UserRole> {
    this.logOperation('assignRole', {
      userId: assignmentData.userId,
      roleId: assignmentData.roleId,
      tenantId: assignmentData.tenantId,
      assignedBy: BackendAuthContext.id,
    });

    // Validate the role exists and get its details
    const role = await this.prisma.role.findUnique({
      where: { id: assignmentData.roleId },
      select: { id: true, isSystemRole: true, type: true, name: true },
    });

    if (!role) {
      throw new NotFoundError(`Role ${assignmentData.roleId} not found`);
    }

    // Security validation: System roles can only be assigned by System Admins
    if (role.isSystemRole && !BackendAuthContext.canBypassTenantScope) {
      throw new ForbiddenError('Only System Admins can assign system roles');
    }

    // Security validation: System roles MUST have null tenantId
    if (role.isSystemRole && assignmentData.tenantId) {
      throw new ForbiddenError(
        'System roles cannot be assigned to a specific tenant'
      );
    }

    // Security validation: Non-system roles MUST have tenantId
    if (!role.isSystemRole && !assignmentData.tenantId) {
      throw new ForbiddenError(
        'Company roles must be assigned to a specific tenant'
      );
    }

    // For tenant-scoped operations, validate tenant access
    const tenantId = assignmentData.tenantId || BackendAuthContext.tenantId;

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Validate the target user exists
        const targetUser = await this.prisma.user.findUnique({
          where: { id: assignmentData.userId },
          select: { id: true, tenantId: true },
        });

        if (!targetUser) {
          throw new NotFoundError(`User ${assignmentData.userId} not found`);
        }

        // For non-system roles, ensure user belongs to the target tenant
        if (
          !role.isSystemRole &&
          targetUser.tenantId !== assignmentData.tenantId
        ) {
          throw new ForbiddenError('User does not belong to the target tenant');
        }

        try {
          return await this.prisma.userRole.create({
            data: {
              userId: assignmentData.userId,
              roleId: assignmentData.roleId,
              tenantId: assignmentData.tenantId,
              assignedBy: BackendAuthContext.id,
            },
            include: {
              role: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                  isSystemRole: true,
                },
              },
            },
          });
        } catch (error) {
          if (error instanceof Prisma.PrismaClientKnownRequestError) {
            if (error.code === 'P2002') {
              throw new ConflictError(
                'User already has this role in this context'
              );
            }
          }
          this.logError(
            'assignRole',
            error as Error,
            assignmentData as unknown as Record<string, unknown>
          );
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Remove a role from a user
   */
  async removeRole(
    userId: string,
    roleId: string,
    tenantId: string | null,
    BackendAuthContext: BackendAuthContext
  ): Promise<void> {
    this.logOperation('removeRole', {
      userId,
      roleId,
      tenantId,
      removedBy: BackendAuthContext.id,
    });

    // Get role details for security validation
    const role = await this.prisma.role.findUnique({
      where: { id: roleId },
      select: { isSystemRole: true, name: true },
    });

    if (!role) {
      throw new NotFoundError(`Role ${roleId} not found`);
    }

    // Security validation: System roles can only be removed by System Admins
    if (role.isSystemRole && !BackendAuthContext.canBypassTenantScope) {
      throw new ForbiddenError('Only System Admins can remove system roles');
    }

    const scopeTenantId = tenantId || BackendAuthContext.tenantId;

    return await this.withTenantScopeForUser(
      scopeTenantId,
      async () => {
        const userRole = await this.prisma.userRole.findFirst({
          where: {
            userId,
            roleId,
            tenantId,
          },
        });

        if (!userRole) {
          throw new NotFoundError('User role assignment not found');
        }

        await this.prisma.userRole.delete({
          where: { id: userRole.id },
        });

        this.logger.info('Role removed from user', {
          userId,
          roleId,
          roleName: role.name,
          tenantId,
          removedBy: BackendAuthContext.id,
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Get all roles for a specific user
   */
  async getUserRoles(
    userId: string,
    BackendAuthContext: BackendAuthContext
  ): Promise<UserRoleWithContext[]> {
    this.logOperation('getUserRoles', {
      userId,
      requestedBy: BackendAuthContext.id,
    });

    // System Admins can view any user's roles, others can only view within their tenant
    if (!BackendAuthContext.canBypassTenantScope) {
      // Validate the target user is in the same tenant
      const targetUser = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { tenantId: true },
      });

      if (!targetUser) {
        throw new NotFoundError(`User ${userId} not found`);
      }

      if (targetUser.tenantId !== BackendAuthContext.tenantId) {
        throw new ForbiddenError(
          'Cannot view roles for users in other tenants'
        );
      }
    }

    const userRoles = await this.prisma.userRole.findMany({
      where: { userId },
      include: {
        role: {
          select: {
            id: true,
            name: true,
            type: true,
            isSystemRole: true,
          },
        },
      },
      orderBy: [{ role: { isSystemRole: 'desc' } }, { role: { name: 'asc' } }],
    });

    return userRoles.map((userRole) => ({
      id: userRole.id,
      role: userRole.role,
      tenantId: userRole.tenantId,
    }));
  }
}
