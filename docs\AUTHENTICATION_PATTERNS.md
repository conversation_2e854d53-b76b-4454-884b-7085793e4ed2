# Authentication Patterns - Tech Notes

Essential Clerk authentication patterns with database-centric tenancy for AI agents.

---

## 🏗️ Core Architecture

### Authentication Flow

```
Frontend: ClerkProvider → SignedIn/SignedOut Guards → useAuthenticatedApi Hook
Backend: clerkAuth() Middleware → Database User Lookup → Tenant Context
```

**Core Principle**: Always use database-centric tenancy with `User.tenantId` as source of truth, NOT Clerk metadata.

**Authentication Model**: Invite-only system with login-only access. Public signup disabled, new users contact sales.

### Implementation Sequence

1. **Token Validation**: Verify Clerk JWT token server-side
2. **Database Lookup**: Find user by `clerkId` in local database
3. **Tenant Context**: Extract `tenantId` from database user record
4. **Route Handler**: Proceed with authenticated context

### Public Access Pattern

- **Public Pages**: Marketing pages (home, pricing) accessible without auth
- **Login Flow**: Public pages → "Login" button → `/sign-in` → Contact Sales CTA
- **Signup**: Disabled in Clerk configuration

---

## 🔧 Backend Implementation

### Middleware Patterns

```typescript
import {
  MiddlewareFactory,
  CommonPermissions,
} from "../middleware/middleware-factory";

// Basic authentication
router.get("/protected", ...middlewareFactory.createAuth(), handler);

// Permission-based protection (recommended)
router.get(
  "/users",
  ...middlewareFactory.createAuthWithPermission(CommonPermissions.USER_READ),
  handler,
);

// Role-based protection
router.get("/admin", ...middlewareFactory.createAuthWithSystemAdmin(), handler);

// Multiple permissions (AND logic)
router.post(
  "/users",
  ...middlewareFactory.createAuthWithPermission([
    CommonPermissions.USER_WRITE,
    CommonPermissions.USER_MANAGE,
  ]),
  handler,
);
```

### Authentication Context

```typescript
interface AuthContext {
  id: string; // User database ID (not Clerk ID)
  clerkId: string; // Clerk user ID from validated token
  tenantId: string; // From database User.tenantId (source of truth)
  email: string; // User email
  firstName?: string | null;
  lastName?: string | null;
  imageUrl?: string | null;
  isActive: boolean; // User activation status
  createdAt: Date;
  updatedAt: Date;
  // RBAC fields (populated by enhanced auth middleware)
  roles?: UserRoleWithContext[]; // User's roles with tenant context
  permissions?: string[]; // Flattened permission strings for quick checks
  canBypassTenantScope?: boolean; // True for System Admins
}

interface AuthenticatedRequest extends Request {
  user?: AuthContext;
}
```

### Route Handler Pattern

```typescript
router.get(
  "/users",
  clerkAuth(),
  async (req: AuthenticatedRequest, res): Promise<void> => {
    const user = req.user!; // Non-null assertion after middleware
    const { tenantId } = user;
    const users = await userService.getUsersByTenant(tenantId);
    res.json({ data: users });
  },
);
```

### Error Response Format

Use consistent error responses: `{ error, message, statusCode, timestamp }`

---

## 🎨 Frontend Implementation

### App-Level Setup

```typescript
// App.tsx - ClerkProvider wrapper
import { ClerkProvider } from '@clerk/clerk-react';

function App() {
  return (
    <ClerkProvider publishableKey={auth.clerkPublishableKey}>
      {/* App content */}
    </ClerkProvider>
  );
}
```

### Dual-Layout Routing

```typescript
// App.tsx - Dual routing pattern
function AppRouter() {
  const location = useLocation();
  const isPublicRoute = ['/', '/pricing', '/accept-invitation'].includes(location.pathname);

  if (isPublicRoute) return <PublicSite />;
  return <AuthenticatedApp />;
}

function AuthenticatedApp() {
  return (
    <>
      <SignedOut><Navigate to="/" replace /></SignedOut>
      <SignedIn>
        <Routes>
          <Route path="/app" element={<AppLayout />}>
            <Route index element={<Dashboard />} />
            <Route path="health" element={<HealthDashboard />} />
          </Route>
        </Routes>
      </SignedIn>
    </>
  );
}
```

### Layout Components

```typescript
// AppLayout.tsx - Authenticated layout
const AppLayout: React.FC = () => (
  <div className="min-h-screen bg-gray-50">
    <header className="bg-white shadow-sm">
      <Navigation />
      <UserButton afterSignOutUrl="/" />
    </header>
    <main className="max-w-7xl mx-auto py-6 px-4">
      <Outlet />
    </main>
  </div>
);

// PublicLayout.tsx - Marketing layout
const PublicLayout: React.FC = () => (
  <div className="min-h-screen bg-white">
    <header className="bg-white border-b border-gray-100">
      <nav className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <Link to="/" className="text-2xl font-bold text-primary-600">Tech Notes</Link>
          <div className="flex items-center space-x-8">
            <Link to="/pricing">Pricing</Link>
            <Button asChild variant="primary" size="sm">
              <Link to="/sign-in">Login</Link>
            </Button>
          </div>
        </div>
      </nav>
    </header>
    <main><Outlet /></main>
  </div>
);
```

### Component-Level Authentication

```typescript
// Secure API calls
const Dashboard: React.FC = () => {
  const authenticatedApi = useAuthenticatedApi();

  const handleApiCall = async () => {
    try {
      const data = await authenticatedApi.health.check(); // Auto Bearer token
      console.log(data);
    } catch (error) {
      console.error('API call failed:', error);
    }
  };

  return <button onClick={handleApiCall}>Check Health</button>;
};

// User information access
const UserComponent: React.FC = () => {
  const { user, isSignedIn } = useUser();
  const { signOut } = useClerk();

  return (
    <div>
      {isSignedIn && (
        <div>
          <p>Welcome, {user?.firstName} {user?.lastName}</p>
          <button onClick={() => signOut()}>Sign Out</button>
        </div>
      )}
    </div>
  );
};
```

---

## 🔐 Role-Based Access Control (RBAC)

### 3-Tier RBAC Model

- **System Admin**: Global access, can bypass tenant scoping
- **Company Admin**: Full access within their tenant
- **Company Tech**: Limited access within their tenant

### RBAC Middleware Patterns

```typescript
// Permission-based protection (recommended)
router.get(
  "/users",
  ...middlewareFactory.createAuthWithPermission(CommonPermissions.USER_READ),
  handler,
);

// Role-based protection
router.get(
  "/admin-panel",
  ...middlewareFactory.createAuthWithSystemAdmin(),
  handler,
);

// Multiple permissions (user must have ALL)
router.post(
  "/users",
  ...middlewareFactory.createAuthWithPermission([
    CommonPermissions.USER_CREATE,
    CommonPermissions.USER_WRITE,
  ]),
  handler,
);
```

### Common Permission Constants

```typescript
export const CommonPermissions = {
  // User management
  USER_READ: { resource: "USER", action: "read" },
  USER_WRITE: { resource: "USER", action: "write" },
  USER_MANAGE: { resource: "USER", action: "manage" },

  // Tenant management
  TENANT_READ: { resource: "TENANT", action: "read" },
  TENANT_MANAGE: { resource: "TENANT", action: "manage" },

  // System administration
  SYSTEM_MANAGE: { resource: "SYSTEM", action: "manage" },
} as const;
```

### Permission Checking in Handlers

```typescript
router.get(
  "/conditional",
  middlewareFactory.createAuth(),
  async (req: AuthenticatedRequest, res) => {
    const { user } = req;

    if (user.canBypassTenantScope) {
      // System Admin logic
      const allData = await service.getAllData();
      return res.json({ data: allData });
    }

    // Regular tenant-scoped logic
    const tenantData = await service.getDataByTenant(user.tenantId);
    res.json({ data: tenantData });
  },
);
```

### RBAC API Endpoints

**Role Management**:

- `GET /api/v1/roles` - List all roles (System Admin)
- `POST /api/v1/roles/assign` - Assign role to user (Company Admin+)
- `GET /api/v1/roles/user/:userId` - Get user roles (Company User+)

**Permission Management**:

- `GET /api/v1/permissions` - List all permissions (System Admin)
- `POST /api/v1/permissions/check` - Check current user permissions

---

## 🛡️ Security Guidelines

### Critical Security Rules

- **Always validate tokens server-side** using Clerk's `verifyToken`
- **Use database `tenantId` as source of truth** - NOT Clerk metadata
- **Validate user exists in database** before proceeding with requests
- **Always scope operations by tenant** using database-sourced `tenantId`
- **Use optional auth** only for truly public endpoints

---

## 📚 Quick Reference

### Essential Imports

```typescript
// Backend
import {
  MiddlewareFactory,
  CommonPermissions,
} from "../middleware/middleware-factory";
import { AuthenticatedRequest } from "../types/auth.types";

// Frontend
import { useUser, useAuth, useClerk } from "@clerk/clerk-react";
import { SignedIn, SignedOut } from "@clerk/clerk-react";
import { useAuthenticatedApi } from "../services/auth-api.service";
```

### Common Patterns

```typescript
// Backend route protection with RBAC
router.get('/users', ...middlewareFactory.createAuthWithPermission(CommonPermissions.USER_READ), handler);
router.get('/admin', ...middlewareFactory.createAuthWithSystemAdmin(), handler);

// Dual routing pattern
const isPublicRoute = ['/', '/pricing'].includes(location.pathname);
if (isPublicRoute) return <PublicSite />;
return <AuthenticatedApp />;

// Authentication guards
<SignedIn><Routes><Route path="/app" element={<AppLayout />} /></Routes></SignedIn>
<SignedOut><Navigate to="/" replace /></SignedOut>

// Authenticated API call
const api = useAuthenticatedApi();
const data = await api.request('/endpoint');
```

---

**Remember**: Always use database as source of truth for tenant relationships and RBAC. Clerk provides authentication, but your database defines authorization, roles, permissions, and tenant boundaries.
