import React, { useState, useEffect } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { Modal, Button, Input, FormField, Alert } from "../index";
import { useTypedApi, type VehicleBrandV2 } from "../../services/api-client";
import { Building2 } from "lucide-react";

interface EditBrandV2ModalProps {
  isOpen: boolean;
  onClose: () => void;
  brand: VehicleBrandV2 | null;
}

export const EditBrandV2Modal: React.FC<EditBrandV2ModalProps> = ({
  isOpen,
  onClose,
  brand,
}) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [name, setName] = useState("");
  const [isActive, setIsActive] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Populate form when brand changes
  useEffect(() => {
    if (brand) {
      setName(brand.name);
      setIsActive(brand.isActive);
    }
  }, [brand]);

  const updateBrandMutation = useMutation({
    mutationFn: (brandData: { name?: string; isActive?: boolean }) => {
      if (!brand) throw new Error("No brand selected");
      return api.vehicleHierarchyV2.updateBrand(brand.id, brandData);
    },
    onSuccess: () => {
      toast.success("Brand updated successfully");
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "brands"],
      });
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "full"],
      });
      handleClose();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update brand");
    },
  });

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = "Brand name is required";
    } else if (name.trim().length < 2) {
      newErrors.name = "Brand name must be at least 2 characters";
    } else if (name.trim().length > 100) {
      newErrors.name = "Brand name must be less than 100 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Only send changed fields
    const updateData: { name?: string; isActive?: boolean } = {};

    if (name.trim() !== brand?.name) {
      updateData.name = name.trim();
    }

    if (isActive !== brand?.isActive) {
      updateData.isActive = isActive;
    }

    // If nothing changed, just close
    if (Object.keys(updateData).length === 0) {
      handleClose();
      return;
    }

    updateBrandMutation.mutate(updateData);
  };

  const handleClose = () => {
    setName("");
    setIsActive(true);
    setErrors({});
    onClose();
  };

  if (!brand) return null;

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Edit Brand" size="md">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex items-center space-x-3 text-gray-600 mb-4">
          <Building2 className="h-5 w-5" />
          <span>Edit vehicle brand details</span>
        </div>

        <FormField label="Brand Name" error={errors.name} required>
          <Input
            type="text"
            placeholder="e.g., Ford, Toyota, Rockwood"
            value={name}
            onChange={(e) => setName(e.target.value)}
            error={errors.name}
            maxLength={100}
          />
        </FormField>

        <FormField label="Status">
          <div className="flex items-center space-x-3">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="isActive"
                checked={isActive}
                onChange={() => setIsActive(true)}
                className="text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm">Active</span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="isActive"
                checked={!isActive}
                onChange={() => setIsActive(false)}
                className="text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm">Inactive</span>
            </label>
          </div>
        </FormField>

        {updateBrandMutation.isError && (
          <Alert variant="error">
            <p>Failed to update brand. Please try again.</p>
          </Alert>
        )}

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={updateBrandMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={updateBrandMutation.isPending || !name.trim()}
          >
            {updateBrandMutation.isPending ? "Updating..." : "Update Brand"}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
