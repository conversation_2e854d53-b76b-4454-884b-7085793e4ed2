import React from "react";
import { FormField } from "../../../components";
import type { VehicleYear, VehicleMake } from "../../../services/api-client";

interface VehicleHierarchyFiltersProps {
  years: VehicleYear[];
  makes: VehicleMake[];
  selectedYearFilter: string;
  selectedMakeFilter: string;
  onYearFilterChange: (yearId: string) => void;
  onMakeFilterChange: (makeId: string) => void;
}

export const VehicleHierarchyFilters: React.FC<VehicleHierarchyFiltersProps> =
  React.memo(
    ({
      years,
      makes,
      selectedYearFilter,
      selectedMakeFilter,
      onYearFilterChange,
      onMakeFilterChange,
    }) => {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg border">
          <FormField label="Filter by Year">
            <select
              value={selectedYearFilter}
              onChange={(e) => onYearFilterChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">All Years</option>
              {years
                .sort((a, b) => b.year - a.year)
                .map((year) => (
                  <option key={year.id} value={year.id}>
                    {year.year}
                  </option>
                ))}
            </select>
          </FormField>

          <FormField label="Filter by Make">
            <select
              value={selectedMakeFilter}
              onChange={(e) => onMakeFilterChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">All Makes</option>
              {makes
                .sort((a, b) => a.name.localeCompare(b.name))
                .map((make) => (
                  <option key={make.id} value={make.id}>
                    {make.name}
                  </option>
                ))}
            </select>
          </FormField>
        </div>
      );
    },
  );
