import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import { TestimonialCard } from "./TestimonialCard";

describe("TestimonialCard", () => {
  const defaultProps = {
    quote:
      "This is an amazing product that has transformed our business operations.",
    author: {
      name: "<PERSON>",
      title: "CEO",
      company: "Tech Corp",
    },
  };

  describe("Rendering", () => {
    it("should render testimonial with quote and author", () => {
      render(<TestimonialCard {...defaultProps} />);

      expect(
        screen.getByText(
          '"This is an amazing product that has transformed our business operations."',
        ),
      ).toBeInTheDocument();
      expect(screen.getByText("John Doe")).toBeInTheDocument();
      expect(screen.getByText("CEO, Tech Corp")).toBeInTheDocument();
    });

    it("should render with custom className", () => {
      render(
        <TestimonialCard {...defaultProps} className="custom-testimonial" />,
      );

      const card = screen.getByText("John Doe").closest(".custom-testimonial");
      expect(card).toBeInTheDocument();
    });

    it("should render author with title only", () => {
      const props = {
        ...defaultProps,
        author: { name: "Jane Smith", title: "Product Manager" },
      };

      render(<TestimonialCard {...props} />);

      expect(screen.getByText("Jane Smith")).toBeInTheDocument();
      expect(screen.getByText("Product Manager")).toBeInTheDocument();
    });

    it("should render author with company only", () => {
      const props = {
        ...defaultProps,
        author: { name: "Bob Johnson", company: "StartupCo" },
      };

      render(<TestimonialCard {...props} />);

      expect(screen.getByText("Bob Johnson")).toBeInTheDocument();
      expect(screen.getByText("StartupCo")).toBeInTheDocument();
    });

    it("should render author with name only", () => {
      const props = {
        ...defaultProps,
        author: { name: "Alice Brown" },
      };

      render(<TestimonialCard {...props} />);

      expect(screen.getByText("Alice Brown")).toBeInTheDocument();
    });
  });

  describe("Variants", () => {
    it("should render default variant with basic shadow", () => {
      render(<TestimonialCard {...defaultProps} variant="default" />);

      const card = screen.getByTestId("testimonial-card-container");
      expect(card).toHaveClass("bg-white", "shadow-xs", "hover:shadow-sm");
    });

    it("should render elevated variant with enhanced shadow", () => {
      render(<TestimonialCard {...defaultProps} variant="elevated" />);

      const card = screen.getByTestId("testimonial-card-container");
      expect(card).toHaveClass("bg-white", "shadow-sm", "hover:shadow-base");
    });

    it("should render bordered variant with border", () => {
      render(<TestimonialCard {...defaultProps} variant="bordered" />);

      const card = screen.getByTestId("testimonial-card-container");
      expect(card).toHaveClass(
        "bg-white",
        "border-2",
        "border-gray-200",
        "hover:border-primary-300",
      );
    });

    it("should render minimal variant with transparent background", () => {
      render(<TestimonialCard {...defaultProps} variant="minimal" />);

      const card = screen.getByTestId("testimonial-card-container");
      expect(card).toHaveClass("bg-transparent");
    });
  });

  describe("Sizes", () => {
    it("should render small size with appropriate classes", () => {
      render(<TestimonialCard {...defaultProps} size="sm" />);

      const card = screen.getByTestId("testimonial-card-container");
      const quote = screen.getByText(
        '"This is an amazing product that has transformed our business operations."',
      );
      const authorName = screen.getByText("John Doe");

      expect(card).toHaveClass("p-4");
      expect(quote).toHaveClass("text-sm");
      expect(authorName).toHaveClass("text-sm");
    });

    it("should render medium size with appropriate classes (default)", () => {
      render(<TestimonialCard {...defaultProps} size="md" />);

      const card = screen.getByTestId("testimonial-card-container");
      const quote = screen.getByText(
        '"This is an amazing product that has transformed our business operations."',
      );
      const authorName = screen.getByText("John Doe");

      expect(card).toHaveClass("p-6");
      expect(quote).toHaveClass("text-base");
      expect(authorName).toHaveClass("text-base");
    });

    it("should render large size with appropriate classes", () => {
      render(<TestimonialCard {...defaultProps} size="lg" />);

      const card = screen.getByTestId("testimonial-card-container");
      const quote = screen.getByText(
        '"This is an amazing product that has transformed our business operations."',
      );
      const authorName = screen.getByText("John Doe");

      expect(card).toHaveClass("p-8");
      expect(quote).toHaveClass("text-lg");
      expect(authorName).toHaveClass("text-lg");
    });
  });

  describe("Layout", () => {
    it("should render vertical layout by default", () => {
      render(<TestimonialCard {...defaultProps} />);

      const card = screen.getByTestId("testimonial-card-container");
      expect(card).toHaveClass("text-center");
    });

    it("should render horizontal layout", () => {
      render(<TestimonialCard {...defaultProps} layout="horizontal" />);

      const card = screen.getByTestId("testimonial-card-container");
      expect(card).toHaveClass("text-left");
    });
  });

  describe("Quote Icon", () => {
    it("should show quote icon by default", () => {
      render(<TestimonialCard {...defaultProps} />);

      const quoteIcon = document.querySelector("svg");
      expect(quoteIcon).toBeInTheDocument();
    });

    it("should hide quote icon when showQuoteIcon is false", () => {
      render(<TestimonialCard {...defaultProps} showQuoteIcon={false} />);

      const quoteIcon = document.querySelector("svg");
      expect(quoteIcon).toBeNull();
    });

    it("should position quote icon at top by default", () => {
      render(<TestimonialCard {...defaultProps} />);

      const quoteIcon = document.querySelector("svg");
      expect(quoteIcon).toHaveClass("mb-4");
    });

    it("should position quote icon inline when specified", () => {
      render(<TestimonialCard {...defaultProps} quoteIconPosition="inline" />);

      const quoteIcon = document.querySelector("svg");
      expect(quoteIcon).toHaveClass("inline", "mr-2");
    });
  });

  describe("Avatar", () => {
    it("should render avatar image when provided", () => {
      const props = {
        ...defaultProps,
        author: {
          ...defaultProps.author,
          avatar: "/test-avatar.jpg",
        },
      };

      render(<TestimonialCard {...props} />);

      const avatar = screen.getByAltText("John Doe avatar");
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveAttribute("src", "/test-avatar.jpg");
      expect(avatar).toHaveClass("rounded-full", "object-cover");
    });

    it("should render initials fallback when no avatar provided", () => {
      render(<TestimonialCard {...defaultProps} />);

      expect(screen.getByText("JD")).toBeInTheDocument();
    });

    it("should render single initial for single name", () => {
      const props = {
        ...defaultProps,
        author: { name: "Madonna" },
      };

      render(<TestimonialCard {...props} />);

      expect(screen.getByText("M")).toBeInTheDocument();
    });

    it("should render first two initials for multiple names", () => {
      const props = {
        ...defaultProps,
        author: { name: "John Michael Smith" },
      };

      render(<TestimonialCard {...props} />);

      expect(screen.getByText("JM")).toBeInTheDocument();
    });

    it("should style initials fallback correctly", () => {
      render(<TestimonialCard {...defaultProps} />);

      const initialsContainer = screen.getByText("JD");
      expect(initialsContainer).toHaveClass(
        "rounded-full",
        "bg-primary-100",
        "text-primary-600",
      );
    });
  });

  describe("Rating", () => {
    it("should not render rating by default", () => {
      render(<TestimonialCard {...defaultProps} />);

      const stars = document.querySelectorAll('svg[viewBox="0 0 20 20"]');
      expect(stars).toHaveLength(0); // No star ratings, quote icon has different viewBox
    });

    it("should render 5-star rating", () => {
      render(<TestimonialCard {...defaultProps} rating={5} />);

      const stars = document.querySelectorAll('svg[viewBox="0 0 20 20"]');
      expect(stars).toHaveLength(5);

      // All stars should be filled (yellow)
      stars.forEach((star) => {
        expect(star).toHaveClass("text-yellow-400", "fill-current");
      });
    });

    it("should render 3-star rating with partial fill", () => {
      render(<TestimonialCard {...defaultProps} rating={3} />);

      const stars = document.querySelectorAll('svg[viewBox="0 0 20 20"]');
      expect(stars).toHaveLength(5);

      // First 3 stars should be filled
      for (let i = 0; i < 3; i++) {
        expect(stars[i]).toHaveClass("text-yellow-400", "fill-current");
      }

      // Last 2 stars should be empty
      for (let i = 3; i < 5; i++) {
        expect(stars[i]).toHaveClass("text-gray-300");
      }
    });

    it("should render 1-star rating", () => {
      render(<TestimonialCard {...defaultProps} rating={1} />);

      const stars = document.querySelectorAll('svg[viewBox="0 0 20 20"]');
      expect(stars).toHaveLength(5);

      // Only first star should be filled
      expect(stars[0]).toHaveClass("text-yellow-400", "fill-current");

      // Other stars should be empty
      for (let i = 1; i < 5; i++) {
        expect(stars[i]).toHaveClass("text-gray-300");
      }
    });
  });

  describe("Interactive Behavior", () => {
    it("should not be interactive by default", () => {
      render(<TestimonialCard {...defaultProps} />);

      const card = screen.getByText("John Doe").closest("div");
      expect(card).not.toHaveClass("cursor-pointer");
    });

    it("should render as interactive when specified", () => {
      render(<TestimonialCard {...defaultProps} interactive />);

      const card = screen.getByTestId("testimonial-card-container");
      expect(card).toHaveClass(
        "cursor-pointer",
        "transition-all",
        "duration-200",
      );
      expect(card).toHaveClass("hover:scale-[1.02]", "active:scale-[0.98]");
    });
  });

  describe("Accessibility", () => {
    it("should use proper blockquote element for quote", () => {
      render(<TestimonialCard {...defaultProps} />);

      const quote = screen.getByText(
        '"This is an amazing product that has transformed our business operations."',
      );
      expect(quote.tagName).toBe("BLOCKQUOTE");
    });

    it("should have proper alt text for avatar images", () => {
      const props = {
        ...defaultProps,
        author: {
          ...defaultProps.author,
          avatar: "/test-avatar.jpg",
        },
      };

      render(<TestimonialCard {...props} />);

      const avatar = screen.getByAltText("John Doe avatar");
      expect(avatar).toBeInTheDocument();
    });

    it("should support custom attributes", () => {
      render(
        <TestimonialCard
          {...defaultProps}
          data-testid="custom-testimonial"
          aria-label="Customer testimonial"
        />,
      );

      const card = screen.getByTestId("custom-testimonial");
      expect(card).toHaveAttribute("aria-label", "Customer testimonial");
    });
  });
});
