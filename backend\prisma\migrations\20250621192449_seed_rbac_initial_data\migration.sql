-- Seed RBAC Initial Data
-- This migration creates the initial roles, permissions, and role-permission assignments

-- Insert Permissions
INSERT INTO "permissions" ("id", "name", "description", "resource", "action", "createdAt", "updatedAt") VALUES
  ('perm_user_read', 'user:read', 'Read user information', 'USER', 'READ', NOW(), NOW()),
  ('perm_user_write', 'user:write', 'Create and update users', 'USER', 'WRITE', NOW(), NOW()),
  ('perm_user_delete', 'user:delete', 'Delete users', 'USER', 'DELETE', NOW(), NOW()),
  ('perm_user_manage', 'user:manage', 'Full user management', 'USER', 'MANAGE', NOW(), NOW()),
  ('perm_tenant_read', 'tenant:read', 'Read tenant information', 'TENANT', 'READ', NOW(), NOW()),
  ('perm_tenant_write', 'tenant:write', 'Update tenant settings', 'TENANT', 'WRITE', NOW(), NOW()),
  ('perm_tenant_manage', 'tenant:manage', 'Full tenant management', 'TENANT', 'MANAGE', NOW(), NOW()),
  ('perm_system_manage', 'system:manage', 'System administration', 'SYSTEM', 'MANAGE', NOW(), NOW()),
  ('perm_data_read', 'data:read', 'Read application data', 'DATA', 'READ', NOW(), NOW()),
  ('perm_data_write', 'data:write', 'Create and update application data', 'DATA', 'WRITE', NOW(), NOW())
ON CONFLICT ("name") DO NOTHING;

-- Insert Roles
INSERT INTO "roles" ("id", "name", "type", "description", "isSystemRole", "createdAt", "updatedAt") VALUES
  ('role_system_admin', 'System Admin', 'SYSTEM_ADMIN', 'Global system administrator with access to all tenants', true, NOW(), NOW()),
  ('role_company_admin', 'Company Admin', 'COMPANY_ADMIN', 'Full administrative access within a specific tenant', false, NOW(), NOW()),
  ('role_company_tech', 'Company Tech', 'COMPANY_TECH', 'Technical user with limited administrative access within a tenant', false, NOW(), NOW())
ON CONFLICT ("name") DO NOTHING;

-- Assign ALL permissions to System Admin
INSERT INTO "role_permissions" ("roleId", "permissionId") VALUES
  ('role_system_admin', 'perm_user_read'),
  ('role_system_admin', 'perm_user_write'),
  ('role_system_admin', 'perm_user_delete'),
  ('role_system_admin', 'perm_user_manage'),
  ('role_system_admin', 'perm_tenant_read'),
  ('role_system_admin', 'perm_tenant_write'),
  ('role_system_admin', 'perm_tenant_manage'),
  ('role_system_admin', 'perm_system_manage'),
  ('role_system_admin', 'perm_data_read'),
  ('role_system_admin', 'perm_data_write')
ON CONFLICT ("roleId", "permissionId") DO NOTHING;

-- Assign tenant and user management permissions to Company Admin (excludes system:manage)
INSERT INTO "role_permissions" ("roleId", "permissionId") VALUES
  ('role_company_admin', 'perm_user_read'),
  ('role_company_admin', 'perm_user_write'),
  ('role_company_admin', 'perm_user_delete'),
  ('role_company_admin', 'perm_user_manage'),
  ('role_company_admin', 'perm_tenant_read'),
  ('role_company_admin', 'perm_tenant_write'),
  ('role_company_admin', 'perm_tenant_manage'),
  ('role_company_admin', 'perm_data_read'),
  ('role_company_admin', 'perm_data_write')
ON CONFLICT ("roleId", "permissionId") DO NOTHING;

-- Assign read permissions and data write to Company Tech
INSERT INTO "role_permissions" ("roleId", "permissionId") VALUES
  ('role_company_tech', 'perm_user_read'),
  ('role_company_tech', 'perm_tenant_read'),
  ('role_company_tech', 'perm_data_read'),
  ('role_company_tech', 'perm_data_write')
ON CONFLICT ("roleId", "permissionId") DO NOTHING;