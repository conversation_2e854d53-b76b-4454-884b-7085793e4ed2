import { Router, Request, Response, NextFunction } from 'express';
import { Logger } from 'winston';

import {
  MiddlewareFactory,
  CommonPermissions,
} from '../../../middleware/middleware-factory.js';
import { UserService } from '../../../services/user.service.js';
import { getRequestUser } from '../../../utils/request-types.js';

interface ServiceDependencies {
  userService: UserService;
  middlewareFactory: MiddlewareFactory;
  logger: Logger;
}

export function createUsersRouter(dependencies: ServiceDependencies): Router {
  const { userService, middlewareFactory, logger } = dependencies;
  const router = Router();

  /**
   * GET /api/v1/users
   * Get users by tenant with their roles (requires user read permission)
   */
  router.get(
    '/',
    ...middlewareFactory.createAuthWithPermission(CommonPermissions.USER_READ),
    async (req: Request, res: Response, next: NextFunction) => {
      const user = getRequestUser(req);
      try {
        const { tenantId } = user!;
        const users = await userService.getUsersByTenantWithRoles(
          tenantId,
          user!
        );

        res.json({
          data: users,
          meta: {
            count: users.length,
            tenantId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch users', {
          error,
          tenantId: user?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/users/:userId
   * Get specific user by ID with roles (requires user read permission)
   */
  router.get(
    '/:userId',
    ...middlewareFactory.createAuthWithPermission(CommonPermissions.USER_READ),
    async (req: Request, res: Response, next: NextFunction) => {
      const user = getRequestUser(req);
      try {
        const { userId } = req.params;
        const { tenantId } = user!;

        if (!userId) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'User ID is required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        const foundUser = await userService.getUserByIdWithRoles(
          userId,
          tenantId,
          user!
        );

        if (!foundUser) {
          return res.status(404).json({
            error: 'Not Found',
            message: 'User not found',
            statusCode: 404,
            timestamp: new Date().toISOString(),
          });
        }

        res.json({
          data: foundUser,
        });
      } catch (error) {
        logger.error('Failed to fetch user', {
          error,
          userId: req.params.userId,
          tenantId: user?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/users/me
   * Get current user's profile with roles
   */
  router.get(
    '/me',
    middlewareFactory.createAuth(),
    async (req: Request, res: Response, next: NextFunction) => {
      const user = getRequestUser(req);
      try {
        const { id: userId, tenantId } = user!;
        const foundUser = await userService.getUserByIdWithRoles(
          userId,
          tenantId,
          user!
        );

        if (!foundUser) {
          return res.status(404).json({
            error: 'Not Found',
            message: 'User profile not found',
            statusCode: 404,
            timestamp: new Date().toISOString(),
          });
        }

        res.json({
          data: foundUser,
        });
      } catch (error) {
        logger.error('Failed to fetch current user profile', {
          error,
          userId: user?.id,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/users/system-admins
   * Get all system administrators (System Admin only)
   */
  router.get(
    '/system-admins',
    ...middlewareFactory.createAuthWithSystemAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      const user = getRequestUser(req);
      try {
        const systemAdmins = await userService.getSystemAdmins(user!);

        res.json({
          data: systemAdmins,
          meta: {
            count: systemAdmins.length,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch system administrators', {
          error,
          userId: user?.id,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/users/:userId/roles
   * Get roles for a specific user (requires user read permission)
   */
  router.get(
    '/:userId/roles',
    ...middlewareFactory.createAuthWithPermission(CommonPermissions.USER_READ),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { userId } = req.params;

        if (!userId) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'User ID is required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        // This will be handled by the roles router, but we can provide a redirect
        res.status(301).json({
          message: 'This endpoint has moved',
          redirectTo: `/api/v1/roles/user/${userId}`,
        });
      } catch (error) {
        logger.error('Failed to fetch user roles', {
          error,
          userId: req.params.userId,
        });
        next(error);
      }
    }
  );

  return router;
}
