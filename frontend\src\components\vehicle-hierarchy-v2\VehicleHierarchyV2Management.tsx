import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  Input,
  Badge,
  Button,
  CompanyAdminOnly,
} from "../index";
import {
  Search,
  Building2,
  Tag,
  Car,
  Plus,
  Edit,
  Calendar,
  Trash2,
  Grid3X3,
  List,
} from "lucide-react";
import {
  useTypedApi,
  type VehicleBrandV2,
  type VehicleSubBrandV2,
  type VehicleModelV2,
  type VehicleHierarchyDataV2,
  type VehicleHierarchyTreeDataV2,
} from "../../services/api-client";
import { CreateBrandV2Modal } from "./CreateBrandV2Modal";
import { CreateSubBrandV2Modal } from "./CreateSubBrandV2Modal";
import { CreateModelV2Modal } from "./CreateModelV2Modal";
import { EditBrandV2Modal } from "./EditBrandV2Modal";
import { EditSubBrandV2Modal } from "./EditSubBrandV2Modal";
import { EditModelV2Modal } from "./EditModelV2Modal";
import { ModelYearAssociationV2Modal } from "./ModelYearAssociationV2Modal";
import { DeleteConfirmationV2Modal } from "./DeleteConfirmationV2Modal";
import { YearManagementV2Modal } from "./YearManagementV2Modal";
import { VehicleHierarchyV2Tree } from "./VehicleHierarchyV2Tree";

interface VehicleHierarchyV2ManagementProps {
  className?: string;
}

export function VehicleHierarchyV2Management({
  className,
}: VehicleHierarchyV2ManagementProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("brands");

  // Modal states
  const [isCreateBrandModalOpen, setIsCreateBrandModalOpen] = useState(false);
  const [isCreateSubBrandModalOpen, setIsCreateSubBrandModalOpen] =
    useState(false);
  const [isCreateModelModalOpen, setIsCreateModelModalOpen] = useState(false);

  // Edit modal states
  const [isEditBrandModalOpen, setIsEditBrandModalOpen] = useState(false);
  const [isEditSubBrandModalOpen, setIsEditSubBrandModalOpen] = useState(false);
  const [isEditModelModalOpen, setIsEditModelModalOpen] = useState(false);

  // Year association modal state
  const [isYearAssociationModalOpen, setIsYearAssociationModalOpen] =
    useState(false);

  // Delete modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteItem, setDeleteItem] = useState<
    VehicleBrandV2 | VehicleSubBrandV2 | VehicleModelV2 | null
  >(null);
  const [deleteItemType, setDeleteItemType] = useState<
    "brand" | "sub-brand" | "model"
  >("brand");

  // Year management modal state
  const [isYearManagementModalOpen, setIsYearManagementModalOpen] =
    useState(false);

  // View mode state
  const [viewMode, setViewMode] = useState<"cards" | "tree">("cards");

  // Selected items for editing
  const [selectedBrand, setSelectedBrand] = useState<VehicleBrandV2 | null>(
    null,
  );
  const [selectedSubBrand, setSelectedSubBrand] =
    useState<VehicleSubBrandV2 | null>(null);
  const [selectedModel, setSelectedModel] = useState<VehicleModelV2 | null>(
    null,
  );

  // Edit handlers
  const handleEditBrand = (brand: VehicleBrandV2) => {
    setSelectedBrand(brand);
    setIsEditBrandModalOpen(true);
  };

  const handleEditSubBrand = (subBrand: VehicleSubBrandV2) => {
    setSelectedSubBrand(subBrand);
    setIsEditSubBrandModalOpen(true);
  };

  const handleEditModel = (model: VehicleModelV2) => {
    setSelectedModel(model);
    setIsEditModelModalOpen(true);
  };

  const handleManageModelYears = (model: VehicleModelV2) => {
    setSelectedModel(model);
    setIsYearAssociationModalOpen(true);
  };

  // Delete handlers
  const handleDeleteBrand = (brand: VehicleBrandV2) => {
    setDeleteItem(brand);
    setDeleteItemType("brand");
    setIsDeleteModalOpen(true);
  };

  const handleDeleteSubBrand = (subBrand: VehicleSubBrandV2) => {
    setDeleteItem(subBrand);
    setDeleteItemType("sub-brand");
    setIsDeleteModalOpen(true);
  };

  const handleDeleteModel = (model: VehicleModelV2) => {
    setDeleteItem(model);
    setDeleteItemType("model");
    setIsDeleteModalOpen(true);
  };

  // Calculate cascade information for delete confirmation
  const getCascadeInfo = () => {
    if (!deleteItem || !hierarchyData) return {};

    switch (deleteItemType) {
      case "brand": {
        const brand = deleteItem as VehicleBrandV2;
        const subBrands =
          hierarchyData.brands.find((b) => b.id === brand.id)?.subBrands || [];
        const models = subBrands.reduce(
          (total, sb) => total + (sb.models?.length || 0),
          0,
        );
        return {
          subBrands: subBrands.length,
          models: models,
        };
      }
      case "sub-brand": {
        const subBrand = deleteItem as VehicleSubBrandV2;
        const models =
          hierarchyData.brands
            .flatMap((b) => b.subBrands || [])
            .find((sb) => sb.id === subBrand.id)?.models || [];
        return {
          models: models.length,
        };
      }
      case "model": {
        // For models, we need to check year associations
        // This would require a separate API call or including this data in the hierarchy
        // For now, we'll return empty and let the backend handle the validation
        return {
          yearAssociations: 0, // TODO: Implement year association count
        };
      }
      default:
        return {};
    }
  };

  // Initialize API client
  const api = useTypedApi();

  // Queries
  const { data: brandsData, isLoading: brandsLoading } = useQuery({
    queryKey: ["vehicle-hierarchy-v2", "brands"],
    queryFn: () => api.vehicleHierarchyV2.getBrands(),
  });

  const { data: subBrandsData, isLoading: subBrandsLoading } = useQuery({
    queryKey: ["vehicle-hierarchy-v2", "sub-brands"],
    queryFn: () => api.vehicleHierarchyV2.getSubBrands(),
  });

  const { data: modelsData, isLoading: modelsLoading } = useQuery({
    queryKey: ["vehicle-hierarchy-v2", "models"],
    queryFn: () => api.vehicleHierarchyV2.getModels(),
  });

  // Full hierarchy for tree view
  const { data: fullHierarchyData } = useQuery({
    queryKey: ["vehicle-hierarchy-v2", "full"],
    queryFn: () => api.vehicleHierarchyV2.getFullHierarchy(),
  });

  const brands = brandsData?.data || [];
  const subBrands = subBrandsData?.data || [];
  const models = modelsData?.data || [];

  // Transform year-first hierarchy to brand-first hierarchy for tree view
  const transformToTreeData = (
    yearFirstData: VehicleHierarchyDataV2,
  ): VehicleHierarchyTreeDataV2 => {
    const brandMap = new Map<
      string,
      VehicleBrandV2 & {
        subBrands: Array<
          VehicleSubBrandV2 & {
            models: Array<
              VehicleModelV2 & {
                years: { id: string; year: number }[];
              }
            >;
          }
        >;
      }
    >();

    // Process all years and collect brands/sub-brands/models
    yearFirstData.years.forEach((year) => {
      year.brands.forEach((brand) => {
        if (!brandMap.has(brand.id)) {
          brandMap.set(brand.id, {
            ...brand,
            subBrands: [],
          });
        }

        const brandEntry = brandMap.get(brand.id)!;

        brand.subBrands.forEach((subBrand) => {
          let subBrandEntry = brandEntry.subBrands.find(
            (sb) => sb.id === subBrand.id,
          );
          if (!subBrandEntry) {
            subBrandEntry = {
              ...subBrand,
              models: [],
            };
            brandEntry.subBrands.push(subBrandEntry);
          }

          if (subBrand.models) {
            subBrand.models.forEach((model) => {
              let modelEntry = subBrandEntry!.models?.find(
                (m) => m.id === model.id,
              );
              if (!modelEntry) {
                modelEntry = {
                  ...model,
                  years: [],
                };
                if (subBrandEntry!.models) {
                  subBrandEntry!.models.push(modelEntry);
                }
              }

              // Add year if not already present
              if (
                modelEntry.years &&
                !modelEntry.years.find((y) => y.id === year.id)
              ) {
                modelEntry.years.push(year);
              }
            });
          }
        });
      });
    });

    return {
      brands: Array.from(brandMap.values()),
    };
  };

  const hierarchyData = fullHierarchyData?.data
    ? transformToTreeData(fullHierarchyData.data)
    : null;

  // Filter functions
  const filteredBrands = brands.filter((brand: VehicleBrandV2) =>
    brand.name.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const filteredSubBrands = subBrands.filter(
    (subBrand: VehicleSubBrandV2) =>
      subBrand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      subBrand.brand?.name.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const filteredModels = models.filter(
    (model: VehicleModelV2) =>
      model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      model.subBrand?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      model.subBrand?.brand?.name
        .toLowerCase()
        .includes(searchTerm.toLowerCase()),
  );

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Vehicle Hierarchy V2
            </h1>
            <p className="text-gray-600">
              Manage the new 4-level vehicle hierarchy: Year ↔ Model, Brand →
              Sub-Brand → Model
            </p>
          </div>

          <CompanyAdminOnly>
            <div className="flex items-center space-x-3">
              <Button
                onClick={() => setIsYearManagementModalOpen(true)}
                className="flex items-center space-x-2"
                variant="outline"
              >
                <Calendar className="h-4 w-4" />
                <span>Manage Years</span>
              </Button>

              <Button
                onClick={() => setIsCreateBrandModalOpen(true)}
                className="flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span>Add Brand</span>
              </Button>

              <Button
                onClick={() => setIsCreateSubBrandModalOpen(true)}
                className="flex items-center space-x-2"
                variant="outline"
              >
                <Plus className="h-4 w-4" />
                <span>Add Sub-Brand</span>
              </Button>

              <Button
                onClick={() => setIsCreateModelModalOpen(true)}
                className="flex items-center space-x-2"
                variant="outline"
              >
                <Plus className="h-4 w-4" />
                <span>Add Model</span>
              </Button>
            </div>
          </CompanyAdminOnly>
        </div>

        {/* Search and View Toggle */}
        <div className="flex items-center justify-between">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search brands, sub-brands, or models..."
              value={searchTerm}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setSearchTerm(e.target.value)
              }
              className="pl-8"
            />
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2 ml-4">
            <Button
              variant={viewMode === "cards" ? "primary" : "outline"}
              size="sm"
              onClick={() => setViewMode("cards")}
              className="flex items-center space-x-2"
            >
              <Grid3X3 className="h-4 w-4" />
              <span>Cards</span>
            </Button>
            <Button
              variant={viewMode === "tree" ? "primary" : "outline"}
              size="sm"
              onClick={() => setViewMode("tree")}
              className="flex items-center space-x-2"
            >
              <List className="h-4 w-4" />
              <span>Tree</span>
            </Button>
          </div>
        </div>

        {/* Tab Navigation - Only show in cards view */}
        {viewMode === "cards" && (
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab("brands")}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "brands"
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <Building2 className="h-4 w-4 inline mr-2" />
                Brands ({filteredBrands.length})
              </button>
              <button
                onClick={() => setActiveTab("sub-brands")}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "sub-brands"
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <Tag className="h-4 w-4 inline mr-2" />
                Sub-Brands ({filteredSubBrands.length})
              </button>
              <button
                onClick={() => setActiveTab("models")}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "models"
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <Car className="h-4 w-4 inline mr-2" />
                Models ({filteredModels.length})
              </button>
            </nav>
          </div>
        )}

        {/* Content */}
        <div className="mt-6">
          {viewMode === "tree" ? (
            /* Tree View */
            hierarchyData ? (
              <VehicleHierarchyV2Tree
                hierarchy={hierarchyData}
                onCreateSubBrand={(brand) => {
                  setSelectedBrand(brand);
                  setIsCreateSubBrandModalOpen(true);
                }}
                onCreateModel={(subBrand) => {
                  setSelectedSubBrand(subBrand);
                  setIsCreateModelModalOpen(true);
                }}
                onEditBrand={handleEditBrand}
                onEditSubBrand={handleEditSubBrand}
                onEditModel={handleEditModel}
                onDeleteBrand={handleDeleteBrand}
                onDeleteSubBrand={handleDeleteSubBrand}
                onDeleteModel={handleDeleteModel}
                onManageModelYears={handleManageModelYears}
                searchQuery={searchTerm}
              />
            ) : (
              <div className="text-center py-8">Loading hierarchy...</div>
            )
          ) : (
            /* Cards View */
            <>
              {activeTab === "brands" && (
                <div className="space-y-4">
                  <h2 className="text-xl font-semibold">Brands</h2>
                  {brandsLoading ? (
                    <div className="text-center py-8">Loading brands...</div>
                  ) : filteredBrands.length === 0 ? (
                    <Card>
                      <CardContent className="text-center py-8">
                        <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">
                          No brands found
                        </h3>
                        <p className="text-gray-600">
                          {searchTerm
                            ? "No brands match your search."
                            : "No brands have been created yet."}
                        </p>
                      </CardContent>
                    </Card>
                  ) : (
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                      {filteredBrands.map((brand: VehicleBrandV2) => (
                        <Card key={brand.id}>
                          <CardHeader title={brand.name}>
                            <div className="flex items-center space-x-2">
                              <Badge
                                variant={
                                  brand.isActive ? "default" : "secondary"
                                }
                              >
                                {brand.isActive ? "Active" : "Inactive"}
                              </Badge>
                              <CompanyAdminOnly>
                                <div className="flex items-center space-x-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleEditBrand(brand)}
                                    className="h-8 w-8 p-0"
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDeleteBrand(brand)}
                                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </CompanyAdminOnly>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <p className="text-sm text-gray-600">
                              Created:{" "}
                              {new Date(brand.createdAt).toLocaleDateString()}
                            </p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === "sub-brands" && (
                <div className="space-y-4">
                  <h2 className="text-xl font-semibold">Sub-Brands</h2>
                  {subBrandsLoading ? (
                    <div className="text-center py-8">
                      Loading sub-brands...
                    </div>
                  ) : filteredSubBrands.length === 0 ? (
                    <Card>
                      <CardContent className="text-center py-8">
                        <Tag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">
                          No sub-brands found
                        </h3>
                        <p className="text-gray-600">
                          {searchTerm
                            ? "No sub-brands match your search."
                            : "No sub-brands have been created yet."}
                        </p>
                      </CardContent>
                    </Card>
                  ) : (
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                      {filteredSubBrands.map((subBrand: VehicleSubBrandV2) => (
                        <Card key={subBrand.id}>
                          <CardHeader
                            title={subBrand.name}
                            subtitle={`${subBrand.brand?.name} → ${subBrand.name}`}
                          >
                            <div className="flex items-center space-x-2">
                              <Badge
                                variant={
                                  subBrand.isActive ? "default" : "secondary"
                                }
                              >
                                {subBrand.isActive ? "Active" : "Inactive"}
                              </Badge>
                              <CompanyAdminOnly>
                                <div className="flex items-center space-x-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleEditSubBrand(subBrand)}
                                    className="h-8 w-8 p-0"
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() =>
                                      handleDeleteSubBrand(subBrand)
                                    }
                                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </CompanyAdminOnly>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <p className="text-sm text-gray-600">
                              Created:{" "}
                              {new Date(
                                subBrand.createdAt,
                              ).toLocaleDateString()}
                            </p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === "models" && (
                <div className="space-y-4">
                  <h2 className="text-xl font-semibold">Models</h2>
                  {modelsLoading ? (
                    <div className="text-center py-8">Loading models...</div>
                  ) : filteredModels.length === 0 ? (
                    <Card>
                      <CardContent className="text-center py-8">
                        <Car className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">
                          No models found
                        </h3>
                        <p className="text-gray-600">
                          {searchTerm
                            ? "No models match your search."
                            : "No models have been created yet."}
                        </p>
                      </CardContent>
                    </Card>
                  ) : (
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                      {filteredModels.map((model: VehicleModelV2) => (
                        <Card key={model.id}>
                          <CardHeader
                            title={model.name}
                            subtitle={`${model.subBrand?.brand?.name} → ${model.subBrand?.name} → ${model.name}`}
                          >
                            <div className="flex items-center space-x-2">
                              <Badge
                                variant={
                                  model.isActive ? "default" : "secondary"
                                }
                              >
                                {model.isActive ? "Active" : "Inactive"}
                              </Badge>
                              <CompanyAdminOnly>
                                <div className="flex items-center space-x-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleEditModel(model)}
                                    className="h-8 w-8 p-0"
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDeleteModel(model)}
                                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </CompanyAdminOnly>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-3">
                              <p className="text-sm text-gray-600">
                                Created:{" "}
                                {new Date(model.createdAt).toLocaleDateString()}
                              </p>

                              {/* Year associations display */}
                              <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                  <span className="text-sm font-medium text-gray-700">
                                    Associated Years:
                                  </span>
                                  <CompanyAdminOnly>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() =>
                                        handleManageModelYears(model)
                                      }
                                      className="flex items-center space-x-1"
                                    >
                                      <Calendar className="h-3 w-3" />
                                      <span>Manage Years</span>
                                    </Button>
                                  </CompanyAdminOnly>
                                </div>

                                {/* TODO: Display actual year associations when available */}
                                <div className="flex flex-wrap gap-1">
                                  <Badge variant="outline" className="text-xs">
                                    Click "Manage Years" to view/edit
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Modals */}
      <CreateBrandV2Modal
        isOpen={isCreateBrandModalOpen}
        onClose={() => setIsCreateBrandModalOpen(false)}
      />

      <CreateSubBrandV2Modal
        isOpen={isCreateSubBrandModalOpen}
        onClose={() => setIsCreateSubBrandModalOpen(false)}
      />

      <CreateModelV2Modal
        isOpen={isCreateModelModalOpen}
        onClose={() => setIsCreateModelModalOpen(false)}
      />

      {/* Edit Modals */}
      <EditBrandV2Modal
        isOpen={isEditBrandModalOpen}
        onClose={() => setIsEditBrandModalOpen(false)}
        brand={selectedBrand}
      />

      <EditSubBrandV2Modal
        isOpen={isEditSubBrandModalOpen}
        onClose={() => setIsEditSubBrandModalOpen(false)}
        subBrand={selectedSubBrand}
      />

      <EditModelV2Modal
        isOpen={isEditModelModalOpen}
        onClose={() => setIsEditModelModalOpen(false)}
        model={selectedModel}
      />

      {/* Year Association Modal */}
      <ModelYearAssociationV2Modal
        isOpen={isYearAssociationModalOpen}
        onClose={() => setIsYearAssociationModalOpen(false)}
        model={selectedModel}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationV2Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        item={deleteItem}
        itemType={deleteItemType}
        cascadeInfo={getCascadeInfo()}
      />

      {/* Year Management Modal */}
      <YearManagementV2Modal
        isOpen={isYearManagementModalOpen}
        onClose={() => setIsYearManagementModalOpen(false)}
      />
    </div>
  );
}
