import React from "react";
import { clsx } from "clsx";
import { Card } from "./Card";

export interface FeatureCardProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Feature title */
  title: string;
  /** Feature description */
  description: string;
  /** Icon element to display */
  icon?: React.ReactNode;
  /** Visual variant */
  variant?: "default" | "elevated" | "bordered";
  /** Size variant */
  size?: "sm" | "md" | "lg";
  /** Layout orientation */
  orientation?: "vertical" | "horizontal";
  /** Icon background style */
  iconStyle?: "circle" | "square" | "none";
  /** Icon color theme */
  iconTheme?: "primary" | "secondary" | "success" | "warning" | "error";
  /** Whether the card is interactive (clickable) */
  interactive?: boolean;
  /** Click handler for interactive cards */
  onCardClick?: () => void;
  /** Link href for interactive cards */
  href?: string;
}

const sizeClasses = {
  sm: {
    padding: "p-4",
    iconSize: "h-10 w-10",
    iconContainer: "h-12 w-12",
    titleSize: "text-lg",
    descriptionSize: "text-sm",
    spacing: "mb-3",
  },
  md: {
    padding: "p-6",
    iconSize: "h-6 w-6",
    iconContainer: "h-14 w-14",
    titleSize: "text-xl",
    descriptionSize: "text-base",
    spacing: "mb-4",
  },
  lg: {
    padding: "p-8",
    iconSize: "h-8 w-8",
    iconContainer: "h-16 w-16",
    titleSize: "text-2xl",
    descriptionSize: "text-lg",
    spacing: "mb-6",
  },
} as const;

const iconStyleClasses = {
  circle: "rounded-full",
  square: "rounded-lg",
  none: "",
} as const;

const iconThemeClasses = {
  primary: "bg-primary-100 text-primary-600",
  secondary: "bg-gray-100 text-gray-600",
  success: "bg-green-100 text-green-600",
  warning: "bg-yellow-100 text-yellow-600",
  error: "bg-red-100 text-red-600",
} as const;

const variantClasses = {
  default: "shadow-xs hover:shadow-sm",
  elevated: "shadow-sm hover:shadow-base",
  bordered: "border-2 border-gray-200 hover:border-primary-300 shadow-xs",
} as const;

const orientationClasses = {
  vertical: "text-center",
  horizontal: "text-left flex items-start space-x-4",
} as const;

export const FeatureCard: React.FC<FeatureCardProps> = ({
  title,
  description,
  icon,
  variant = "default",
  size = "md",
  orientation = "vertical",
  iconStyle = "circle",
  iconTheme = "primary",
  interactive = false,
  onCardClick,
  href,
  className,
  children,
  ...props
}) => {
  const sizeConfig = sizeClasses[size];

  const cardContent = (
    <>
      {icon && (
        <div
          className={clsx(
            orientation === "vertical" && "mx-auto",
            orientation === "vertical" && sizeConfig.spacing,
            orientation === "horizontal" && "flex-shrink-0",
          )}
        >
          <div
            className={clsx(
              "flex items-center justify-center",
              sizeConfig.iconContainer,
              iconStyle !== "none" && iconStyleClasses[iconStyle],
              iconStyle !== "none" && iconThemeClasses[iconTheme],
            )}
          >
            <div className={sizeConfig.iconSize}>{icon}</div>
          </div>
        </div>
      )}

      <div className={clsx(orientation === "horizontal" && "flex-1")}>
        <h3
          className={clsx(
            "font-semibold text-gray-900 tracking-tight",
            sizeConfig.titleSize,
            sizeConfig.spacing,
          )}
        >
          {title}
        </h3>

        <p
          className={clsx(
            "text-gray-600 leading-relaxed",
            sizeConfig.descriptionSize,
          )}
        >
          {description}
        </p>

        {children && <div className="mt-4">{children}</div>}
      </div>
    </>
  );

  const cardProps = {
    className: clsx(
      sizeConfig.padding,
      orientationClasses[orientation],
      variantClasses[variant],
      interactive && "cursor-pointer transition-all duration-200",
      interactive && "hover:scale-[1.02] active:scale-[0.98]",
      className,
    ),
    onClick: interactive ? onCardClick : undefined,
    "data-testid": "feature-card-container",
    ...props,
  };

  if (href && interactive) {
    return (
      <a href={href} className={clsx("block", cardProps.className)}>
        <Card
          className={clsx(
            sizeConfig.padding,
            orientationClasses[orientation],
            variantClasses[variant],
            "cursor-pointer transition-all duration-200",
            "hover:scale-[1.02] active:scale-[0.98]",
          )}
        >
          {cardContent}
        </Card>
      </a>
    );
  }

  return <Card {...cardProps}>{cardContent}</Card>;
};
