import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON><PERSON>, <PERSON>, CardHeader, CardContent, DocumentUploadModal } from "../../components";
import { useTypedApi } from "../../services/api-client";
import { Upload, FileText } from "lucide-react";

export const DocumentsPage: React.FC = () => {
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const api = useTypedApi();

  // Query for documents list (for future phases)
  const { refetch: refetchDocuments } = useQuery({
    queryKey: ['documents'],
    queryFn: () => api.documents.getDocuments({ page: 1, limit: 20 }),
    enabled: false, // Disable for now since Phase 6 will implement the full list
  });

  const handleUploadSuccess = () => {
    // Refresh documents list when upload succeeds
    refetchDocuments();
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Documents</h1>
          <p className="text-gray-600 mt-1">
            Upload and manage your documents securely
          </p>
        </div>
        <Button
          variant="primary"
          onClick={() => setIsUploadModalOpen(true)}
          className="flex items-center space-x-2"
        >
          <Upload className="h-4 w-4" />
          <span>Upload Document</span>
        </Button>
      </div>

      {/* Upload Instructions Card */}
      <Card>
        <CardHeader title="Getting Started" />
        <CardContent>
          <div className="space-y-4">
            <p className="text-gray-600">
              Upload your documents to securely store and manage them in the cloud. 
              Supported file types include:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2 text-sm text-gray-700">
                <FileText className="h-4 w-4 text-red-500" />
                <span>PDF files (up to 50MB)</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-700">
                <FileText className="h-4 w-4 text-blue-500" />
                <span>Office files (up to 25MB)</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-700">
                <FileText className="h-4 w-4 text-green-500" />
                <span>Images & Data (up to 10MB)</span>
              </div>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-sm text-blue-800">
                <strong>Note:</strong> This is Phase 5 of the document storage implementation. 
                Document listing, viewing, and management features will be available in upcoming phases.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Placeholder for future document list (Phase 6) */}
      <Card>
        <CardHeader title="Your Documents" />
        <CardContent>
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Document List Coming Soon
            </h3>
            <p className="text-gray-600 mb-6">
              The document listing and management interface will be implemented in Phase 6.
              For now, you can upload documents using the button above.
            </p>
            <Button
              variant="primary"
              onClick={() => setIsUploadModalOpen(true)}
              className="flex items-center space-x-2 mx-auto"
            >
              <Upload className="h-4 w-4" />
              <span>Upload Your First Document</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Upload Modal */}
      <DocumentUploadModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        onUploadSuccess={handleUploadSuccess}
      />
    </div>
  );
};

export default DocumentsPage;
