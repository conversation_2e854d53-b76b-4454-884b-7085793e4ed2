import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import { FeatureCard } from "./FeatureCard";
import { Users } from "lucide-react";

describe("FeatureCard", () => {
  const defaultProps = {
    title: "Test Feature",
    description:
      "This is a test feature description that explains the functionality.",
  };

  describe("Rendering", () => {
    it("should render feature card with title and description", () => {
      render(<FeatureCard {...defaultProps} />);

      expect(screen.getByRole("heading", { level: 3 })).toHaveTextContent(
        "Test Feature",
      );
      expect(
        screen.getByText(
          "This is a test feature description that explains the functionality.",
        ),
      ).toBeInTheDocument();
    });

    it("should render with custom className", () => {
      render(<FeatureCard {...defaultProps} className="custom-feature" />);

      const card = screen
        .getByRole("heading", { level: 3 })
        .closest(".custom-feature");
      expect(card).toBeInTheDocument();
    });

    it("should render with icon", () => {
      render(
        <FeatureCard
          {...defaultProps}
          icon={<Users data-testid="users-icon" />}
        />,
      );

      expect(screen.getByTestId("users-icon")).toBeInTheDocument();
    });

    it("should render additional children content", () => {
      render(
        <FeatureCard {...defaultProps}>
          <div data-testid="additional-content">Extra content</div>
        </FeatureCard>,
      );

      expect(screen.getByTestId("additional-content")).toBeInTheDocument();
      expect(screen.getByText("Extra content")).toBeInTheDocument();
    });
  });

  describe("Variants", () => {
    it("should render default variant with basic shadow", () => {
      render(<FeatureCard {...defaultProps} variant="default" />);

      const card = screen.getByTestId("feature-card-container");
      expect(card).toHaveClass("shadow-xs", "hover:shadow-sm");
    });

    it("should render elevated variant with enhanced shadow", () => {
      render(<FeatureCard {...defaultProps} variant="elevated" />);

      const card = screen.getByTestId("feature-card-container");
      expect(card).toHaveClass("shadow-sm", "hover:shadow-base");
    });

    it("should render bordered variant with border", () => {
      render(<FeatureCard {...defaultProps} variant="bordered" />);

      const card = screen.getByTestId("feature-card-container");
      expect(card).toHaveClass(
        "border-2",
        "border-gray-200",
        "hover:border-primary-300",
      );
    });
  });

  describe("Sizes", () => {
    it("should render small size with appropriate classes", () => {
      render(
        <FeatureCard
          {...defaultProps}
          size="sm"
          icon={<Users data-testid="icon" />}
        />,
      );

      const card = screen.getByTestId("feature-card-container");
      const title = screen.getByRole("heading", { level: 3 });
      const iconContainer =
        screen.getByTestId("icon").parentElement?.parentElement;

      expect(card).toHaveClass("p-4");
      expect(title).toHaveClass("text-lg");
      expect(iconContainer).toHaveClass("h-12", "w-12");
    });

    it("should render medium size with appropriate classes (default)", () => {
      render(
        <FeatureCard
          {...defaultProps}
          size="md"
          icon={<Users data-testid="icon" />}
        />,
      );

      const card = screen.getByTestId("feature-card-container");
      const title = screen.getByRole("heading", { level: 3 });
      const iconContainer =
        screen.getByTestId("icon").parentElement?.parentElement;

      expect(card).toHaveClass("p-6");
      expect(title).toHaveClass("text-xl");
      expect(iconContainer).toHaveClass("h-14", "w-14");
    });

    it("should render large size with appropriate classes", () => {
      render(
        <FeatureCard
          {...defaultProps}
          size="lg"
          icon={<Users data-testid="icon" />}
        />,
      );

      const card = screen.getByTestId("feature-card-container");
      const title = screen.getByRole("heading", { level: 3 });
      const iconContainer =
        screen.getByTestId("icon").parentElement?.parentElement;

      expect(card).toHaveClass("p-8");
      expect(title).toHaveClass("text-2xl");
      expect(iconContainer).toHaveClass("h-16", "w-16");
    });
  });

  describe("Orientation", () => {
    it("should render vertical orientation by default", () => {
      render(
        <FeatureCard {...defaultProps} icon={<Users data-testid="icon" />} />,
      );

      const card = screen.getByTestId("feature-card-container");
      expect(card).toHaveClass("text-center");
      expect(card).not.toHaveClass("flex", "items-start", "space-x-4");
    });

    it("should render horizontal orientation", () => {
      render(
        <FeatureCard
          {...defaultProps}
          orientation="horizontal"
          icon={<Users data-testid="icon" />}
        />,
      );

      const card = screen.getByTestId("feature-card-container");
      expect(card).toHaveClass("text-left", "flex", "items-start", "space-x-4");
    });

    it("should center icon in vertical orientation", () => {
      render(
        <FeatureCard
          {...defaultProps}
          orientation="vertical"
          icon={<Users data-testid="icon" />}
        />,
      );

      const iconWrapper =
        screen.getByTestId("icon").parentElement?.parentElement?.parentElement;
      expect(iconWrapper).toHaveClass("mx-auto");
    });

    it("should not center icon in horizontal orientation", () => {
      render(
        <FeatureCard
          {...defaultProps}
          orientation="horizontal"
          icon={<Users data-testid="icon" />}
        />,
      );

      const iconWrapper =
        screen.getByTestId("icon").parentElement?.parentElement?.parentElement;
      expect(iconWrapper).toHaveClass("flex-shrink-0");
      expect(iconWrapper).not.toHaveClass("mx-auto");
    });
  });

  describe("Icon Styling", () => {
    it("should render circle icon style by default", () => {
      render(
        <FeatureCard {...defaultProps} icon={<Users data-testid="icon" />} />,
      );

      const iconContainer =
        screen.getByTestId("icon").parentElement?.parentElement;
      expect(iconContainer).toHaveClass("rounded-full");
    });

    it("should render square icon style", () => {
      render(
        <FeatureCard
          {...defaultProps}
          icon={<Users data-testid="icon" />}
          iconStyle="square"
        />,
      );

      const iconContainer =
        screen.getByTestId("icon").parentElement?.parentElement;
      expect(iconContainer).toHaveClass("rounded-lg");
    });

    it("should render no icon style", () => {
      render(
        <FeatureCard
          {...defaultProps}
          icon={<Users data-testid="icon" />}
          iconStyle="none"
        />,
      );

      const iconContainer =
        screen.getByTestId("icon").parentElement?.parentElement;
      expect(iconContainer).not.toHaveClass("rounded-full", "rounded-lg");
    });
  });

  describe("Icon Themes", () => {
    it("should render primary theme by default", () => {
      render(
        <FeatureCard {...defaultProps} icon={<Users data-testid="icon" />} />,
      );

      const iconContainer =
        screen.getByTestId("icon").parentElement?.parentElement;
      expect(iconContainer).toHaveClass("bg-primary-100", "text-primary-600");
    });

    it("should render secondary theme", () => {
      render(
        <FeatureCard
          {...defaultProps}
          icon={<Users data-testid="icon" />}
          iconTheme="secondary"
        />,
      );

      const iconContainer =
        screen.getByTestId("icon").parentElement?.parentElement;
      expect(iconContainer).toHaveClass("bg-gray-100", "text-gray-600");
    });

    it("should render success theme", () => {
      render(
        <FeatureCard
          {...defaultProps}
          icon={<Users data-testid="icon" />}
          iconTheme="success"
        />,
      );

      const iconContainer =
        screen.getByTestId("icon").parentElement?.parentElement;
      expect(iconContainer).toHaveClass("bg-green-100", "text-green-600");
    });

    it("should render warning theme", () => {
      render(
        <FeatureCard
          {...defaultProps}
          icon={<Users data-testid="icon" />}
          iconTheme="warning"
        />,
      );

      const iconContainer =
        screen.getByTestId("icon").parentElement?.parentElement;
      expect(iconContainer).toHaveClass("bg-yellow-100", "text-yellow-600");
    });

    it("should render error theme", () => {
      render(
        <FeatureCard
          {...defaultProps}
          icon={<Users data-testid="icon" />}
          iconTheme="error"
        />,
      );

      const iconContainer =
        screen.getByTestId("icon").parentElement?.parentElement;
      expect(iconContainer).toHaveClass("bg-red-100", "text-red-600");
    });
  });

  describe("Interactive Behavior", () => {
    it("should not be interactive by default", () => {
      render(<FeatureCard {...defaultProps} />);

      const card = screen.getByRole("heading", { level: 3 }).closest("div");
      expect(card).not.toHaveClass("cursor-pointer");
    });

    it("should render as interactive when specified", () => {
      render(<FeatureCard {...defaultProps} interactive />);

      const card = screen.getByTestId("feature-card-container");
      expect(card).toHaveClass(
        "cursor-pointer",
        "transition-all",
        "duration-200",
      );
      expect(card).toHaveClass("hover:scale-[1.02]", "active:scale-[0.98]");
    });

    it("should handle click events when interactive", () => {
      const handleClick = vi.fn();
      render(
        <FeatureCard {...defaultProps} interactive onCardClick={handleClick} />,
      );

      const card = screen.getByRole("heading", { level: 3 }).closest("div");
      fireEvent.click(card!);

      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it("should render as link when href provided", () => {
      render(
        <FeatureCard {...defaultProps} interactive href="/feature-details" />,
      );

      const link = screen.getByRole("link");
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute("href", "/feature-details");
      expect(link).toHaveClass("block");
    });

    it("should not render as link when not interactive", () => {
      render(<FeatureCard {...defaultProps} href="/feature-details" />);

      expect(screen.queryByRole("link")).not.toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("should have proper heading hierarchy", () => {
      render(<FeatureCard {...defaultProps} />);

      const heading = screen.getByRole("heading", { level: 3 });
      expect(heading).toBeInTheDocument();
      expect(heading).toHaveTextContent("Test Feature");
    });

    it("should support keyboard navigation when interactive", () => {
      const handleClick = vi.fn();
      render(
        <FeatureCard
          {...defaultProps}
          interactive
          onCardClick={handleClick}
          tabIndex={0}
        />,
      );

      const card = screen.getByTestId("feature-card-container");
      card?.focus();
      expect(document.activeElement).toBe(card);
    });

    it("should be accessible as a link when href provided", () => {
      render(
        <FeatureCard {...defaultProps} interactive href="/feature-details" />,
      );

      const link = screen.getByRole("link");
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute("href", "/feature-details");
    });
  });
});
