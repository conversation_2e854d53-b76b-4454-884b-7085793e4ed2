import { BackendAuthContext, UserRoleWithContext } from '@tech-notes/shared';

/**
 * Test fixtures for creating BackendAuthContext objects with different user types
 * This eliminates the need for system-level context workarounds in tests
 */

export interface CreateAuthContextOptions {
  id?: string;
  clerkId?: string;
  tenantId?: string;
  email?: string;
  firstName?: string | null;
  lastName?: string | null;
  imageUrl?: string | null;
  isActive?: boolean;
  roles?: UserRoleWithContext[];
  permissions?: string[];
  canBypassTenantScope?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Create a test BackendAuthContext with sensible defaults
 */
export function createTestAuthContext(
  overrides: CreateAuthContextOptions = {}
): BackendAuthContext {
  const now = new Date();

  return {
    id: overrides.id || 'test-user-id',
    clerkId: overrides.clerkId || 'test-clerk-id',
    tenantId: overrides.tenantId || 'test-tenant-id',
    email: overrides.email || '<EMAIL>',
    firstName: overrides.firstName !== undefined ? overrides.firstName : 'Test',
    lastName: overrides.lastName !== undefined ? overrides.lastName : 'User',
    imageUrl: overrides.imageUrl !== undefined ? overrides.imageUrl : null,
    isActive: overrides.isActive !== undefined ? overrides.isActive : true,
    createdAt: overrides.createdAt || now,
    updatedAt: overrides.updatedAt || now,
    roles: overrides.roles || [],
    permissions: overrides.permissions || [],
    canBypassTenantScope: overrides.canBypassTenantScope || false,
  };
}

/**
 * Create a System Admin BackendAuthContext
 */
export function createSystemAdminContext(
  overrides: CreateAuthContextOptions = {}
): BackendAuthContext {
  return createTestAuthContext({
    id: 'system-admin-id',
    clerkId: 'clerk-system-admin',
    email: '<EMAIL>',
    firstName: 'System',
    lastName: 'Admin',
    canBypassTenantScope: true,
    roles: [
      {
        id: 'system-admin-role-id',
        role: {
          id: 'system-admin-role',
          name: 'System Administrator',
          type: 'SYSTEM_ADMIN' as const,
          isSystemRole: true,
        },
        tenantId: null,
      },
    ],
    permissions: ['SYSTEM:ADMIN'],
    ...overrides,
  });
}

/**
 * Create a Company Admin BackendAuthContext
 */
export function createCompanyAdminContext(
  overrides: CreateAuthContextOptions = {}
): BackendAuthContext {
  return createTestAuthContext({
    id: 'company-admin-id',
    clerkId: 'clerk-company-admin',
    email: '<EMAIL>',
    firstName: 'Company',
    lastName: 'Admin',
    canBypassTenantScope: false,
    roles: [
      {
        id: 'company-admin-role-id',
        role: {
          id: 'company-admin-role',
          name: 'Company Administrator',
          type: 'COMPANY_ADMIN' as const,
          isSystemRole: false,
        },
        tenantId: overrides.tenantId || 'test-tenant-id',
      },
    ],
    permissions: [
      'USER:READ',
      'USER:WRITE',
      'USER:DELETE',
      'TENANT:READ',
      'TENANT:WRITE',
    ],
    ...overrides,
  });
}

/**
 * Create a Company Tech BackendAuthContext
 */
export function createCompanyTechContext(
  overrides: CreateAuthContextOptions = {}
): BackendAuthContext {
  return createTestAuthContext({
    id: 'company-tech-id',
    clerkId: 'clerk-company-tech',
    email: '<EMAIL>',
    firstName: 'Company',
    lastName: 'Tech',
    canBypassTenantScope: false,
    roles: [
      {
        id: 'company-tech-role-id',
        role: {
          id: 'company-tech-role',
          name: 'Company Technician',
          type: 'COMPANY_TECH' as const,
          isSystemRole: false,
        },
        tenantId: overrides.tenantId || 'test-tenant-id',
      },
    ],
    permissions: ['USER:READ', 'DATA:READ'],
    ...overrides,
  });
}

/**
 * Create an BackendAuthContext for a different tenant (for cross-tenant testing)
 */
export function createCrossTenantContext(
  tenantId: string,
  overrides: CreateAuthContextOptions = {}
): BackendAuthContext {
  return createTestAuthContext({
    id: `user-${tenantId}`,
    clerkId: `clerk-user-${tenantId}`,
    tenantId,
    email: `user@${tenantId}.com`,
    firstName: 'Cross',
    lastName: 'Tenant',
    ...overrides,
  });
}

/**
 * Create an BackendAuthContext for onboarding scenarios (no tenant yet)
 */
export function createOnboardingContext(
  overrides: CreateAuthContextOptions = {}
): BackendAuthContext {
  return createTestAuthContext({
    id: 'onboarding-user-id',
    clerkId: 'clerk-onboarding-user',
    tenantId: '', // No tenant during onboarding
    email: '<EMAIL>',
    firstName: 'New',
    lastName: 'User',
    roles: [],
    permissions: [],
    canBypassTenantScope: false,
    ...overrides,
  });
}

/**
 * Create multiple BackendAuthContext objects for different tenants
 */
export function createMultiTenantContexts(
  tenantIds: string[]
): BackendAuthContext[] {
  return tenantIds.map((tenantId, index) =>
    createCrossTenantContext(tenantId, {
      id: `user-${index + 1}`,
      clerkId: `clerk-user-${index + 1}`,
      email: `user${index + 1}@${tenantId}.com`,
    })
  );
}
