import { Router, NextFunction, Request, Response } from 'express';
import { z } from 'zod';

import { MiddlewareFactory } from '../../../middleware/middleware-factory.js';
import { createValidationMiddleware } from '../../../middleware/validation.middleware.js';
import { VehicleHierarchyService } from '../../../services/vehicle-hierarchy.service.js';
import { Logger } from '../../../utils/logger.js';
import { getRequestUser } from '../../../utils/request-types.js';

interface ServiceDependencies {
  vehicleHierarchyService: VehicleHierarchyService;
  middlewareFactory: MiddlewareFactory;
  logger: Logger;
}

// Validation schemas
const createYearSchema = z.object({
  year: z.number().int().min(1900).max(2050),
});

const createMakeSchema = z.object({
  name: z.string().min(1).max(100).trim(),
});

const createModelSchema = z.object({
  name: z.string().min(1).max(100).trim(),
  makeId: z.string().cuid(),
  isActive: z.boolean().optional(),
});

const bulkCreateYearsSchema = z.object({
  startYear: z.number().int().min(1900).max(2050),
  endYear: z.number().int().min(1900).max(2050),
});

const bulkCreateModelsSchema = z.object({
  modelNames: z.array(z.string().min(1).max(100).trim()).min(1).max(100),
});

const associateModelYearsSchema = z.object({
  yearIds: z.array(z.string().cuid()).min(1),
});

const bulkAssociateModelYearsSchema = z.object({
  associations: z
    .array(
      z.object({
        modelId: z.string().cuid(),
        yearIds: z.array(z.string().cuid()).min(1),
      })
    )
    .min(1),
});

const idParamSchema = z.object({
  id: z.string().cuid(),
});

const yearParamSchema = z.object({
  year: z.string().transform(Number).pipe(z.number().int().min(1900).max(2050)),
});

const modelYearParamsSchema = z.object({
  modelId: z.string().cuid(),
  yearId: z.string().cuid(),
});

export function createVehicleHierarchyRouter(
  dependencies: ServiceDependencies
): Router {
  const { vehicleHierarchyService, middlewareFactory, logger } = dependencies;
  const router = Router();

  const validate = createValidationMiddleware({ logger });

  // ===== YEAR ROUTES =====

  /**
   * GET /api/v1/vehicle-hierarchy/years
   * Get all years for tenant
   */
  router.get(
    '/years',
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const years = await vehicleHierarchyService.getYearsByTenant(
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: years,
          meta: {
            count: years.length,
            tenantId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch years', {
          error,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy/years
   * Create a new year
   */
  router.post(
    '/years',
    validate({ body: createYearSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const yearData = req.body;

        const year = await vehicleHierarchyService.createYear(
          yearData,
          tenantId,
          getRequestUser(req)!
        );

        res.status(201).json({
          data: year,
          message: 'Year created successfully',
        });
      } catch (error) {
        logger.error('Failed to create year', {
          error,
          yearData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy/years/bulk-create
   * Create multiple years in a range
   */
  router.post(
    '/years/bulk-create',
    validate({ body: bulkCreateYearsSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req, res, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { startYear, endYear } = req.body;

        const years = await vehicleHierarchyService.createYearRange(
          startYear,
          endYear,
          tenantId,
          getRequestUser(req)!
        );

        res.status(201).json({
          data: years,
          message: `Created ${years.length} years successfully`,
          meta: {
            count: years.length,
            range: `${startYear}-${endYear}`,
          },
        });
      } catch (error) {
        logger.error('Failed to bulk create years', {
          error,
          yearRange: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * DELETE /api/v1/vehicle-hierarchy/years/:id
   * Delete a year
   */
  router.delete(
    '/years/:id',
    validate({ params: idParamSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id } = req.params;

        await vehicleHierarchyService.deleteYear(
          id,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Year deleted successfully',
        });
      } catch (error) {
        logger.error('Failed to delete year', {
          error,
          yearId: req.params.id,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  // ===== MAKE ROUTES =====

  /**
   * GET /api/v1/vehicle-hierarchy/makes
   * Get all makes for tenant
   */
  router.get(
    '/makes',
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const makes = await vehicleHierarchyService.getMakesByTenant(
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: makes,
          meta: {
            count: makes.length,
            tenantId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch makes', {
          error,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy/makes
   * Create a new make
   */
  router.post(
    '/makes',
    validate({ body: createMakeSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const makeData = req.body;

        const make = await vehicleHierarchyService.createMake(
          makeData,
          tenantId,
          getRequestUser(req)!
        );

        res.status(201).json({
          data: make,
          message: 'Make created successfully',
        });
      } catch (error) {
        logger.error('Failed to create make', {
          error,
          makeData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * PUT /api/v1/vehicle-hierarchy/makes/:id
   * Update a make
   */
  router.put(
    '/makes/:id',
    validate({ params: idParamSchema, body: createMakeSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id } = req.params;
        const updateData = req.body;

        const make = await vehicleHierarchyService.updateMake(
          id,
          updateData,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: make,
          message: 'Make updated successfully',
        });
      } catch (error) {
        logger.error('Failed to update make', {
          error,
          makeId: req.params.id,
          updateData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy/makes/:id/models/bulk-create
   * Bulk create models for a make
   */
  router.post(
    '/makes/:id/models/bulk-create',
    validate({ params: idParamSchema, body: bulkCreateModelsSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id: makeId } = req.params;
        const { modelNames } = req.body;

        const models = await vehicleHierarchyService.bulkCreateMakeModels(
          makeId,
          modelNames,
          tenantId,
          getRequestUser(req)!
        );

        res.status(201).json({
          data: models,
          message: `Created ${models.length} models successfully`,
          meta: {
            count: models.length,
            makeId,
          },
        });
      } catch (error) {
        logger.error('Failed to bulk create models', {
          error,
          makeId: req.params.id,
          modelNames: req.body.modelNames,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * DELETE /api/v1/vehicle-hierarchy/makes/:id
   * Delete a make
   */
  router.delete(
    '/makes/:id',
    validate({ params: idParamSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id } = req.params;

        await vehicleHierarchyService.deleteMake(
          id,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Make deleted successfully',
        });
      } catch (error) {
        logger.error('Failed to delete make', {
          error,
          makeId: req.params.id,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  // ===== MODEL ROUTES =====

  /**
   * GET /api/v1/vehicle-hierarchy/models
   * Get all models for tenant (optionally filtered by make)
   */
  router.get(
    '/models',
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { makeId } = req.query;

        let models;
        if (makeId && typeof makeId === 'string') {
          models = await vehicleHierarchyService.getModelsByMake(
            makeId,
            tenantId,
            getRequestUser(req)!
          );
        } else {
          // For now, require makeId - we can add getAllModels later if needed
          return res.status(400).json({
            error: 'Bad Request',
            message: 'makeId query parameter is required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        res.json({
          data: models,
          meta: {
            count: models.length,
            tenantId,
            makeId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch models', {
          error,
          tenantId: getRequestUser(req)?.tenantId,
          makeId: req.query.makeId,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/vehicle-hierarchy/models/all
   * Get all models for tenant with their makes
   */
  router.get(
    '/models/all',
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const models = await vehicleHierarchyService.getAllModelsByTenant(
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: models,
          meta: {
            count: models.length,
            tenantId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch all models', {
          error,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy/models
   * Create a new model
   */
  router.post(
    '/models',
    validate({ body: createModelSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const modelData = req.body;

        const model = await vehicleHierarchyService.createModel(
          modelData,
          tenantId,
          getRequestUser(req)!
        );

        res.status(201).json({
          data: model,
          message: 'Model created successfully',
        });
      } catch (error) {
        logger.error('Failed to create model', {
          error,
          modelData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * PUT /api/v1/vehicle-hierarchy/models/:id
   * Update a model
   */
  router.put(
    '/models/:id',
    validate({
      params: idParamSchema,
      body: z.object({ name: z.string().min(1).max(100).trim() }),
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id } = req.params;
        const updateData = req.body;

        const model = await vehicleHierarchyService.updateModel(
          id,
          updateData,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: model,
          message: 'Model updated successfully',
        });
      } catch (error) {
        logger.error('Failed to update model', {
          error,
          modelId: req.params.id,
          updateData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy/models/:id/years/associate
   * Associate a model with multiple years
   */
  router.post(
    '/models/:id/years/associate',
    validate({ params: idParamSchema, body: associateModelYearsSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id: modelId } = req.params;
        const { yearIds } = req.body;

        await vehicleHierarchyService.associateModelWithYears(
          modelId,
          yearIds,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: `Model associated with ${yearIds.length} years successfully`,
          meta: {
            modelId,
            yearCount: yearIds.length,
          },
        });
      } catch (error) {
        logger.error('Failed to associate model with years', {
          error,
          modelId: req.params.id,
          yearIds: req.body.yearIds,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/vehicle-hierarchy/models/:id/years
   * Get years associated with a model
   */
  router.get(
    '/models/:id/years',
    validate({ params: idParamSchema }),
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id: modelId } = req.params;

        const years = await vehicleHierarchyService.getYearsByModel(
          modelId,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: years,
          meta: {
            count: years.length,
            modelId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch years for model', {
          error,
          modelId: req.params.id,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * DELETE /api/v1/vehicle-hierarchy/models/:modelId/years/:yearId
   * Remove model-year association
   */
  router.delete(
    '/models/:modelId/years/:yearId',
    validate({ params: modelYearParamsSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { modelId, yearId } = req.params;

        await vehicleHierarchyService.removeModelYearAssociation(
          modelId,
          yearId,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Model-year association removed successfully',
        });
      } catch (error) {
        logger.error('Failed to remove model-year association', {
          error,
          modelId: req.params.modelId,
          yearId: req.params.yearId,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * DELETE /api/v1/vehicle-hierarchy/models/:id
   * Delete a model
   */
  router.delete(
    '/models/:id',
    validate({ params: idParamSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id } = req.params;

        await vehicleHierarchyService.deleteModel(
          id,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Model deleted successfully',
        });
      } catch (error) {
        logger.error('Failed to delete model', {
          error,
          modelId: req.params.id,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  // ===== HIERARCHY QUERY ROUTES =====

  /**
   * GET /api/v1/vehicle-hierarchy/full
   * Get full hierarchy tree for tenant
   */
  router.get(
    '/full',
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const hierarchy = await vehicleHierarchyService.getFullHierarchy(
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: hierarchy,
          meta: {
            tenantId,
            yearCount: hierarchy.years.length,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch full hierarchy', {
          error,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/vehicle-hierarchy/by-year/:year
   * Get hierarchy for a specific year
   */
  router.get(
    '/by-year/:year',
    validate({ params: yearParamSchema }),
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { year } = req.params;

        const hierarchy = await vehicleHierarchyService.getHierarchyByYear(
          Number(year),
          tenantId,
          getRequestUser(req)!
        );

        if (!hierarchy) {
          return res.status(404).json({
            error: 'Not Found',
            message: `No data found for year ${year}`,
            statusCode: 404,
            timestamp: new Date().toISOString(),
          });
        }

        res.json({
          data: hierarchy,
          meta: {
            tenantId,
            year,
            makeCount: hierarchy.makes.length,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch hierarchy by year', {
          error,
          year: req.params.year,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/vehicle-hierarchy/models/by-year/:yearId
   * Get models for a specific year
   */
  router.get(
    '/models/by-year/:yearId',
    validate({ params: idParamSchema }),
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id: yearId } = req.params;

        const models = await vehicleHierarchyService.getModelsByYear(
          yearId,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: models,
          meta: {
            count: models.length,
            tenantId,
            yearId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch models by year', {
          error,
          yearId: req.params.id,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy/models/bulk-associate-years
   * Bulk associate multiple models with years
   */
  router.post(
    '/models/bulk-associate-years',
    validate({ body: bulkAssociateModelYearsSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { associations } = req.body;

        await vehicleHierarchyService.bulkAssociateModelYears(
          associations,
          tenantId,
          getRequestUser(req)!
        );

        const totalAssociations = associations.reduce(
          (sum: number, assoc: { yearIds: string[] }) =>
            sum + assoc.yearIds.length,
          0
        );

        res.json({
          message: `Bulk association completed successfully`,
          meta: {
            modelCount: associations.length,
            totalAssociations,
          },
        });
      } catch (error) {
        logger.error('Failed to bulk associate model years', {
          error,
          associationCount: req.body.associations?.length,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  // ===== EXPORT ROUTES =====

  /**
   * GET /api/v1/vehicle-hierarchy/export/csv
   * Export hierarchy data as CSV
   */
  router.get(
    '/export/csv',
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const csvData = await vehicleHierarchyService.exportToCSV(
          tenantId,
          getRequestUser(req)!
        );

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader(
          'Content-Disposition',
          'attachment; filename="vehicle-hierarchy.csv"'
        );
        res.send(csvData);
      } catch (error) {
        logger.error('Failed to export CSV', {
          error,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/vehicle-hierarchy/export/json
   * Export hierarchy data as JSON
   */
  router.get(
    '/export/json',
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const hierarchy = await vehicleHierarchyService.getFullHierarchy(
          tenantId,
          getRequestUser(req)!
        );

        res.setHeader('Content-Type', 'application/json');
        res.setHeader(
          'Content-Disposition',
          'attachment; filename="vehicle-hierarchy.json"'
        );
        res.json({
          exportedAt: new Date().toISOString(),
          tenantId,
          data: hierarchy,
        });
      } catch (error) {
        logger.error('Failed to export JSON', {
          error,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  return router;
}
