import React, { useState } from "react";
import {
  Search,
  User,
  Mail,
  Phone,
  TrendingUp,
  Users,
  DollarSign,
  Activity,
} from "lucide-react";

// Import all our design system components
import { Button } from "../atoms/Button";
import { <PERSON>, <PERSON>Header, CardContent } from "../atoms/Card";
import { Input } from "../atoms/Input";
import { Badge } from "../atoms/Badge";
import { Alert } from "../atoms/Alert";
import { Avatar } from "../atoms/Avatar";
import { LoadingSpinner } from "../atoms/LoadingSpinner";
import { FormField } from "../molecules/FormField";
import { StatCard } from "../molecules/StatCard";
import { DataTable, type Column } from "../molecules/DataTable";

// Sample data for demonstrations
const sampleUsers = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "Active",
    role: "Admin",
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "Inactive",
    role: "User",
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "Active",
    role: "Editor",
  },
];

const userColumns: Column<(typeof sampleUsers)[0]>[] = [
  { key: "name", header: "Name", className: "font-medium text-gray-900" },
  { key: "email", header: "Email", className: "text-gray-500" },
  {
    key: "status",
    header: "Status",
    render: (value) => (
      <Badge variant={value === "Active" ? "success" : "error"}>
        {String(value)}
      </Badge>
    ),
  },
  { key: "role", header: "Role", className: "text-gray-500" },
];

export const ComponentShowcase: React.FC = () => {
  const [alertVisible, setAlertVisible] = useState(true);
  const [inputValue, setInputValue] = useState("");

  return (
    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Design System Showcase
        </h1>
        <p className="text-gray-600 mt-2">
          A comprehensive view of all available components in our design system
        </p>
      </div>

      <div className="space-y-12">
        {/* Buttons Section */}
        <section>
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">Buttons</h2>
          <Card className="p-6">
            <div className="space-y-6">
              {/* Button Variants */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Variants
                </h3>
                <div className="flex flex-wrap gap-3">
                  <Button variant="primary">Primary</Button>
                  <Button variant="secondary">Secondary</Button>
                  <Button variant="outline">Outline</Button>
                  <Button variant="ghost">Ghost</Button>
                  <Button variant="danger">Danger</Button>
                </div>
              </div>

              {/* Button Sizes */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Sizes
                </h3>
                <div className="flex flex-wrap items-center gap-3">
                  <Button size="xs">Extra Small</Button>
                  <Button size="sm">Small</Button>
                  <Button size="md">Medium</Button>
                  <Button size="lg">Large</Button>
                  <Button size="xl">Extra Large</Button>
                </div>
              </div>

              {/* Button States */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  States
                </h3>
                <div className="flex flex-wrap gap-3">
                  <Button>Normal</Button>
                  <Button disabled>Disabled</Button>
                </div>
              </div>
            </div>
          </Card>
        </section>

        {/* Cards Section */}
        <section>
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">Cards</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card variant="default">
              <CardContent>
                <h3 className="font-semibold text-gray-900">Default Card</h3>
                <p className="text-gray-600 mt-2">
                  Basic card with subtle shadow
                </p>
              </CardContent>
            </Card>

            <Card variant="elevated">
              <CardContent>
                <h3 className="font-semibold text-gray-900">Elevated Card</h3>
                <p className="text-gray-600 mt-2">
                  Card with enhanced shadow on hover
                </p>
              </CardContent>
            </Card>

            <Card variant="interactive">
              <CardContent>
                <h3 className="font-semibold text-gray-900">
                  Interactive Card
                </h3>
                <p className="text-gray-600 mt-2">
                  Clickable card with hover effects
                </p>
              </CardContent>
            </Card>
          </div>

          <Card className="mt-6">
            <CardHeader
              title="Card with Header"
              subtitle="This card demonstrates the header component"
            />
            <CardContent>
              <p className="text-gray-600">
                This is the card content area. It can contain any type of
                content including text, forms, buttons, and other components.
              </p>
            </CardContent>
          </Card>
        </section>

        {/* Form Components Section */}
        <section>
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">
            Form Components
          </h2>
          <Card className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField label="Basic Input" required>
                <Input
                  placeholder="Enter some text..."
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                />
              </FormField>

              <FormField label="Input with Icon">
                <Input
                  placeholder="Search..."
                  icon={<Search className="h-4 w-4 text-gray-400" />}
                />
              </FormField>

              <FormField label="Email Input">
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  icon={<Mail className="h-4 w-4 text-gray-400" />}
                />
              </FormField>

              <FormField
                label="Input with Error"
                error="This field is required"
              >
                <Input
                  placeholder="Required field..."
                  className="border-red-300"
                />
              </FormField>
            </div>
          </Card>
        </section>

        {/* Feedback Components Section */}
        <section>
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">
            Feedback Components
          </h2>
          <div className="space-y-4">
            <Alert variant="info" title="Information">
              This is an informational message to keep users informed.
            </Alert>

            <Alert variant="success">Operation completed successfully!</Alert>

            <Alert variant="warning" title="Warning">
              Please review your settings before proceeding.
            </Alert>

            {alertVisible && (
              <Alert
                variant="error"
                title="Error occurred"
                dismissible
                onDismiss={() => setAlertVisible(false)}
              >
                Something went wrong. Please try again or contact support.
              </Alert>
            )}

            <Card className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Loading States
              </h3>
              <div className="flex flex-wrap items-center gap-6">
                <LoadingSpinner size="sm" text="Small" />
                <LoadingSpinner size="md" text="Medium" />
                <LoadingSpinner size="lg" text="Large" />
              </div>
            </Card>
          </div>
        </section>

        {/* Display Components Section */}
        <section>
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">
            Display Components
          </h2>
          <Card className="p-6">
            <div className="space-y-6">
              {/* Badges */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Badges
                </h3>
                <div className="flex flex-wrap gap-3">
                  <Badge variant="default">Default</Badge>
                  <Badge variant="primary">Primary</Badge>
                  <Badge variant="success">Success</Badge>
                  <Badge variant="warning">Warning</Badge>
                  <Badge variant="error">Error</Badge>
                  <Badge variant="primary" icon={<User className="h-3 w-3" />}>
                    With Icon
                  </Badge>
                  <Badge
                    variant="default"
                    removable
                    onRemove={() => console.log("Badge removed")}
                  >
                    Removable
                  </Badge>
                </div>
              </div>

              {/* Avatars */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Avatars
                </h3>
                <div className="flex flex-wrap items-center gap-4">
                  <Avatar size="xs" initials="XS" />
                  <Avatar size="sm" initials="SM" />
                  <Avatar size="md" initials="MD" />
                  <Avatar size="lg" initials="LG" />
                  <Avatar size="xl" initials="XL" />
                  <Avatar size="md" initials="GR" variant="gray" />
                </div>
              </div>
            </div>
          </Card>
        </section>

        {/* Data Components Section */}
        <section>
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">
            Data Components
          </h2>

          {/* Stat Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <StatCard
              title="Total Users"
              value="12,345"
              change={{ value: "+12%", trend: "up", period: "vs last month" }}
              icon={<Users className="h-6 w-6 text-primary-600" />}
            />
            <StatCard
              title="Revenue"
              value="$45,678"
              change={{ value: "+8%", trend: "up", period: "vs last month" }}
              icon={<DollarSign className="h-6 w-6 text-primary-600" />}
            />
            <StatCard
              title="Conversion Rate"
              value="3.24%"
              change={{ value: "-2%", trend: "down", period: "vs last month" }}
              icon={<TrendingUp className="h-6 w-6 text-primary-600" />}
            />
            <StatCard
              title="Active Sessions"
              value="1,234"
              change={{ value: "0%", trend: "neutral", period: "vs last hour" }}
              icon={<Activity className="h-6 w-6 text-primary-600" />}
            />
          </div>

          {/* Data Table */}
          <Card>
            <CardHeader
              title="Users Table"
              subtitle="Example data table with sample user data"
            />
            <CardContent className="p-0">
              <DataTable
                data={sampleUsers}
                columns={userColumns}
                onRowClick={(user) => console.log("Clicked user:", user)}
              />
            </CardContent>
          </Card>
        </section>

        {/* Layout Examples Section */}
        <section>
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">
            Layout Examples
          </h2>

          {/* Form Layout */}
          <Card>
            <CardHeader
              title="Form Layout Example"
              subtitle="Typical form layout using our components"
            />
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField label="First Name" required>
                  <Input placeholder="John" />
                </FormField>
                <FormField label="Last Name" required>
                  <Input placeholder="Doe" />
                </FormField>
                <FormField label="Email" required>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    icon={<Mail className="h-4 w-4 text-gray-400" />}
                  />
                </FormField>
                <FormField label="Phone">
                  <Input
                    type="tel"
                    placeholder="+****************"
                    icon={<Phone className="h-4 w-4 text-gray-400" />}
                  />
                </FormField>
              </div>

              <div className="mt-6 flex justify-end gap-3">
                <Button variant="outline">Cancel</Button>
                <Button variant="primary">Save Changes</Button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  );
};
