import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import { Button } from "./Button";

describe("Button", () => {
  describe("Rendering", () => {
    it("should render button with default props", () => {
      render(<Button>Click me</Button>);

      const button = screen.getByRole("button", { name: "Click me" });
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass(
        "bg-gradient-to-r",
        "from-primary-600",
        "to-primary-700",
      );
    });

    it("should render button with custom className", () => {
      render(<Button className="custom-class">Click me</Button>);

      const button = screen.getByRole("button", { name: "Click me" });
      expect(button).toHaveClass("custom-class");
    });
  });

  describe("Variants", () => {
    it("should render primary variant with gradient background", () => {
      render(<Button variant="primary">Primary</Button>);

      const button = screen.getByRole("button", { name: "Primary" });
      expect(button).toHaveClass(
        "bg-gradient-to-r",
        "from-primary-600",
        "to-primary-700",
      );
      expect(button).toHaveClass("shadow-xs");
    });

    it("should render secondary variant with gray background", () => {
      render(<Button variant="secondary">Secondary</Button>);

      const button = screen.getByRole("button", { name: "Secondary" });
      expect(button).toHaveClass("bg-gray-100", "text-gray-800");
      expect(button).toHaveClass("shadow-xs", "border", "border-gray-200");
    });

    it("should render outline variant with border", () => {
      render(<Button variant="outline">Outline</Button>);

      const button = screen.getByRole("button", { name: "Outline" });
      expect(button).toHaveClass("border-2", "border-gray-300", "bg-white");
      expect(button).toHaveClass("shadow-xs");
    });

    it("should render ghost variant without background", () => {
      render(<Button variant="ghost">Ghost</Button>);

      const button = screen.getByRole("button", { name: "Ghost" });
      expect(button).toHaveClass("text-gray-700");
      expect(button).not.toHaveClass("bg-primary-600");
    });

    it("should render danger variant with error gradient", () => {
      render(<Button variant="danger">Danger</Button>);

      const button = screen.getByRole("button", { name: "Danger" });
      expect(button).toHaveClass(
        "bg-gradient-to-r",
        "from-error-600",
        "to-error-700",
      );
      expect(button).toHaveClass("shadow-xs", "border", "border-error-700");
    });
  });

  describe("Sizes", () => {
    it("should render xs size with correct padding and font", () => {
      render(<Button size="xs">Extra Small</Button>);

      const button = screen.getByRole("button", { name: "Extra Small" });
      expect(button).toHaveClass("px-2.5", "py-1.5", "text-xs", "font-medium");
    });

    it("should render sm size with correct padding and font", () => {
      render(<Button size="sm">Small</Button>);

      const button = screen.getByRole("button", { name: "Small" });
      expect(button).toHaveClass("px-3", "py-2", "text-sm", "font-medium");
    });

    it("should render md size with correct padding and font", () => {
      render(<Button size="md">Medium</Button>);

      const button = screen.getByRole("button", { name: "Medium" });
      expect(button).toHaveClass("px-4", "py-2", "text-sm", "font-semibold");
    });

    it("should render lg size with correct padding and font", () => {
      render(<Button size="lg">Large</Button>);

      const button = screen.getByRole("button", { name: "Large" });
      expect(button).toHaveClass("px-4", "py-2", "text-base", "font-semibold");
    });

    it("should render xl size with correct padding and font", () => {
      render(<Button size="xl">Extra Large</Button>);

      const button = screen.getByRole("button", { name: "Extra Large" });
      expect(button).toHaveClass("px-6", "py-3", "text-base", "font-semibold");
    });
  });

  describe("Enhanced Styling", () => {
    it("should have enhanced transition and transform classes", () => {
      render(<Button>Enhanced Button</Button>);

      const button = screen.getByRole("button", { name: "Enhanced Button" });
      expect(button).toHaveClass("transition-all", "duration-200");
      expect(button).toHaveClass("hover:scale-[1.02]", "active:scale-[0.98]");
    });

    it("should have focus ring styling", () => {
      render(<Button>Focus Button</Button>);

      const button = screen.getByRole("button", { name: "Focus Button" });
      expect(button).toHaveClass(
        "focus:outline-none",
        "focus:ring-2",
        "focus:ring-offset-2",
      );
    });

    it("should have disabled styling", () => {
      render(<Button disabled>Disabled Button</Button>);

      const button = screen.getByRole("button", { name: "Disabled Button" });
      expect(button).toHaveClass(
        "disabled:opacity-50",
        "disabled:cursor-not-allowed",
      );
      expect(button).toBeDisabled();
    });
  });

  describe("Props", () => {
    it("should pass through HTML button props", () => {
      const handleClick = vi.fn();
      render(
        <Button onClick={handleClick} type="submit">
          Submit
        </Button>,
      );

      const button = screen.getByRole("button", { name: "Submit" });
      expect(button).toHaveAttribute("type", "submit");

      button.click();
      expect(handleClick).toHaveBeenCalledOnce();
    });

    it("should render with custom id and data attributes", () => {
      render(
        <Button id="custom-button" data-testid="test-button">
          Custom Button
        </Button>,
      );

      const button = screen.getByRole("button", { name: "Custom Button" });
      expect(button).toHaveAttribute("id", "custom-button");
      expect(button).toHaveAttribute("data-testid", "test-button");
    });
  });
});
