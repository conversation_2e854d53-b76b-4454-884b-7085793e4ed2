import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { Star } from "lucide-react";
import { TestimonialSection } from "./TestimonialSection";

const mockTestimonials = [
  {
    id: "1",
    quote:
      "This product has completely transformed our workflow. Highly recommended!",
    author: {
      name: "<PERSON>",
      title: "Product Manager",
      company: "TechCorp",
      avatar: "/avatar1.jpg",
    },
    rating: 5,
  },
  {
    id: "2",
    quote:
      "Outstanding customer service and excellent features. Worth every penny.",
    author: {
      name: "<PERSON>",
      title: "CTO",
      company: "StartupXYZ",
    },
    rating: 4,
  },
  {
    id: "3",
    quote:
      "Simple, powerful, and reliable. Everything we needed in one package.",
    author: {
      name: "<PERSON>",
      title: "Operations Director",
      company: "Enterprise Inc",
    },
    rating: 5,
  },
];

const defaultProps = {
  testimonials: mockTestimonials,
};

describe("TestimonialSection", () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe("Basic Rendering", () => {
    it("should render all testimonials", () => {
      render(<TestimonialSection {...defaultProps} />);

      expect(
        screen.getByText(
          /This product has completely transformed our workflow/,
        ),
      ).toBeInTheDocument();
      expect(
        screen.getByText(/Outstanding customer service and excellent features/),
      ).toBeInTheDocument();
      expect(
        screen.getByText(/Simple, powerful, and reliable/),
      ).toBeInTheDocument();
    });

    it("should render with custom className", () => {
      render(
        <TestimonialSection
          {...defaultProps}
          className="custom-testimonials"
        />,
      );

      const container = screen
        .getByText("Sarah Johnson")
        .closest(".custom-testimonials");
      expect(container).toBeInTheDocument();
    });

    it("should apply default grid layout", () => {
      render(<TestimonialSection {...defaultProps} />);

      const container = screen.getByTestId("testimonial-container");
      expect(container).toHaveClass(
        "grid",
        "gap-6",
        "grid-cols-1",
        "md:grid-cols-2",
        "lg:grid-cols-3",
      );
    });
  });

  describe("Layout Variants", () => {
    it("should render grid layout with specified columns", () => {
      render(
        <TestimonialSection {...defaultProps} layout="grid" columns={2} />,
      );

      const container = screen.getByTestId("testimonial-container");
      expect(container).toHaveClass("grid", "grid-cols-1", "md:grid-cols-2");
    });

    it("should render carousel layout", () => {
      render(<TestimonialSection {...defaultProps} layout="carousel" />);

      const container = screen.getByTestId("testimonial-container");
      expect(container).toHaveClass(
        "flex",
        "gap-6",
        "overflow-x-auto",
        "snap-x",
        "snap-mandatory",
      );
    });

    it("should render single layout", () => {
      render(<TestimonialSection {...defaultProps} layout="single" />);

      const container = screen.getByTestId("testimonial-container");
      expect(container).toHaveClass("flex", "justify-center");
    });

    it("should render masonry layout", () => {
      render(<TestimonialSection {...defaultProps} layout="masonry" />);

      const container = screen.getByTestId("testimonial-container");
      expect(container).toHaveClass(
        "columns-1",
        "md:columns-2",
        "lg:columns-3",
      );
    });
  });

  describe("Spacing Options", () => {
    it("should apply tight spacing", () => {
      render(<TestimonialSection {...defaultProps} spacing="tight" />);

      const container = screen.getByTestId("testimonial-container");
      expect(container).toHaveClass("gap-4");
    });

    it("should apply normal spacing by default", () => {
      render(<TestimonialSection {...defaultProps} />);

      const container = screen.getByTestId("testimonial-container");
      expect(container).toHaveClass("gap-6");
    });

    it("should apply loose spacing", () => {
      render(<TestimonialSection {...defaultProps} spacing="loose" />);

      const container = screen.getByTestId("testimonial-container");
      expect(container).toHaveClass("gap-8");
    });
  });

  describe("Header Section", () => {
    it("should render title and subtitle", () => {
      render(
        <TestimonialSection
          {...defaultProps}
          title="What Our Customers Say"
          subtitle="Real feedback from real users who love our product"
        />,
      );

      expect(screen.getByRole("heading", { level: 2 })).toHaveTextContent(
        "What Our Customers Say",
      );
      expect(
        screen.getByText("Real feedback from real users who love our product"),
      ).toBeInTheDocument();
    });

    it("should render badge", () => {
      const badge = {
        text: "Customer Reviews",
        variant: "primary" as const,
        icon: <Star data-testid="star-icon" />,
      };

      render(<TestimonialSection {...defaultProps} badge={badge} />);

      expect(screen.getByText("Customer Reviews")).toBeInTheDocument();
      expect(screen.getByTestId("star-icon")).toBeInTheDocument();
    });

    it("should not render header when no title, subtitle, or badge", () => {
      render(<TestimonialSection {...defaultProps} />);

      expect(
        screen.queryByRole("heading", { level: 2 }),
      ).not.toBeInTheDocument();
    });
  });

  describe("Card Properties", () => {
    it("should apply card variant to all testimonials", () => {
      render(<TestimonialSection {...defaultProps} cardVariant="elevated" />);

      const testimonialCards = screen.getAllByTestId(
        "testimonial-card-container",
      );
      testimonialCards.forEach((card) => {
        expect(card).toHaveClass("bg-white", "shadow-sm", "hover:shadow-base");
      });
    });

    it("should apply card size to all testimonials", () => {
      render(<TestimonialSection {...defaultProps} cardSize="lg" />);

      const testimonialCards = screen.getAllByTestId(
        "testimonial-card-container",
      );
      testimonialCards.forEach((card) => {
        expect(card).toHaveClass("p-8");
      });
    });

    it("should apply card layout to all testimonials", () => {
      render(<TestimonialSection {...defaultProps} cardLayout="horizontal" />);

      const testimonialCards = screen.getAllByTestId(
        "testimonial-card-container",
      );
      testimonialCards.forEach((card) => {
        expect(card).toHaveClass("text-left");
      });
    });

    it("should make all cards interactive when specified", () => {
      render(<TestimonialSection {...defaultProps} interactive />);

      const testimonialCards = screen.getAllByTestId(
        "testimonial-card-container",
      );
      testimonialCards.forEach((card) => {
        expect(card).toHaveClass(
          "cursor-pointer",
          "transition-all",
          "duration-200",
        );
      });
    });
  });

  describe("Custom Render Function", () => {
    it("should use custom render function when provided", () => {
      const customRender = vi.fn((testimonial, index) => (
        <div key={testimonial.id} data-testid={`custom-testimonial-${index}`}>
          Custom: {testimonial.author.name}
        </div>
      ));

      render(
        <TestimonialSection
          {...defaultProps}
          renderTestimonial={customRender}
        />,
      );

      expect(screen.getByTestId("custom-testimonial-0")).toBeInTheDocument();
      expect(screen.getByText("Custom: Sarah Johnson")).toBeInTheDocument();
      expect(customRender).toHaveBeenCalledTimes(3);
    });
  });

  describe("Carousel Functionality", () => {
    it("should render carousel controls when enabled", () => {
      render(
        <TestimonialSection
          {...defaultProps}
          layout="carousel"
          carousel={{ showControls: true }}
        />,
      );

      expect(screen.getByLabelText("Previous testimonial")).toBeInTheDocument();
      expect(screen.getByLabelText("Next testimonial")).toBeInTheDocument();
      expect(screen.getByText("1 of 3")).toBeInTheDocument();
    });

    it("should render carousel dots when enabled", () => {
      render(
        <TestimonialSection
          {...defaultProps}
          layout="carousel"
          carousel={{ showDots: true }}
        />,
      );

      const dots = screen.getAllByLabelText(/go to testimonial/i);
      expect(dots).toHaveLength(3);
    });

    it("should handle next button click", () => {
      render(
        <TestimonialSection
          {...defaultProps}
          layout="carousel"
          carousel={{ showControls: true }}
        />,
      );

      const nextButton = screen.getByLabelText("Next testimonial");
      fireEvent.click(nextButton);

      expect(screen.getByText("2 of 3")).toBeInTheDocument();
    });

    it("should handle previous button click", () => {
      render(
        <TestimonialSection
          {...defaultProps}
          layout="carousel"
          carousel={{ showControls: true }}
        />,
      );

      const prevButton = screen.getByLabelText("Previous testimonial");
      fireEvent.click(prevButton);

      expect(screen.getByText("3 of 3")).toBeInTheDocument();
    });

    it("should handle dot navigation", () => {
      render(
        <TestimonialSection
          {...defaultProps}
          layout="carousel"
          carousel={{ showDots: true, showControls: true }}
        />,
      );

      const thirdDot = screen.getByLabelText("Go to testimonial 3");
      fireEvent.click(thirdDot);

      expect(screen.getByText("3 of 3")).toBeInTheDocument();
    });

    it("should not show controls for single testimonial", () => {
      const singleTestimonial = [mockTestimonials[0]];

      render(
        <TestimonialSection
          testimonials={singleTestimonial}
          layout="carousel"
          carousel={{ showControls: true, showDots: true }}
        />,
      );

      expect(
        screen.queryByLabelText("Previous testimonial"),
      ).not.toBeInTheDocument();
      expect(
        screen.queryByLabelText("Next testimonial"),
      ).not.toBeInTheDocument();
      expect(
        screen.queryByLabelText(/go to testimonial/i),
      ).not.toBeInTheDocument();
    });
  });

  describe("Auto-play Functionality", () => {
    it("should setup auto-play interval when enabled", () => {
      const setIntervalSpy = vi.spyOn(global, "setInterval");

      render(
        <TestimonialSection
          {...defaultProps}
          layout="carousel"
          carousel={{
            autoPlay: true,
            autoPlayInterval: 2000,
            showControls: true,
          }}
        />,
      );

      expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 2000);

      setIntervalSpy.mockRestore();
    });

    it("should not setup auto-play when disabled", () => {
      const setIntervalSpy = vi.spyOn(global, "setInterval");

      render(
        <TestimonialSection
          {...defaultProps}
          layout="carousel"
          carousel={{
            autoPlay: false,
            showControls: true,
          }}
        />,
      );

      expect(setIntervalSpy).not.toHaveBeenCalled();

      setIntervalSpy.mockRestore();
    });
  });

  describe("Layout-Specific Classes", () => {
    it("should apply carousel-specific classes to testimonial cards", () => {
      render(<TestimonialSection {...defaultProps} layout="carousel" />);

      const testimonialContainers = screen
        .getAllByText(/Sarah Johnson|Mike Chen|Emily Rodriguez/)
        .map((text) => text.closest('div[class*="flex-shrink-0"]'));

      testimonialContainers.forEach((container) => {
        expect(container).toHaveClass("flex-shrink-0", "w-80", "snap-start");
      });
    });

    it("should apply masonry-specific classes to testimonial cards", () => {
      render(<TestimonialSection {...defaultProps} layout="masonry" />);

      const testimonialContainers = screen
        .getAllByText(/Sarah Johnson|Mike Chen|Emily Rodriguez/)
        .map((text) => text.closest('div[class*="break-inside-avoid"]'));

      testimonialContainers.forEach((container) => {
        expect(container).toHaveClass("break-inside-avoid");
      });
    });

    it("should apply single layout classes to testimonial cards", () => {
      render(<TestimonialSection {...defaultProps} layout="single" />);

      const testimonialContainers = screen
        .getAllByText(/Sarah Johnson|Mike Chen|Emily Rodriguez/)
        .map((text) => text.closest('div[class*="max-w-2xl"]'));

      testimonialContainers.forEach((container) => {
        expect(container).toHaveClass("max-w-2xl");
      });
    });
  });

  describe("Accessibility", () => {
    it("should have proper heading hierarchy", () => {
      render(
        <TestimonialSection {...defaultProps} title="Customer Testimonials" />,
      );

      const sectionHeading = screen.getByRole("heading", { level: 2 });
      expect(sectionHeading).toBeInTheDocument();
    });

    it("should have proper button accessibility for carousel controls", () => {
      render(
        <TestimonialSection
          {...defaultProps}
          layout="carousel"
          carousel={{ showControls: true }}
        />,
      );

      const prevButton = screen.getByLabelText("Previous testimonial");
      const nextButton = screen.getByLabelText("Next testimonial");

      expect(prevButton).toHaveClass(
        "focus:outline-none",
        "focus:ring-2",
        "focus:ring-primary-500",
      );
      expect(nextButton).toHaveClass(
        "focus:outline-none",
        "focus:ring-2",
        "focus:ring-primary-500",
      );
    });

    it("should have proper aria labels for dot navigation", () => {
      render(
        <TestimonialSection
          {...defaultProps}
          layout="carousel"
          carousel={{ showDots: true }}
        />,
      );

      expect(screen.getByLabelText("Go to testimonial 1")).toBeInTheDocument();
      expect(screen.getByLabelText("Go to testimonial 2")).toBeInTheDocument();
      expect(screen.getByLabelText("Go to testimonial 3")).toBeInTheDocument();
    });
  });

  describe("Responsive Design", () => {
    it("should have responsive grid classes", () => {
      render(<TestimonialSection {...defaultProps} columns={4} />);

      const container = screen.getByTestId("testimonial-container");
      expect(container).toHaveClass(
        "grid-cols-1",
        "md:grid-cols-2",
        "lg:grid-cols-4",
      );
    });

    it("should have responsive typography in header", () => {
      render(
        <TestimonialSection {...defaultProps} title="Customer Testimonials" />,
      );

      const heading = screen.getByRole("heading", { level: 2 });
      expect(heading).toHaveClass("text-3xl", "sm:text-4xl");
    });
  });
});
