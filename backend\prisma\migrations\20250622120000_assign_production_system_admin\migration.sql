-- Assign System Admin Role to Production User
-- This migration assigns the System Admin role to the specified production user
-- Migration is idempotent and safe to run multiple times

-- Assign System Admin role to the specified user (by Clerk ID)
-- This will only work if the user already exists in the database
INSERT INTO "user_roles" ("id", "userId", "roleId", "tenantId", "assignedBy", "createdAt", "updatedAt")
SELECT 
  'ur_prod_system_admin_' || u.id,  -- Generate unique ID
  u.id,                             -- User ID from database
  'role_system_admin',              -- System Admin role ID
  NULL,                             -- System roles have no tenant scope
  u.id,                             -- Self-assigned for initial setup
  NOW(),                            -- Created timestamp
  NOW()                             -- Updated timestamp
FROM "users" u
WHERE u."clerkId" = 'user_2yqMjrYXAekqQVzPeusn3YFLB8X'  -- Target Clerk ID
  AND NOT EXISTS (
    -- Only insert if user doesn't already have System Admin role
    SELECT 1 FROM "user_roles" ur
    JOIN "roles" r ON ur."roleId" = r.id
    WHERE ur."userId" = u.id 
      AND r."type" = 'SYSTEM_ADMIN'
  );

-- Verify the assignment was successful (this will show in migration logs)
-- Note: This is a comment for verification - the actual verification would be done post-migration
-- SELECT u.email, u."firstName", u."lastName", r.name as role_name
-- FROM "users" u
-- JOIN "user_roles" ur ON u.id = ur."userId"
-- JOIN "roles" r ON ur."roleId" = r.id
-- WHERE u."clerkId" = 'cmc70yuc20002egp3p8i1dg16' AND r."type" = 'SYSTEM_ADMIN';
