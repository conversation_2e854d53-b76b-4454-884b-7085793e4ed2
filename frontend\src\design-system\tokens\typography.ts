/**
 * Design System Typography Tokens
 * Inspired by Salient template typography patterns
 *
 * Provides consistent typography scale for headings, body text,
 * and UI elements across all platforms
 */

export const typography = {
  // Font Families
  fontFamily: {
    sans: ["Inter", "system-ui", "sans-serif"],
    mono: ["ui-monospace", "SFMono-Regular", "Monaco", "Consolas", "monospace"],
  },

  // Font Sizes (Salient-inspired scale)
  fontSize: {
    xs: ["0.75rem", { lineHeight: "1rem" }], // 12px - Small captions, badges
    sm: ["0.875rem", { lineHeight: "1.25rem" }], // 14px - UI text, labels
    base: ["1rem", { lineHeight: "1.5rem" }], // 16px - Body text
    lg: ["1.125rem", { lineHeight: "1.75rem" }], // 18px - Large body text
    xl: ["1.25rem", { lineHeight: "1.75rem" }], // 20px - Small headings
    "2xl": ["1.5rem", { lineHeight: "2rem" }], // 24px - Section headings
    "3xl": ["1.875rem", { lineHeight: "2.25rem" }], // 30px - Page headings
    "4xl": ["2.25rem", { lineHeight: "2.5rem" }], // 36px - Hero headings
    "5xl": ["3rem", { lineHeight: "1" }], // 48px - Large hero
    "6xl": ["3.75rem", { lineHeight: "1" }], // 60px - XL hero
    "7xl": ["4.5rem", { lineHeight: "1" }], // 72px - XXL hero
  },

  // Font Weights
  fontWeight: {
    thin: "100",
    extralight: "200",
    light: "300",
    normal: "400", // Body text
    medium: "500", // UI elements, emphasis
    semibold: "600", // Headings, buttons
    bold: "700", // Strong emphasis
    extrabold: "800", // Hero headings
    black: "900", // Maximum emphasis
  },

  // Line Heights
  lineHeight: {
    none: "1",
    tight: "1.25", // Headings
    snug: "1.375", // Large text
    normal: "1.5", // Body text
    relaxed: "1.625", // Reading text
    loose: "2", // Spaced text
  },

  // Letter Spacing
  letterSpacing: {
    tighter: "-0.05em",
    tight: "-0.025em",
    normal: "0em",
    wide: "0.025em",
    wider: "0.05em",
    widest: "0.1em",
  },

  // Text Styles (Semantic combinations)
  textStyles: {
    // Hero Styles
    heroXL: {
      fontSize: "4.5rem", // 72px
      lineHeight: "1",
      fontWeight: "800",
      letterSpacing: "-0.025em",
    },
    heroLarge: {
      fontSize: "3.75rem", // 60px
      lineHeight: "1",
      fontWeight: "700",
      letterSpacing: "-0.025em",
    },
    heroMedium: {
      fontSize: "3rem", // 48px
      lineHeight: "1",
      fontWeight: "700",
      letterSpacing: "-0.025em",
    },

    // Heading Styles
    h1: {
      fontSize: "2.25rem", // 36px
      lineHeight: "2.5rem",
      fontWeight: "700",
      letterSpacing: "-0.025em",
    },
    h2: {
      fontSize: "1.875rem", // 30px
      lineHeight: "2.25rem",
      fontWeight: "600",
      letterSpacing: "-0.025em",
    },
    h3: {
      fontSize: "1.5rem", // 24px
      lineHeight: "2rem",
      fontWeight: "600",
    },
    h4: {
      fontSize: "1.25rem", // 20px
      lineHeight: "1.75rem",
      fontWeight: "600",
    },
    h5: {
      fontSize: "1.125rem", // 18px
      lineHeight: "1.75rem",
      fontWeight: "600",
    },
    h6: {
      fontSize: "1rem", // 16px
      lineHeight: "1.5rem",
      fontWeight: "600",
    },

    // Body Styles
    bodyLarge: {
      fontSize: "1.125rem", // 18px
      lineHeight: "1.75rem",
      fontWeight: "400",
    },
    body: {
      fontSize: "1rem", // 16px
      lineHeight: "1.5rem",
      fontWeight: "400",
    },
    bodySmall: {
      fontSize: "0.875rem", // 14px
      lineHeight: "1.25rem",
      fontWeight: "400",
    },

    // UI Styles
    buttonLarge: {
      fontSize: "1rem", // 16px
      lineHeight: "1.5rem",
      fontWeight: "600",
    },
    button: {
      fontSize: "0.875rem", // 14px
      lineHeight: "1.25rem",
      fontWeight: "600",
    },
    buttonSmall: {
      fontSize: "0.75rem", // 12px
      lineHeight: "1rem",
      fontWeight: "600",
    },
    label: {
      fontSize: "0.875rem", // 14px
      lineHeight: "1.25rem",
      fontWeight: "500",
    },
    caption: {
      fontSize: "0.75rem", // 12px
      lineHeight: "1rem",
      fontWeight: "400",
    },
    overline: {
      fontSize: "0.75rem", // 12px
      lineHeight: "1rem",
      fontWeight: "600",
      letterSpacing: "0.05em",
      textTransform: "uppercase" as const,
    },
  },
} as const;

// Type definitions
export type FontSize = keyof typeof typography.fontSize;
export type FontWeight = keyof typeof typography.fontWeight;
export type TextStyle = keyof typeof typography.textStyles;

// Utility function for accessing typography values
export const getTypography = (path: string): unknown => {
  const keys = path.split(".");
  let value: unknown = typography;

  for (const key of keys) {
    if (typeof value === "object" && value !== null && key in value) {
      value = (value as Record<string, unknown>)[key];
    } else {
      throw new Error(`Typography token "${path}" not found`);
    }
  }

  return value;
};

// Export individual scales for convenience
export const {
  fontFamily,
  fontSize,
  fontWeight,
  lineHeight,
  letterSpacing,
  textStyles,
} = typography;
