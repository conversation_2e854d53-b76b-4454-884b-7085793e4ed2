import { describe, it, expect } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { SetupRequiredBadge } from "../../../src/components/atoms/SetupRequiredBadge";

describe("SetupRequiredBadge", () => {
  it("renders the setup required badge with default asterisk and styling", () => {
    render(<SetupRequiredBadge />);

    const asterisk = screen.getByText("*");
    expect(asterisk).toBeInTheDocument();
    expect(asterisk).toHaveClass("text-gray-500");
  });

  it("renders with custom tooltip content when showTooltip is true", () => {
    const customTooltip = "Custom setup fee message";
    render(
      <SetupRequiredBadge showTooltip={true} tooltipContent={customTooltip} />,
    );

    const asterisk = screen.getByText("*");
    expect(asterisk).toBeInTheDocument();

    // Test tooltip appears on hover
    fireEvent.mouseEnter(asterisk.parentElement!);
    expect(screen.getByRole("tooltip")).toBeInTheDocument();
    expect(screen.getByText(customTooltip)).toBeInTheDocument();
  });

  it("renders without tooltip when showTooltip is false", () => {
    render(<SetupRequiredBadge showTooltip={false} />);

    const asterisk = screen.getByText("*");
    expect(asterisk).toBeInTheDocument();

    // Test no tooltip appears on hover
    fireEvent.mouseEnter(asterisk);
    expect(screen.queryByRole("tooltip")).not.toBeInTheDocument();
  });

  it("renders with custom className", () => {
    render(<SetupRequiredBadge className="custom-class" />);

    const asterisk = screen.getByText("*");
    expect(asterisk).toBeInTheDocument();
    expect(asterisk).toHaveClass("text-gray-500", "custom-class");
  });

  it("shows default tooltip content on hover", () => {
    render(<SetupRequiredBadge />);

    const asterisk = screen.getByText("*");
    fireEvent.mouseEnter(asterisk.parentElement!);

    expect(screen.getByRole("tooltip")).toBeInTheDocument();
    expect(
      screen.getByText("This feature requires a one-time setup fee"),
    ).toBeInTheDocument();
  });
});
