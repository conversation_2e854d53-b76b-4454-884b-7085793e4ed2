# Render Deployment Troubleshooting Guide

This guide helps resolve common Render deployment issues, focusing on build failures, service startup problems, and configuration issues.

---

## 🚨 Common Issue: Service Failed to Start

### Symptoms

- Render dashboard shows "Deploy failed" or "Service unhealthy"
- Service builds successfully but fails to start
- Health check endpoint returns errors or timeouts

### Root Cause

The most common causes are:

1. **Host Binding Issue**: Application not binding to `0.0.0.0:PORT` (required for Render)
2. **Port Configuration**: Not using the PORT environment variable correctly
3. **Missing Environment Variables**: Critical secrets not set in Render dashboard
4. **Database Connection**: Database connectivity issues during startup
5. **Health Check Failures**: `/health` endpoint not responding correctly

### Quick Fix

1. **Ensure Host Binding**: Application must bind to `0.0.0.0:PORT`, not `localhost:PORT`
2. **Set Environment Variables**: Configure all required secrets in Render dashboard
3. **Test Container Locally**: Run the container locally to verify it starts correctly
4. **Check Health Endpoint**: Ensure `/health` endpoint responds with 200 status

### Manual Debugging

```bash
# Test container locally with same environment
docker run -p 8080:8080 -e PORT=8080 -e NODE_ENV=production YOUR_IMAGE

# Test health endpoint
curl http://localhost:8080/health

# Check Render service logs
# Go to Render Dashboard → Service → Logs tab for real-time debugging
```

---

## 🚨 Common Issue: Build Failures

### Symptoms

- Render dashboard shows "Build failed" status
- Build logs show dependency installation errors
- Docker build context issues or missing files

### Root Cause

Common build failure causes:

1. **Incorrect Dockerfile Path**: Wrong dockerfilePath in render.yaml
2. **Build Context Issues**: dockerContext not set to project root
3. **Missing Dependencies**: Package.json or yarn.lock issues
4. **Environment Variables**: Missing build-time environment variables
5. **Docker Layer Caching**: Dependency changes not properly cached

### Quick Fix

1. **Verify render.yaml Configuration**:

```yaml
services:
  - type: web
    name: tech-notes-backend
    dockerfilePath: ./infrastructure/docker/backend/Dockerfile # Correct path
    dockerContext: . # Build from project root
```

2. **Check Build Logs**: Review detailed build logs in Render dashboard
3. **Test Locally**: Build the same Docker image locally to identify issues

### Manual Debugging

```bash
# Test the exact build that Render will run
docker build -f infrastructure/docker/backend/Dockerfile .

# For frontend with build args
docker build -f infrastructure/docker/frontend/Dockerfile \
  --build-arg VITE_API_URL=https://your-backend.onrender.com \
  --build-arg VITE_CLERK_PUBLISHABLE_KEY=pk_test_... \
  .
```

---

## 🚨 Common Issue: Package Lock File Not Found in Monorepo

### Symptoms

- Build fails with error: `failed to calculate checksum of ref: "/backend/package-lock.json": not found`
- Error occurs on Render but Docker build works locally
- Affects monorepo structures with nested package.json files

### Root Cause

The issue occurs when the Dockerfile expects a `package-lock.json` file in the backend directory, but the project uses **npm workspaces** where only the root directory contains the `package-lock.json` file.

### Solution

For npm workspaces monorepos, only copy the backend `package.json` (not `package-lock.json`):

```dockerfile
# For npm workspaces - only root has package-lock.json
COPY package.json package-lock.json ./

# Create backend directory and copy only package.json
RUN mkdir -p ./backend
COPY backend/package.json ./backend/

# npm ci will install all workspace dependencies from root package-lock.json
RUN npm ci
```

### Implementation

Update your Dockerfile for npm workspaces monorepo structure:

```dockerfile
# Build stage
FROM node:20-alpine AS builder
WORKDIR /app

# Copy root package files (only root has package-lock.json in workspaces)
COPY package.json package-lock.json ./

# Create backend directory and copy only package.json
RUN mkdir -p ./backend
COPY backend/package.json ./backend/

# Install all workspace dependencies from root package-lock.json
RUN npm ci

# Continue with rest of build...
```

This approach correctly handles npm workspaces where dependencies are managed centrally from the root `package-lock.json`.

---

## 🚨 Common Issue: Environment Variable Configuration

### Symptoms

- Service starts but fails to connect to database or external APIs
- Authentication errors with Clerk or Stripe
- CORS errors between frontend and backend

### Root Cause

Common environment variable issues:

1. **Missing Secrets**: Required secrets not set in Render dashboard
2. **Incorrect Variable Names**: Typos in environment variable keys
3. **Build vs Runtime Variables**: Frontend needs build-time variables (VITE\_ prefixed)
4. **Service Communication**: Frontend/backend URL configuration
5. **Docker Build Args**: Frontend Docker build not receiving environment variables

### Quick Fix

1. **Set Required Secrets in Render Dashboard**:
   - Go to Service → Environment tab
   - Add sensitive variables like CLERK_SECRET_KEY, STRIPE_SECRET_KEY
   - Restart service after adding variables

2. **Frontend Build-Time Variables**: For Vite/React apps, ensure Dockerfile has:

   ```dockerfile
   ARG VITE_API_URL
   ARG VITE_CLERK_PUBLISHABLE_KEY
   ENV VITE_API_URL=$VITE_API_URL
   ENV VITE_CLERK_PUBLISHABLE_KEY=$VITE_CLERK_PUBLISHABLE_KEY
   ```

3. **Verify Service URLs**: Check that services can communicate
4. **Test Environment Variables**: Use service shell to verify variables are set

### Manual Debugging

```bash
# Check environment variables in Render service shell
# Go to Service → Shell tab in Render dashboard
echo $DATABASE_URL
echo $CLERK_SECRET_KEY  # Should show value (be careful with logs)

# Test database connection
node -e "console.log(process.env.DATABASE_URL ? 'DB URL set' : 'DB URL missing')"

# Test API connectivity between services
curl https://your-backend.onrender.com/health
```

---

## 🚨 Common Issue: Database Connection Problems

### Symptoms

- Service starts but cannot connect to database
- Prisma migration errors or connection timeouts
- Database-related errors in service logs

### Root Cause

Common database connection issues:

1. **Incorrect DATABASE_URL**: Connection string format or credentials
2. **Database Not Ready**: Service starts before database is fully initialized
3. **Connection Limits**: Too many concurrent connections
4. **Migration Issues**: Database schema not up to date

### Quick Fix

1. **Verify DATABASE_URL**: Check connection string in Render dashboard
2. **Run Migrations**: Use Render service shell to run Prisma migrations
3. **Check Database Status**: Ensure database service is healthy

### Manual Debugging

```bash
# Test database connection in service shell
npx prisma db push  # Test connection and sync schema
npx prisma migrate deploy  # Run pending migrations

# Check database status in Render dashboard
# Go to Database → Metrics to see connection count and performance
```

---

## 🚨 Common Issue: Prisma Migration Conflicts

### Symptoms

- Migration errors during deployment: "relation already exists"
- Error P3018: "A migration failed to apply"
- Error P3009: "migrate found failed migrations in the target database"
- Service fails to start due to migration issues

### Root Cause

Migration conflicts occur when:

1. **Previous Failed Deployment**: Database has partial schema from failed deployment
2. **Migration State Mismatch**: Prisma thinks migrations need to run but tables already exist
3. **Manual Database Changes**: Schema was modified outside of migrations
4. **Incomplete Migration Rollback**: Previous migration wasn't properly rolled back

### Quick Fix Options

#### Option 1: Reset Migration State (Recommended for new deployments)

```sql
-- Connect to your Render database and run:
DROP TABLE IF EXISTS "_prisma_migrations";

-- Optionally, for complete reset:
DROP TABLE IF EXISTS "role_permissions" CASCADE;
DROP TABLE IF EXISTS "user_roles" CASCADE;
DROP TABLE IF EXISTS "permissions" CASCADE;
DROP TABLE IF EXISTS "roles" CASCADE;
DROP TABLE IF EXISTS "users" CASCADE;
DROP TABLE IF EXISTS "tenants" CASCADE;
```

#### Option 2: Mark Migrations as Applied

```sql
-- If tables exist and are correct, mark migrations as applied:
CREATE TABLE "_prisma_migrations" (
  "id" VARCHAR(36) NOT NULL,
  "checksum" VARCHAR(64) NOT NULL,
  "finished_at" TIMESTAMPTZ,
  "migration_name" VARCHAR(255) NOT NULL,
  "logs" TEXT,
  "rolled_back_at" TIMESTAMPTZ,
  "started_at" TIMESTAMPTZ NOT NULL DEFAULT now(),
  "applied_steps_count" INTEGER NOT NULL DEFAULT 0,
  PRIMARY KEY ("id")
);

-- Mark your migrations as applied:
INSERT INTO "_prisma_migrations" ("id", "checksum", "migration_name", "finished_at", "applied_steps_count") VALUES
('20250618152638_init_with_is_active', 'checksum1', '20250618152638_init_with_is_active', NOW(), 1),
('20250621185840_add_rbac_system', 'checksum2', '20250621185840_add_rbac_system', NOW(), 1),
('20250621192449_seed_rbac_initial_data', 'checksum3', '20250621192449_seed_rbac_initial_data', NOW(), 1);
```

#### Option 3: Use Prisma Migration Resolution

```bash
# In Render service shell:
npx prisma migrate status
npx prisma migrate resolve --rolled-back MIGRATION_NAME
npx prisma migrate deploy
```

### Prevention

- Always test migrations locally before deploying
- Use `npx prisma migrate deploy` for production (not `migrate dev`)
- Monitor deployment logs for migration issues
- Keep database backups before major schema changes

---

## 🚨 Common Issue: Service Performance Problems

### Symptoms

- Slow response times or timeouts
- High memory or CPU usage
- Service restarts due to resource limits

### Root Cause

Performance issues often stem from:

1. **Insufficient Resources**: Service plan too small for workload
2. **Memory Leaks**: Application not properly managing memory
3. **Database Query Performance**: Slow or inefficient queries
4. **Cold Starts**: Service sleeping and taking time to wake up

### Quick Fix

1. **Upgrade Service Plan**: Move from Starter to Standard or Pro plan
2. **Monitor Resource Usage**: Check CPU/memory metrics in dashboard
3. **Optimize Database Queries**: Review slow queries and add indexes
4. **Keep Services Warm**: Consider upgrading to plans with minimum instances

### Performance Monitoring

```bash
# Monitor service metrics in Render dashboard
# - Response times
# - Memory usage
# - CPU utilization
# - Request volume

# Database performance monitoring
# - Connection count
# - Query performance
# - Storage usage
```

---

## 🔍 Diagnostic Steps

### 1. Check Service Status in Render Dashboard

- Go to your service in Render dashboard
- Check the "Events" tab for deployment history
- Review "Logs" tab for real-time service logs
- Monitor "Metrics" tab for performance data

### 2. Verify Service Configuration

```yaml
# Check render.yaml configuration
services:
  - type: web
    name: tech-notes-backend
    healthCheckPath: /health # Ensure this is correct
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: tech-notes-db
          property: connectionString
```

### 3. Test Service Endpoints

```bash
# Test health endpoint
curl -v https://your-service.onrender.com/health

# Test main application endpoint
curl -v https://your-service.onrender.com/api/v1/health

# Check service response headers
curl -I https://your-service.onrender.com/
```

### 4. Review Service Logs

- Use Render dashboard Logs tab for real-time log streaming
- Look for startup errors, database connection issues, or runtime errors
- Check for memory/CPU limit warnings

---

## 🛠️ Common Solutions

### Solution 1: Fix Environment Variables

1. Go to Render Dashboard → Service → Environment
2. Add missing environment variables:
   - CLERK_SECRET_KEY
   - STRIPE_SECRET_KEY
   - STRIPE_WEBHOOK_SECRET
3. Restart the service after adding variables

### Solution 2: Fix Build Configuration

```yaml
# Ensure correct paths in render.yaml
services:
  - type: web
    dockerfilePath: ./infrastructure/docker/backend/Dockerfile
    dockerContext: . # Build from project root
```

### Solution 3: Test Container Locally

```bash
# Test the exact container that will be deployed
docker build -f infrastructure/docker/backend/Dockerfile .
docker run -p 8080:8080 -e PORT=8080 -e NODE_ENV=production IMAGE_ID

# Test health endpoint
curl http://localhost:8080/health
```

---

## 🔧 Prevention

### 1. Use Render Blueprint Configuration

Always deploy using the render.yaml blueprint for consistent configuration:

```yaml
# Complete service definition prevents configuration drift
services:
  - type: web
    name: tech-notes-backend
    runtime: docker
    dockerfilePath: ./infrastructure/docker/backend/Dockerfile
    dockerContext: .
    healthCheckPath: /health
```

### 2. Test Locally Before Deployment

```bash
# Test Docker builds locally
docker build -f infrastructure/docker/backend/Dockerfile .
docker build -f infrastructure/docker/frontend/Dockerfile .

# Test with production-like environment variables
docker run -p 8080:8080 -e PORT=8080 -e NODE_ENV=production IMAGE_ID
```

### 3. Monitor Render Dashboard

- Set up email/Slack notifications for deployment failures
- Monitor service metrics regularly
- Review logs for early warning signs of issues

---

## 📋 Troubleshooting Checklist

When Render deployment fails:

**Service Configuration:**

- [ ] **render.yaml has correct dockerfilePath and dockerContext**
- [ ] **Health check path is correctly configured**
- [ ] **All required environment variables are set in Render dashboard**
- [ ] **Service plan has sufficient resources for the workload**

**Container Issues:**

- [ ] **Application binds to `0.0.0.0:PORT` not `localhost:PORT`**
- [ ] **PORT environment variable is properly parsed (string to number)**
- [ ] **Container starts successfully locally with same environment**
- [ ] **Health endpoint responds with 200 status**
- [ ] **All dependencies are properly installed during build**

**Database Issues:**

- [ ] **DATABASE_URL is correctly configured**
- [ ] **Database service is healthy and accessible**
- [ ] **Prisma migrations have been run**
- [ ] **Connection limits are not exceeded**

**Environment Variables:**

- [ ] **All sensitive secrets are set in Render dashboard**
- [ ] **Frontend build-time variables are properly configured**
- [ ] **Service-to-service communication URLs are correct**

---

## 🆘 Getting Help

If issues persist after following this guide:

1. **Check Render service logs** - Use the Logs tab in Render dashboard for detailed error messages
2. **Test containers locally** - Ensure your Docker containers work locally before deploying
3. **Verify render.yaml configuration** - Double-check all paths and settings
4. **Check Render status page** - Verify there are no platform-wide issues
5. **Contact Render support** - Use their excellent support system for platform-specific issues

### Useful Render Resources

- **Render Documentation**: https://render.com/docs
- **Render Community**: https://community.render.com
- **Render Status**: https://status.render.com

---

## 📚 Related Documentation

- [Render Deployment Guide](../RENDER_DEPLOYMENT.md)
- [Docker Deployment Patterns](../DOCKER_DEPLOYMENT.md)
- [Deployment Monitoring](../DEPLOYMENT_MONITORING.md)
