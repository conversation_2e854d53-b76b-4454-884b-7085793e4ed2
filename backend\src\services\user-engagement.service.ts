import { BackendAuthContext } from '@tech-notes/shared';

import { BaseTenantService } from './base-tenant.service.js';

export interface EngagementMetrics {
  dau: number; // Daily Active Users
  wau: number; // Weekly Active Users
  mau: number; // Monthly Active Users
  totalUsers: number;
  inactiveOneWeek: number;
  inactiveOneMonth: number;
}

export interface UserActivitySummary {
  id: string;
  email: string;
  firstName?: string | null;
  lastName?: string | null;
  lastLoginAt?: Date | null;
  lastActivityAt?: Date | null;
  isActive: boolean;
  createdAt: Date;
}

export interface InactiveUsersReport {
  users: UserActivitySummary[];
  totalCount: number;
  thresholdDays: number;
}

/**
 * Service for tracking and reporting user engagement metrics
 * Provides tenant-scoped engagement data with RBAC compliance
 */
export class UserEngagementService extends BaseTenantService {
  /**
   * Update user's last login timestamp
   * Called during authentication when a new session is established
   */
  async updateLastLogin(
    userId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('updateLastLogin', { userId, tenantId });

    await this.withTenantScopeForUser(
      tenantId,
      async () => {
        await this.prisma.user.update({
          where: {
            id: userId,
            tenantId, // Double validation
          },
          data: {
            lastLoginAt: new Date(),
          },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Update user's last activity timestamp (throttled)
   * Called during user activity with throttling to prevent excessive DB writes
   */
  async updateLastActivity(
    userId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('updateLastActivity', { userId, tenantId });

    await this.withTenantScopeForUser(
      tenantId,
      async () => {
        await this.prisma.user.update({
          where: {
            id: userId,
            tenantId, // Double validation
          },
          data: {
            lastActivityAt: new Date(),
          },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Get engagement metrics for a tenant
   * Returns DAU, WAU, MAU and inactive user counts
   */
  async getEngagementMetrics(
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<EngagementMetrics> {
    this.logOperation('getEngagementMetrics', { tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        const now = new Date();
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

        // Get all metrics in parallel for better performance
        const [
          dauCount,
          wauCount,
          mauCount,
          totalUsersCount,
          inactiveOneWeekCount,
          inactiveOneMonthCount,
        ] = await Promise.all([
          // DAU: Users with activity in last 24 hours
          this.prisma.user.count({
            where: {
              tenantId,
              isActive: true,
              lastActivityAt: {
                gte: oneDayAgo,
              },
            },
          }),
          // WAU: Users with activity in last 7 days
          this.prisma.user.count({
            where: {
              tenantId,
              isActive: true,
              lastActivityAt: {
                gte: oneWeekAgo,
              },
            },
          }),
          // MAU: Users with activity in last 30 days
          this.prisma.user.count({
            where: {
              tenantId,
              isActive: true,
              lastActivityAt: {
                gte: oneMonthAgo,
              },
            },
          }),
          // Total active users
          this.prisma.user.count({
            where: {
              tenantId,
              isActive: true,
            },
          }),
          // Inactive users (no activity in 7+ days)
          this.prisma.user.count({
            where: {
              tenantId,
              isActive: true,
              OR: [
                { lastActivityAt: null },
                { lastActivityAt: { lt: oneWeekAgo } },
              ],
            },
          }),
          // Inactive users (no activity in 30+ days)
          this.prisma.user.count({
            where: {
              tenantId,
              isActive: true,
              OR: [
                { lastActivityAt: null },
                { lastActivityAt: { lt: oneMonthAgo } },
              ],
            },
          }),
        ]);

        return {
          dau: dauCount,
          wau: wauCount,
          mau: mauCount,
          totalUsers: totalUsersCount,
          inactiveOneWeek: inactiveOneWeekCount,
          inactiveOneMonth: inactiveOneMonthCount,
        };
      },
      BackendAuthContext
    );
  }

  /**
   * Get inactive users report
   * Returns users who haven't been active for the specified number of days
   */
  async getInactiveUsers(
    tenantId: string,
    thresholdDays: number,
    BackendAuthContext?: BackendAuthContext,
    limit: number = 50,
    offset: number = 0
  ): Promise<InactiveUsersReport> {
    this.logOperation('getInactiveUsers', {
      tenantId,
      thresholdDays,
      limit,
      offset,
    });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        const thresholdDate = new Date(
          Date.now() - thresholdDays * 24 * 60 * 60 * 1000
        );

        const whereClause = {
          tenantId,
          isActive: true,
          OR: [
            { lastActivityAt: null },
            { lastActivityAt: { lt: thresholdDate } },
          ],
        };

        // Get total count and users in parallel
        const [totalCount, users] = await Promise.all([
          this.prisma.user.count({ where: whereClause }),
          this.prisma.user.findMany({
            where: whereClause,
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              lastLoginAt: true,
              lastActivityAt: true,
              isActive: true,
              createdAt: true,
            },
            orderBy: [
              { lastActivityAt: 'asc' }, // Null values first, then oldest activity
              { createdAt: 'desc' },
            ],
            take: limit,
            skip: offset,
          }),
        ]);

        return {
          users,
          totalCount,
          thresholdDays,
        };
      },
      BackendAuthContext
    );
  }

  /**
   * Get user activity summary with pagination
   * Returns all users with their activity timestamps
   */
  async getUserActivitySummary(
    tenantId: string,
    BackendAuthContext?: BackendAuthContext,
    limit: number = 50,
    offset: number = 0,
    sortBy: 'lastLoginAt' | 'lastActivityAt' | 'email' = 'lastActivityAt',
    sortOrder: 'asc' | 'desc' = 'desc'
  ): Promise<{ users: UserActivitySummary[]; totalCount: number }> {
    this.logOperation('getUserActivitySummary', {
      tenantId,
      limit,
      offset,
      sortBy,
      sortOrder,
    });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        const whereClause = {
          tenantId,
          isActive: true,
        };

        // Get total count and users in parallel
        const [totalCount, users] = await Promise.all([
          this.prisma.user.count({ where: whereClause }),
          this.prisma.user.findMany({
            where: whereClause,
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              lastLoginAt: true,
              lastActivityAt: true,
              isActive: true,
              createdAt: true,
            },
            orderBy: { [sortBy]: sortOrder },
            take: limit,
            skip: offset,
          }),
        ]);

        return {
          users,
          totalCount,
        };
      },
      BackendAuthContext
    );
  }
}
