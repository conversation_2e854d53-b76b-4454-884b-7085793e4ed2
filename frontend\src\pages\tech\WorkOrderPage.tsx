import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Badge } from "../../components";
import {
  ClipboardList,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  Calendar,
  MapPin,
} from "lucide-react";

interface WorkOrder {
  id: string;
  title: string;
  description: string;
  status: "pending" | "in-progress" | "completed" | "urgent";
  priority: "low" | "medium" | "high" | "urgent";
  assignedTo: string;
  dueDate: string;
  location: string;
  vehicleInfo?: string;
}

// Mock data - in real app this would come from API
const mockWorkOrders: WorkOrder[] = [
  {
    id: "WO-001",
    title: "Oil Change Service",
    description: "Routine oil change and filter replacement",
    status: "pending",
    priority: "medium",
    assignedTo: "You",
    dueDate: "2024-01-15",
    location: "Bay 3",
    vehicleInfo: "2020 Honda Civic - ABC123",
  },
  {
    id: "WO-002",
    title: "Brake Inspection",
    description: "Complete brake system inspection and report",
    status: "urgent",
    priority: "urgent",
    assignedTo: "You",
    dueDate: "2024-01-14",
    location: "Bay 1",
    vehicleInfo: "2019 Ford F-150 - XYZ789",
  },
];

type FilterStatus = "all" | "pending" | "in-progress" | "completed" | "urgent";

export const WorkOrderPage: React.FC = () => {
  const [filter, setFilter] = useState<FilterStatus>("all");
  const [workOrders] = useState<WorkOrder[]>(mockWorkOrders);

  const filteredOrders = workOrders.filter(
    (order) => filter === "all" || order.status === filter,
  );

  const getStatusIcon = (status: WorkOrder["status"]) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4" />;
      case "in-progress":
        return <AlertCircle className="h-4 w-4" />;
      case "completed":
        return <CheckCircle className="h-4 w-4" />;
      case "urgent":
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: WorkOrder["status"]) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "in-progress":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "urgent":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: WorkOrder["priority"]) => {
    switch (priority) {
      case "low":
        return "bg-gray-100 text-gray-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "high":
        return "bg-orange-100 text-orange-800";
      case "urgent":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Work Orders</h1>
        <p className="text-gray-600">Manage your assigned tasks</p>
      </div>

      {/* Filter Tabs */}
      <div className="flex overflow-x-auto space-x-1 bg-gray-100 rounded-lg p-1">
        {(
          [
            "all",
            "pending",
            "in-progress",
            "urgent",
            "completed",
          ] as FilterStatus[]
        ).map((status) => (
          <button
            key={status}
            onClick={() => setFilter(status)}
            className={`flex-shrink-0 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              filter === status
                ? "bg-white text-primary-600 shadow-sm"
                : "text-gray-600 hover:text-gray-900"
            }`}
          >
            {status.charAt(0).toUpperCase() + status.slice(1).replace("-", " ")}
          </button>
        ))}
      </div>

      {/* Work Orders List */}
      <div className="space-y-4">
        {filteredOrders.length === 0 ? (
          <Card>
            <CardContent className="p-8">
              <div className="text-center">
                <ClipboardList className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No work orders found</p>
                <p className="text-gray-400 text-sm mt-1">
                  {filter === "all"
                    ? "You have no assigned work orders"
                    : `No ${filter.replace("-", " ")} work orders`}
                </p>
              </div>
            </CardContent>
          </Card>
        ) : (
          filteredOrders.map((order) => (
            <Card key={order.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {order.title}
                        </h3>
                        <Badge
                          className={`${getStatusColor(order.status)} flex items-center space-x-1`}
                        >
                          {getStatusIcon(order.status)}
                          <span className="capitalize">
                            {order.status.replace("-", " ")}
                          </span>
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        {order.description}
                      </p>
                      <div className="text-xs text-gray-500">
                        Order #{order.id}
                      </div>
                    </div>
                    <Badge className={getPriorityColor(order.priority)}>
                      {order.priority.toUpperCase()}
                    </Badge>
                  </div>

                  {/* Details */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                    <div className="flex items-center space-x-2 text-gray-600">
                      <User className="h-4 w-4" />
                      <span>{order.assignedTo}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-600">
                      <Calendar className="h-4 w-4" />
                      <span>Due: {order.dueDate}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-600">
                      <MapPin className="h-4 w-4" />
                      <span>{order.location}</span>
                    </div>
                    {order.vehicleInfo && (
                      <div className="flex items-center space-x-2 text-gray-600">
                        <ClipboardList className="h-4 w-4" />
                        <span>{order.vehicleInfo}</span>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-3 pt-2">
                    {order.status === "pending" && (
                      <Button variant="primary" className="flex-1">
                        Start Work
                      </Button>
                    )}
                    {order.status === "in-progress" && (
                      <Button variant="primary" className="flex-1">
                        Mark Complete
                      </Button>
                    )}
                    {order.status === "urgent" && (
                      <Button
                        variant="primary"
                        className="flex-1 bg-red-600 hover:bg-red-700"
                      >
                        Start Urgent
                      </Button>
                    )}
                    <Button variant="outline" className="px-6">
                      Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};
