import { Router, NextFunction, Request, Response } from 'express';
import { Logger } from 'winston';

import { MiddlewareFactory } from '../../../middleware/middleware-factory.js';
import { UserEngagementService } from '../../../services/user-engagement.service.js';
import { getRequestUser } from '../../../utils/request-types.js';

interface ServiceDependencies {
  userEngagementService: UserEngagementService;
  middlewareFactory: MiddlewareFactory;
  logger: Logger;
}

export function createEngagementRouter(
  dependencies: ServiceDependencies
): Router {
  const { userEngagementService, middlewareFactory, logger } = dependencies;
  const router = Router();

  /**
   * GET /api/v1/engagement/metrics
   * Get engagement metrics (DAU, WAU, MAU) for tenant
   * Requires Company Admin or System Admin access
   */
  router.get(
    '/metrics',
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { canBypassTenantScope } = getRequestUser(req)!;
        const targetTenantId =
          canBypassTenantScope && req.query.tenantId
            ? (req.query.tenantId as string)
            : getRequestUser(req)!.tenantId;

        const metrics = await userEngagementService.getEngagementMetrics(
          targetTenantId,
          getRequestUser(req)!
        );

        res.json({
          data: metrics,
          meta: {
            tenantId: targetTenantId,
            timestamp: new Date().toISOString(),
          },
        });
      } catch (error) {
        logger.error('Failed to fetch engagement metrics', {
          error,
          tenantId: getRequestUser(req)?.tenantId,
          canBypassTenantScope: getRequestUser(req)?.canBypassTenantScope,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/engagement/inactive-users
   * Get inactive users report
   * Requires Company Admin or System Admin access
   */
  router.get(
    '/inactive-users',
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        // Parse and validate query parameters
        const thresholdDaysStr = req.query.thresholdDays as string;
        const limitStr = req.query.limit as string;
        const offsetStr = req.query.offset as string;

        const thresholdDays = parseInt(thresholdDaysStr, 10);
        const limit = limitStr ? parseInt(limitStr, 10) : 50;
        const offset = offsetStr ? parseInt(offsetStr, 10) : 0;

        // Validate parameters
        if (
          !thresholdDaysStr ||
          isNaN(thresholdDays) ||
          thresholdDays < 1 ||
          thresholdDays > 365
        ) {
          return res.status(400).json({
            error:
              'Invalid thresholdDays parameter. Must be between 1 and 365.',
          });
        }

        if (limit < 1 || limit > 100) {
          return res.status(400).json({
            error: 'Invalid limit parameter. Must be between 1 and 100.',
          });
        }

        if (offset < 0) {
          return res.status(400).json({
            error: 'Invalid offset parameter. Must be non-negative.',
          });
        }
        const { canBypassTenantScope } = getRequestUser(req)!;
        const targetTenantId =
          canBypassTenantScope && req.query.tenantId
            ? (req.query.tenantId as string)
            : getRequestUser(req)!.tenantId;

        const report = await userEngagementService.getInactiveUsers(
          targetTenantId,
          thresholdDays,
          getRequestUser(req)!,
          limit,
          offset
        );

        res.json({
          data: report,
          meta: {
            tenantId: targetTenantId,
            thresholdDays,
            limit,
            offset,
            timestamp: new Date().toISOString(),
          },
        });
      } catch (error) {
        logger.error('Failed to fetch inactive users', {
          error,
          tenantId: getRequestUser(req)?.tenantId,
          canBypassTenantScope: getRequestUser(req)?.canBypassTenantScope,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/engagement/user-activity
   * Get user activity summary with pagination
   * Requires Company Admin or System Admin access
   */
  router.get(
    '/user-activity',
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        // Parse and validate query parameters
        const limitStr = req.query.limit as string;
        const offsetStr = req.query.offset as string;
        const sortBy = (req.query.sortBy as string) || 'lastActivityAt';
        const sortOrder = (req.query.sortOrder as string) || 'desc';

        const limit = limitStr ? parseInt(limitStr, 10) : 50;
        const offset = offsetStr ? parseInt(offsetStr, 10) : 0;

        // Validate parameters
        if (limit < 1 || limit > 100) {
          return res.status(400).json({
            error: 'Invalid limit parameter. Must be between 1 and 100.',
          });
        }

        if (offset < 0) {
          return res.status(400).json({
            error: 'Invalid offset parameter. Must be non-negative.',
          });
        }

        if (!['lastLoginAt', 'lastActivityAt', 'email'].includes(sortBy)) {
          return res.status(400).json({
            error:
              'Invalid sortBy parameter. Must be one of: lastLoginAt, lastActivityAt, email.',
          });
        }

        if (!['asc', 'desc'].includes(sortOrder)) {
          return res.status(400).json({
            error: 'Invalid sortOrder parameter. Must be asc or desc.',
          });
        }
        const { canBypassTenantScope } = getRequestUser(req)!;
        const targetTenantId =
          canBypassTenantScope && req.query.tenantId
            ? (req.query.tenantId as string)
            : getRequestUser(req)!.tenantId;

        const result = await userEngagementService.getUserActivitySummary(
          targetTenantId,
          getRequestUser(req)!,
          limit,
          offset,
          sortBy as 'lastLoginAt' | 'lastActivityAt' | 'email',
          sortOrder as 'asc' | 'desc'
        );

        res.json({
          data: result,
          meta: {
            tenantId: targetTenantId,
            limit,
            offset,
            sortBy,
            sortOrder,
            timestamp: new Date().toISOString(),
          },
        });
      } catch (error) {
        logger.error('Failed to fetch user activity summary', {
          error,
          tenantId: getRequestUser(req)?.tenantId,
          canBypassTenantScope: getRequestUser(req)?.canBypassTenantScope,
        });
        next(error);
      }
    }
  );

  return router;
}
