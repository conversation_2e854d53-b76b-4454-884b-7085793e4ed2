import React from "react";
import { clsx } from "clsx";
import { Card } from "../atoms/Card";
import { Button } from "../atoms/Button";
import { Badge } from "../atoms/Badge";
import { Check, X, Star } from "lucide-react";

export interface PricingTier {
  id: string;
  name: string;
  price: string;
  period?: string;
  description: string;
  icon?: React.ReactNode;
  popular?: boolean;
  features: Array<{
    name: string;
    included: boolean;
    highlight?: boolean;
  }>;
  buttonText: string;
  buttonVariant?: "primary" | "secondary" | "outline";
  buttonAction?: () => void;
  buttonHref?: string;
  customContent?: React.ReactNode;
}

export interface PricingCardsProps
  extends React.HTMLAttributes<HTMLDivElement> {
  /** Array of pricing tiers to display */
  tiers: PricingTier[];
  /** Layout variant for the pricing cards */
  layout?: "grid" | "comparison" | "stacked";
  /** Number of columns for grid layout */
  columns?: 2 | 3 | 4;
  /** Spacing between cards */
  spacing?: "tight" | "normal" | "loose";
  /** Optional section title */
  title?: string;
  /** Optional section subtitle */
  subtitle?: string;
  /** Optional section badge/announcement */
  badge?: {
    text: string;
    variant?: "primary" | "success" | "warning";
    icon?: React.ReactNode;
  };
  /** Whether to show feature comparison */
  showComparison?: boolean;
  /** Whether to highlight popular tier */
  highlightPopular?: boolean;
  /** Custom render function for individual tiers */
  renderTier?: (tier: PricingTier, index: number) => React.ReactNode;
  /** Billing period toggle */
  billingPeriod?: {
    current: "monthly" | "yearly";
    onToggle: (period: "monthly" | "yearly") => void;
    discount?: string;
  };
}

const layoutClasses = {
  grid: "grid gap-8",
  comparison: "grid gap-8 relative",
  stacked: "space-y-6",
};

const columnClasses = {
  2: "grid-cols-1 md:grid-cols-2",
  3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
  4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
};

const spacingClasses = {
  tight: "gap-4",
  normal: "gap-8",
  loose: "gap-12",
};

export const PricingCards: React.FC<PricingCardsProps> = ({
  tiers,
  layout = "grid",
  columns = 3,
  spacing = "normal",
  title,
  subtitle,
  badge,
  showComparison = false, // eslint-disable-line @typescript-eslint/no-unused-vars
  highlightPopular = true,
  renderTier,
  billingPeriod,
  className,
  ...props
}) => {
  const renderHeader = () => {
    if (!title && !subtitle && !badge && !billingPeriod) return null;

    return (
      <div className="text-center mb-12">
        {badge && (
          <div className="mb-4 flex justify-center">
            <Badge
              variant={badge.variant || "primary"}
              size="md"
              icon={badge.icon}
              className="px-4 py-2 text-sm font-medium"
            >
              {badge.text}
            </Badge>
          </div>
        )}

        {title && (
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            {title}
          </h2>
        )}

        {subtitle && (
          <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
            {subtitle}
          </p>
        )}

        {billingPeriod && (
          <div className="mt-8 flex items-center justify-center">
            <div className="bg-gray-100 p-1 rounded-lg">
              <button
                onClick={() => billingPeriod.onToggle("monthly")}
                className={clsx(
                  "px-4 py-2 text-sm font-medium rounded-md transition-all duration-200",
                  billingPeriod.current === "monthly"
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900",
                )}
              >
                Monthly
              </button>
              <button
                onClick={() => billingPeriod.onToggle("yearly")}
                className={clsx(
                  "px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 flex items-center gap-2",
                  billingPeriod.current === "yearly"
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900",
                )}
              >
                Yearly
                {billingPeriod.discount && (
                  <Badge variant="success" size="sm">
                    {billingPeriod.discount}
                  </Badge>
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderFeatureList = (features: PricingTier["features"]) => {
    return (
      <ul className="space-y-3">
        {features.map((feature, index) => (
          <li key={index} className="flex items-start">
            {feature.included ? (
              <Check className="h-5 w-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
            ) : (
              <X className="h-5 w-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" />
            )}
            <span
              className={clsx(
                "text-sm",
                feature.included ? "text-gray-700" : "text-gray-400",
                feature.highlight && "font-semibold text-primary-700",
              )}
            >
              {feature.name}
            </span>
          </li>
        ))}
      </ul>
    );
  };

  const renderPricingCard = (tier: PricingTier, index: number) => {
    if (renderTier) {
      return renderTier(tier, index);
    }

    const isPopular = highlightPopular && tier.popular;

    return (
      <div
        key={tier.id}
        className={clsx("relative", layout === "stacked" && "max-w-md mx-auto")}
      >
        <Card
          variant={isPopular ? "elevated" : "default"}
          className={clsx(
            "h-full flex flex-col p-8",
            isPopular && "ring-2 ring-primary-600 shadow-lg scale-105",
            layout === "comparison" && "relative z-10",
          )}
        >
          {/* Popular Badge */}
          {isPopular && (
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <Badge
                variant="primary"
                size="md"
                icon={<Star className="h-4 w-4" />}
              >
                Most Popular
              </Badge>
            </div>
          )}

          {/* Header */}
          <div className="text-center mb-8">
            {tier.icon && (
              <div className="flex justify-center mb-4">
                <div className="p-3 bg-primary-100 rounded-full">
                  {tier.icon}
                </div>
              </div>
            )}

            <h3 className="text-2xl font-bold text-gray-900 mb-2">
              {tier.name}
            </h3>

            <div className="mb-4">
              <span className="text-4xl font-bold text-gray-900">
                {tier.price}
              </span>
              {tier.period && (
                <span className="text-gray-600 ml-1">/{tier.period}</span>
              )}
            </div>

            <p className="text-gray-600 text-sm">{tier.description}</p>
          </div>

          {/* Features */}
          <div className="flex-1 mb-8">{renderFeatureList(tier.features)}</div>

          {/* Custom Content */}
          {tier.customContent && (
            <div className="mb-8">{tier.customContent}</div>
          )}

          {/* CTA Button */}
          <div className="mt-auto">
            <Button
              variant={tier.buttonVariant || "primary"}
              size="lg"
              className="w-full"
              onClick={tier.buttonAction}
              asChild={!!tier.buttonHref}
            >
              {tier.buttonHref ? (
                <a href={tier.buttonHref}>{tier.buttonText}</a>
              ) : (
                tier.buttonText
              )}
            </Button>
          </div>
        </Card>
      </div>
    );
  };

  const containerClasses = clsx(
    layout === "grid" && layoutClasses.grid,
    layout === "grid" && columnClasses[columns],
    layout === "grid" && spacingClasses[spacing],
    layout === "comparison" && layoutClasses.comparison,
    layout === "comparison" && columnClasses[columns],
    layout === "comparison" && spacingClasses[spacing],
    layout === "stacked" && layoutClasses.stacked,
  );

  return (
    <div className={clsx("w-full", className)} {...props}>
      {renderHeader()}

      <div className={containerClasses}>
        {tiers.map((tier, index) => renderPricingCard(tier, index))}
      </div>
    </div>
  );
};
