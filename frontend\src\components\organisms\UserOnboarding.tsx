import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { Button } from "../atoms/Button";
import { useAuth } from "../../hooks/useAuth";
import type { OnboardingData } from "../../services/auth-api.service";
import toast from "react-hot-toast";

export function UserOnboarding() {
  const { api, clerkUser, refreshAuth } = useAuth();
  const [formData, setFormData] = useState<OnboardingData>({
    tenantName: "",
    tenantSlug: "",
    firstName: clerkUser?.firstName || "",
    lastName: clerkUser?.lastName || "",
  });

  const onboardingMutation = useMutation({
    mutationFn: (data: OnboardingData) => api.completeOnboarding(data),
    onSuccess: () => {
      toast.success("Welcome! Your account has been set up successfully.");
      refreshAuth(); // Refresh auth state to update onboarding status
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to complete onboarding");
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.tenantName.trim()) {
      toast.error("Organization name is required");
      return;
    }

    if (!formData.tenantSlug.trim()) {
      toast.error("Organization slug is required");
      return;
    }

    // Validate slug format
    if (!/^[a-z0-9-]+$/.test(formData.tenantSlug)) {
      toast.error(
        "Organization slug must contain only lowercase letters, numbers, and hyphens",
      );
      return;
    }

    onboardingMutation.mutate(formData);
  };

  const handleInputChange = (field: keyof OnboardingData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Auto-generate slug from tenant name
    if (field === "tenantName") {
      const slug = value
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .replace(/^-|-$/g, "");
      setFormData((prev) => ({ ...prev, tenantSlug: slug }));
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Welcome to Tech Notes
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Let's set up your organization to get started
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label
                htmlFor="tenantName"
                className="block text-sm font-medium text-gray-700"
              >
                Organization Name
              </label>
              <div className="mt-1">
                <input
                  id="tenantName"
                  name="tenantName"
                  type="text"
                  required
                  value={formData.tenantName}
                  onChange={(e) =>
                    handleInputChange("tenantName", e.target.value)
                  }
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Your Organization"
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="tenantSlug"
                className="block text-sm font-medium text-gray-700"
              >
                Organization Slug
              </label>
              <div className="mt-1">
                <input
                  id="tenantSlug"
                  name="tenantSlug"
                  type="text"
                  required
                  value={formData.tenantSlug}
                  onChange={(e) =>
                    handleInputChange("tenantSlug", e.target.value)
                  }
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="your-organization"
                  pattern="^[a-z0-9-]+$"
                />
              </div>
              <p className="mt-2 text-sm text-gray-500">
                Used in URLs. Only lowercase letters, numbers, and hyphens
                allowed.
              </p>
            </div>

            <div>
              <label
                htmlFor="firstName"
                className="block text-sm font-medium text-gray-700"
              >
                First Name
              </label>
              <div className="mt-1">
                <input
                  id="firstName"
                  name="firstName"
                  type="text"
                  value={formData.firstName}
                  onChange={(e) =>
                    handleInputChange("firstName", e.target.value)
                  }
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="John"
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="lastName"
                className="block text-sm font-medium text-gray-700"
              >
                Last Name
              </label>
              <div className="mt-1">
                <input
                  id="lastName"
                  name="lastName"
                  type="text"
                  value={formData.lastName}
                  onChange={(e) =>
                    handleInputChange("lastName", e.target.value)
                  }
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Doe"
                />
              </div>
            </div>

            <div>
              <Button
                type="submit"
                variant="primary"
                className="w-full flex justify-center py-2 px-4"
                disabled={onboardingMutation.isPending}
              >
                {onboardingMutation.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Setting up...
                  </>
                ) : (
                  "Complete Setup"
                )}
              </Button>
            </div>
          </form>

          {clerkUser && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="text-sm text-gray-500 text-center">
                Signed in as {clerkUser.emailAddresses[0]?.emailAddress}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
