import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import { BrowserRouter } from "react-router-dom";

import { RoleBasedRedirect } from "./RoleBasedRedirect";
import { useAuth } from "../../hooks/useAuth";
import { usePermissions } from "../../hooks/usePermissions";
import {
  createMockServices,
  setupMockAuthService,
  testData,
  type MockServices,
} from "../../__tests__/helpers/service-factory.helper";

// Mock the hooks
vi.mock("../../hooks/useAuth");
vi.mock("../../hooks/usePermissions");

// Mock Navigate component
const mockNavigate = vi.fn();
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    Navigate: ({ to, replace }: { to: string; replace?: boolean }) => {
      mockNavigate(to, replace);
      return <div data-testid="navigate" data-to={to} data-replace={replace} />;
    },
  };
});

const mockUseAuth = vi.mocked(useAuth);
const mockUsePermissions = vi.mocked(usePermissions);

const renderRoleBasedRedirect = () => {
  return render(
    <BrowserRouter>
      <RoleBasedRedirect />
    </BrowserRouter>,
  );
};

describe("RoleBasedRedirect", () => {
  let mockServices: MockServices;

  beforeEach(() => {
    vi.clearAllMocks();
    mockServices = createMockServices();
  });

  it("shows loading spinner when auth is loading", () => {
    mockUseAuth.mockReturnValue({
      userProfile: null,
      isLoading: true,
      isAuthenticated: false,
      isOnboarded: false,
      needsOnboarding: false,
      isServiceUnavailable: false,
      clerkUser: null,
      authStatus: undefined,
      signOut: vi.fn(),
      signIn: vi.fn(),
      refreshAuth: vi.fn(),
      getToken: vi.fn(),
      api: mockServices.authService,
      statusError: null,
      profileError: null,
    });

    mockUsePermissions.mockReturnValue({
      isCompanyTech: false,
      isAdmin: false,
      permissions: [],
      roles: [],
      hasPermission: vi.fn(),
      hasRole: vi.fn(),
      hasAnyRole: vi.fn(),
      hasAllRoles: vi.fn(),
      hasAllPermissions: vi.fn(),
      hasAnyPermission: vi.fn(),
      isSystemAdmin: false,
      isCompanyAdmin: false,
      canBypassTenantScope: false,
      canManageUsers: false,
      canViewUsers: false,
      canDeleteUsers: false,
      canManageTenant: false,
      canViewTenant: false,
      canManageData: false,
      canViewData: false,
      canManageSystem: false,
    });

    renderRoleBasedRedirect();

    expect(screen.getByRole("status")).toBeInTheDocument(); // Loading spinner
  });

  it("redirects Company Tech (non-admin) to /tech", () => {
    setupMockAuthService(mockServices.authService, "authenticated");

    mockUseAuth.mockReturnValue({
      userProfile: testData.userProfile,
      isLoading: false,
      isAuthenticated: true,
      isOnboarded: true,
      needsOnboarding: false,
      isServiceUnavailable: false,
      clerkUser: testData.clerkUser as any, // eslint-disable-line @typescript-eslint/no-explicit-any
      authStatus: testData.authStatus,
      signOut: vi.fn(),
      signIn: vi.fn(),
      refreshAuth: vi.fn(),
      getToken: vi.fn(),
      api: mockServices.authService,
      statusError: null,
      profileError: null,
    });

    mockUsePermissions.mockReturnValue({
      isCompanyTech: true,
      isAdmin: false,
      permissions: [],
      roles: ["COMPANY_TECH"],
      hasPermission: vi.fn(),
      hasRole: vi.fn(),
      hasAnyRole: vi.fn(),
      hasAllRoles: vi.fn(),
      hasAllPermissions: vi.fn(),
      hasAnyPermission: vi.fn(),
      isSystemAdmin: false,
      isCompanyAdmin: false,
      canBypassTenantScope: false,
      canManageUsers: false,
      canViewUsers: false,
      canDeleteUsers: false,
      canManageTenant: false,
      canViewTenant: false,
      canManageData: false,
      canViewData: false,
      canManageSystem: false,
    });

    renderRoleBasedRedirect();

    const navigate = screen.getByTestId("navigate");
    expect(navigate).toHaveAttribute("data-to", "/tech");
    expect(navigate).toHaveAttribute("data-replace", "true");
  });
});
