-- CreateTable
CREATE TABLE "vehicle_years" (
    "id" TEXT NOT NULL,
    "year" INTEGER NOT NULL,
    "tenantId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vehicle_years_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "vehicle_makes" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vehicle_makes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "vehicle_models" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "makeId" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vehicle_models_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "vehicle_model_years" (
    "modelId" TEXT NOT NULL,
    "yearId" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vehicle_model_years_pkey" PRIMARY KEY ("modelId","yearId")
);

-- CreateIndex
CREATE UNIQUE INDEX "vehicle_years_year_key" ON "vehicle_years"("year");

-- CreateIndex
CREATE INDEX "vehicle_years_tenantId_idx" ON "vehicle_years"("tenantId");

-- CreateIndex
CREATE INDEX "vehicle_years_year_idx" ON "vehicle_years"("year");

-- CreateIndex
CREATE INDEX "vehicle_makes_tenantId_idx" ON "vehicle_makes"("tenantId");

-- CreateIndex
CREATE INDEX "vehicle_makes_name_idx" ON "vehicle_makes"("name");

-- CreateIndex
CREATE UNIQUE INDEX "vehicle_makes_name_tenantId_key" ON "vehicle_makes"("name", "tenantId");

-- CreateIndex
CREATE INDEX "vehicle_models_tenantId_idx" ON "vehicle_models"("tenantId");

-- CreateIndex
CREATE INDEX "vehicle_models_makeId_idx" ON "vehicle_models"("makeId");

-- CreateIndex
CREATE INDEX "vehicle_models_isActive_idx" ON "vehicle_models"("isActive");

-- CreateIndex
CREATE UNIQUE INDEX "vehicle_models_name_makeId_tenantId_key" ON "vehicle_models"("name", "makeId", "tenantId");

-- CreateIndex
CREATE INDEX "vehicle_model_years_tenantId_idx" ON "vehicle_model_years"("tenantId");

-- CreateIndex
CREATE INDEX "vehicle_model_years_modelId_idx" ON "vehicle_model_years"("modelId");

-- CreateIndex
CREATE INDEX "vehicle_model_years_yearId_idx" ON "vehicle_model_years"("yearId");

-- AddForeignKey
ALTER TABLE "vehicle_years" ADD CONSTRAINT "vehicle_years_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_makes" ADD CONSTRAINT "vehicle_makes_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_models" ADD CONSTRAINT "vehicle_models_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_models" ADD CONSTRAINT "vehicle_models_makeId_fkey" FOREIGN KEY ("makeId") REFERENCES "vehicle_makes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_model_years" ADD CONSTRAINT "vehicle_model_years_modelId_fkey" FOREIGN KEY ("modelId") REFERENCES "vehicle_models"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_model_years" ADD CONSTRAINT "vehicle_model_years_yearId_fkey" FOREIGN KEY ("yearId") REFERENCES "vehicle_years"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_model_years" ADD CONSTRAINT "vehicle_model_years_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;
