import React, { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { Modal, Button, Input, FormField, Alert } from "../index";
import { useTypedApi } from "../../services/api-client";
import { Building2 } from "lucide-react";

interface CreateBrandV2ModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const CreateBrandV2Modal: React.FC<CreateBrandV2ModalProps> = ({
  isOpen,
  onClose,
}) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [name, setName] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});

  const createBrandMutation = useMutation({
    mutationFn: (brandData: { name: string }) =>
      api.vehicleHierarchyV2.createBrand(brandData),
    onSuccess: () => {
      toast.success("Brand created successfully");
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "brands"],
      });
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "full"],
      });
      handleClose();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create brand");
    },
  });

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = "Brand name is required";
    } else if (name.trim().length < 2) {
      newErrors.name = "Brand name must be at least 2 characters";
    } else if (name.trim().length > 100) {
      newErrors.name = "Brand name must be less than 100 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    createBrandMutation.mutate({
      name: name.trim(),
    });
  };

  const handleClose = () => {
    setName("");
    setErrors({});
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Create New Brand"
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex items-center space-x-3 text-gray-600 mb-4">
          <Building2 className="h-5 w-5" />
          <span>Add a new vehicle brand to the hierarchy</span>
        </div>

        <FormField label="Brand Name" error={errors.name} required>
          <Input
            type="text"
            placeholder="e.g., Ford, Toyota, Rockwood"
            value={name}
            onChange={(e) => setName(e.target.value)}
            error={errors.name}
            maxLength={100}
          />
        </FormField>

        {createBrandMutation.isError && (
          <Alert variant="error">
            <p>Failed to create brand. Please try again.</p>
          </Alert>
        )}

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={createBrandMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={createBrandMutation.isPending || !name.trim()}
          >
            {createBrandMutation.isPending ? "Creating..." : "Create Brand"}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
