import { describe, it, expect, beforeEach } from '@jest/globals';

import {
  createMockPrismaService,
  createMockLogger,
  createMockS3Service,
  testData,
  MockPrismaService,
  MockLogger,
  MockS3Service,
} from '../__tests__/simple-mocks.js';
import { NotFoundError, ValidationError } from '../types/error.types.js';

import { DocumentService } from './document.service.js';

// Mock the dependencies
jest.mock('./prisma.service');
jest.mock('./s3.service');

describe('DocumentService', () => {
  let documentService: DocumentService;
  let mockPrismaService: MockPrismaService;
  let mockLogger: MockLogger;
  let mockS3Service: MockS3Service;

  beforeEach(() => {
    mockPrismaService = createMockPrismaService();
    mockLogger = createMockLogger();
    mockS3Service = createMockS3Service();

    documentService = new DocumentService(
      mockPrismaService as unknown as any,
      mockLogger as unknown as any,
      mockS3Service as unknown as any
    );
  });

  describe('createDocument', () => {
    const createRequest = {
      ...testData.createDocumentData,
      tenantId: testData.tenant.id,
      createdBy: testData.user.id,
    };

    it('should create document with presigned upload URL when valid data provided', async () => {
      // Mock successful tenant validation
      mockPrismaService.prisma.tenant.findUnique.mockResolvedValue(
        testData.tenant
      );

      // Mock document creation
      mockPrismaService.prisma.document.create.mockResolvedValue(
        testData.document
      );

      // Mock S3 presigned URL generation
      mockS3Service.generatePresignedUploadUrl.mockResolvedValue(
        testData.s3UploadResponse
      );

      // Mock document update with S3 key
      const updatedDocument = {
        ...testData.document,
        s3Key: testData.s3UploadResponse.s3Key,
      };
      mockPrismaService.prisma.document.update.mockResolvedValue(
        updatedDocument
      );

      const result = await documentService.createDocument(createRequest);

      expect(result).toEqual({
        ...updatedDocument,
        uploadUrl: testData.s3UploadResponse.uploadUrl,
        s3Key: testData.s3UploadResponse.s3Key,
        expiresIn: testData.s3UploadResponse.expiresIn,
      });

      // Verify tenant isolation
      expect(mockPrismaService.prisma.tenant.findUnique).toHaveBeenCalledWith({
        where: { id: testData.tenant.id },
        select: { id: true },
      });

      // Verify document creation with tenant scoping
      expect(mockPrismaService.prisma.document.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          tenantId: testData.tenant.id,
          fileName: createRequest.fileName,
          originalName: createRequest.originalName,
          fileSize: createRequest.fileSize,
          mimeType: createRequest.mimeType,
          createdBy: createRequest.createdBy,
        }),
      });

      // Verify S3 service called with correct parameters
      expect(mockS3Service.generatePresignedUploadUrl).toHaveBeenCalledWith({
        fileName: createRequest.fileName,
        fileSize: createRequest.fileSize,
        mimeType: createRequest.mimeType,
        tenantId: createRequest.tenantId,
        documentId: testData.document.id,
      });
    });

    it('should throw ValidationError when invalid file type provided', async () => {
      const invalidRequest = {
        ...createRequest,
        mimeType: 'application/invalid',
        fileName: 'test.invalid',
      };

      await expect(
        documentService.createDocument(invalidRequest)
      ).rejects.toThrow(ValidationError);
      await expect(
        documentService.createDocument(invalidRequest)
      ).rejects.toThrow('File type application/invalid is not allowed');
    });

    it('should throw ValidationError when file size exceeds limit', async () => {
      const oversizedRequest = {
        ...createRequest,
        fileSize: 100 * 1024 * 1024, // 100MB (exceeds 50MB PDF limit)
      };

      await expect(
        documentService.createDocument(oversizedRequest)
      ).rejects.toThrow(ValidationError);
      await expect(
        documentService.createDocument(oversizedRequest)
      ).rejects.toThrow('File size 100MB exceeds maximum allowed size of 50MB');
    });

    it('should throw ValidationError when file extension does not match MIME type', async () => {
      const mismatchedRequest = {
        ...createRequest,
        fileName: 'test.txt', // .txt extension
        mimeType: 'application/pdf', // PDF MIME type
      };

      await expect(
        documentService.createDocument(mismatchedRequest)
      ).rejects.toThrow(ValidationError);
      await expect(
        documentService.createDocument(mismatchedRequest)
      ).rejects.toThrow(
        'File extension .txt does not match MIME type application/pdf'
      );
    });

    it('should throw ValidationError when filename is empty', async () => {
      const emptyNameRequest = {
        ...createRequest,
        fileName: '',
      };

      await expect(
        documentService.createDocument(emptyNameRequest)
      ).rejects.toThrow(ValidationError);
      await expect(
        documentService.createDocument(emptyNameRequest)
      ).rejects.toThrow('File name cannot be empty');
    });
  });

  describe('getDocuments', () => {
    const mockDocuments = [testData.document];
    const mockCount = 1;

    beforeEach(() => {
      // Mock successful tenant validation
      mockPrismaService.prisma.tenant.findUnique.mockResolvedValue(
        testData.tenant
      );
      mockPrismaService.prisma.document.count.mockResolvedValue(mockCount);
      mockPrismaService.prisma.document.findMany.mockResolvedValue(
        mockDocuments
      );
    });

    it('should return tenant-scoped documents with pagination when valid tenant ID provided', async () => {
      const result = await documentService.getDocuments(testData.tenant.id);

      expect(result.documents).toEqual(mockDocuments);
      expect(result.pagination).toEqual({
        page: 1,
        limit: 20,
        total: mockCount,
        totalPages: 1,
      });

      // Verify tenant isolation in count query
      expect(mockPrismaService.prisma.document.count).toHaveBeenCalledWith({
        where: {
          tenantId: testData.tenant.id,
          deletedAt: null,
        },
      });

      // Verify tenant isolation in findMany query
      expect(mockPrismaService.prisma.document.findMany).toHaveBeenCalledWith({
        where: {
          tenantId: testData.tenant.id,
          deletedAt: null,
        },
        orderBy: { createdAt: 'desc' },
        skip: 0,
        take: 20,
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });
    });

    it('should apply search filter when search term provided', async () => {
      const searchTerm = 'test';
      await documentService.getDocuments(testData.tenant.id, {
        search: searchTerm,
      });

      expect(mockPrismaService.prisma.document.findMany).toHaveBeenCalledWith({
        where: {
          tenantId: testData.tenant.id,
          deletedAt: null,
          OR: [
            { fileName: { contains: searchTerm, mode: 'insensitive' } },
            { originalName: { contains: searchTerm, mode: 'insensitive' } },
            { title: { contains: searchTerm, mode: 'insensitive' } },
            { description: { contains: searchTerm, mode: 'insensitive' } },
          ],
        },
        orderBy: { createdAt: 'desc' },
        skip: 0,
        take: 20,
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });
    });

    it('should apply custom pagination when page and limit provided', async () => {
      await documentService.getDocuments(testData.tenant.id, {
        page: 2,
        limit: 10,
      });

      expect(mockPrismaService.prisma.document.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 10, // (page 2 - 1) * limit 10
          take: 10,
        })
      );
    });

    it('should apply custom sorting when sortBy and sortOrder provided', async () => {
      await documentService.getDocuments(testData.tenant.id, {
        sortBy: 'fileName',
        sortOrder: 'asc',
      });

      expect(mockPrismaService.prisma.document.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          orderBy: { fileName: 'asc' },
        })
      );
    });
  });

  describe('deleteDocument', () => {
    it('should soft delete document when valid document ID and tenant ID provided', async () => {
      // Mock successful tenant validation
      mockPrismaService.prisma.tenant.findUnique.mockResolvedValue(
        testData.tenant
      );

      // Mock finding the document
      mockPrismaService.prisma.document.findFirst.mockResolvedValue(
        testData.document
      );

      // Mock successful update (soft delete)
      const deletedDocument = { ...testData.document, deletedAt: new Date() };
      mockPrismaService.prisma.document.update.mockResolvedValue(
        deletedDocument
      );

      const result = await documentService.deleteDocument(
        testData.document.id,
        testData.tenant.id
      );

      expect(result).toEqual(deletedDocument);

      // Verify tenant isolation in find query
      expect(mockPrismaService.prisma.document.findFirst).toHaveBeenCalledWith({
        where: {
          id: testData.document.id,
          tenantId: testData.tenant.id,
          deletedAt: null,
        },
      });

      // Verify soft delete update
      expect(mockPrismaService.prisma.document.update).toHaveBeenCalledWith({
        where: { id: testData.document.id },
        data: { deletedAt: expect.any(Date) },
      });
    });

    it('should throw NotFoundError when document does not exist', async () => {
      // Mock successful tenant validation
      mockPrismaService.prisma.tenant.findUnique.mockResolvedValue(
        testData.tenant
      );

      // Mock document not found
      mockPrismaService.prisma.document.findFirst.mockResolvedValue(null);

      await expect(
        documentService.deleteDocument('non-existent-id', testData.tenant.id)
      ).rejects.toThrow(NotFoundError);
      await expect(
        documentService.deleteDocument('non-existent-id', testData.tenant.id)
      ).rejects.toThrow('Document not found');
    });

    it('should throw NotFoundError when document belongs to different tenant', async () => {
      // Mock successful tenant validation
      mockPrismaService.prisma.tenant.findUnique.mockResolvedValue(
        testData.tenant
      );

      // Mock document not found (due to tenant mismatch)
      mockPrismaService.prisma.document.findFirst.mockResolvedValue(null);

      await expect(
        documentService.deleteDocument(
          testData.document.id,
          'different-tenant-id'
        )
      ).rejects.toThrow(NotFoundError);
    });
  });

  describe('file validation', () => {
    it('should validate all allowed MIME types correctly', () => {
      const validFiles = [
        { fileName: 'test.pdf', mimeType: 'application/pdf', fileSize: 1024 },
        {
          fileName: 'test.docx',
          mimeType:
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          fileSize: 1024,
        },
        {
          fileName: 'test.doc',
          mimeType: 'application/msword',
          fileSize: 1024,
        },
        {
          fileName: 'test.xlsx',
          mimeType:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          fileSize: 1024,
        },
        {
          fileName: 'test.xls',
          mimeType: 'application/vnd.ms-excel',
          fileSize: 1024,
        },
        { fileName: 'test.html', mimeType: 'text/html', fileSize: 1024 },
        { fileName: 'test.csv', mimeType: 'text/csv', fileSize: 1024 },
        { fileName: 'test.jpg', mimeType: 'image/jpeg', fileSize: 1024 },
        { fileName: 'test.png', mimeType: 'image/png', fileSize: 1024 },
      ];

      validFiles.forEach((file) => {
        expect(() => {
          (documentService as any).validateFile(
            file.fileName,
            file.fileSize,
            file.mimeType
          );
        }).not.toThrow();
      });
    });

    it('should enforce correct size limits for different file types', () => {
      // Test PDF size limit (50MB)
      expect(() => {
        (documentService as any).validateFile(
          'test.pdf',
          51 * 1024 * 1024,
          'application/pdf'
        );
      }).toThrow('File size 51MB exceeds maximum allowed size of 50MB');

      // Test Office file size limit (25MB)
      expect(() => {
        (documentService as any).validateFile(
          'test.docx',
          26 * 1024 * 1024,
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        );
      }).toThrow('File size 26MB exceeds maximum allowed size of 25MB');

      // Test image size limit (10MB)
      expect(() => {
        (documentService as any).validateFile(
          'test.jpg',
          11 * 1024 * 1024,
          'image/jpeg'
        );
      }).toThrow('File size 11MB exceeds maximum allowed size of 10MB');
    });
  });

  describe('generateDownloadUrl', () => {
    const documentId = testData.document.id;
    const tenantId = testData.tenant.id;
    const authContext = testData.user;

    it('should generate download URL when document exists and belongs to tenant', async () => {
      // Mock successful tenant validation
      mockPrismaService.prisma.tenant.findUnique.mockResolvedValue(
        testData.tenant
      );

      // Mock user-tenant relationship validation
      mockPrismaService.prisma.user.findFirst.mockResolvedValue({
        id: testData.user.id,
        clerkId: testData.user.clerkId,
        tenantId: testData.user.tenantId,
      });

      // Mock document found
      mockPrismaService.prisma.document.findFirst.mockResolvedValue(
        testData.document
      );

      // Mock S3 service response
      const mockDownloadResponse = {
        downloadUrl: 'https://s3.amazonaws.com/presigned-download-url',
        expiresIn: 3600,
      };
      mockS3Service.generatePresignedDownloadUrl.mockResolvedValue(
        mockDownloadResponse
      );

      // Act
      const result = await documentService.generateDownloadUrl(
        documentId,
        tenantId,
        authContext
      );

      // Assert
      expect(result).toEqual({
        downloadUrl: mockDownloadResponse.downloadUrl,
        expiresIn: mockDownloadResponse.expiresIn,
      });

      expect(mockPrismaService.prisma.document.findFirst).toHaveBeenCalledWith({
        where: {
          id: documentId,
          tenantId,
          deletedAt: null,
        },
      });

      expect(mockS3Service.generatePresignedDownloadUrl).toHaveBeenCalledWith({
        s3Key: testData.document.s3Key,
        fileName: testData.document.originalName,
      });
    });

    it('should throw NotFoundError when document does not exist', async () => {
      // Mock successful tenant validation
      mockPrismaService.prisma.tenant.findUnique.mockResolvedValue(
        testData.tenant
      );

      // Mock user-tenant relationship validation
      mockPrismaService.prisma.user.findFirst.mockResolvedValue({
        id: testData.user.id,
        clerkId: testData.user.clerkId,
        tenantId: testData.user.tenantId,
      });

      // Mock document not found
      mockPrismaService.prisma.document.findFirst.mockResolvedValue(null);

      // Act & Assert
      await expect(
        documentService.generateDownloadUrl(documentId, tenantId, authContext)
      ).rejects.toThrow(NotFoundError);
      await expect(
        documentService.generateDownloadUrl(documentId, tenantId, authContext)
      ).rejects.toThrow('Document not found');

      expect(mockS3Service.generatePresignedDownloadUrl).not.toHaveBeenCalled();
    });

    it('should throw NotFoundError when document belongs to different tenant', async () => {
      // Mock successful tenant validation
      mockPrismaService.prisma.tenant.findUnique.mockResolvedValue(
        testData.tenant
      );

      // Mock user-tenant relationship validation
      mockPrismaService.prisma.user.findFirst.mockResolvedValue({
        id: testData.user.id,
        clerkId: testData.user.clerkId,
        tenantId: testData.user.tenantId,
      });

      // Mock document not found for this tenant (due to tenant isolation)
      mockPrismaService.prisma.document.findFirst.mockResolvedValue(null);

      // Act & Assert
      await expect(
        documentService.generateDownloadUrl(documentId, tenantId, authContext)
      ).rejects.toThrow(NotFoundError);

      expect(mockPrismaService.prisma.document.findFirst).toHaveBeenCalledWith({
        where: {
          id: documentId,
          tenantId,
          deletedAt: null,
        },
      });
    });

    it('should throw NotFoundError when document is soft deleted', async () => {
      // Mock successful tenant validation
      mockPrismaService.prisma.tenant.findUnique.mockResolvedValue(
        testData.tenant
      );

      // Mock user-tenant relationship validation
      mockPrismaService.prisma.user.findFirst.mockResolvedValue({
        id: testData.user.id,
        clerkId: testData.user.clerkId,
        tenantId: testData.user.tenantId,
      });

      // Mock document not found due to deletedAt filter
      mockPrismaService.prisma.document.findFirst.mockResolvedValue(null);

      // Act & Assert
      await expect(
        documentService.generateDownloadUrl(documentId, tenantId, authContext)
      ).rejects.toThrow(NotFoundError);

      expect(mockPrismaService.prisma.document.findFirst).toHaveBeenCalledWith({
        where: {
          id: documentId,
          tenantId,
          deletedAt: null,
        },
      });
    });

    it('should handle S3 service errors gracefully', async () => {
      // Mock successful tenant validation
      mockPrismaService.prisma.tenant.findUnique.mockResolvedValue(
        testData.tenant
      );

      // Mock user-tenant relationship validation
      mockPrismaService.prisma.user.findFirst.mockResolvedValue({
        id: testData.user.id,
        clerkId: testData.user.clerkId,
        tenantId: testData.user.tenantId,
      });

      // Mock document found
      mockPrismaService.prisma.document.findFirst.mockResolvedValue(
        testData.document
      );

      // Mock S3 service error
      const s3Error = new Error('S3 service unavailable');
      mockS3Service.generatePresignedDownloadUrl.mockRejectedValue(s3Error);

      // Act & Assert
      await expect(
        documentService.generateDownloadUrl(documentId, tenantId, authContext)
      ).rejects.toThrow('S3 service unavailable');
    });

    it('should enforce tenant isolation in database query', async () => {
      // Mock successful tenant validation
      mockPrismaService.prisma.tenant.findUnique.mockResolvedValue(
        testData.tenant
      );

      // Mock user-tenant relationship validation
      mockPrismaService.prisma.user.findFirst.mockResolvedValue({
        id: testData.user.id,
        clerkId: testData.user.clerkId,
        tenantId: testData.user.tenantId,
      });

      // Mock document found
      mockPrismaService.prisma.document.findFirst.mockResolvedValue(
        testData.document
      );

      // Mock S3 service response
      mockS3Service.generatePresignedDownloadUrl.mockResolvedValue({
        downloadUrl: 'https://s3.amazonaws.com/test-url',
        expiresIn: 3600,
      });

      // Act
      await documentService.generateDownloadUrl(
        documentId,
        tenantId,
        authContext
      );

      // Assert that tenant isolation is enforced
      expect(mockPrismaService.prisma.document.findFirst).toHaveBeenCalledWith({
        where: {
          id: documentId,
          tenantId: tenantId, // Ensures tenant isolation
          deletedAt: null, // Ensures soft-deleted documents are excluded
        },
      });
    });
  });
});
