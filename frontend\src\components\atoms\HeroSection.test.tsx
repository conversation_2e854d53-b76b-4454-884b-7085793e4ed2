import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import { HeroSection } from "./HeroSection";
import { ArrowRight } from "lucide-react";

describe("HeroSection", () => {
  const defaultProps = {
    title: "Test Hero Title",
    description:
      "Test hero description text that explains the value proposition.",
  };

  describe("Rendering", () => {
    it("should render hero section with title and description", () => {
      render(<HeroSection {...defaultProps} />);

      expect(screen.getByRole("heading", { level: 1 })).toHaveTextContent(
        "Test Hero Title",
      );
      expect(
        screen.getByText(
          "Test hero description text that explains the value proposition.",
        ),
      ).toBeInTheDocument();
    });

    it("should render with custom className", () => {
      render(<HeroSection {...defaultProps} className="custom-hero" />);

      const heroSection = screen.getByTestId("hero-section-container");
      expect(heroSection).toHaveClass("custom-hero");
    });

    it("should apply default gradient background", () => {
      render(<HeroSection {...defaultProps} />);

      const heroSection = screen.getByTestId("hero-section-container");
      expect(heroSection).toHaveClass(
        "bg-gradient-to-b",
        "from-primary-50",
        "via-white",
        "to-gray-50",
      );
    });
  });

  describe("Variants", () => {
    it("should render gradient variant with gradient background", () => {
      render(<HeroSection {...defaultProps} variant="gradient" />);

      const heroSection = screen.getByTestId("hero-section-container");
      expect(heroSection).toHaveClass(
        "bg-gradient-to-b",
        "from-primary-50",
        "via-white",
        "to-gray-50",
      );
    });

    it("should render solid variant with white background", () => {
      render(<HeroSection {...defaultProps} variant="solid" />);

      const heroSection = screen.getByTestId("hero-section-container");
      expect(heroSection).toHaveClass("bg-white");
      expect(heroSection).not.toHaveClass("bg-gradient-to-b");
    });

    it("should render image variant with background image", () => {
      render(
        <HeroSection
          {...defaultProps}
          variant="image"
          backgroundImage="/test-image.jpg"
        />,
      );

      const heroSection = screen.getByTestId("hero-section-container");
      expect(heroSection).toHaveClass(
        "bg-cover",
        "bg-center",
        "bg-no-repeat",
        "relative",
      );
      expect(heroSection).toHaveStyle({
        backgroundImage: "url(/test-image.jpg)",
      });
    });

    it("should render overlay for image variant", () => {
      render(<HeroSection {...defaultProps} variant="image" />);

      const overlay = document.querySelector(".bg-gray-900\\/40");
      expect(overlay).toBeInTheDocument();
      expect(overlay).toHaveClass("absolute", "inset-0");
    });

    it("should use white text for image variant", () => {
      render(<HeroSection {...defaultProps} variant="image" />);

      const title = screen.getByRole("heading", { level: 1 });
      const description = screen.getByText(defaultProps.description);

      expect(title).toHaveClass("text-white");
      expect(description).toHaveClass("text-gray-100");
    });
  });

  describe("Sizes", () => {
    it("should render small size with appropriate classes", () => {
      render(<HeroSection {...defaultProps} size="sm" />);

      const heroSection = screen.getByTestId("hero-section-container");
      const title = screen.getByRole("heading", { level: 1 });

      expect(heroSection).toHaveClass("py-12", "sm:py-16");
      expect(title).toHaveClass("text-3xl", "sm:text-4xl", "lg:text-5xl");
    });

    it("should render medium size with appropriate classes", () => {
      render(<HeroSection {...defaultProps} size="md" />);

      const heroSection = screen.getByTestId("hero-section-container");
      const title = screen.getByRole("heading", { level: 1 });

      expect(heroSection).toHaveClass("py-16", "sm:py-20");
      expect(title).toHaveClass("text-4xl", "sm:text-5xl", "lg:text-6xl");
    });

    it("should render large size with appropriate classes (default)", () => {
      render(<HeroSection {...defaultProps} size="lg" />);

      const heroSection = screen.getByTestId("hero-section-container");
      const title = screen.getByRole("heading", { level: 1 });

      expect(heroSection).toHaveClass("py-20", "sm:py-32");
      expect(title).toHaveClass("text-4xl", "sm:text-6xl", "lg:text-7xl");
    });

    it("should render extra large size with appropriate classes", () => {
      render(<HeroSection {...defaultProps} size="xl" />);

      const heroSection = screen.getByTestId("hero-section-container");
      const title = screen.getByRole("heading", { level: 1 });

      expect(heroSection).toHaveClass("py-24", "sm:py-40");
      expect(title).toHaveClass("text-5xl", "sm:text-7xl", "lg:text-8xl");
    });
  });

  describe("Alignment", () => {
    it("should render center alignment by default", () => {
      render(<HeroSection {...defaultProps} />);

      const contentWrapper = screen.getByRole("heading", {
        level: 1,
      }).parentElement;
      expect(contentWrapper).toHaveClass("text-center", "max-w-4xl", "mx-auto");
    });

    it("should render left alignment", () => {
      render(<HeroSection {...defaultProps} align="left" />);

      const contentWrapper = screen.getByRole("heading", {
        level: 1,
      }).parentElement;
      expect(contentWrapper).toHaveClass("text-left");
      expect(contentWrapper).not.toHaveClass("max-w-4xl", "mx-auto");
    });

    it("should render right alignment", () => {
      render(<HeroSection {...defaultProps} align="right" />);

      const contentWrapper = screen.getByRole("heading", {
        level: 1,
      }).parentElement;
      expect(contentWrapper).toHaveClass("text-right");
      expect(contentWrapper).not.toHaveClass("max-w-4xl", "mx-auto");
    });
  });

  describe("Call-to-Action Buttons", () => {
    it("should render primary CTA button", () => {
      const primaryCta = {
        text: "Get Started",
        onClick: vi.fn(),
      };

      render(<HeroSection {...defaultProps} primaryCta={primaryCta} />);

      const button = screen.getByRole("button", { name: "Get Started" });
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass(
        "bg-gradient-to-r",
        "from-primary-600",
        "to-primary-700",
      );
    });

    it("should render secondary CTA button", () => {
      const secondaryCta = {
        text: "Learn More",
        variant: "outline" as const,
      };

      render(<HeroSection {...defaultProps} secondaryCta={secondaryCta} />);

      const button = screen.getByRole("button", { name: "Learn More" });
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass("border-2", "border-gray-300", "bg-white");
    });

    it("should render both primary and secondary CTAs", () => {
      const primaryCta = { text: "Get Started" };
      const secondaryCta = { text: "Learn More" };

      render(
        <HeroSection
          {...defaultProps}
          primaryCta={primaryCta}
          secondaryCta={secondaryCta}
        />,
      );

      expect(
        screen.getByRole("button", { name: "Get Started" }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: "Learn More" }),
      ).toBeInTheDocument();
    });

    it("should handle primary CTA click", () => {
      const handleClick = vi.fn();
      const primaryCta = {
        text: "Get Started",
        onClick: handleClick,
      };

      render(<HeroSection {...defaultProps} primaryCta={primaryCta} />);

      const button = screen.getByRole("button", { name: "Get Started" });
      fireEvent.click(button);

      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it("should render primary CTA with icon", () => {
      const primaryCta = {
        text: "Get Started",
        icon: <ArrowRight data-testid="arrow-icon" />,
      };

      render(<HeroSection {...defaultProps} primaryCta={primaryCta} />);

      expect(screen.getByTestId("arrow-icon")).toBeInTheDocument();
      expect(screen.getByTestId("arrow-icon").parentElement).toHaveClass(
        "group-hover:translate-x-1",
      );
    });

    it("should render CTA as link when href provided", () => {
      const primaryCta = {
        text: "Get Started",
        href: "/signup",
      };

      render(<HeroSection {...defaultProps} primaryCta={primaryCta} />);

      const link = screen.getByRole("link", { name: "Get Started" });
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute("href", "/signup");
    });

    it("should position CTAs based on alignment", () => {
      const primaryCta = { text: "Get Started" };

      render(
        <HeroSection {...defaultProps} primaryCta={primaryCta} align="right" />,
      );

      const ctaContainer = screen.getByRole("button", {
        name: "Get Started",
      }).parentElement;
      expect(ctaContainer).toHaveClass("justify-end");
    });

    it("should use larger buttons for xl size", () => {
      const primaryCta = { text: "Get Started" };

      render(
        <HeroSection {...defaultProps} primaryCta={primaryCta} size="xl" />,
      );

      const button = screen.getByRole("button", { name: "Get Started" });
      expect(button).toHaveClass("px-6", "py-3", "text-base"); // xl button classes
    });
  });

  describe("Accessibility", () => {
    it("should have proper heading hierarchy", () => {
      render(<HeroSection {...defaultProps} />);

      const heading = screen.getByRole("heading", { level: 1 });
      expect(heading).toBeInTheDocument();
      expect(heading).toHaveTextContent("Test Hero Title");
    });

    it("should have descriptive text for screen readers", () => {
      render(<HeroSection {...defaultProps} />);

      const description = screen.getByText(defaultProps.description);
      expect(description).toBeInTheDocument();
    });

    it("should support keyboard navigation for CTAs", () => {
      const primaryCta = { text: "Get Started", onClick: vi.fn() };

      render(<HeroSection {...defaultProps} primaryCta={primaryCta} />);

      const button = screen.getByRole("button", { name: "Get Started" });
      expect(button).toBeInTheDocument();

      button.focus();
      expect(document.activeElement).toBe(button);
    });
  });
});
