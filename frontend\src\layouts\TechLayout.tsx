import React from "react";
import { Outlet } from "react-router-dom";
import { UserButton } from "@clerk/clerk-react";
import { useAuth } from "../hooks/useAuth";
import { TechNavigation } from "../components/navigation/TechNavigation";
import { Building2 } from "lucide-react";

export const TechLayout: React.FC = () => {
  const { userProfile } = useAuth();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex flex-col">
      {/* Enhanced Mobile-optimized header with Salient branding */}
      <header className="bg-white/95 backdrop-blur-sm shadow-lg border-b border-primary-100/50 sticky top-0 z-50">
        <div className="px-4 py-4">
          <div className="flex justify-between items-center">
            {/* Enhanced App title - simplified for mobile with gradient */}
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-br from-primary-50 to-primary-100 rounded-xl">
                <Building2 className="h-6 w-6 text-primary-600" />
              </div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">
                Tech Notes
              </h1>
            </div>

            {/* Enhanced User info and profile */}
            <div className="flex items-center space-x-3">
              {/* User name with enhanced styling - hidden on very small screens */}
              {userProfile?.firstName && (
                <div className="hidden sm:block">
                  <span className="text-sm font-semibold text-gray-800">
                    {userProfile.firstName}
                  </span>
                  <div className="text-xs text-primary-600 font-medium">
                    Technician
                  </div>
                </div>
              )}

              {/* Enhanced User button with larger touch target */}
              <div className="p-1">
                <UserButton
                  appearance={{
                    elements: {
                      avatarBox:
                        "w-10 h-10 ring-2 ring-primary-100 hover:ring-primary-200 transition-all duration-200", // Larger avatar for touch with ring
                      userButtonPopoverCard:
                        "mt-2 shadow-xl border-0 ring-1 ring-gray-200", // Better positioning and styling
                    },
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Enhanced Main content area - flex-1 to push navigation to bottom */}
      <main className="flex-1 overflow-auto">
        <div className="px-4 py-6">
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl shadow-sm border border-gray-200/60 min-h-[calc(100vh-12rem)]">
            <div className="p-6">
              <Outlet />
            </div>
          </div>
        </div>
      </main>

      {/* Enhanced Bottom navigation - fixed at bottom for thumb accessibility */}
      <TechNavigation />
    </div>
  );
};
