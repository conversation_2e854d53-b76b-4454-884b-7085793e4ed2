# Environment Setup Guide

## Overview

This project uses a **consolidated environment variable structure** where all configuration is managed from the root directory. Both frontend and backend applications read from the same environment files.

## File Structure

```
tech-notes/
├── .env                    # Placeholder values (tracked in git)
├── .env.example           # Template with all variables
├── .env.local             # Your actual credentials (ignored by git)
├── backend/               # No .env files here anymore
└── frontend/              # No .env files here anymore
```

## Setup Process

### 1. Initial Setup

```bash
# Copy the example file to create your local environment
cp .env.example .env.local

# Edit .env.local with your actual credentials
nano .env.local  # or use your preferred editor
```

### 2. Required Variables

Fill in these variables in your `.env.local` file:

#### Database Configuration

```bash
DATABASE_URL="postgresql://user:password@localhost:5432/tech_notes_dev"
DIRECT_URL="postgresql://user:password@localhost:5432/tech_notes_dev"
TEST_DATABASE_URL="postgresql://user:password@localhost:5433/tech_notes_test"
TEST_DATABASE_ADMIN_URL="your_test_database_admin_url_here"
```

#### Authentication (Clerk)

```bash
CLERK_SECRET_KEY="sk_test_your_actual_clerk_secret_key"
CLERK_PUBLISHABLE_KEY="pk_test_your_actual_clerk_publishable_key"
```

#### Frontend Configuration

```bash
VITE_CLERK_PUBLISHABLE_KEY="pk_test_your_actual_clerk_publishable_key"
```

#### Optional: Payments (Stripe)

```bash
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"
VITE_STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"
```

### 3. How It Works

- **Backend**: Loads environment variables from root directory using dotenv
- **Frontend**: Vite automatically loads environment variables from parent directory
- **Tests**: All test files load from root directory environment files
- **Scripts**: Database scripts load from root directory environment files

## Security Features

### ✅ What's Secure

- `.env.local` is ignored by git (contains real credentials)
- `.env` contains only placeholder values (safe to commit)
- All real credentials are kept out of version control

### ⚠️ Important Notes

- Never commit real credentials to `.env` file
- Always use `.env.local` for actual development credentials
- Production uses environment variables set directly in Render dashboard

## Development Workflow

### Starting Development

```bash
# Install dependencies
yarn install

# Start both frontend and backend
yarn dev
```

Both applications will automatically load environment variables from the root directory.

### Adding New Variables

1. Add placeholder to `.env` and `.env.example`
2. Add actual value to `.env.local`
3. Update environment validation in `backend/src/utils/env-validation.ts` if needed

## Troubleshooting

### Common Issues

1. **"Missing environment variable" errors**
   - Check that `.env.local` exists and contains the required variables
   - Verify variable names match exactly (case-sensitive)

2. **Database connection issues**
   - Ensure Docker containers are running: `docker-compose up -d`
   - Check database URLs in `.env.local`

3. **Clerk authentication issues**
   - Verify Clerk keys are correct in `.env.local`
   - Check that frontend and backend use the same publishable key

### Validation

The backend includes environment validation that runs on startup. If you see validation errors, check that all required variables are present in `.env.local`.

## Migration from Old Structure

If you're migrating from the old structure with separate `.env` files in subdirectories:

1. Copy values from `backend/.env` and `frontend/.env` to root `.env.local`
2. Remove old `.env` files from subdirectories (already done)
3. Restart development servers

The applications have been updated to automatically load from the root directory.

## Render Production Deployment

### Environment Variables in Render

For production deployment on Render, environment variables are configured through the Render dashboard:

#### Backend Service Environment Variables

Set these in the Render dashboard for the `tech-notes-backend` service:

```bash
# Authentication (Required)
CLERK_SECRET_KEY=sk_live_your_clerk_secret_key
CLERK_PUBLISHABLE_KEY=pk_live_your_clerk_publishable_key

# Payments (Optional)
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# CORS Configuration
CORS_ORIGIN=https://your-frontend-url.onrender.com

# Frontend URL (for Clerk invitation redirects)
FRONTEND_URL=https://your-frontend-url.onrender.com
```

**Note**: `DATABASE_URL` and `DIRECT_URL` are automatically set by Render from the connected database.

#### Frontend Service Environment Variables

Set these in the Render dashboard for the `tech-notes-frontend` service:

```bash
# API Configuration
VITE_API_URL=https://your-backend-url.onrender.com

# Authentication
VITE_CLERK_PUBLISHABLE_KEY=pk_live_your_clerk_publishable_key

# Payments (Optional)
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
```

### Render Deployment Process

1. **Connect Repository**: Link your GitHub repository to Render
2. **Deploy via Blueprint**: Render automatically detects `render.yaml` and creates all services
3. **Configure Environment Variables**: Set the required secrets in each service's dashboard
4. **Update Service URLs**: Update `VITE_API_URL` and `CORS_ORIGIN` with actual Render URLs
5. **Test Deployment**: Verify all services are healthy and communicating correctly

For detailed deployment instructions, see `docs/RENDER_DEPLOYMENT.md`.
