import React from "react";
import { clsx } from "clsx";
import { Card } from "../atoms/Card";

export interface StatCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  value: string | number;
  change?: {
    value: string;
    trend: "up" | "down" | "neutral";
    period?: string;
  };
  icon?: React.ReactNode;
}

export const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  change,
  icon,
  className,
  ...props
}) => {
  const getTrendColor = (trend: "up" | "down" | "neutral") => {
    switch (trend) {
      case "up":
        return "text-green-600";
      case "down":
        return "text-red-600";
      case "neutral":
      default:
        return "text-gray-600";
    }
  };

  return (
    <Card className={clsx("p-6", className)} {...props}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
        {icon && (
          <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
            {icon}
          </div>
        )}
      </div>
      {change && (
        <div className="mt-4 flex items-center text-sm">
          <span className={clsx("font-medium", getTrendColor(change.trend))}>
            {change.value}
          </span>
          {change.period && (
            <span className="text-gray-500 ml-1">{change.period}</span>
          )}
        </div>
      )}
    </Card>
  );
};
