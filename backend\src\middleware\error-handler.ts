import { Request, Response, NextFunction } from 'express';

import { AppError, ErrorResponse } from '../types/error.types.js';
import { env } from '../utils/env-validation.js';
import { Logger } from '../utils/logger.js';
import { getRequestUser } from '../utils/request-types.js';

export interface ErrorHandlerDependencies {
  logger: Logger;
}

/**
 * Factory function to create error handling middleware
 */
export function createErrorHandler(dependencies: ErrorHandlerDependencies) {
  const { logger } = dependencies;

  return (
    error: Error,
    req: Request,
    res: Response,
    next: NextFunction
  ): void => {
    // If response already sent, delegate to default Express error handler
    if (res.headersSent) {
      return next(error);
    }

    let statusCode = 500;
    let message = 'Internal Server Error';
    let errorType = 'InternalServerError';

    // Handle known application errors
    if (error instanceof AppError) {
      statusCode = error.statusCode;
      message = error.message;
      errorType = error.constructor.name;
    }

    // Log error with context
    const errorContext = {
      error: error.message,
      stack: error.stack,
      statusCode,
      method: req.method,
      path: req.path,
      query: req.query,
      body: req.body,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: getRequestUser(req)?.id,
      tenantId: getRequestUser(req)?.tenantId,
    };

    if (statusCode >= 500) {
      logger.error('Server error occurred', errorContext);
    } else {
      logger.warn('Client error occurred', errorContext);
    }

    // Prepare error response
    const errorResponse: ErrorResponse = {
      error: errorType,
      message: message,
      statusCode: statusCode,
      timestamp: new Date().toISOString(),
    };

    // In development, include stack trace
    if (env.NODE_ENV === 'development') {
      (errorResponse as ErrorResponse & { stack?: string }).stack = error.stack;
    }

    res.status(statusCode).json(errorResponse);
  };
}

/**
 * Middleware to handle 404 errors for unmatched routes
 */
export function createNotFoundHandler(dependencies: ErrorHandlerDependencies) {
  const { logger } = dependencies;

  return (req: Request, res: Response): void => {
    logger.warn('Route not found', {
      method: req.method,
      path: req.path,
      query: req.query,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
    });

    const errorResponse: ErrorResponse = {
      error: 'NotFound',
      message: `Route ${req.method} ${req.path} not found`,
      statusCode: 404,
      timestamp: new Date().toISOString(),
    };

    res.status(404).json(errorResponse);
  };
}
