import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useTypedApi } from "../../services/api-client";
import { SystemAdminOnly } from "../../components/guards/PermissionGuard";
import { <PERSON>, CardHeader, CardContent } from "../../components/atoms/Card";
import { Button } from "../../components/atoms/Button";
import { Badge } from "../../components/atoms/Badge";
import { Modal } from "../../components/atoms/Modal";
import { Alert } from "../../components/atoms/Alert";
import { LoadingSpinner } from "../../components/atoms/LoadingSpinner";
import { Shield, Plus, Edit, Trash2 } from "lucide-react";
import { toast } from "react-hot-toast";
import type {
  Role,
  CreateRoleData,
  UpdateRoleData,
} from "../../services/api-client";

interface RoleFormData {
  name: string;
  description: string;
  type: "SYSTEM_ADMIN" | "COMPANY_ADMIN" | "COMPANY_TECH";
  isSystemRole: boolean;
}

interface RoleFormProps {
  role?: Role;
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: RoleFormData) => void;
  isLoading?: boolean;
}

const RoleForm: React.FC<RoleFormProps> = ({
  role,
  isOpen,
  onClose,
  onSubmit,
  isLoading = false,
}) => {
  const [formData, setFormData] = useState<RoleFormData>({
    name: role?.name || "",
    description: role?.description || "",
    type: (role?.type as RoleFormData["type"]) || "COMPANY_TECH",
    isSystemRole: role?.isSystemRole || false,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleTypeChange = (type: string) => {
    const roleType = type as RoleFormData["type"];
    setFormData((prev) => ({
      ...prev,
      type: roleType,
      isSystemRole: roleType === "SYSTEM_ADMIN",
    }));
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={role ? "Edit Role" : "Create Role"}
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Role Name
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, name: e.target.value }))
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="Enter role name"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            value={formData.description}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, description: e.target.value }))
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="Enter role description"
            rows={3}
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Role Type
          </label>
          <select
            value={formData.type}
            onChange={(e) => handleTypeChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            required
          >
            <option value="COMPANY_TECH">Company Tech</option>
            <option value="COMPANY_ADMIN">Company Admin</option>
            <option value="SYSTEM_ADMIN">System Admin</option>
          </select>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="isSystemRole"
            checked={formData.isSystemRole}
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                isSystemRole: e.target.checked,
              }))
            }
            disabled={formData.type === "SYSTEM_ADMIN"}
            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <label
            htmlFor="isSystemRole"
            className="ml-2 block text-sm text-gray-900"
          >
            System Role (global access)
          </label>
        </div>

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Saving..." : role ? "Update Role" : "Create Role"}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

interface DeleteRoleModalProps {
  role: Role | null;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading?: boolean;
}

const DeleteRoleModal: React.FC<DeleteRoleModalProps> = ({
  role,
  isOpen,
  onClose,
  onConfirm,
  isLoading = false,
}) => {
  if (!role) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Delete Role">
      <div className="space-y-4">
        <Alert variant="warning">
          <p>
            Are you sure you want to delete the role{" "}
            <strong>{role.name}</strong>? This action cannot be undone.
          </p>
        </Alert>

        <div className="bg-gray-50 p-3 rounded-md">
          <h4 className="font-medium text-gray-900 mb-2">Role Details:</h4>
          <div className="space-y-1 text-sm text-gray-600">
            <p>
              <strong>Name:</strong> {role.name}
            </p>
            <p>
              <strong>Type:</strong> {role.type}
            </p>
            <p>
              <strong>System Role:</strong> {role.isSystemRole ? "Yes" : "No"}
            </p>
            <p>
              <strong>Description:</strong> {role.description}
            </p>
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="danger" onClick={onConfirm} disabled={isLoading}>
            {isLoading ? "Deleting..." : "Delete Role"}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export const RoleManagementPage: React.FC = () => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [deletingRole, setDeletingRole] = useState<Role | null>(null);

  // Fetch roles
  const {
    data: rolesResponse,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["roles"],
    queryFn: () => api.roles.getAll(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Create role mutation
  const createRoleMutation = useMutation({
    mutationFn: (data: CreateRoleData) => api.roles.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      setIsCreateModalOpen(false);
      toast.success("Role created successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create role");
    },
  });

  // Update role mutation
  const updateRoleMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateRoleData }) =>
      api.roles.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      setEditingRole(null);
      toast.success("Role updated successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update role");
    },
  });

  // Delete role mutation
  const deleteRoleMutation = useMutation({
    mutationFn: (id: string) => api.roles.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      setDeletingRole(null);
      toast.success("Role deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete role");
    },
  });

  // Event handlers
  const handleCreateRole = (data: RoleFormData) => {
    createRoleMutation.mutate(data);
  };

  const handleUpdateRole = (data: RoleFormData) => {
    if (editingRole) {
      updateRoleMutation.mutate({
        id: editingRole.id,
        data,
      });
    }
  };

  const handleDeleteRole = () => {
    if (deletingRole) {
      deleteRoleMutation.mutate(deletingRole.id);
    }
  };

  const getRoleTypeColor = (type: string): "admin" | "primary" | "tech" => {
    switch (type) {
      case "SYSTEM_ADMIN":
        return "admin";
      case "COMPANY_ADMIN":
        return "primary";
      case "COMPANY_TECH":
        return "tech";
      default:
        return "tech";
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-gray-900">Role Management</h1>
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <SystemAdminOnly>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Role Management
            </h1>
            <p className="text-gray-600 mt-1">
              Manage system roles and permissions
            </p>
          </div>
          <Button
            onClick={() => setIsCreateModalOpen(true)}
            className="flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Create Role</span>
          </Button>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="error">
            <p>
              Failed to load roles. Please refresh the page or check your
              permissions.
            </p>
          </Alert>
        )}

        {/* Roles List */}
        <Card>
          <CardHeader title="System Roles" />
          <CardContent>
            {rolesResponse?.data && rolesResponse.data.length > 0 ? (
              <div className="space-y-4">
                {rolesResponse.data.map((role) => (
                  <div
                    key={role.id}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                        <Shield className="h-5 w-5 text-primary-600" />
                      </div>
                      <div>
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="font-medium text-gray-900">
                            {role.name}
                          </h3>
                          <Badge variant={getRoleTypeColor(role.type)}>
                            {role.type.replace("_", " ")}
                          </Badge>
                          {role.isSystemRole && (
                            <Badge variant="admin">System</Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-500">
                          {role.description}
                        </p>
                        <div className="flex items-center space-x-4 mt-1 text-xs text-gray-400">
                          <span>
                            Created:{" "}
                            {new Date(role.createdAt).toLocaleDateString()}
                          </span>
                          <span>
                            Updated:{" "}
                            {new Date(role.updatedAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingRole(role)}
                        className="flex items-center space-x-1"
                      >
                        <Edit className="h-4 w-4" />
                        <span>Edit</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setDeletingRole(role)}
                        className="flex items-center space-x-1 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                        <span>Delete</span>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No roles found
                </h3>
                <p className="text-gray-500 mb-4">
                  Get started by creating your first role.
                </p>
                <Button onClick={() => setIsCreateModalOpen(true)}>
                  Create Role
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Create Role Modal */}
        <RoleForm
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          onSubmit={handleCreateRole}
          isLoading={createRoleMutation.isPending}
        />

        {/* Edit Role Modal */}
        <RoleForm
          role={editingRole || undefined}
          isOpen={!!editingRole}
          onClose={() => setEditingRole(null)}
          onSubmit={handleUpdateRole}
          isLoading={updateRoleMutation.isPending}
        />

        {/* Delete Role Modal */}
        <DeleteRoleModal
          role={deletingRole}
          isOpen={!!deletingRole}
          onClose={() => setDeletingRole(null)}
          onConfirm={handleDeleteRole}
          isLoading={deleteRoleMutation.isPending}
        />
      </div>
    </SystemAdminOnly>
  );
};
