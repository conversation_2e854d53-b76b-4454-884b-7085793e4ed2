import React from "react";
import { useTypedApi } from "../../services/api-client";
import type { VehicleBrandV3 } from "../../services/api-client";
import { SimpleEntityModal } from "./SimpleEntityModal";
import { useBaseCRUD } from "../../hooks/useBaseCRUD";
import { createBrandConfig } from "./configs/entity-configs";

interface BrandManagementV3ModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const BrandManagementV3Modal: React.FC<BrandManagementV3ModalProps> = ({
  isOpen,
  onClose,
}) => {
  const api = useTypedApi();

  // Create Brand configuration
  const brandConfig = createBrandConfig(api);

  // Use base CRUD hook with Brand configuration
  const crud = useBaseCRUD<VehicleBrandV3>(brandConfig, isOpen);

  return (
    <SimpleEntityModal
      isOpen={isOpen}
      onClose={onClose}
      title={brandConfig.ui.title}
      entityName={brandConfig.entityName}
      icon={brandConfig.ui.icon}
      colorScheme={brandConfig.ui.colorScheme}
      placeholder={brandConfig.ui.placeholder}
      emptyStateMessage={brandConfig.ui.emptyStateMessage}
      crud={crud}
    />
  );
};