import { VehicleModelYearV3, VehicleYearV3 } from '@prisma/client';
import { BackendAuthContext } from '@tech-notes/shared';

import { NotFoundError, ConflictError } from '../../types/error.types.js';
import { Logger } from '../../utils/logger.js';
import { PrismaService } from '../prisma.service.js';

import { BaseVehicleHierarchyV3Service } from './base-vehicle-hierarchy-v3.service.js';
import { VehicleModelV3Service } from './vehicle-model-v3.service.js';
import { VehicleYearV3Service } from './vehicle-year-v3.service.js';

export interface CreateModelYearV3Data {
  modelId: string;
  yearId: string;
}

export interface ModelYearV3WithRelations extends VehicleModelYearV3 {
  year: VehicleYearV3;
}

/**
 * Service for managing Vehicle Model-Year V3 associations
 * Handles many-to-many relationships between models and years
 */
export class VehicleModelYearV3Service extends BaseVehicleHierarchyV3Service {
  constructor(
    prismaService: PrismaService,
    logger: Logger,
    private modelService: VehicleModelV3Service,
    private yearService: VehicleYearV3Service
  ) {
    super(prismaService, logger);
  }

  /**
   * Get all years associated with a specific model
   */
  async getYearsByModel(
    modelId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<ModelYearV3WithRelations[]> {
    this.logOperation('getYearsByModel', { modelId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Validate model ownership first
        await this.modelService.validateModelOwnership(modelId, tenantId);

        return await this.prisma.vehicleModelYearV3.findMany({
          where: {
            modelId,
            tenantId,
          },
          include: {
            year: true,
          },
          orderBy: {
            year: {
              displayOrder: 'asc',
            },
          },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Get all models associated with a specific year
   */
  async getModelsByYear(
    yearId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModelYearV3[]> {
    this.logOperation('getModelsByYear', { yearId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Validate year ownership first
        await this.yearService.validateYearOwnership(yearId, tenantId);

        return await this.prisma.vehicleModelYearV3.findMany({
          where: {
            yearId,
            tenantId,
          },
          include: {
            model: {
              include: {
                subBrand: {
                  include: {
                    brand: true,
                  },
                },
              },
            },
          },
          orderBy: {
            model: {
              displayOrder: 'asc',
            },
          },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Create a new model-year association
   */
  async createModelYear(
    associationData: CreateModelYearV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModelYearV3> {
    this.logOperation('createModelYear', {
      modelId: associationData.modelId,
      yearId: associationData.yearId,
      tenantId,
    });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Validate both model and year ownership
          await this.modelService.validateModelOwnership(
            associationData.modelId,
            tenantId
          );
          await this.yearService.validateYearOwnership(
            associationData.yearId,
            tenantId
          );

          // Check if association already exists
          const existingAssociation =
            await this.prisma.vehicleModelYearV3.findFirst({
              where: {
                modelId: associationData.modelId,
                yearId: associationData.yearId,
                tenantId,
              },
            });

          if (existingAssociation) {
            throw new ConflictError('Model-Year association already exists');
          }

          return await this.prisma.vehicleModelYearV3.create({
            data: {
              modelId: associationData.modelId,
              yearId: associationData.yearId,
              tenantId,
            },
          });
        } catch (error) {
          this.logError('createModelYear', error as Error, {
            associationData,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Delete a model-year association
   */
  async deleteModelYear(
    modelId: string,
    yearId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('deleteModelYear', { modelId, yearId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          const association = await this.prisma.vehicleModelYearV3.findFirst({
            where: {
              modelId,
              yearId,
              tenantId,
            },
          });

          if (!association) {
            throw new NotFoundError('Model-Year association not found');
          }

          await this.prisma.vehicleModelYearV3.delete({
            where: {
              id: association.id,
            },
          });
        } catch (error) {
          this.logError('deleteModelYear', error as Error, {
            modelId,
            yearId,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Delete all year associations for a specific model
   */
  async deleteAllYearsForModel(
    modelId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('deleteAllYearsForModel', { modelId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Validate model ownership first
          await this.modelService.validateModelOwnership(modelId, tenantId);

          await this.prisma.vehicleModelYearV3.deleteMany({
            where: {
              modelId,
              tenantId,
            },
          });
        } catch (error) {
          this.logError('deleteAllYearsForModel', error as Error, {
            modelId,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Delete all model associations for a specific year
   */
  async deleteAllModelsForYear(
    yearId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('deleteAllModelsForYear', { yearId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Validate year ownership first
          await this.yearService.validateYearOwnership(yearId, tenantId);

          await this.prisma.vehicleModelYearV3.deleteMany({
            where: {
              yearId,
              tenantId,
            },
          });
        } catch (error) {
          this.logError('deleteAllModelsForYear', error as Error, {
            yearId,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Check if a model-year association exists
   */
  async associationExists(
    modelId: string,
    yearId: string,
    tenantId: string
  ): Promise<boolean> {
    const association = await this.prisma.vehicleModelYearV3.findFirst({
      where: {
        modelId,
        yearId,
        tenantId,
      },
      select: { id: true },
    });

    return !!association;
  }
}
