import { InvitationStatus, RoleType } from '@prisma/client';
import { Tenant, User } from '@prisma/client';
import { BackendAuthContext } from '@tech-notes/shared';

import {
  NotFoundError,
  ForbiddenError,
  ValidationError,
  ConflictError,
} from '../types/error.types.js';
import {
  CreateInvitationData,
  InvitationWithDetails,
  InvitationListItem,
  CreateTenantWithAdminData,
  InviteUserData,
  AcceptInvitationData,
} from '../types/invitation.types.js';
import { env } from '../utils/env-validation.js';
import { Logger } from '../utils/logger.js';

import { BaseTenantService } from './base-tenant.service.js';
import { ClerkInvitationService } from './clerk-invitation.service.js';
import { PrismaService } from './prisma.service.js';
import { RoleService } from './role.service.js';
import { TenantService } from './tenant.service.js';
import { UserService } from './user.service.js';

export class InvitationService extends BaseTenantService {
  constructor(
    prismaService: PrismaService,
    logger: Logger,
    private clerkInvitationService: ClerkInvitationService,
    private userService: UserService,
    private tenantService: TenantService,
    private roleService: RoleService
  ) {
    super(prismaService, logger);
  }

  /**
   * Create a new tenant with initial Company Admin (System Admin only)
   */
  async createTenantWithAdmin(
    data: CreateTenantWithAdminData,
    BackendAuthContext: BackendAuthContext
  ): Promise<{ tenant: Tenant; invitation: InvitationWithDetails }> {
    this.logOperation('createTenantWithAdmin', {
      tenantName: data.tenantName,
      adminEmail: data.adminEmail,
      createdBy: BackendAuthContext.id,
    });

    // Only System Admins can create new tenants
    if (!BackendAuthContext.canBypassTenantScope) {
      throw new ForbiddenError('Only System Admins can create new tenants');
    }

    // Validate input
    if (!data.tenantName?.trim()) {
      throw new ValidationError('Tenant name is required');
    }
    if (!data.tenantSlug?.trim()) {
      throw new ValidationError('Tenant slug is required');
    }
    if (!data.adminEmail?.trim()) {
      throw new ValidationError('Admin email is required');
    }

    try {
      return await this.transaction(async (tx) => {
        // Create the tenant
        const tenant = await this.tenantService.createTenant(
          {
            name: data.tenantName,
            slug: data.tenantSlug,
          },
          BackendAuthContext
        );

        // Get Company Admin role
        const companyAdminRole = await tx.role.findFirst({
          where: { type: RoleType.COMPANY_ADMIN },
        });

        if (!companyAdminRole) {
          throw new NotFoundError('Company Admin role not found');
        }

        // Create invitation
        const invitation = await this.createInvitation({
          email: data.adminEmail,
          tenantId: tenant.id,
          roleId: companyAdminRole.id,
          createdBy: BackendAuthContext.id,
        });

        return { tenant, invitation };
      });
    } catch (error) {
      this.logError(
        'createTenantWithAdmin',
        error as Error,
        data as unknown as Record<string, unknown>
      );
      throw error;
    }
  }

  /**
   * Invite a user to an existing tenant (Company Admin only)
   */
  async inviteUserToTenant(
    data: InviteUserData,
    BackendAuthContext: BackendAuthContext
  ): Promise<InvitationWithDetails> {
    this.logOperation('inviteUserToTenant', {
      email: data.email,
      roleType: data.roleType,
      tenantId: BackendAuthContext.tenantId,
      createdBy: BackendAuthContext.id,
    });

    // Only Company Admins can invite users to their tenant
    const hasPermission = BackendAuthContext.roles?.some(
      (userRole) =>
        userRole.role.type === RoleType.COMPANY_ADMIN &&
        userRole.tenantId === BackendAuthContext.tenantId
    );

    if (!hasPermission && !BackendAuthContext.canBypassTenantScope) {
      throw new ForbiddenError(
        'Only Company Admins can invite users to their tenant'
      );
    }

    // Validate input
    if (!data.email?.trim()) {
      throw new ValidationError('Email is required');
    }

    // Get the role for the invitation
    const role = await this.prisma.role.findFirst({
      where: { type: data.roleType },
    });

    if (!role) {
      throw new NotFoundError(`Role ${data.roleType} not found`);
    }

    // Create invitation
    return await this.createInvitation({
      email: data.email,
      tenantId: BackendAuthContext.tenantId,
      roleId: role.id,
      createdBy: BackendAuthContext.id,
    });
  }

  /**
   * Create an invitation record and Clerk invitation
   */
  async createInvitation(
    data: CreateInvitationData
  ): Promise<InvitationWithDetails> {
    this.logOperation('createInvitation', {
      email: data.email,
      tenantId: data.tenantId,
      roleId: data.roleId,
      createdBy: data.createdBy,
    });

    // Check if user already exists
    const existingUser = await this.userService.getUserByEmail(data.email);
    if (existingUser) {
      throw new ConflictError('User with this email already exists');
    }

    // Check for existing pending invitation
    const existingInvitation = await this.prisma.invitation.findFirst({
      where: {
        email: data.email,
        tenantId: data.tenantId,
        status: InvitationStatus.PENDING,
      },
    });

    if (existingInvitation) {
      throw new ConflictError(
        'Pending invitation already exists for this email and tenant'
      );
    }

    try {
      return await this.transaction(async (tx) => {
        // Create database invitation record first
        const expiresAt =
          data.expiresAt || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days

        const invitation = await tx.invitation.create({
          data: {
            clerkInvitationId: '', // Will be updated after Clerk invitation is created
            email: data.email,
            tenantId: data.tenantId,
            roleId: data.roleId,
            createdBy: data.createdBy,
            expiresAt,
            status: InvitationStatus.PENDING,
          },
          include: {
            tenant: {
              select: { id: true, name: true, slug: true },
            },
            role: {
              select: { id: true, name: true, type: true },
            },
            creator: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        });

        // Create Clerk invitation
        const redirectUrl = this.clerkInvitationService.generateRedirectUrl(
          env.FRONTEND_URL,
          invitation.id
        );

        const metadata = this.clerkInvitationService.generateInvitationMetadata(
          data.tenantId,
          data.roleId,
          invitation.id
        );

        // Create Clerk invitation
        const clerkInvitation =
          await this.clerkInvitationService.createInvitation(
            data.email,
            redirectUrl,
            metadata
          );

        // Update invitation with Clerk ID
        const updatedInvitation = await tx.invitation.update({
          where: { id: invitation.id },
          data: { clerkInvitationId: clerkInvitation.id },
          include: {
            tenant: {
              select: { id: true, name: true, slug: true },
            },
            role: {
              select: { id: true, name: true, type: true },
            },
            creator: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        });

        this.logger.info('Invitation created successfully', {
          invitationId: invitation.id,
          clerkInvitationId: clerkInvitation.id,
          email: data.email,
          tenantId: data.tenantId,
        });

        return updatedInvitation as InvitationWithDetails;
      });
    } catch (error) {
      this.logError(
        'createInvitation',
        error as Error,
        data as unknown as Record<string, unknown>
      );
      throw error;
    }
  }

  /**
   * Get invitations for a tenant (Company Admin only)
   */
  async getInvitationsByTenant(
    tenantId: string,
    BackendAuthContext: BackendAuthContext
  ): Promise<InvitationListItem[]> {
    this.logOperation('getInvitationsByTenant', {
      tenantId,
      requestedBy: BackendAuthContext.id,
    });

    // Ensure user can only access their own tenant's invitations
    if (
      tenantId !== BackendAuthContext.tenantId &&
      !BackendAuthContext.canBypassTenantScope
    ) {
      throw new ForbiddenError('Access denied to tenant invitations');
    }

    return await this.withTenantScope(tenantId, async () => {
      const invitations = await this.prisma.invitation.findMany({
        where: { tenantId },
        include: {
          role: {
            select: { name: true, type: true },
          },
          creator: {
            select: { email: true, firstName: true, lastName: true },
          },
          user: {
            select: { email: true, firstName: true, lastName: true },
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      return invitations.map((invitation) => ({
        id: invitation.id,
        email: invitation.email,
        status: invitation.status,
        expiresAt: invitation.expiresAt,
        createdAt: invitation.createdAt,
        role: invitation.role,
        creator: invitation.creator,
        user: invitation.user,
      }));
    });
  }

  /**
   * Revoke an invitation (Company Admin only)
   */
  async revokeInvitation(
    invitationId: string,
    BackendAuthContext: BackendAuthContext
  ): Promise<void> {
    this.logOperation('revokeInvitation', {
      invitationId,
      revokedBy: BackendAuthContext.id,
    });

    const invitation = await this.prisma.invitation.findUnique({
      where: { id: invitationId },
      include: { tenant: true },
    });

    if (!invitation) {
      throw new NotFoundError('Invitation not found');
    }

    // Ensure user can only revoke invitations from their tenant
    if (
      invitation.tenantId !== BackendAuthContext.tenantId &&
      !BackendAuthContext.canBypassTenantScope
    ) {
      throw new ForbiddenError('Access denied to revoke this invitation');
    }

    if (invitation.status !== InvitationStatus.PENDING) {
      throw new ValidationError('Only pending invitations can be revoked');
    }

    try {
      await this.transaction(async (tx) => {
        // Update database record
        await tx.invitation.update({
          where: { id: invitationId },
          data: { status: InvitationStatus.REVOKED },
        });

        // Revoke Clerk invitation
        await this.clerkInvitationService.revokeInvitation(
          invitation.clerkInvitationId
        );
      });

      this.logger.info('Invitation revoked successfully', {
        invitationId,
        email: invitation.email,
        revokedBy: BackendAuthContext.id,
      });
    } catch (error) {
      this.logError('revokeInvitation', error as Error, { invitationId });
      throw error;
    }
  }

  /**
   * Accept an invitation and create user account
   */
  async acceptInvitation(
    data: AcceptInvitationData,
    clerkUserId: string
  ): Promise<{ user: User; invitation: InvitationWithDetails }> {
    this.logOperation('acceptInvitation', {
      clerkUserId,
      hasToken: !!data.invitationToken,
    });

    // This method will be called after Clerk signup is complete
    // We need to find the invitation by the metadata in the Clerk user
    // For now, we'll implement a simpler version that finds by email

    try {
      return await this.transaction(async (tx) => {
        // Get user info from Clerk
        const clerkUser = await this.userService.getClerkUser(clerkUserId);

        // Find pending invitation by token (invitation ID)
        const invitation = await tx.invitation.findFirst({
          where: {
            id: data.invitationToken,
            status: InvitationStatus.PENDING,
          },
          include: {
            tenant: { select: { id: true, name: true, slug: true } },
            role: { select: { id: true, name: true, type: true } },
            creator: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        });

        if (!invitation) {
          throw new NotFoundError(
            'No pending invitation found with this token'
          );
        }

        // Verify the invitation email matches the Clerk user's email
        const userEmail = clerkUser.emailAddresses[0]?.emailAddress;
        if (invitation.email !== userEmail) {
          throw new ValidationError(
            'Invitation email does not match your account email'
          );
        }

        if (invitation.expiresAt < new Date()) {
          await tx.invitation.update({
            where: { id: invitation.id },
            data: { status: InvitationStatus.EXPIRED },
          });
          throw new ValidationError('Invitation has expired');
        }

        // Check if user already exists
        const existingUser = await tx.user.findUnique({
          where: { clerkId: clerkUserId },
        });

        if (existingUser) {
          throw new ConflictError(
            'User account already exists. Please sign in instead.'
          );
        }

        // Create user in our database
        const user = await this.userService.createUser({
          clerkId: clerkUserId,
          tenantId: invitation.tenantId,
          email: invitation.email,
          firstName: data.firstName || clerkUser.firstName || undefined,
          lastName: data.lastName || clerkUser.lastName || undefined,
          imageUrl: clerkUser.imageUrl || undefined,
          isActive: true,
        });

        // Assign role to user - get the invitation creator's full context for proper authorization
        const invitationCreator = await tx.user.findUnique({
          where: { id: invitation.createdBy },
          include: {
            userRoles: {
              include: {
                role: {
                  select: {
                    id: true,
                    name: true,
                    type: true,
                    isSystemRole: true,
                  },
                },
              },
            },
          },
        });

        if (!invitationCreator) {
          throw new NotFoundError('Invitation creator not found');
        }

        // Create proper auth context for the invitation creator
        const creatorAuthContext: BackendAuthContext = {
          id: invitationCreator.id,
          clerkId: invitationCreator.clerkId,
          tenantId: invitationCreator.tenantId,
          email: invitationCreator.email,
          firstName: invitationCreator.firstName,
          lastName: invitationCreator.lastName,
          imageUrl: invitationCreator.imageUrl,
          isActive: invitationCreator.isActive,
          createdAt: invitationCreator.createdAt,
          updatedAt: invitationCreator.updatedAt,
          roles: invitationCreator.userRoles.map((userRole) => ({
            id: userRole.id,
            role: userRole.role,
            tenantId: userRole.tenantId,
          })),
          canBypassTenantScope: invitationCreator.userRoles.some(
            (userRole) => userRole.role.type === RoleType.SYSTEM_ADMIN
          ),
        };

        await this.roleService.assignRole(
          {
            userId: user.id,
            roleId: invitation.roleId,
            tenantId: invitation.tenantId,
            assignedBy: invitation.createdBy,
          },
          creatorAuthContext
        );

        // Mark invitation as accepted
        const updatedInvitation = await tx.invitation.update({
          where: { id: invitation.id },
          data: {
            status: InvitationStatus.ACCEPTED,
            usedBy: user.id,
            usedAt: new Date(),
          },
          include: {
            tenant: { select: { id: true, name: true, slug: true } },
            role: { select: { id: true, name: true, type: true } },
            creator: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        });

        this.logger.info('Invitation accepted successfully', {
          invitationId: invitation.id,
          userId: user.id,
          tenantId: invitation.tenantId,
          roleType: invitation.role.type,
        });

        return { user, invitation: updatedInvitation as InvitationWithDetails };
      });
    } catch (error) {
      this.logError('acceptInvitation', error as Error, { clerkUserId });
      throw error;
    }
  }
}
