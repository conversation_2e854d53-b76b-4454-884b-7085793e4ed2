import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { BackendAuthContext } from '@tech-notes/shared';

import {
  NotFoundError,
  ConflictError,
  ValidationError,
} from '../types/error.types.js';
import { Logger } from '../utils/logger.js';

import { PrismaService } from './prisma.service.js';
import { VehicleHierarchyV2Service } from './vehicle-hierarchy-v2.service.js';

// Mock Prisma Client
const mockPrisma = {
  vehicleBrandV2: {
    create: jest.fn(),
    findMany: jest.fn(),
    findFirst: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    deleteMany: jest.fn(),
    count: jest.fn(),
  },
  vehicleSubBrandV2: {
    create: jest.fn(),
    findMany: jest.fn(),
    findFirst: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    deleteMany: jest.fn(),
    count: jest.fn(),
  },
  vehicleModelV2: {
    create: jest.fn(),
    findMany: jest.fn(),
    findFirst: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    deleteMany: jest.fn(),
    count: jest.fn(),
  },
  vehicleModelYearV2: {
    create: jest.fn(),
    findMany: jest.fn(),
    findFirst: jest.fn(),
    deleteMany: jest.fn(),
    createMany: jest.fn(),
    count: jest.fn(),
  },
  vehicleYear: {
    findMany: jest.fn(),
    findFirst: jest.fn(),
  },
  tenant: {
    findUnique: jest.fn(),
  },
  user: {
    findFirst: jest.fn(),
  },
  $transaction: jest.fn(),
};

// Mock services
const mockPrismaService = {
  prisma: mockPrisma,
} as unknown as PrismaService;

const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  verbose: jest.fn(),
  silly: jest.fn(),
  log: jest.fn(),
  query: jest.fn(),
} as unknown as Logger;

describe('VehicleHierarchyV2Service', () => {
  let service: VehicleHierarchyV2Service;
  const testTenantId = 'test-tenant-id';
  const testAuthContext: BackendAuthContext = {
    id: 'test-user-id',
    clerkId: 'test-clerk-id',
    tenantId: testTenantId,
    email: '<EMAIL>',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    canBypassTenantScope: false,
  };

  beforeEach(() => {
    service = new VehicleHierarchyV2Service(mockPrismaService, mockLogger);
    jest.clearAllMocks();

    // Default tenant validation mock
    mockPrisma.tenant.findUnique.mockResolvedValue({ id: testTenantId });
    // Default user validation mock
    mockPrisma.user.findFirst.mockResolvedValue({
      id: 'test-user-id',
      clerkId: 'test-clerk-id',
      tenantId: testTenantId,
    });
  });

  describe('Brand Operations', () => {
    describe('createBrand', () => {
      it('should create a new brand successfully', async () => {
        const brandData = { name: 'Ford', isActive: true };
        const expectedBrand = {
          id: 'brand-id',
          name: 'Ford',
          tenantId: testTenantId,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        mockPrisma.vehicleBrandV2.create.mockResolvedValue(expectedBrand);

        const result = await service.createBrand(
          brandData,
          testTenantId,
          testAuthContext
        );

        expect(mockPrisma.vehicleBrandV2.create).toHaveBeenCalledWith({
          data: {
            name: 'Ford',
            tenantId: testTenantId,
            isActive: true,
          },
        });
        expect(result).toEqual(expectedBrand);
      });

      it('should throw ValidationError for empty brand name', async () => {
        const brandData = { name: '   ' };

        await expect(
          service.createBrand(brandData, testTenantId, testAuthContext)
        ).rejects.toThrow(ValidationError);
        await expect(
          service.createBrand(brandData, testTenantId, testAuthContext)
        ).rejects.toThrow('Brand name cannot be empty');
      });

      it('should throw ConflictError for duplicate brand', async () => {
        const brandData = { name: 'Ford' };
        const prismaError = new PrismaClientKnownRequestError(
          'Unique constraint violation',
          {
            code: 'P2002',
            clientVersion: '5.0.0',
          }
        );

        mockPrisma.vehicleBrandV2.create.mockRejectedValue(prismaError);

        await expect(
          service.createBrand(brandData, testTenantId, testAuthContext)
        ).rejects.toThrow(ConflictError);
        await expect(
          service.createBrand(brandData, testTenantId, testAuthContext)
        ).rejects.toThrow("Brand 'Ford' already exists for this tenant");
      });

      it('should default isActive to true when not provided', async () => {
        const brandData = { name: 'Toyota' };
        const expectedBrand = {
          id: 'brand-id',
          name: 'Toyota',
          tenantId: testTenantId,
          isActive: true,
        };

        mockPrisma.vehicleBrandV2.create.mockResolvedValue(expectedBrand);

        await service.createBrand(brandData, testTenantId, testAuthContext);

        expect(mockPrisma.vehicleBrandV2.create).toHaveBeenCalledWith({
          data: {
            name: 'Toyota',
            tenantId: testTenantId,
            isActive: true,
          },
        });
      });
    });

    describe('getBrandsByTenant', () => {
      it('should return brands for tenant', async () => {
        const expectedBrands = [
          {
            id: 'brand1',
            name: 'Ford',
            tenantId: testTenantId,
            isActive: true,
          },
          {
            id: 'brand2',
            name: 'Toyota',
            tenantId: testTenantId,
            isActive: true,
          },
        ];

        mockPrisma.vehicleBrandV2.findMany.mockResolvedValue(expectedBrands);

        const result = await service.getBrandsByTenant(
          testTenantId,
          testAuthContext
        );

        expect(mockPrisma.vehicleBrandV2.findMany).toHaveBeenCalledWith({
          where: { tenantId: testTenantId },
          orderBy: { name: 'asc' },
        });
        expect(result).toEqual(expectedBrands);
      });
    });

    describe('updateBrand', () => {
      it('should update brand successfully', async () => {
        const brandId = 'brand-id';
        const updateData = { name: 'Ford Updated', isActive: false };
        const expectedBrand = {
          id: brandId,
          name: 'Ford Updated',
          tenantId: testTenantId,
          isActive: false,
        };

        // Mock the findFirst call that checks if brand exists
        mockPrisma.vehicleBrandV2.findFirst.mockResolvedValue({
          id: brandId,
          tenantId: testTenantId,
        });
        mockPrisma.vehicleBrandV2.update.mockResolvedValue(expectedBrand);

        const result = await service.updateBrand(
          brandId,
          updateData,
          testTenantId,
          testAuthContext
        );

        expect(mockPrisma.vehicleBrandV2.findFirst).toHaveBeenCalledWith({
          where: { id: brandId, tenantId: testTenantId },
        });
        expect(mockPrisma.vehicleBrandV2.update).toHaveBeenCalledWith({
          where: { id: brandId },
          data: updateData,
        });
        expect(result).toEqual(expectedBrand);
      });

      it('should throw ValidationError for empty name', async () => {
        const brandId = 'brand-id';
        const updateData = { name: '   ' };

        await expect(
          service.updateBrand(
            brandId,
            updateData,
            testTenantId,
            testAuthContext
          )
        ).rejects.toThrow(ValidationError);
        await expect(
          service.updateBrand(
            brandId,
            updateData,
            testTenantId,
            testAuthContext
          )
        ).rejects.toThrow('Brand name cannot be empty');
      });

      it('should throw NotFoundError for non-existent brand', async () => {
        const brandId = 'non-existent-brand';
        const updateData = { name: 'Updated Name' };

        mockPrisma.vehicleBrandV2.findFirst.mockResolvedValue(null);

        await expect(
          service.updateBrand(
            brandId,
            updateData,
            testTenantId,
            testAuthContext
          )
        ).rejects.toThrow(NotFoundError);
        await expect(
          service.updateBrand(
            brandId,
            updateData,
            testTenantId,
            testAuthContext
          )
        ).rejects.toThrow('Brand not found or does not belong to tenant');
      });
    });

    describe('deleteBrand', () => {
      it('should delete brand successfully when no sub-brands exist', async () => {
        const brandId = 'brand-id';

        mockPrisma.vehicleSubBrandV2.count.mockResolvedValue(0);
        mockPrisma.vehicleBrandV2.deleteMany.mockResolvedValue({ count: 1 });

        await service.deleteBrand(brandId, testTenantId, testAuthContext);

        expect(mockPrisma.vehicleSubBrandV2.count).toHaveBeenCalledWith({
          where: { brandId, tenantId: testTenantId },
        });
        expect(mockPrisma.vehicleBrandV2.deleteMany).toHaveBeenCalledWith({
          where: { id: brandId, tenantId: testTenantId },
        });
      });

      it('should throw ConflictError when sub-brands exist', async () => {
        const brandId = 'brand-id';

        mockPrisma.vehicleSubBrandV2.count.mockResolvedValue(2);

        await expect(
          service.deleteBrand(brandId, testTenantId, testAuthContext)
        ).rejects.toThrow(ConflictError);
        await expect(
          service.deleteBrand(brandId, testTenantId, testAuthContext)
        ).rejects.toThrow(
          'Cannot delete brand that has sub-brands. Delete sub-brands first.'
        );
      });

      it('should throw NotFoundError when brand does not exist', async () => {
        const brandId = 'non-existent-brand';

        mockPrisma.vehicleSubBrandV2.count.mockResolvedValue(0);
        mockPrisma.vehicleBrandV2.deleteMany.mockResolvedValue({ count: 0 });

        await expect(
          service.deleteBrand(brandId, testTenantId, testAuthContext)
        ).rejects.toThrow(NotFoundError);
        await expect(
          service.deleteBrand(brandId, testTenantId, testAuthContext)
        ).rejects.toThrow('Brand not found or does not belong to tenant');
      });
    });
  });

  describe('Sub-Brand Operations', () => {
    describe('createSubBrand', () => {
      it('should create a new sub-brand successfully', async () => {
        const subBrandData = {
          name: 'F-150',
          brandId: 'brand-id',
          isActive: true,
        };
        const expectedSubBrand = {
          id: 'sub-brand-id',
          name: 'F-150',
          brandId: 'brand-id',
          tenantId: testTenantId,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        // Mock brand validation
        mockPrisma.vehicleBrandV2.findFirst.mockResolvedValue({
          id: 'brand-id',
          tenantId: testTenantId,
        });
        mockPrisma.vehicleSubBrandV2.create.mockResolvedValue(expectedSubBrand);

        const result = await service.createSubBrand(
          subBrandData,
          testTenantId,
          testAuthContext
        );

        expect(mockPrisma.vehicleBrandV2.findFirst).toHaveBeenCalledWith({
          where: { id: 'brand-id', tenantId: testTenantId },
        });
        expect(mockPrisma.vehicleSubBrandV2.create).toHaveBeenCalledWith({
          data: {
            name: 'F-150',
            brandId: 'brand-id',
            tenantId: testTenantId,
            isActive: true,
          },
        });
        expect(result).toEqual(expectedSubBrand);
      });

      it('should throw ValidationError for empty sub-brand name', async () => {
        const subBrandData = { name: '   ', brandId: 'brand-id' };

        await expect(
          service.createSubBrand(subBrandData, testTenantId, testAuthContext)
        ).rejects.toThrow(ValidationError);
        await expect(
          service.createSubBrand(subBrandData, testTenantId, testAuthContext)
        ).rejects.toThrow('Sub-brand name cannot be empty');
      });

      it('should throw NotFoundError for invalid brand', async () => {
        const subBrandData = { name: 'F-150', brandId: 'invalid-brand-id' };

        mockPrisma.vehicleBrandV2.findFirst.mockResolvedValue(null);

        await expect(
          service.createSubBrand(subBrandData, testTenantId, testAuthContext)
        ).rejects.toThrow(NotFoundError);
        await expect(
          service.createSubBrand(subBrandData, testTenantId, testAuthContext)
        ).rejects.toThrow('Brand not found or does not belong to tenant');
      });

      it('should throw ConflictError for duplicate sub-brand within brand', async () => {
        const subBrandData = { name: 'F-150', brandId: 'brand-id' };
        const prismaError = new PrismaClientKnownRequestError(
          'Unique constraint violation',
          {
            code: 'P2002',
            clientVersion: '5.0.0',
          }
        );

        mockPrisma.vehicleBrandV2.findFirst.mockResolvedValue({
          id: 'brand-id',
          tenantId: testTenantId,
        });
        mockPrisma.vehicleSubBrandV2.create.mockRejectedValue(prismaError);

        await expect(
          service.createSubBrand(subBrandData, testTenantId, testAuthContext)
        ).rejects.toThrow(ConflictError);
        await expect(
          service.createSubBrand(subBrandData, testTenantId, testAuthContext)
        ).rejects.toThrow("Sub-brand 'F-150' already exists for this brand");
      });
    });

    describe('getSubBrandsByTenant', () => {
      it('should return sub-brands for tenant', async () => {
        const expectedSubBrands = [
          {
            id: 'sub-brand1',
            name: 'F-150',
            brandId: 'brand1',
            tenantId: testTenantId,
            brand: { id: 'brand1', name: 'Ford' },
          },
          {
            id: 'sub-brand2',
            name: 'Camry',
            brandId: 'brand2',
            tenantId: testTenantId,
            brand: { id: 'brand2', name: 'Toyota' },
          },
        ];

        mockPrisma.vehicleSubBrandV2.findMany.mockResolvedValue(
          expectedSubBrands
        );

        const result = await service.getSubBrandsByTenant(
          testTenantId,
          undefined,
          testAuthContext
        );

        expect(mockPrisma.vehicleSubBrandV2.findMany).toHaveBeenCalledWith({
          where: { tenantId: testTenantId },
          include: { brand: true },
          orderBy: [{ brand: { name: 'asc' } }, { name: 'asc' }],
        });
        expect(result).toEqual(expectedSubBrands);
      });

      it('should filter by brandId when provided', async () => {
        const brandId = 'brand-id';
        const expectedSubBrands = [
          {
            id: 'sub-brand1',
            name: 'F-150',
            brandId,
            tenantId: testTenantId,
            brand: { id: brandId, name: 'Ford' },
          },
        ];

        mockPrisma.vehicleSubBrandV2.findMany.mockResolvedValue(
          expectedSubBrands
        );

        const result = await service.getSubBrandsByTenant(
          testTenantId,
          brandId,
          testAuthContext
        );

        expect(mockPrisma.vehicleSubBrandV2.findMany).toHaveBeenCalledWith({
          where: { tenantId: testTenantId, brandId },
          include: { brand: true },
          orderBy: [{ brand: { name: 'asc' } }, { name: 'asc' }],
        });
        expect(result).toEqual(expectedSubBrands);
      });
    });

    describe('getSubBrandsByBrand', () => {
      it('should return sub-brands for specific brand', async () => {
        const brandId = 'brand-id';
        const expectedSubBrands = [
          { id: 'sub-brand1', name: 'F-150', brandId, tenantId: testTenantId },
          { id: 'sub-brand2', name: 'F-250', brandId, tenantId: testTenantId },
        ];

        mockPrisma.vehicleBrandV2.findFirst.mockResolvedValue({
          id: brandId,
          tenantId: testTenantId,
        });
        mockPrisma.vehicleSubBrandV2.findMany.mockResolvedValue(
          expectedSubBrands
        );

        const result = await service.getSubBrandsByBrand(
          brandId,
          testTenantId,
          testAuthContext
        );

        expect(mockPrisma.vehicleBrandV2.findFirst).toHaveBeenCalledWith({
          where: { id: brandId, tenantId: testTenantId },
        });
        expect(mockPrisma.vehicleSubBrandV2.findMany).toHaveBeenCalledWith({
          where: { brandId, tenantId: testTenantId },
          orderBy: { name: 'asc' },
        });
        expect(result).toEqual(expectedSubBrands);
      });

      it('should throw NotFoundError for invalid brand', async () => {
        const brandId = 'invalid-brand-id';

        mockPrisma.vehicleBrandV2.findFirst.mockResolvedValue(null);

        await expect(
          service.getSubBrandsByBrand(brandId, testTenantId, testAuthContext)
        ).rejects.toThrow(NotFoundError);
        await expect(
          service.getSubBrandsByBrand(brandId, testTenantId, testAuthContext)
        ).rejects.toThrow('Brand not found or does not belong to tenant');
      });
    });

    describe('deleteSubBrand', () => {
      it('should delete sub-brand successfully when no models exist', async () => {
        const subBrandId = 'sub-brand-id';

        mockPrisma.vehicleModelV2.count.mockResolvedValue(0);
        mockPrisma.vehicleSubBrandV2.deleteMany.mockResolvedValue({ count: 1 });

        await service.deleteSubBrand(subBrandId, testTenantId, testAuthContext);

        expect(mockPrisma.vehicleModelV2.count).toHaveBeenCalledWith({
          where: { subBrandId, tenantId: testTenantId },
        });
        expect(mockPrisma.vehicleSubBrandV2.deleteMany).toHaveBeenCalledWith({
          where: { id: subBrandId, tenantId: testTenantId },
        });
      });

      it('should throw ConflictError when models exist', async () => {
        const subBrandId = 'sub-brand-id';

        mockPrisma.vehicleModelV2.count.mockResolvedValue(3);

        await expect(
          service.deleteSubBrand(subBrandId, testTenantId, testAuthContext)
        ).rejects.toThrow(ConflictError);
        await expect(
          service.deleteSubBrand(subBrandId, testTenantId, testAuthContext)
        ).rejects.toThrow(
          'Cannot delete sub-brand that has models. Delete models first.'
        );
      });
    });
  });

  describe('Model Operations', () => {
    describe('createModel', () => {
      it('should create a new model successfully', async () => {
        const modelData = {
          name: 'Regular Cab',
          subBrandId: 'sub-brand-id',
          isActive: true,
        };
        const expectedModel = {
          id: 'model-id',
          name: 'Regular Cab',
          subBrandId: 'sub-brand-id',
          tenantId: testTenantId,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        // Mock sub-brand validation
        mockPrisma.vehicleSubBrandV2.findFirst.mockResolvedValue({
          id: 'sub-brand-id',
          tenantId: testTenantId,
        });
        mockPrisma.vehicleModelV2.create.mockResolvedValue(expectedModel);

        const result = await service.createModel(
          modelData,
          testTenantId,
          testAuthContext
        );

        expect(mockPrisma.vehicleSubBrandV2.findFirst).toHaveBeenCalledWith({
          where: { id: 'sub-brand-id', tenantId: testTenantId },
        });
        expect(mockPrisma.vehicleModelV2.create).toHaveBeenCalledWith({
          data: {
            name: 'Regular Cab',
            subBrandId: 'sub-brand-id',
            tenantId: testTenantId,
            isActive: true,
          },
        });
        expect(result).toEqual(expectedModel);
      });

      it('should throw ValidationError for empty model name', async () => {
        const modelData = { name: '   ', subBrandId: 'sub-brand-id' };

        await expect(
          service.createModel(modelData, testTenantId, testAuthContext)
        ).rejects.toThrow(ValidationError);
        await expect(
          service.createModel(modelData, testTenantId, testAuthContext)
        ).rejects.toThrow('Model name cannot be empty');
      });

      it('should throw NotFoundError for invalid sub-brand', async () => {
        const modelData = {
          name: 'Regular Cab',
          subBrandId: 'invalid-sub-brand-id',
        };

        mockPrisma.vehicleSubBrandV2.findFirst.mockResolvedValue(null);

        await expect(
          service.createModel(modelData, testTenantId, testAuthContext)
        ).rejects.toThrow(NotFoundError);
        await expect(
          service.createModel(modelData, testTenantId, testAuthContext)
        ).rejects.toThrow('Sub-brand not found or does not belong to tenant');
      });

      it('should throw ConflictError for duplicate model within sub-brand', async () => {
        const modelData = { name: 'Regular Cab', subBrandId: 'sub-brand-id' };
        const prismaError = new PrismaClientKnownRequestError(
          'Unique constraint violation',
          {
            code: 'P2002',
            clientVersion: '5.0.0',
          }
        );

        mockPrisma.vehicleSubBrandV2.findFirst.mockResolvedValue({
          id: 'sub-brand-id',
          tenantId: testTenantId,
        });
        mockPrisma.vehicleModelV2.create.mockRejectedValue(prismaError);

        await expect(
          service.createModel(modelData, testTenantId, testAuthContext)
        ).rejects.toThrow(ConflictError);
        await expect(
          service.createModel(modelData, testTenantId, testAuthContext)
        ).rejects.toThrow(
          "Model 'Regular Cab' already exists for this sub-brand"
        );
      });
    });

    describe('getModelsByTenant', () => {
      it('should return models for tenant with nested includes', async () => {
        const expectedModels = [
          {
            id: 'model1',
            name: 'Regular Cab',
            subBrandId: 'sub-brand1',
            tenantId: testTenantId,
            subBrand: {
              id: 'sub-brand1',
              name: 'F-150',
              brand: { id: 'brand1', name: 'Ford' },
            },
          },
          {
            id: 'model2',
            name: 'LE',
            subBrandId: 'sub-brand2',
            tenantId: testTenantId,
            subBrand: {
              id: 'sub-brand2',
              name: 'Camry',
              brand: { id: 'brand2', name: 'Toyota' },
            },
          },
        ];

        mockPrisma.vehicleModelV2.findMany.mockResolvedValue(expectedModels);

        const result = await service.getModelsByTenant(
          testTenantId,
          undefined,
          testAuthContext
        );

        expect(mockPrisma.vehicleModelV2.findMany).toHaveBeenCalledWith({
          where: { tenantId: testTenantId },
          include: {
            subBrand: {
              include: {
                brand: true,
              },
            },
          },
          orderBy: [
            { subBrand: { brand: { name: 'asc' } } },
            { subBrand: { name: 'asc' } },
            { name: 'asc' },
          ],
        });
        expect(result).toEqual(expectedModels);
      });

      it('should filter by subBrandId when provided', async () => {
        const subBrandId = 'sub-brand-id';
        const expectedModels = [
          {
            id: 'model1',
            name: 'Regular Cab',
            subBrandId,
            tenantId: testTenantId,
            subBrand: {
              id: subBrandId,
              name: 'F-150',
              brand: { id: 'brand1', name: 'Ford' },
            },
          },
        ];

        mockPrisma.vehicleModelV2.findMany.mockResolvedValue(expectedModels);

        const result = await service.getModelsByTenant(
          testTenantId,
          subBrandId,
          testAuthContext
        );

        expect(mockPrisma.vehicleModelV2.findMany).toHaveBeenCalledWith({
          where: { tenantId: testTenantId, subBrandId },
          include: {
            subBrand: {
              include: {
                brand: true,
              },
            },
          },
          orderBy: [
            { subBrand: { brand: { name: 'asc' } } },
            { subBrand: { name: 'asc' } },
            { name: 'asc' },
          ],
        });
        expect(result).toEqual(expectedModels);
      });
    });

    describe('deleteModel', () => {
      it('should delete model successfully when no year associations exist', async () => {
        const modelId = 'model-id';

        mockPrisma.vehicleModelYearV2.count.mockResolvedValue(0);
        mockPrisma.vehicleModelV2.deleteMany.mockResolvedValue({ count: 1 });

        await service.deleteModel(modelId, testTenantId, testAuthContext);

        expect(mockPrisma.vehicleModelYearV2.count).toHaveBeenCalledWith({
          where: { modelId, tenantId: testTenantId },
        });
        expect(mockPrisma.vehicleModelV2.deleteMany).toHaveBeenCalledWith({
          where: { id: modelId, tenantId: testTenantId },
        });
      });

      it('should throw ConflictError when year associations exist', async () => {
        const modelId = 'model-id';

        mockPrisma.vehicleModelYearV2.count.mockResolvedValue(2);

        await expect(
          service.deleteModel(modelId, testTenantId, testAuthContext)
        ).rejects.toThrow(ConflictError);
        await expect(
          service.deleteModel(modelId, testTenantId, testAuthContext)
        ).rejects.toThrow(
          'Cannot delete model that has year associations. Remove year associations first.'
        );
      });

      it('should throw NotFoundError when model does not exist', async () => {
        const modelId = 'non-existent-model';

        mockPrisma.vehicleModelYearV2.count.mockResolvedValue(0);
        mockPrisma.vehicleModelV2.deleteMany.mockResolvedValue({ count: 0 });

        await expect(
          service.deleteModel(modelId, testTenantId, testAuthContext)
        ).rejects.toThrow(NotFoundError);
        await expect(
          service.deleteModel(modelId, testTenantId, testAuthContext)
        ).rejects.toThrow('Model not found or does not belong to tenant');
      });
    });
  });

  describe('Year Association Operations', () => {
    describe('associateModelWithYears', () => {
      it('should associate model with years successfully', async () => {
        const modelId = 'model-id';
        const yearIds = ['year1', 'year2'];

        // Mock the transaction method from BaseTenantService
        const mockTransaction = jest.spyOn(service as any, 'transaction');
        mockTransaction.mockImplementation(async (callback) => {
          const mockTx = {
            vehicleModelV2: {
              findFirst: jest.fn().mockResolvedValue({
                id: modelId,
                tenantId: testTenantId,
              }),
            },
            vehicleYear: {
              findMany: jest.fn().mockResolvedValue([
                { id: 'year1', tenantId: testTenantId },
                { id: 'year2', tenantId: testTenantId },
              ]),
            },
            vehicleModelYearV2: {
              findMany: jest.fn().mockResolvedValue([]), // No existing associations
              createMany: jest.fn().mockResolvedValue({ count: 2 }),
              create: jest
                .fn()
                .mockResolvedValueOnce({
                  modelId,
                  yearId: 'year1',
                  tenantId: testTenantId,
                })
                .mockResolvedValueOnce({
                  modelId,
                  yearId: 'year2',
                  tenantId: testTenantId,
                }),
            },
          };
          return await (callback as any)(mockTx);
        });

        await service.associateModelWithYears(
          modelId,
          yearIds,
          testTenantId,
          testAuthContext
        );

        expect(mockTransaction).toHaveBeenCalled();
        mockTransaction.mockRestore();
      });

      it('should throw ValidationError for empty yearIds array', async () => {
        const modelId = 'model-id';
        const yearIds: string[] = [];

        await expect(
          service.associateModelWithYears(
            modelId,
            yearIds,
            testTenantId,
            testAuthContext
          )
        ).rejects.toThrow(ValidationError);
        await expect(
          service.associateModelWithYears(
            modelId,
            yearIds,
            testTenantId,
            testAuthContext
          )
        ).rejects.toThrow('At least one year ID must be provided');
      });
    });

    describe('removeModelYearAssociations', () => {
      it('should remove model-year associations successfully', async () => {
        const modelId = 'model-id';
        const yearIds = ['year1', 'year2'];

        mockPrisma.vehicleModelV2.findFirst.mockResolvedValue({
          id: modelId,
          tenantId: testTenantId,
        });
        mockPrisma.vehicleModelYearV2.deleteMany.mockResolvedValue({
          count: 2,
        });

        await service.removeModelYearAssociations(
          modelId,
          yearIds,
          testTenantId,
          testAuthContext
        );

        expect(mockPrisma.vehicleModelV2.findFirst).toHaveBeenCalledWith({
          where: { id: modelId, tenantId: testTenantId },
        });
        expect(mockPrisma.vehicleModelYearV2.deleteMany).toHaveBeenCalledWith({
          where: {
            modelId,
            yearId: { in: yearIds },
            tenantId: testTenantId,
          },
        });
      });

      it('should throw ValidationError for empty yearIds array', async () => {
        const modelId = 'model-id';
        const yearIds: string[] = [];

        await expect(
          service.removeModelYearAssociations(
            modelId,
            yearIds,
            testTenantId,
            testAuthContext
          )
        ).rejects.toThrow(ValidationError);
        await expect(
          service.removeModelYearAssociations(
            modelId,
            yearIds,
            testTenantId,
            testAuthContext
          )
        ).rejects.toThrow('At least one year ID must be provided');
      });
    });

    describe('getYearsByModel', () => {
      it('should return years associated with model', async () => {
        const modelId = 'model-id';
        const expectedYears = [
          { id: 'year1', year: 2024, tenantId: testTenantId },
          { id: 'year2', year: 2023, tenantId: testTenantId },
        ];

        mockPrisma.vehicleModelV2.findFirst.mockResolvedValue({
          id: modelId,
          tenantId: testTenantId,
        });
        mockPrisma.vehicleModelYearV2.findMany.mockResolvedValue([
          { modelId, yearId: 'year1', year: expectedYears[0] },
          { modelId, yearId: 'year2', year: expectedYears[1] },
        ]);

        const result = await service.getYearsByModel(
          modelId,
          testTenantId,
          testAuthContext
        );

        expect(mockPrisma.vehicleModelYearV2.findMany).toHaveBeenCalledWith({
          where: { modelId, tenantId: testTenantId },
          include: { year: true },
          orderBy: { year: { year: 'desc' } },
        });
        expect(result).toEqual(expectedYears);
      });

      it('should throw NotFoundError for invalid model', async () => {
        const modelId = 'invalid-model-id';

        mockPrisma.vehicleModelV2.findFirst.mockResolvedValue(null);

        await expect(
          service.getYearsByModel(modelId, testTenantId, testAuthContext)
        ).rejects.toThrow(NotFoundError);
        await expect(
          service.getYearsByModel(modelId, testTenantId, testAuthContext)
        ).rejects.toThrow('Model not found or does not belong to tenant');
      });
    });
  });

  describe('Tenant Isolation', () => {
    it('should validate tenant access for all operations', async () => {
      mockPrisma.tenant.findUnique.mockResolvedValue(null);

      await expect(
        service.getBrandsByTenant('invalid-tenant', testAuthContext)
      ).rejects.toThrow(NotFoundError);

      expect(mockPrisma.tenant.findUnique).toHaveBeenCalledWith({
        where: { id: 'invalid-tenant' },
        select: { id: true },
      });
    });

    it('should bypass tenant validation for System Admins', async () => {
      const systemAdminContext = {
        ...testAuthContext,
        canBypassTenantScope: true,
      };

      const expectedBrands = [
        {
          id: 'brand1',
          name: 'Ford',
          tenantId: 'other-tenant',
          isActive: true,
        },
      ];

      mockPrisma.vehicleBrandV2.findMany.mockResolvedValue(expectedBrands);

      const result = await service.getBrandsByTenant(
        'other-tenant',
        systemAdminContext
      );

      // Should not call tenant validation for System Admins
      expect(mockPrisma.tenant.findUnique).not.toHaveBeenCalled();
      expect(result).toEqual(expectedBrands);
    });
  });
});
