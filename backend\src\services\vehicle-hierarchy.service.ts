import {
  VehicleYear,
  VehicleMake,
  VehicleModel,
  VehicleModelYear,
} from '@prisma/client';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { BackendAuthContext } from '@tech-notes/shared';

import {
  NotFoundError,
  ConflictError,
  ValidationError,
} from '../types/error.types.js';
import { Logger } from '../utils/logger.js';

import { BaseTenantService } from './base-tenant.service.js';
import { PrismaService } from './prisma.service.js';

// Type definitions for service operations
export interface CreateYearData {
  year: number;
}

export interface CreateMakeData {
  name: string;
}

export interface CreateModelData {
  name: string;
  makeId: string;
  isActive?: boolean;
}

export interface ModelYearAssociation {
  modelId: string;
  yearIds: string[];
}

export interface HierarchyTree {
  years: (VehicleYear & {
    makes: (VehicleMake & {
      models: (VehicleModel & {
        years: VehicleYear[];
      })[];
    })[];
  })[];
}

export interface YearHierarchy {
  year: VehicleYear;
  makes: (VehicleMake & {
    models: VehicleModel[];
  })[];
}

/**
 * Service for managing vehicle hierarchy operations (Year → Make → Model)
 * Extends BaseTenantService for consistent tenant scoping patterns
 */
export class VehicleHierarchyService extends BaseTenantService {
  constructor(prismaService: PrismaService, logger: Logger) {
    super(prismaService, logger);
  }

  // ===== YEAR OPERATIONS =====

  /**
   * Create a new vehicle year
   */
  async createYear(
    yearData: CreateYearData,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleYear> {
    this.logOperation('createYear', { year: yearData.year, tenantId });

    // Validate year range
    if (yearData.year < 1900 || yearData.year > 2050) {
      throw new ValidationError('Year must be between 1900 and 2050');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          return await this.prisma.vehicleYear.create({
            data: {
              year: yearData.year,
              tenantId,
            },
          });
        } catch (error) {
          if (error instanceof PrismaClientKnownRequestError) {
            if (error.code === 'P2002') {
              throw new ConflictError(`Year ${yearData.year} already exists`);
            }
          }
          this.logError('createYear', error as Error, { yearData, tenantId });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Get all years for a tenant
   */
  async getYearsByTenant(
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleYear[]> {
    this.logOperation('getYearsByTenant', { tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleYear.findMany({
          where: { tenantId },
          orderBy: { year: 'desc' },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Create multiple years in a range
   */
  async createYearRange(
    startYear: number,
    endYear: number,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleYear[]> {
    this.logOperation('createYearRange', { startYear, endYear, tenantId });

    if (startYear > endYear) {
      throw new ValidationError(
        'Start year must be less than or equal to end year'
      );
    }

    if (endYear - startYear > 50) {
      throw new ValidationError('Year range cannot exceed 50 years');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // First, check which years already exist to avoid transaction conflicts
        const existingYears = await this.prisma.vehicleYear.findMany({
          where: {
            tenantId,
            year: {
              gte: startYear,
              lte: endYear,
            },
          },
        });

        const existingYearNumbers = new Set(existingYears.map((y) => y.year));
        const yearsToCreate: number[] = [];

        for (let year = startYear; year <= endYear; year++) {
          if (!existingYearNumbers.has(year)) {
            yearsToCreate.push(year);
          }
        }

        if (yearsToCreate.length === 0) {
          this.logger.info('All years in range already exist', {
            startYear,
            endYear,
            tenantId,
          });
          return [];
        }

        // Create only the years that don't exist
        return await this.transaction(async (tx) => {
          const years: VehicleYear[] = [];

          for (const year of yearsToCreate) {
            try {
              const vehicleYear = await tx.vehicleYear.create({
                data: { year, tenantId },
              });
              years.push(vehicleYear);
            } catch (error) {
              if (
                error instanceof PrismaClientKnownRequestError &&
                error.code === 'P2002'
              ) {
                // Year was created by another process between our check and creation
                this.logger.warn(
                  `Year ${year} was created concurrently, skipping`,
                  {
                    tenantId,
                  }
                );
                continue;
              }
              throw error;
            }
          }

          return years;
        });
      },
      BackendAuthContext
    );
  }

  // ===== MAKE OPERATIONS =====

  /**
   * Create a new vehicle make
   */
  async createMake(
    makeData: CreateMakeData,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleMake> {
    this.logOperation('createMake', { name: makeData.name, tenantId });

    // Validate make name
    if (!makeData.name.trim()) {
      throw new ValidationError('Make name cannot be empty');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          return await this.prisma.vehicleMake.create({
            data: {
              name: makeData.name.trim(),
              tenantId,
            },
          });
        } catch (error) {
          if (error instanceof PrismaClientKnownRequestError) {
            if (error.code === 'P2002') {
              throw new ConflictError(`Make "${makeData.name}" already exists`);
            }
          }
          this.logError('createMake', error as Error, { makeData, tenantId });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Get all makes for a tenant
   */
  async getMakesByTenant(
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleMake[]> {
    this.logOperation('getMakesByTenant', { tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleMake.findMany({
          where: { tenantId },
          orderBy: { name: 'asc' },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Update a make
   */
  async updateMake(
    makeId: string,
    updateData: { name: string },
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleMake> {
    this.logOperation('updateMake', { makeId, tenantId });

    if (!updateData.name.trim()) {
      throw new ValidationError('Make name cannot be empty');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        const make = await this.prisma.vehicleMake.findFirst({
          where: { id: makeId, tenantId },
        });

        if (!make) {
          throw new NotFoundError(
            'Make not found or does not belong to tenant'
          );
        }

        try {
          return await this.prisma.vehicleMake.update({
            where: { id: makeId },
            data: {
              name: updateData.name.trim(),
            },
          });
        } catch (error) {
          if (error instanceof PrismaClientKnownRequestError) {
            if (error.code === 'P2002') {
              throw new ConflictError(
                `Make "${updateData.name}" already exists`
              );
            }
          }
          this.logError('updateMake', error as Error, { makeId, tenantId });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  // ===== MODEL OPERATIONS =====

  /**
   * Create a new vehicle model
   */
  async createModel(
    modelData: CreateModelData,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModel> {
    this.logOperation('createModel', {
      name: modelData.name,
      makeId: modelData.makeId,
      tenantId,
    });

    // Validate model name
    if (!modelData.name.trim()) {
      throw new ValidationError('Model name cannot be empty');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Verify make exists and belongs to tenant
        const make = await this.prisma.vehicleMake.findFirst({
          where: { id: modelData.makeId, tenantId },
        });

        if (!make) {
          throw new NotFoundError(
            'Make not found or does not belong to tenant'
          );
        }

        try {
          return await this.prisma.vehicleModel.create({
            data: {
              name: modelData.name.trim(),
              makeId: modelData.makeId,
              tenantId,
              isActive: modelData.isActive ?? true,
            },
          });
        } catch (error) {
          if (error instanceof PrismaClientKnownRequestError) {
            if (error.code === 'P2002') {
              throw new ConflictError(
                `Model "${modelData.name}" already exists for this make`
              );
            }
          }
          this.logError('createModel', error as Error, { modelData, tenantId });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Get models by make
   */
  async getModelsByMake(
    makeId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModel[]> {
    this.logOperation('getModelsByMake', { makeId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleModel.findMany({
          where: { makeId, tenantId },
          orderBy: { name: 'asc' },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Get all models for a tenant with their makes
   */
  async getAllModelsByTenant(
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModel[]> {
    this.logOperation('getAllModelsByTenant', { tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleModel.findMany({
          where: { tenantId },
          include: {
            make: true,
          },
          orderBy: [{ make: { name: 'asc' } }, { name: 'asc' }],
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Update a model
   */
  async updateModel(
    modelId: string,
    updateData: { name: string },
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModel> {
    this.logOperation('updateModel', { modelId, tenantId });

    if (!updateData.name.trim()) {
      throw new ValidationError('Model name cannot be empty');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        const model = await this.prisma.vehicleModel.findFirst({
          where: { id: modelId, tenantId },
        });

        if (!model) {
          throw new NotFoundError(
            'Model not found or does not belong to tenant'
          );
        }

        try {
          return await this.prisma.vehicleModel.update({
            where: { id: modelId },
            data: {
              name: updateData.name.trim(),
            },
          });
        } catch (error) {
          if (error instanceof PrismaClientKnownRequestError) {
            if (error.code === 'P2002') {
              throw new ConflictError(
                `Model "${updateData.name}" already exists for this make`
              );
            }
          }
          this.logError('updateModel', error as Error, { modelId, tenantId });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Get models by year
   */
  async getModelsByYear(
    yearId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModel[]> {
    this.logOperation('getModelsByYear', { yearId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleModel.findMany({
          where: {
            tenantId,
            modelYears: {
              some: { yearId },
            },
          },
          include: {
            make: true,
          },
          orderBy: [{ make: { name: 'asc' } }, { name: 'asc' }],
        });
      },
      BackendAuthContext
    );
  }

  // ===== MODEL-YEAR ASSOCIATION OPERATIONS =====

  /**
   * Associate a model with multiple years
   */
  async associateModelWithYears(
    modelId: string,
    yearIds: string[],
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('associateModelWithYears', {
      modelId,
      yearIds,
      tenantId,
    });

    if (yearIds.length === 0) {
      throw new ValidationError('At least one year must be provided');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Verify model exists and belongs to tenant
        const model = await this.prisma.vehicleModel.findFirst({
          where: { id: modelId, tenantId },
        });

        if (!model) {
          throw new NotFoundError(
            'Model not found or does not belong to tenant'
          );
        }

        // Verify all years exist and belong to tenant
        const years = await this.prisma.vehicleYear.findMany({
          where: { id: { in: yearIds }, tenantId },
        });

        if (years.length !== yearIds.length) {
          throw new NotFoundError(
            'One or more years not found or do not belong to tenant'
          );
        }

        return await this.transaction(async (tx) => {
          // Create associations (ignore duplicates)
          for (const yearId of yearIds) {
            try {
              await tx.vehicleModelYear.create({
                data: {
                  modelId,
                  yearId,
                  tenantId,
                },
              });
            } catch (error) {
              if (
                error instanceof PrismaClientKnownRequestError &&
                error.code === 'P2002'
              ) {
                // Skip duplicate associations
                continue;
              }
              throw error;
            }
          }
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Remove model-year association
   */
  async removeModelYearAssociation(
    modelId: string,
    yearId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('removeModelYearAssociation', {
      modelId,
      yearId,
      tenantId,
    });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        const association = await this.prisma.vehicleModelYear.findFirst({
          where: { modelId, yearId, tenantId },
        });

        if (!association) {
          throw new NotFoundError('Model-year association not found');
        }

        await this.prisma.vehicleModelYear.delete({
          where: {
            modelId_yearId: { modelId, yearId },
          },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Get years associated with a model
   */
  async getYearsByModel(
    modelId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleYear[]> {
    this.logOperation('getYearsByModel', { modelId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleYear.findMany({
          where: {
            tenantId,
            modelYears: {
              some: { modelId },
            },
          },
          orderBy: { year: 'desc' },
        });
      },
      BackendAuthContext
    );
  }

  // ===== BULK OPERATIONS =====

  /**
   * Bulk create models for a make
   */
  async bulkCreateMakeModels(
    makeId: string,
    modelNames: string[],
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModel[]> {
    this.logOperation('bulkCreateMakeModels', {
      makeId,
      modelCount: modelNames.length,
      tenantId,
    });

    if (modelNames.length === 0) {
      throw new ValidationError('At least one model name must be provided');
    }

    if (modelNames.length > 100) {
      throw new ValidationError('Cannot create more than 100 models at once');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Verify make exists and belongs to tenant
        const make = await this.prisma.vehicleMake.findFirst({
          where: { id: makeId, tenantId },
        });

        if (!make) {
          throw new NotFoundError(
            'Make not found or does not belong to tenant'
          );
        }

        // First, check which models already exist to avoid transaction conflicts
        const trimmedNames = modelNames
          .map((name) => name.trim())
          .filter((name) => name);

        if (trimmedNames.length === 0) {
          return [];
        }

        const existingModels = await this.prisma.vehicleModel.findMany({
          where: {
            makeId,
            tenantId,
            name: { in: trimmedNames },
          },
        });

        const existingModelNames = new Set(existingModels.map((m) => m.name));
        const modelsToCreate = trimmedNames.filter(
          (name) => !existingModelNames.has(name)
        );

        if (modelsToCreate.length === 0) {
          this.logger.info('All models already exist for make', {
            makeId,
            tenantId,
            modelCount: trimmedNames.length,
          });
          return [];
        }

        // Create only the models that don't exist
        return await this.transaction(async (tx) => {
          const models: VehicleModel[] = [];

          for (const modelName of modelsToCreate) {
            try {
              const model = await tx.vehicleModel.create({
                data: {
                  name: modelName,
                  makeId,
                  tenantId,
                  isActive: true,
                },
              });
              models.push(model);
            } catch (error) {
              if (
                error instanceof PrismaClientKnownRequestError &&
                error.code === 'P2002'
              ) {
                // Model was created by another process between our check and creation
                this.logger.warn(
                  `Model "${modelName}" was created concurrently, skipping`,
                  { makeId, tenantId }
                );
                continue;
              }
              throw error;
            }
          }

          return models;
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Bulk associate models with years
   */
  async bulkAssociateModelYears(
    associations: ModelYearAssociation[],
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('bulkAssociateModelYears', {
      associationCount: associations.length,
      tenantId,
    });

    if (associations.length === 0) {
      throw new ValidationError('At least one association must be provided');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.transaction(async (tx) => {
          for (const association of associations) {
            // Verify model exists and belongs to tenant
            const model = await tx.vehicleModel.findFirst({
              where: { id: association.modelId, tenantId },
            });

            if (!model) {
              throw new NotFoundError(
                `Model ${association.modelId} not found or does not belong to tenant`
              );
            }

            // Create associations for this model
            for (const yearId of association.yearIds) {
              try {
                await tx.vehicleModelYear.create({
                  data: {
                    modelId: association.modelId,
                    yearId,
                    tenantId,
                  },
                });
              } catch (error) {
                if (
                  error instanceof PrismaClientKnownRequestError &&
                  error.code === 'P2002'
                ) {
                  // Skip duplicate associations
                  continue;
                }
                throw error;
              }
            }
          }
        });
      },
      BackendAuthContext
    );
  }

  // ===== HIERARCHY QUERIES =====

  /**
   * Get full hierarchy tree for a tenant
   */
  async getFullHierarchy(
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<HierarchyTree> {
    this.logOperation('getFullHierarchy', { tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Get all years
        const years = await this.prisma.vehicleYear.findMany({
          where: { tenantId },
          orderBy: { year: 'desc' },
        });

        // Get all models with their makes and associated years
        const models = await this.prisma.vehicleModel.findMany({
          where: { tenantId },
          include: {
            make: true,
            modelYears: {
              include: {
                year: true,
              },
            },
          },
          orderBy: [{ make: { name: 'asc' } }, { name: 'asc' }],
        });

        // Transform the data into the desired hierarchy structure
        const hierarchyTree: HierarchyTree = {
          years: years.map((year) => {
            // Find models available in this year
            const modelsInYear = models.filter((model) =>
              model.modelYears.some((my) => my.yearId === year.id)
            );

            return {
              ...year,
              makes: this.groupModelsByMakeWithYears(modelsInYear),
            };
          }),
        };

        return hierarchyTree;
      },
      BackendAuthContext
    );
  }

  /**
   * Get hierarchy for a specific year
   */
  async getHierarchyByYear(
    year: number,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<YearHierarchy | null> {
    this.logOperation('getHierarchyByYear', { year, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        const vehicleYear = await this.prisma.vehicleYear.findFirst({
          where: { year, tenantId },
          include: {
            modelYears: {
              include: {
                model: {
                  include: {
                    make: true,
                  },
                },
              },
            },
          },
        });

        if (!vehicleYear) {
          return null;
        }

        const makes = this.groupModelsByMake(
          vehicleYear.modelYears.map((my) => my.model)
        );

        return {
          year: vehicleYear,
          makes,
        };
      },
      BackendAuthContext
    );
  }

  // ===== EXPORT METHODS =====

  /**
   * Export hierarchy data to CSV format
   */
  async exportToCSV(
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<string> {
    this.logOperation('exportToCSV', { tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Get all data with relationships
        const years = await this.prisma.vehicleYear.findMany({
          where: { tenantId },
          include: {
            modelYears: {
              include: {
                model: {
                  include: {
                    make: true,
                  },
                },
              },
            },
          },
          orderBy: { year: 'desc' },
        });

        // Build CSV data
        const csvRows: string[] = [];
        csvRows.push('Year,Make,Model,Active'); // Header

        for (const year of years) {
          for (const modelYear of year.modelYears) {
            const model = modelYear.model;
            const make = model.make;
            csvRows.push(
              `${year.year},"${make.name}","${model.name}",${model.isActive ? 'Yes' : 'No'}`
            );
          }
        }

        return csvRows.join('\n');
      },
      BackendAuthContext
    );
  }

  // ===== UTILITY METHODS =====

  /**
   * Group models by their make
   */
  private groupModelsByMake(
    models: (VehicleModel & { make: VehicleMake })[]
  ): (VehicleMake & { models: (VehicleModel & { years: VehicleYear[] })[] })[] {
    const makeMap = new Map<
      string,
      VehicleMake & { models: (VehicleModel & { years: VehicleYear[] })[] }
    >();

    for (const model of models) {
      if (!makeMap.has(model.make.id)) {
        makeMap.set(model.make.id, {
          ...model.make,
          models: [],
        });
      }

      const makeEntry = makeMap.get(model.make.id)!;
      makeEntry.models.push({
        ...model,
        years: [], // Years will be populated separately if needed
      });
    }

    return Array.from(makeMap.values()).sort((a, b) =>
      a.name.localeCompare(b.name)
    );
  }

  /**
   * Group models by their make with years populated
   */
  private groupModelsByMakeWithYears(
    models: (VehicleModel & {
      make: VehicleMake;
      modelYears: (VehicleModelYear & { year: VehicleYear })[];
    })[]
  ): (VehicleMake & { models: (VehicleModel & { years: VehicleYear[] })[] })[] {
    const makeMap = new Map<
      string,
      VehicleMake & { models: (VehicleModel & { years: VehicleYear[] })[] }
    >();

    for (const model of models) {
      if (!makeMap.has(model.make.id)) {
        makeMap.set(model.make.id, {
          ...model.make,
          models: [],
        });
      }

      const makeEntry = makeMap.get(model.make.id)!;
      makeEntry.models.push({
        ...model,
        years: model.modelYears
          .map((my) => my.year)
          .sort((a, b) => b.year - a.year),
      });
    }

    return Array.from(makeMap.values()).sort((a, b) =>
      a.name.localeCompare(b.name)
    );
  }

  /**
   * Delete a year (with cascade validation)
   */
  async deleteYear(
    yearId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('deleteYear', { yearId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        const year = await this.prisma.vehicleYear.findFirst({
          where: { id: yearId, tenantId },
          include: {
            modelYears: true,
          },
        });

        if (!year) {
          throw new NotFoundError(
            'Year not found or does not belong to tenant'
          );
        }

        if (year.modelYears.length > 0) {
          throw new ConflictError(
            'Cannot delete year that has associated models'
          );
        }

        await this.prisma.vehicleYear.delete({
          where: { id: yearId },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Delete a make (with cascade validation)
   */
  async deleteMake(
    makeId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('deleteMake', { makeId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        const make = await this.prisma.vehicleMake.findFirst({
          where: { id: makeId, tenantId },
          include: {
            models: true,
          },
        });

        if (!make) {
          throw new NotFoundError(
            'Make not found or does not belong to tenant'
          );
        }

        if (make.models.length > 0) {
          throw new ConflictError(
            'Cannot delete make that has associated models'
          );
        }

        await this.prisma.vehicleMake.delete({
          where: { id: makeId },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Delete a model (with cascade cleanup)
   */
  async deleteModel(
    modelId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('deleteModel', { modelId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        const model = await this.prisma.vehicleModel.findFirst({
          where: { id: modelId, tenantId },
        });

        if (!model) {
          throw new NotFoundError(
            'Model not found or does not belong to tenant'
          );
        }

        // Delete model (model-year associations will cascade delete)
        await this.prisma.vehicleModel.delete({
          where: { id: modelId },
        });
      },
      BackendAuthContext
    );
  }
}
