import { VehicleBrandV3, Prisma } from '@prisma/client';
import { BackendAuthContext } from '@tech-notes/shared';

import { NotFoundError } from '../../types/error.types.js';
import { Logger } from '../../utils/logger.js';
import { PrismaService } from '../prisma.service.js';

import { BaseVehicleHierarchyV3Service } from './base-vehicle-hierarchy-v3.service.js';

export interface CreateBrandV3Data {
  name: string;
}

export interface UpdateBrandV3Data {
  name?: string;
  isActive?: boolean;
  displayOrder?: number;
}

/**
 * Service for managing Vehicle Brand V3 operations
 * Handles CRUD operations for brands with tenant isolation
 */
export class VehicleBrandV3Service extends BaseVehicleHierarchyV3Service {
  constructor(prismaService: PrismaService, logger: Logger) {
    super(prismaService, logger);
  }

  /**
   * Get all brands for a tenant
   */
  async getBrandsByTenant(
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleBrandV3[]> {
    this.logOperation('getBrandsByTenant', { tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleBrandV3.findMany({
          where: this.getActiveEntityWhere({ tenantId }),
          orderBy: this.getStandardOrderBy(),
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Get brand by ID with tenant validation
   */
  async getBrandById(
    brandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleBrandV3 | null> {
    this.logOperation('getBrandById', { brandId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleBrandV3.findFirst({
          where: this.getActiveEntityWhere({
            id: brandId,
            tenantId,
          }),
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Create a new brand
   */
  async createBrand(
    brandData: CreateBrandV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleBrandV3> {
    this.logOperation('createBrand', { brandData, tenantId });

    const trimmedName = this.validateAndTrimName(brandData.name, 'Brand');

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Get next display order for this tenant
          const nextDisplayOrder = await this.getNextDisplayOrder(
            this.prisma.vehicleBrandV3,
            { tenantId }
          );

          return await this.prisma.vehicleBrandV3.create({
            data: {
              name: trimmedName,
              tenantId,
              displayOrder: nextDisplayOrder,
            },
          });
        } catch (error) {
          this.handleUniqueConstraintError(error, trimmedName, 'Brand');
          this.logError('createBrand', error as Error, { brandData, tenantId });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Update an existing brand
   */
  async updateBrand(
    brandId: string,
    brandData: UpdateBrandV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleBrandV3> {
    this.logOperation('updateBrand', { brandId, brandData, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Verify brand exists and belongs to tenant
          const existingBrand = await this.prisma.vehicleBrandV3.findFirst({
            where: this.getActiveEntityWhere({
              id: brandId,
              tenantId,
            }),
          });

          if (!existingBrand) {
            throw new NotFoundError('Brand not found');
          }

          // Prepare update data
          const updateData: Prisma.VehicleBrandV3UpdateInput = {};

          if (brandData.name !== undefined) {
            updateData.name = this.validateAndTrimName(brandData.name, 'Brand');
          }

          if (brandData.isActive !== undefined) {
            updateData.isActive = brandData.isActive;
          }

          if (brandData.displayOrder !== undefined) {
            updateData.displayOrder = brandData.displayOrder;
          }

          return await this.prisma.vehicleBrandV3.update({
            where: { id: brandId },
            data: updateData,
          });
        } catch (error) {
          this.handleUniqueConstraintError(
            error,
            brandData.name || '',
            'Brand'
          );
          this.logError('updateBrand', error as Error, {
            brandId,
            brandData,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Soft delete a brand
   */
  async deleteBrand(
    brandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('deleteBrand', { brandId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Verify brand exists and belongs to tenant
          const existingBrand = await this.prisma.vehicleBrandV3.findFirst({
            where: this.getActiveEntityWhere({
              id: brandId,
              tenantId,
            }),
          });

          if (!existingBrand) {
            throw new NotFoundError('Brand not found');
          }

          // TODO: In future phases, check for child records (sub-brands)
          // and prevent deletion if they exist

          // Soft delete the brand
          await this.executeSoftDelete(this.prisma.vehicleBrandV3, brandId, {
            tenantId,
          });
        } catch (error) {
          this.logError('deleteBrand', error as Error, { brandId, tenantId });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Reorder brands within a tenant
   */
  async reorderBrands(
    brandOrders: { id: string; displayOrder: number }[],
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('reorderBrands', { brandOrders, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          await this.executeReorder('vehicleBrandV3', brandOrders, {
            tenantId,
          });
        } catch (error) {
          this.logError('reorderBrands', error as Error, {
            brandOrders,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Validate that a brand exists and belongs to the tenant
   * Used by other services for relationship validation
   */
  async validateBrandOwnership(
    brandId: string,
    tenantId: string
  ): Promise<void> {
    const brand = await this.prisma.vehicleBrandV3.findFirst({
      where: this.getActiveEntityWhere({
        id: brandId,
        tenantId,
      }),
      select: { id: true },
    });

    if (!brand) {
      throw new NotFoundError(`Brand ${brandId} not found or not accessible`);
    }
  }
}
