import { VehicleBrandV3, VehicleSubBrandV3, Prisma } from '@prisma/client';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { BackendAuthContext } from '@tech-notes/shared';

import {
  NotFoundError,
  ConflictError,
  ValidationError,
} from '../types/error.types.js';
import { Logger } from '../utils/logger.js';

import { BaseTenantService } from './base-tenant.service.js';
import { PrismaService } from './prisma.service.js';

export interface CreateBrandV3Data {
  name: string;
}

export interface UpdateBrandV3Data {
  name?: string;
  isActive?: boolean;
  displayOrder?: number;
}

export interface CreateSubBrandV3Data {
  name: string;
  brandId: string;
}

export interface UpdateSubBrandV3Data {
  name?: string;
  isActive?: boolean;
  displayOrder?: number;
}

/**
 * Service for managing vehicle hierarchy V3 operations (Brand → Sub-Brand → Model ↔ Year)
 * Extends BaseTenantService for consistent tenant scoping patterns
 * Phase 2: Brand and Sub-Brand operations
 */
export class VehicleHierarchyV3Service extends BaseTenantService {
  constructor(prismaService: PrismaService, logger: Logger) {
    super(prismaService, logger);
  }

  // ===== BRAND OPERATIONS =====

  /**
   * Get all brands for a tenant
   */
  async getBrandsByTenant(
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleBrandV3[]> {
    this.logOperation('getBrandsByTenant', { tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleBrandV3.findMany({
          where: {
            tenantId,
            deletedAt: null, // Only return non-deleted brands
          },
          orderBy: [{ displayOrder: 'asc' }, { name: 'asc' }],
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Get brand by ID with tenant validation
   */
  async getBrandById(
    brandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleBrandV3 | null> {
    this.logOperation('getBrandById', { brandId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleBrandV3.findFirst({
          where: {
            id: brandId,
            tenantId, // Double validation: scope + where clause
            deletedAt: null,
          },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Create a new brand
   */
  async createBrand(
    brandData: CreateBrandV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleBrandV3> {
    this.logOperation('createBrand', { brandData, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Get next display order for this tenant
          const maxDisplayOrder = await this.prisma.vehicleBrandV3.aggregate({
            where: {
              tenantId,
              deletedAt: null,
            },
            _max: {
              displayOrder: true,
            },
          });

          const nextDisplayOrder = (maxDisplayOrder._max.displayOrder || 0) + 1;

          return await this.prisma.vehicleBrandV3.create({
            data: {
              name: brandData.name.trim(),
              tenantId,
              displayOrder: nextDisplayOrder,
            },
          });
        } catch (error) {
          if (error instanceof PrismaClientKnownRequestError) {
            if (error.code === 'P2002') {
              throw new ConflictError(
                `Brand '${brandData.name}' already exists in this tenant`
              );
            }
          }
          this.logError('createBrand', error as Error, { brandData, tenantId });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Update an existing brand
   */
  async updateBrand(
    brandId: string,
    brandData: UpdateBrandV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleBrandV3> {
    this.logOperation('updateBrand', { brandId, brandData, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Verify brand exists and belongs to tenant
          const existingBrand = await this.prisma.vehicleBrandV3.findFirst({
            where: {
              id: brandId,
              tenantId,
              deletedAt: null,
            },
          });

          if (!existingBrand) {
            throw new NotFoundError('Brand not found');
          }

          // Prepare update data
          const updateData: Prisma.VehicleBrandV3UpdateInput = {};

          if (brandData.name !== undefined) {
            updateData.name = brandData.name.trim();
          }

          if (brandData.isActive !== undefined) {
            updateData.isActive = brandData.isActive;
          }

          if (brandData.displayOrder !== undefined) {
            updateData.displayOrder = brandData.displayOrder;
          }

          return await this.prisma.vehicleBrandV3.update({
            where: { id: brandId },
            data: updateData,
          });
        } catch (error) {
          if (error instanceof PrismaClientKnownRequestError) {
            if (error.code === 'P2002') {
              throw new ConflictError(
                `Brand name '${brandData.name}' already exists in this tenant`
              );
            }
          }
          this.logError('updateBrand', error as Error, {
            brandId,
            brandData,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Soft delete a brand
   */
  async deleteBrand(
    brandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('deleteBrand', { brandId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Verify brand exists and belongs to tenant
          const existingBrand = await this.prisma.vehicleBrandV3.findFirst({
            where: {
              id: brandId,
              tenantId,
              deletedAt: null,
            },
          });

          if (!existingBrand) {
            throw new NotFoundError('Brand not found');
          }

          // TODO: In future phases, check for child records (sub-brands)
          // and prevent deletion if they exist

          // Soft delete the brand
          await this.prisma.vehicleBrandV3.update({
            where: { id: brandId },
            data: {
              deletedAt: new Date(),
              isActive: false, // Also mark as inactive
            },
          });
        } catch (error) {
          this.logError('deleteBrand', error as Error, { brandId, tenantId });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Reorder brands within a tenant
   */
  async reorderBrands(
    brandOrders: { id: string; displayOrder: number }[],
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('reorderBrands', { brandOrders, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          await this.transaction(async (tx) => {
            for (const { id, displayOrder } of brandOrders) {
              await tx.vehicleBrandV3.update({
                where: {
                  id,
                  tenantId, // Ensure brand belongs to tenant
                  deletedAt: null,
                },
                data: { displayOrder },
              });
            }
          });
        } catch (error) {
          this.logError('reorderBrands', error as Error, {
            brandOrders,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  // ===== SUB-BRAND OPERATIONS =====

  /**
   * Validate that a brand exists and belongs to the tenant
   */
  private async validateBrandOwnership(
    brandId: string,
    tenantId: string
  ): Promise<void> {
    const brand = await this.prisma.vehicleBrandV3.findFirst({
      where: {
        id: brandId,
        tenantId,
        deletedAt: null,
      },
      select: { id: true },
    });

    if (!brand) {
      throw new NotFoundError(`Brand ${brandId} not found or not accessible`);
    }
  }

  /**
   * Get all sub-brands for a specific brand
   */
  async getSubBrandsByBrand(
    brandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleSubBrandV3[]> {
    this.logOperation('getSubBrandsByBrand', { brandId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Validate brand ownership first
        await this.validateBrandOwnership(brandId, tenantId);

        return await this.prisma.vehicleSubBrandV3.findMany({
          where: {
            brandId,
            tenantId,
            deletedAt: null,
          },
          orderBy: { displayOrder: 'asc' },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Get a specific sub-brand by ID
   */
  async getSubBrandById(
    subBrandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleSubBrandV3 | null> {
    this.logOperation('getSubBrandById', { subBrandId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleSubBrandV3.findFirst({
          where: {
            id: subBrandId,
            tenantId,
            deletedAt: null,
          },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Create a new sub-brand
   */
  async createSubBrand(
    subBrandData: CreateSubBrandV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleSubBrandV3> {
    this.logOperation('createSubBrand', {
      name: subBrandData.name,
      brandId: subBrandData.brandId,
      tenantId,
    });

    if (!subBrandData.name.trim()) {
      throw new ValidationError('Sub-brand name cannot be empty');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Validate brand ownership first
          await this.validateBrandOwnership(subBrandData.brandId, tenantId);

          // Get next display order for this brand
          const maxDisplayOrder = await this.prisma.vehicleSubBrandV3.aggregate(
            {
              where: {
                brandId: subBrandData.brandId,
                tenantId,
                deletedAt: null,
              },
              _max: {
                displayOrder: true,
              },
            }
          );

          const nextDisplayOrder = (maxDisplayOrder._max.displayOrder || 0) + 1;

          return await this.prisma.vehicleSubBrandV3.create({
            data: {
              name: subBrandData.name.trim(),
              brandId: subBrandData.brandId,
              tenantId,
              displayOrder: nextDisplayOrder,
            },
          });
        } catch (error) {
          if (error instanceof PrismaClientKnownRequestError) {
            if (error.code === 'P2002') {
              throw new ConflictError(
                `Sub-brand '${subBrandData.name}' already exists for this brand`
              );
            }
          }
          this.logError('createSubBrand', error as Error, {
            subBrandData,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Update a sub-brand
   */
  async updateSubBrand(
    subBrandId: string,
    updateData: UpdateSubBrandV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleSubBrandV3> {
    this.logOperation('updateSubBrand', { subBrandId, updateData, tenantId });

    if (updateData.name !== undefined && !updateData.name.trim()) {
      throw new ValidationError('Sub-brand name cannot be empty');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Prepare update data
          const dataToUpdate: {
            name?: string;
            isActive?: boolean;
            displayOrder?: number;
          } = {};
          if (updateData.name !== undefined) {
            dataToUpdate.name = updateData.name.trim();
          }
          if (updateData.isActive !== undefined) {
            dataToUpdate.isActive = updateData.isActive;
          }
          if (updateData.displayOrder !== undefined) {
            dataToUpdate.displayOrder = updateData.displayOrder;
          }

          return await this.prisma.vehicleSubBrandV3.update({
            where: {
              id: subBrandId,
              tenantId, // Ensure sub-brand belongs to tenant
              deletedAt: null,
            },
            data: dataToUpdate,
          });
        } catch (error) {
          if (error instanceof PrismaClientKnownRequestError) {
            if (error.code === 'P2025') {
              throw new NotFoundError(`Sub-brand ${subBrandId} not found`);
            }
            if (error.code === 'P2002') {
              throw new ConflictError(
                `Sub-brand name '${updateData.name}' already exists for this brand`
              );
            }
          }
          this.logError('updateSubBrand', error as Error, {
            subBrandId,
            updateData,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Soft delete a sub-brand
   */
  async deleteSubBrand(
    subBrandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('deleteSubBrand', { subBrandId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          const result = await this.prisma.vehicleSubBrandV3.update({
            where: {
              id: subBrandId,
              tenantId, // Ensure sub-brand belongs to tenant
              deletedAt: null, // Only delete non-deleted sub-brands
            },
            data: {
              deletedAt: new Date(),
              isActive: false, // Also mark as inactive
            },
          });

          if (!result) {
            throw new NotFoundError(`Sub-brand ${subBrandId} not found`);
          }
        } catch (error) {
          if (error instanceof PrismaClientKnownRequestError) {
            if (error.code === 'P2025') {
              throw new NotFoundError(`Sub-brand ${subBrandId} not found`);
            }
          }
          this.logError('deleteSubBrand', error as Error, {
            subBrandId,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Reorder sub-brands within a brand
   */
  async reorderSubBrands(
    brandId: string,
    subBrandOrders: { id: string; displayOrder: number }[],
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('reorderSubBrands', {
      brandId,
      subBrandOrders,
      tenantId,
    });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Validate brand ownership first
          await this.validateBrandOwnership(brandId, tenantId);

          await this.transaction(async (tx) => {
            for (const { id, displayOrder } of subBrandOrders) {
              await tx.vehicleSubBrandV3.update({
                where: {
                  id,
                  brandId, // Ensure sub-brand belongs to the specified brand
                  tenantId, // Ensure sub-brand belongs to tenant
                  deletedAt: null,
                },
                data: { displayOrder },
              });
            }
          });
        } catch (error) {
          this.logError('reorderSubBrands', error as Error, {
            brandId,
            subBrandOrders,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }
}
