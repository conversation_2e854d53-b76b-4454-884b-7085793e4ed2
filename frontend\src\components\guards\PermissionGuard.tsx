import React from "react";
import {
  usePermissions,
  type PermissionCheck,
} from "../../hooks/usePermissions";
import type { RoleType } from "../../types/auth.types";
import { Alert } from "../atoms/Alert";

interface PermissionGuardProps {
  /** Single permission requirement */
  permission?: PermissionCheck;
  /** Multiple permissions (AND logic by default) */
  permissions?: PermissionCheck[];
  /** Single role requirement */
  role?: RoleType;
  /** Multiple roles */
  roles?: RoleType[];
  /** Whether to require ALL permissions/roles (true) or ANY (false) */
  requireAll?: boolean;
  /** Custom fallback component when access is denied */
  fallback?: React.ReactNode;
  /** Whether to show fallback component (default: false - renders nothing) */
  showFallback?: boolean;
  /** Children to render when access is granted */
  children: React.ReactNode;
}

/**
 * PermissionGuard component for conditional rendering based on user permissions and roles
 *
 * @example
 * // Single permission check
 * <PermissionGuard permission={{ resource: 'USER', action: 'READ' }}>
 *   <UsersList />
 * </PermissionGuard>
 *
 * @example
 * // Multiple permissions (AND logic)
 * <PermissionGuard permissions={[
 *   { resource: 'USER', action: 'READ' },
 *   { resource: 'USER', action: 'WRITE' }
 * ]}>
 *   <UserManagement />
 * </PermissionGuard>
 *
 * @example
 * // Role-based check with fallback
 * <PermissionGuard
 *   role="SYSTEM_ADMIN"
 *   fallback={<Alert variant="warning">Admin access required</Alert>}
 *   showFallback
 * >
 *   <AdminPanel />
 * </PermissionGuard>
 */
export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permission,
  permissions,
  role,
  roles,
  requireAll = true,
  fallback,
  showFallback = false,
  children,
}) => {
  const {
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    hasAllPermissions,
    hasAnyPermission,
  } = usePermissions();

  let hasAccess = false;

  // Single permission check
  if (permission) {
    hasAccess = hasPermission(permission.resource, permission.action);
  }
  // Multiple permissions check
  else if (permissions && permissions.length > 0) {
    hasAccess = requireAll
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);
  }
  // Single role check
  else if (role) {
    hasAccess = hasRole(role);
  }
  // Multiple roles check
  else if (roles && roles.length > 0) {
    hasAccess = requireAll ? hasAllRoles(roles) : hasAnyRole(roles);
  }
  // No requirements specified - deny access by default
  else {
    hasAccess = false;
  }

  // Grant access - render children
  if (hasAccess) {
    return <>{children}</>;
  }

  // Access denied - handle fallback
  if (showFallback) {
    if (fallback) {
      return <>{fallback}</>;
    }

    // Default fallback message
    return (
      <Alert variant="warning">
        You don't have permission to access this feature.
      </Alert>
    );
  }

  // Access denied - render nothing
  return null;
};

/**
 * Convenience component for System Admin only access
 */
export const SystemAdminOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}> = ({ children, fallback, showFallback = false }) => (
  <PermissionGuard
    role="SYSTEM_ADMIN"
    fallback={fallback}
    showFallback={showFallback}
  >
    {children}
  </PermissionGuard>
);

/**
 * Convenience component for Company Admin or higher access
 */
export const CompanyAdminOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}> = ({ children, fallback, showFallback = false }) => (
  <PermissionGuard
    roles={["SYSTEM_ADMIN", "COMPANY_ADMIN"]}
    requireAll={false}
    fallback={fallback}
    showFallback={showFallback}
  >
    {children}
  </PermissionGuard>
);

/**
 * Convenience component for any admin access (System Admin or Company Admin)
 */
export const AdminOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}> = ({ children, fallback, showFallback = false }) => (
  <PermissionGuard
    roles={["SYSTEM_ADMIN", "COMPANY_ADMIN"]}
    requireAll={false}
    fallback={fallback}
    showFallback={showFallback}
  >
    {children}
  </PermissionGuard>
);

/**
 * Convenience component for user management access
 */
export const UserManagementAccess: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}> = ({ children, fallback, showFallback = false }) => (
  <PermissionGuard
    permission={{ resource: "USER", action: "READ" }}
    fallback={fallback}
    showFallback={showFallback}
  >
    {children}
  </PermissionGuard>
);

/**
 * Convenience component for tenant management access
 */
export const TenantManagementAccess: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}> = ({ children, fallback, showFallback = false }) => (
  <PermissionGuard
    permission={{ resource: "TENANT", action: "READ" }}
    fallback={fallback}
    showFallback={showFallback}
  >
    {children}
  </PermissionGuard>
);
