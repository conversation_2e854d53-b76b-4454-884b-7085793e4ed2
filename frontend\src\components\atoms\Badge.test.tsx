import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import { Badge } from "./Badge";

describe("Badge", () => {
  describe("Rendering", () => {
    it("should render badge with default variant and size", () => {
      render(<Badge>Default Badge</Badge>);

      const badge = screen.getByText("Default Badge");
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass("inline-flex", "items-center", "rounded-full");
      expect(badge).toHaveClass(
        "bg-gray-100",
        "text-gray-800",
        "border",
        "border-gray-200",
        "shadow-xs",
      );
      expect(badge).toHaveClass(
        "px-3",
        "py-1.5",
        "text-sm",
        "font-semibold",
        "tracking-wide",
      );
    });

    it("should render badge with custom className", () => {
      render(<Badge className="custom-class">Custom Badge</Badge>);

      const badge = screen.getByText("Custom Badge");
      expect(badge).toHaveClass("custom-class");
    });
  });

  describe("Variants", () => {
    it("should render primary variant with gradient background", () => {
      render(<Badge variant="primary">Primary Badge</Badge>);

      const badge = screen.getByText("Primary Badge");
      expect(badge).toHaveClass(
        "bg-gradient-to-r",
        "from-primary-50",
        "to-primary-100",
      );
      expect(badge).toHaveClass(
        "text-primary-800",
        "border",
        "border-primary-200",
      );
    });

    it("should render secondary variant with gray styling", () => {
      render(<Badge variant="secondary">Secondary Badge</Badge>);

      const badge = screen.getByText("Secondary Badge");
      expect(badge).toHaveClass(
        "bg-gray-50",
        "text-gray-700",
        "border",
        "border-gray-200",
      );
    });

    it("should render outline variant with border styling", () => {
      render(<Badge variant="outline">Outline Badge</Badge>);

      const badge = screen.getByText("Outline Badge");
      expect(badge).toHaveClass(
        "border-2",
        "border-gray-300",
        "text-gray-700",
        "bg-white",
      );
      expect(badge).toHaveClass(
        "hover:border-gray-400",
        "transition-colors",
        "duration-200",
      );
    });

    it("should render success variant with green gradient", () => {
      render(<Badge variant="success">Success Badge</Badge>);

      const badge = screen.getByText("Success Badge");
      expect(badge).toHaveClass(
        "bg-gradient-to-r",
        "from-success-50",
        "to-success-100",
      );
      expect(badge).toHaveClass(
        "text-success-800",
        "border",
        "border-success-200",
      );
    });

    it("should render warning variant with yellow gradient", () => {
      render(<Badge variant="warning">Warning Badge</Badge>);

      const badge = screen.getByText("Warning Badge");
      expect(badge).toHaveClass(
        "bg-gradient-to-r",
        "from-warning-50",
        "to-warning-100",
      );
      expect(badge).toHaveClass(
        "text-warning-800",
        "border",
        "border-warning-200",
      );
    });

    it("should render error variant with red gradient", () => {
      render(<Badge variant="error">Error Badge</Badge>);

      const badge = screen.getByText("Error Badge");
      expect(badge).toHaveClass(
        "bg-gradient-to-r",
        "from-error-50",
        "to-error-100",
      );
      expect(badge).toHaveClass("text-error-800", "border", "border-error-200");
    });
  });

  describe("Role Variants", () => {
    it("should render admin variant with enhanced red styling", () => {
      render(<Badge variant="admin">System Admin</Badge>);

      const badge = screen.getByText("System Admin");
      expect(badge).toHaveClass(
        "bg-gradient-to-r",
        "from-error-50",
        "to-error-100",
      );
      expect(badge).toHaveClass("text-error-800", "border", "border-error-200");
      expect(badge).toHaveClass("ring-1", "ring-error-200");
    });

    it("should render user variant with enhanced blue styling", () => {
      render(<Badge variant="user">Company Admin</Badge>);

      const badge = screen.getByText("Company Admin");
      expect(badge).toHaveClass(
        "bg-gradient-to-r",
        "from-primary-50",
        "to-primary-100",
      );
      expect(badge).toHaveClass(
        "text-primary-800",
        "border",
        "border-primary-200",
      );
      expect(badge).toHaveClass("ring-1", "ring-primary-200");
    });

    it("should render tech variant with enhanced purple styling", () => {
      render(<Badge variant="tech">Company Tech</Badge>);

      const badge = screen.getByText("Company Tech");
      expect(badge).toHaveClass(
        "bg-gradient-to-r",
        "from-purple-50",
        "to-purple-100",
      );
      expect(badge).toHaveClass(
        "text-purple-800",
        "border",
        "border-purple-200",
      );
      expect(badge).toHaveClass("ring-1", "ring-purple-200");
    });
  });

  describe("Sizes", () => {
    it("should render small size with correct padding and font", () => {
      render(<Badge size="sm">Small Badge</Badge>);

      const badge = screen.getByText("Small Badge");
      expect(badge).toHaveClass(
        "px-2.5",
        "py-1",
        "text-xs",
        "font-semibold",
        "tracking-wide",
      );
    });

    it("should render medium size with correct padding and font", () => {
      render(<Badge size="md">Medium Badge</Badge>);

      const badge = screen.getByText("Medium Badge");
      expect(badge).toHaveClass(
        "px-3",
        "py-1.5",
        "text-sm",
        "font-semibold",
        "tracking-wide",
      );
    });
  });

  describe("Icon", () => {
    it("should render badge with icon", () => {
      const icon = <span data-testid="badge-icon">⭐</span>;
      render(<Badge icon={icon}>Badge with Icon</Badge>);

      const iconElement = screen.getByTestId("badge-icon");

      expect(iconElement).toBeInTheDocument();
      expect(iconElement.parentElement).toHaveClass("mr-1.5", "-ml-0.5");
    });

    it("should render badge without icon when not provided", () => {
      render(<Badge>Badge without Icon</Badge>);

      const badge = screen.getByText("Badge without Icon");
      expect(badge).toBeInTheDocument();
      expect(
        badge.querySelector('[data-testid="badge-icon"]'),
      ).not.toBeInTheDocument();
    });
  });

  describe("Removable Badge", () => {
    it("should render removable badge with remove button", () => {
      const handleRemove = vi.fn();
      render(
        <Badge removable onRemove={handleRemove}>
          Removable Badge
        </Badge>,
      );

      const badge = screen.getByText("Removable Badge");
      const removeButton = screen.getByRole("button", { name: "Remove" });

      expect(badge).toBeInTheDocument();
      expect(removeButton).toBeInTheDocument();
      expect(removeButton).toHaveClass("ml-1.5", "-mr-0.5", "h-4", "w-4");
      expect(removeButton).toHaveClass(
        "focus:ring-2",
        "focus:ring-current",
        "focus:ring-opacity-20",
      );
    });

    it("should call onRemove when remove button is clicked", () => {
      const handleRemove = vi.fn();
      render(
        <Badge removable onRemove={handleRemove}>
          Clickable Badge
        </Badge>,
      );

      const removeButton = screen.getByRole("button", { name: "Remove" });
      removeButton.click();

      expect(handleRemove).toHaveBeenCalledOnce();
    });

    it("should not render remove button when not removable", () => {
      render(<Badge>Non-removable Badge</Badge>);

      const badge = screen.getByText("Non-removable Badge");
      expect(badge).toBeInTheDocument();
      expect(
        screen.queryByRole("button", { name: "Remove" }),
      ).not.toBeInTheDocument();
    });

    it("should not render remove button when removable but no onRemove handler", () => {
      render(<Badge removable>Badge without Handler</Badge>);

      const badge = screen.getByText("Badge without Handler");
      expect(badge).toBeInTheDocument();
      expect(
        screen.queryByRole("button", { name: "Remove" }),
      ).not.toBeInTheDocument();
    });
  });

  describe("Enhanced Styling", () => {
    it("should have transition classes", () => {
      render(<Badge>Animated Badge</Badge>);

      const badge = screen.getByText("Animated Badge");
      expect(badge).toHaveClass("transition-all", "duration-200");
    });
  });

  describe("Complete Badge with All Features", () => {
    it("should render complete badge with icon and remove functionality", () => {
      const icon = <span data-testid="complete-icon">🏷️</span>;
      const handleRemove = vi.fn();

      render(
        <Badge
          variant="primary"
          size="md"
          icon={icon}
          removable
          onRemove={handleRemove}
        >
          Complete Badge
        </Badge>,
      );

      const badge = screen.getByText("Complete Badge");
      const iconElement = screen.getByTestId("complete-icon");
      const removeButton = screen.getByRole("button", { name: "Remove" });

      expect(badge).toBeInTheDocument();
      expect(iconElement).toBeInTheDocument();
      expect(removeButton).toBeInTheDocument();

      // Verify primary variant styling
      expect(badge).toHaveClass(
        "bg-gradient-to-r",
        "from-primary-50",
        "to-primary-100",
      );
    });
  });
});
