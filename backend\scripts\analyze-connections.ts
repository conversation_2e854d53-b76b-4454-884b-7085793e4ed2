#!/usr/bin/env ts-node

import { Client } from 'pg';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from root directory
dotenv.config({ path: path.resolve(__dirname, '../../.env') });
dotenv.config({
  path: path.resolve(__dirname, '../../.env.local'),
  override: true,
});

async function analyzeConnections() {
  const adminUrl = process.env.TEST_DATABASE_ADMIN_URL;

  if (!adminUrl) {
    console.error(
      '❌ TEST_DATABASE_ADMIN_URL environment variable is required'
    );
    process.exit(1);
  }

  const client = new Client({
    connectionString: adminUrl,
    ssl: {
      rejectUnauthorized: false,
    },
  });

  try {
    console.log('🔗 Connecting to database...');
    await client.connect();

    // Get detailed connection analysis
    const result = await client.query(`
      SELECT 
        datname,
        pid,
        usename,
        application_name,
        client_addr,
        client_hostname,
        client_port,
        backend_start,
        query_start,
        state_change,
        state,
        query,
        backend_type,
        EXTRACT(EPOCH FROM (NOW() - backend_start))::int as connection_age_seconds,
        EXTRACT(EPOCH FROM (NOW() - state_change))::int as state_age_seconds,
        -- Check if we can see the process details
        CASE 
          WHEN pg_backend_pid() = pid THEN 'SELF'
          ELSE 'OTHER'
        END as process_relation
      FROM pg_stat_activity
      WHERE datname LIKE 'tech_notes_test_%'
      ORDER BY backend_start
    `);

    if (result.rows.length === 0) {
      console.log('✨ No active connections to test databases found');
      return;
    }

    console.log(
      `\n🔍 Detailed Connection Analysis (${result.rows.length} connections):`
    );
    console.log('═'.repeat(100));

    result.rows.forEach((row, index) => {
      const ageHours = Math.floor(row.connection_age_seconds / 3600);
      const ageMinutes = Math.floor((row.connection_age_seconds % 3600) / 60);
      const stateAgeMinutes = Math.floor(row.state_age_seconds / 60);

      console.log(`\n${index + 1}. Database: ${row.datname}`);
      console.log(
        `   PID: ${row.pid} | User: ${row.usename} | Type: ${row.backend_type || 'unknown'}`
      );
      console.log(
        `   State: ${row.state || 'null'} | App: ${row.application_name || 'unknown'}`
      );
      console.log(
        `   Client: ${row.client_addr || 'unknown'}:${row.client_port || 'unknown'}`
      );
      console.log(
        `   Connection Age: ${ageHours}h ${ageMinutes}m | State Age: ${stateAgeMinutes}m`
      );
      console.log(`   Started: ${row.backend_start}`);
      console.log(
        `   Query: ${row.query ? row.query.substring(0, 100) + '...' : 'none'}`
      );
      console.log(`   Relation: ${row.process_relation}`);
    });

    console.log('\n🎯 Termination Analysis:');
    console.log('─'.repeat(50));

    // Check our current user and permissions
    const currentUserResult = await client.query(
      'SELECT current_user, session_user'
    );
    const currentUser = currentUserResult.rows[0];
    console.log(
      `Current User: ${currentUser.current_user} | Session User: ${currentUser.session_user}`
    );

    // Check if we have superuser privileges
    const superuserResult = await client.query(`
      SELECT rolsuper 
      FROM pg_roles 
      WHERE rolname = current_user
    `);
    const isSuperuser = superuserResult.rows[0]?.rolsuper || false;
    console.log(`Superuser: ${isSuperuser ? 'YES' : 'NO'}`);

    // Check connection ownership
    const ownedConnections = result.rows.filter(
      (row) => row.usename === currentUser.current_user
    );
    const otherConnections = result.rows.filter(
      (row) => row.usename !== currentUser.current_user
    );

    console.log(
      `Connections owned by us (${currentUser.current_user}): ${ownedConnections.length}`
    );
    console.log(`Connections owned by others: ${otherConnections.length}`);

    if (otherConnections.length > 0) {
      console.log("\n⚠️  Other users' connections:");
      otherConnections.forEach((row) => {
        console.log(`   - PID ${row.pid}: ${row.usename} to ${row.datname}`);
      });
      console.log('\n💡 This explains why pg_terminate_backend fails:');
      console.log(
        '   - You can only terminate your own connections without SUPERUSER privileges'
      );
      console.log('   - These connections belong to other users/processes');
    }

    // Test termination capability on our own connections
    if (ownedConnections.length > 0) {
      console.log('\n🧪 Testing termination on our own connections...');
      // Don't actually terminate, just check if we could
      console.log("   (This would work since they're owned by us)");
    }
  } catch (error) {
    console.error('❌ Analysis failed:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run the analysis
analyzeConnections().catch(console.error);
