import React from "react";
import HealthDashboard from "../../components/organisms/HealthDashboard";
import { healthService } from "../../services/api";
import { Activity } from "lucide-react";

export const HealthDashboardPage: React.FC = () => {
  return (
    <div className="space-y-8">
      <div className="text-center bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-100/50">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
            Health Dashboard
          </h1>
          <p className="mt-4 text-lg text-gray-600 leading-relaxed">
            Monitor system health and performance metrics in real-time
          </p>
          <div className="mt-4 inline-flex items-center px-3 py-1 rounded-full bg-white/60 border border-green-200/60">
            <Activity className="h-4 w-4 text-green-500 mr-2" />
            <span className="text-sm font-medium text-gray-700">
              Live Monitoring Active
            </span>
          </div>
        </div>
      </div>
      <HealthDashboard healthService={healthService} />
    </div>
  );
};
