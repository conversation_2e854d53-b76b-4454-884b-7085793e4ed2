import { ConflictError, ValidationError } from '../types/error.types.js';
import { Logger } from '../utils/logger.js';

import { TenantService } from './tenant.service.js';
import { UserService } from './user.service.js';

export interface OnboardingTenantData {
  name: string;
  slug: string;
}

export interface OnboardingUserData {
  clerkId: string;
  email: string;
  firstName?: string;
  lastName?: string;
  imageUrl?: string;
}

export interface OnboardingResult {
  user: {
    id: string;
    clerkId: string;
    tenantId: string;
    email: string;
    firstName?: string | null;
    lastName?: string | null;
    imageUrl?: string | null;
    createdAt: Date;
    updatedAt: Date;
  };
  tenant: {
    id: string;
    name: string;
    slug: string;
    createdAt: Date;
    updatedAt: Date;
  };
}

/**
 * Onboarding Service
 *
 * Handles the special case of tenant and user creation during onboarding
 * where no existing BackendAuthContext is available. This service operates with
 * elevated privileges to create the initial tenant and user.
 */
export class OnboardingService {
  constructor(
    private tenantService: TenantService,
    private userService: UserService,
    private logger: Logger
  ) {}

  /**
   * Create a new tenant and user during onboarding
   * This is a special operation that doesn't require existing BackendAuthContext
   */
  async createTenantAndUser(
    tenantData: OnboardingTenantData,
    userData: OnboardingUserData
  ): Promise<OnboardingResult> {
    this.logger.info('Starting onboarding process', {
      tenantSlug: tenantData.slug,
      userEmail: userData.email,
      clerkId: userData.clerkId,
    });

    // Validate tenant data
    if (!tenantData.name?.trim()) {
      throw new ValidationError('Tenant name is required');
    }

    if (!tenantData.slug?.trim()) {
      throw new ValidationError('Tenant slug is required');
    }

    // Validate slug format
    if (!/^[a-z0-9-]+$/.test(tenantData.slug)) {
      throw new ValidationError(
        'Tenant slug must contain only lowercase letters, numbers, and hyphens'
      );
    }

    // Validate user data
    if (!userData.clerkId?.trim()) {
      throw new ValidationError('Clerk ID is required');
    }

    if (!userData.email?.trim()) {
      throw new ValidationError('Email is required');
    }

    try {
      // Check if tenant slug is available
      const isSlugAvailable = await this.tenantService.isSlugAvailable(
        tenantData.slug
      );
      if (!isSlugAvailable) {
        throw new ConflictError(
          `Tenant slug '${tenantData.slug}' is already taken`
        );
      }

      // Create a system-level context for onboarding operations
      // This is the only place where we create a synthetic context
      const onboardingContext = {
        id: 'onboarding-system',
        clerkId: userData.clerkId,
        tenantId: 'system-onboarding',
        email: userData.email,
        firstName: userData.firstName || null,
        lastName: userData.lastName || null,
        imageUrl: userData.imageUrl || null,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        canBypassTenantScope: true, // System context for onboarding
      };

      // Create tenant first
      const tenant = await this.tenantService.createTenant(
        {
          name: tenantData.name,
          slug: tenantData.slug,
        },
        onboardingContext
      );

      this.logger.info('Tenant created during onboarding', {
        tenantId: tenant.id,
        tenantSlug: tenant.slug,
      });

      // Create user with the new tenant
      const user = await this.userService.createUser({
        clerkId: userData.clerkId,
        tenantId: tenant.id,
        email: userData.email,
        firstName: userData.firstName || undefined,
        lastName: userData.lastName || undefined,
        imageUrl: userData.imageUrl || undefined,
        isActive: true,
      });

      this.logger.info('User created during onboarding', {
        userId: user.id,
        tenantId: tenant.id,
        email: user.email,
      });

      return {
        user: {
          id: user.id,
          clerkId: user.clerkId,
          tenantId: user.tenantId,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          imageUrl: user.imageUrl,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
        tenant: {
          id: tenant.id,
          name: tenant.name,
          slug: tenant.slug,
          createdAt: tenant.createdAt,
          updatedAt: tenant.updatedAt,
        },
      };
    } catch (error) {
      this.logger.error('Onboarding failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        tenantSlug: tenantData.slug,
        userEmail: userData.email,
        clerkId: userData.clerkId,
      });

      throw error;
    }
  }

  /**
   * Check if a tenant slug is available for onboarding
   */
  async isSlugAvailable(slug: string): Promise<boolean> {
    if (!slug?.trim()) {
      return false;
    }

    // Validate slug format
    if (!/^[a-z0-9-]+$/.test(slug)) {
      return false;
    }

    return this.tenantService.isSlugAvailable(slug);
  }
}
