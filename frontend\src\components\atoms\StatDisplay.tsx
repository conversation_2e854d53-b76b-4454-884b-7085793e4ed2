import React from "react";
import { clsx } from "clsx";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";

export interface StatDisplayProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Stat label/title */
  label: string;
  /** Main stat value */
  value: string | number;
  /** Optional change/trend information */
  change?: {
    value: string | number;
    trend: "up" | "down" | "neutral";
    period?: string;
    percentage?: boolean;
  };
  /** Optional icon */
  icon?: React.ReactNode;
  /** Visual variant */
  variant?: "default" | "card" | "minimal" | "highlighted";
  /** Size variant */
  size?: "sm" | "md" | "lg" | "xl";
  /** Color theme */
  theme?: "default" | "primary" | "success" | "warning" | "error";
  /** Layout orientation */
  orientation?: "vertical" | "horizontal";
  /** Whether to show trend icon */
  showTrendIcon?: boolean;
  /** Custom prefix for the value (e.g., "$", "#") */
  valuePrefix?: string;
  /** Custom suffix for the value (e.g., "%", "K", "M") */
  valueSuffix?: string;
}

const sizeClasses = {
  sm: {
    container: "p-3",
    value: "text-lg font-bold",
    label: "text-xs font-medium",
    change: "text-xs",
    icon: "h-4 w-4",
    iconContainer: "h-8 w-8",
  },
  md: {
    container: "p-4",
    value: "text-2xl font-bold",
    label: "text-sm font-medium",
    change: "text-sm",
    icon: "h-5 w-5",
    iconContainer: "h-10 w-10",
  },
  lg: {
    container: "p-6",
    value: "text-3xl font-bold",
    label: "text-base font-medium",
    change: "text-base",
    icon: "h-6 w-6",
    iconContainer: "h-12 w-12",
  },
  xl: {
    container: "p-8",
    value: "text-4xl font-bold",
    label: "text-lg font-medium",
    change: "text-lg",
    icon: "h-8 w-8",
    iconContainer: "h-16 w-16",
  },
} as const;

const variantClasses = {
  default: "bg-white",
  card: "bg-white shadow-xs hover:shadow-sm transition-shadow rounded-lg border border-gray-100",
  minimal: "bg-transparent",
  highlighted:
    "bg-gradient-to-br from-primary-50 to-primary-100/50 border border-primary-200 rounded-lg",
} as const;

const themeClasses = {
  default: {
    value: "text-gray-900",
    label: "text-gray-600",
    icon: "bg-gray-100 text-gray-600",
  },
  primary: {
    value: "text-primary-900",
    label: "text-primary-700",
    icon: "bg-primary-100 text-primary-600",
  },
  success: {
    value: "text-green-900",
    label: "text-green-700",
    icon: "bg-green-100 text-green-600",
  },
  warning: {
    value: "text-yellow-900",
    label: "text-yellow-700",
    icon: "bg-yellow-100 text-yellow-600",
  },
  error: {
    value: "text-red-900",
    label: "text-red-700",
    icon: "bg-red-100 text-red-600",
  },
} as const;

const trendClasses = {
  up: "text-green-600",
  down: "text-red-600",
  neutral: "text-gray-600",
} as const;

const orientationClasses = {
  vertical: "flex-col text-center",
  horizontal: "flex-row items-center justify-between",
} as const;

export const StatDisplay: React.FC<StatDisplayProps> = ({
  label,
  value,
  change,
  icon,
  variant = "default",
  size = "md",
  theme = "default",
  orientation = "vertical",
  showTrendIcon = true,
  valuePrefix = "",
  valueSuffix = "",
  className,
  ...props
}) => {
  const sizeConfig = sizeClasses[size];
  const themeConfig = themeClasses[theme];

  const getTrendIcon = (trend: "up" | "down" | "neutral") => {
    if (!showTrendIcon) return null;

    const iconClass = clsx(sizeConfig.icon, trendClasses[trend]);

    switch (trend) {
      case "up":
        return <TrendingUp className={iconClass} />;
      case "down":
        return <TrendingDown className={iconClass} />;
      case "neutral":
      default:
        return <Minus className={iconClass} />;
    }
  };

  const formatChangeValue = (
    changeValue: string | number,
    percentage?: boolean,
  ) => {
    const prefix =
      typeof changeValue === "number" && changeValue > 0 ? "+" : "";
    const suffix = percentage ? "%" : "";
    return `${prefix}${changeValue}${suffix}`;
  };

  return (
    <div
      className={clsx(
        "flex",
        orientationClasses[orientation],
        variantClasses[variant],
        sizeConfig.container,
        className,
      )}
      data-testid="stat-display-container"
      {...props}
    >
      {/* Main content */}
      <div
        className={clsx(
          "flex",
          orientation === "vertical"
            ? "flex-col items-center space-y-2"
            : "flex-col flex-1",
        )}
      >
        <div
          className={clsx(sizeConfig.label, themeConfig.label, "tracking-wide")}
        >
          {label.toUpperCase()}
        </div>

        <div
          className={clsx(
            sizeConfig.value,
            themeConfig.value,
            "tracking-tight",
          )}
        >
          {valuePrefix}
          {value}
          {valueSuffix}
        </div>

        {change && (
          <div
            className={clsx(
              "flex items-center gap-1",
              sizeConfig.change,
              orientation === "vertical" && "justify-center",
            )}
          >
            {getTrendIcon(change.trend)}
            <span className={clsx("font-medium", trendClasses[change.trend])}>
              {formatChangeValue(change.value, change.percentage)}
            </span>
            {change.period && (
              <span className="text-gray-500 ml-1">{change.period}</span>
            )}
          </div>
        )}
      </div>

      {/* Icon */}
      {icon && orientation === "horizontal" && (
        <div
          className={clsx(
            "flex items-center justify-center rounded-lg flex-shrink-0",
            sizeConfig.iconContainer,
            themeConfig.icon,
          )}
        >
          <div className={sizeConfig.icon}>{icon}</div>
        </div>
      )}

      {icon && orientation === "vertical" && (
        <div
          className={clsx(
            "flex items-center justify-center rounded-lg",
            sizeConfig.iconContainer,
            themeConfig.icon,
          )}
        >
          <div className={sizeConfig.icon}>{icon}</div>
        </div>
      )}
    </div>
  );
};
