import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { Users, Shield, Zap, Star } from "lucide-react";
import { FeatureGrid } from "./FeatureGrid";

const mockFeatures = [
  {
    id: "1",
    title: "Easy Setup",
    description: "Get started in minutes with our simple setup process.",
    icon: <Users data-testid="users-icon" />,
  },
  {
    id: "2",
    title: "Secure & Reliable",
    description: "Enterprise-grade security with 99.9% uptime guarantee.",
    icon: <Shield data-testid="shield-icon" />,
  },
  {
    id: "3",
    title: "Lightning Fast",
    description: "Optimized performance for the best user experience.",
    icon: <Zap data-testid="zap-icon" />,
  },
];

const defaultProps = {
  features: mockFeatures,
};

describe("FeatureGrid", () => {
  describe("Basic Rendering", () => {
    it("should render all features", () => {
      render(<FeatureGrid {...defaultProps} />);

      expect(screen.getByText("Easy Setup")).toBeInTheDocument();
      expect(screen.getByText("Secure & Reliable")).toBeInTheDocument();
      expect(screen.getByText("Lightning Fast")).toBeInTheDocument();
    });

    it("should render with custom className", () => {
      render(<FeatureGrid {...defaultProps} className="custom-grid" />);

      const container = screen.getByText("Easy Setup").closest(".custom-grid");
      expect(container).toBeInTheDocument();
    });

    it("should apply default grid layout", () => {
      render(<FeatureGrid {...defaultProps} />);

      const gridContainer = screen
        .getByText("Easy Setup")
        .closest('div[class*="grid"]');
      expect(gridContainer).toHaveClass(
        "grid",
        "gap-6",
        "grid-cols-1",
        "md:grid-cols-2",
        "lg:grid-cols-3",
      );
    });
  });

  describe("Layout Variants", () => {
    it("should render grid layout with specified columns", () => {
      render(<FeatureGrid {...defaultProps} layout="grid" columns={2} />);

      const gridContainer = screen
        .getByText("Easy Setup")
        .closest('div[class*="grid"]');
      expect(gridContainer).toHaveClass(
        "grid",
        "grid-cols-1",
        "md:grid-cols-2",
      );
    });

    it("should render masonry layout", () => {
      render(<FeatureGrid {...defaultProps} layout="masonry" />);

      const container = screen
        .getByText("Easy Setup")
        .closest('div[class*="columns"]');
      expect(container).toHaveClass(
        "columns-1",
        "md:columns-2",
        "lg:columns-3",
      );
    });

    it("should render list layout", () => {
      render(<FeatureGrid {...defaultProps} layout="list" />);

      const container = screen
        .getByText("Easy Setup")
        .closest('div[class*="space-y"]');
      expect(container).toHaveClass("space-y-4");
    });

    it("should render carousel layout", () => {
      render(<FeatureGrid {...defaultProps} layout="carousel" />);

      // Find the carousel container (parent of the feature cards)
      const carouselContainer = screen
        .getByText("Easy Setup")
        .closest('div[class*="overflow-x-auto"]');
      expect(carouselContainer).toHaveClass(
        "flex",
        "gap-6",
        "overflow-x-auto",
        "snap-x",
        "snap-mandatory",
      );
    });
  });

  describe("Spacing Options", () => {
    it("should apply tight spacing", () => {
      render(<FeatureGrid {...defaultProps} spacing="tight" />);

      const gridContainer = screen
        .getByText("Easy Setup")
        .closest('div[class*="gap-4"]');
      expect(gridContainer).toHaveClass("gap-4");
    });

    it("should apply normal spacing by default", () => {
      render(<FeatureGrid {...defaultProps} />);

      const gridContainer = screen
        .getByText("Easy Setup")
        .closest('div[class*="gap-6"]');
      expect(gridContainer).toHaveClass("gap-6");
    });

    it("should apply loose spacing", () => {
      render(<FeatureGrid {...defaultProps} spacing="loose" />);

      const gridContainer = screen
        .getByText("Easy Setup")
        .closest('div[class*="gap-8"]');
      expect(gridContainer).toHaveClass("gap-8");
    });
  });

  describe("Header Section", () => {
    it("should render title and subtitle", () => {
      render(
        <FeatureGrid
          {...defaultProps}
          title="Our Features"
          subtitle="Discover what makes our platform special"
        />,
      );

      expect(screen.getByRole("heading", { level: 2 })).toHaveTextContent(
        "Our Features",
      );
      expect(
        screen.getByText("Discover what makes our platform special"),
      ).toBeInTheDocument();
    });

    it("should render badge", () => {
      const badge = {
        text: "New Features",
        variant: "primary" as const,
        icon: <Star data-testid="star-icon" />,
      };

      render(<FeatureGrid {...defaultProps} badge={badge} />);

      expect(screen.getByText("New Features")).toBeInTheDocument();
      expect(screen.getByTestId("star-icon")).toBeInTheDocument();
    });

    it("should not render header when no title, subtitle, or badge", () => {
      render(<FeatureGrid {...defaultProps} />);

      expect(
        screen.queryByRole("heading", { level: 2 }),
      ).not.toBeInTheDocument();
    });
  });

  describe("Card Properties", () => {
    it("should apply card variant to all features", () => {
      render(<FeatureGrid {...defaultProps} cardVariant="elevated" />);

      const featureCards = screen.getAllByTestId("feature-card-container");
      featureCards.forEach((card) => {
        expect(card).toHaveClass("shadow-sm", "hover:shadow-base");
      });
    });

    it("should apply card size to all features", () => {
      render(<FeatureGrid {...defaultProps} cardSize="lg" />);

      const featureCards = screen.getAllByTestId("feature-card-container");
      featureCards.forEach((card) => {
        expect(card).toHaveClass("p-8");
      });
    });

    it("should apply card orientation to all features", () => {
      render(<FeatureGrid {...defaultProps} cardOrientation="horizontal" />);

      const featureCards = screen.getAllByTestId("feature-card-container");
      featureCards.forEach((card) => {
        expect(card).toHaveClass(
          "text-left",
          "flex",
          "items-start",
          "space-x-4",
        );
      });
    });

    it("should make all cards interactive when specified", () => {
      render(<FeatureGrid {...defaultProps} interactive />);

      const featureCards = screen.getAllByTestId("feature-card-container");
      featureCards.forEach((card) => {
        expect(card).toHaveClass(
          "cursor-pointer",
          "transition-all",
          "duration-200",
        );
      });
    });
  });

  describe("Custom Render Function", () => {
    it("should use custom render function when provided", () => {
      const customRender = vi.fn((feature, index) => (
        <div key={feature.id} data-testid={`custom-feature-${index}`}>
          Custom: {feature.title}
        </div>
      ));

      render(<FeatureGrid {...defaultProps} renderFeature={customRender} />);

      expect(screen.getByTestId("custom-feature-0")).toBeInTheDocument();
      expect(screen.getByText("Custom: Easy Setup")).toBeInTheDocument();
      expect(customRender).toHaveBeenCalledTimes(3);
    });
  });

  describe("Collapsible Functionality", () => {
    const manyFeatures = [
      ...mockFeatures,
      { id: "4", title: "Feature 4", description: "Description 4" },
      { id: "5", title: "Feature 5", description: "Description 5" },
      { id: "6", title: "Feature 6", description: "Description 6" },
    ];

    it("should show only initial count when collapsible", () => {
      render(
        <FeatureGrid
          features={manyFeatures}
          collapsible={{ initialCount: 3 }}
        />,
      );

      expect(screen.getByText("Easy Setup")).toBeInTheDocument();
      expect(screen.getByText("Secure & Reliable")).toBeInTheDocument();
      expect(screen.getByText("Lightning Fast")).toBeInTheDocument();
      expect(screen.queryByText("Feature 4")).not.toBeInTheDocument();

      const showMoreButton = screen.getByRole("button", {
        name: /show all 6 features/i,
      });
      expect(showMoreButton).toBeInTheDocument();
    });

    it("should expand to show all features when show more clicked", () => {
      render(
        <FeatureGrid
          features={manyFeatures}
          collapsible={{ initialCount: 3 }}
        />,
      );

      const showMoreButton = screen.getByRole("button", {
        name: /show all 6 features/i,
      });
      fireEvent.click(showMoreButton);

      expect(screen.getByText("Feature 4")).toBeInTheDocument();
      expect(screen.getByText("Feature 5")).toBeInTheDocument();
      expect(screen.getByText("Feature 6")).toBeInTheDocument();

      const showLessButton = screen.getByRole("button", { name: /show less/i });
      expect(showLessButton).toBeInTheDocument();
    });

    it("should use custom show more/less text", () => {
      render(
        <FeatureGrid
          features={manyFeatures}
          collapsible={{
            initialCount: 3,
            showMoreText: "View All Features",
            showLessText: "Hide Extra Features",
          }}
        />,
      );

      expect(
        screen.getByRole("button", { name: "View All Features" }),
      ).toBeInTheDocument();

      fireEvent.click(
        screen.getByRole("button", { name: "View All Features" }),
      );
      expect(
        screen.getByRole("button", { name: "Hide Extra Features" }),
      ).toBeInTheDocument();
    });

    it("should not show toggle button when features count is less than initial count", () => {
      render(
        <FeatureGrid
          features={mockFeatures}
          collapsible={{ initialCount: 5 }}
        />,
      );

      expect(screen.queryByRole("button")).not.toBeInTheDocument();
    });
  });

  describe("Carousel Layout Specific", () => {
    it("should apply carousel-specific classes to feature cards", () => {
      render(<FeatureGrid {...defaultProps} layout="carousel" />);

      const featureContainers = screen
        .getAllByText(/Easy Setup|Secure & Reliable|Lightning Fast/)
        .map((text) => text.closest('div[class*="flex-shrink-0"]'));

      featureContainers.forEach((container) => {
        expect(container).toHaveClass("flex-shrink-0", "w-80", "snap-start");
      });
    });
  });

  describe("Masonry Layout Specific", () => {
    it("should apply masonry-specific classes to feature cards", () => {
      render(<FeatureGrid {...defaultProps} layout="masonry" />);

      const featureContainers = screen
        .getAllByText(/Easy Setup|Secure & Reliable|Lightning Fast/)
        .map((text) => text.closest('div[class*="break-inside-avoid"]'));

      featureContainers.forEach((container) => {
        expect(container).toHaveClass("break-inside-avoid");
      });
    });
  });

  describe("Accessibility", () => {
    it("should have proper heading hierarchy", () => {
      render(<FeatureGrid {...defaultProps} title="Our Features" />);

      const sectionHeading = screen.getByRole("heading", { level: 2 });
      expect(sectionHeading).toBeInTheDocument();

      const featureHeadings = screen.getAllByRole("heading", { level: 3 });
      expect(featureHeadings).toHaveLength(3);
    });

    it("should have proper button accessibility for toggle", () => {
      const manyFeatures = [
        ...mockFeatures,
        { id: "4", title: "Feature 4", description: "Description 4" },
      ];

      render(
        <FeatureGrid
          features={manyFeatures}
          collapsible={{ initialCount: 2 }}
        />,
      );

      const toggleButton = screen.getByRole("button");
      expect(toggleButton).toHaveClass(
        "focus:outline-none",
        "focus:ring-2",
        "focus:ring-primary-500",
      );
    });
  });

  describe("Responsive Design", () => {
    it("should have responsive grid classes", () => {
      render(<FeatureGrid {...defaultProps} columns={4} />);

      const gridContainer = screen
        .getByText("Easy Setup")
        .closest('div[class*="grid"]');
      expect(gridContainer).toHaveClass(
        "grid-cols-1",
        "md:grid-cols-2",
        "lg:grid-cols-4",
      );
    });

    it("should have responsive typography in header", () => {
      render(<FeatureGrid {...defaultProps} title="Our Features" />);

      const heading = screen.getByRole("heading", { level: 2 });
      expect(heading).toHaveClass("text-3xl", "sm:text-4xl");
    });
  });
});
