import React, { useState, useEffect } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import {
  Modal,
  Button,
  Input,
  FormField,
  Alert,
  Badge,
} from "../../../components";
import { useTypedApi } from "../../../services/api-client";
import type { VehicleMake } from "../../../services/api-client";
import { Settings } from "lucide-react";

interface CreateModelModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedMake: VehicleMake | null;
}

export const CreateModelModal: React.FC<CreateModelModalProps> = ({
  isOpen,
  onClose,
  selectedMake,
}) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [name, setName] = useState("");
  const [makeId, setMakeId] = useState("");
  const [selectedYearIds, setSelectedYearIds] = useState<string[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch makes and years for selection
  const { data: makesResponse } = useQuery({
    queryKey: ["vehicle-makes"],
    queryFn: () => api.vehicleHierarchy.getMakes(),
    enabled: isOpen,
  });

  const { data: yearsResponse } = useQuery({
    queryKey: ["vehicle-years"],
    queryFn: () => api.vehicleHierarchy.getYears(),
    enabled: isOpen,
  });

  const createModelMutation = useMutation({
    mutationFn: async (modelData: { name: string; makeId: string }) => {
      const response = await api.vehicleHierarchy.createModel(modelData);

      // If years are selected, associate them with the model
      if (selectedYearIds.length > 0) {
        await api.vehicleHierarchy.associateModelWithYears(response.data.id, {
          yearIds: selectedYearIds,
        });
      }

      return response;
    },
    onSuccess: () => {
      toast.success("Model created successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      handleClose();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create model");
    },
  });

  // Set the selected make when modal opens
  useEffect(() => {
    if (selectedMake && isOpen) {
      setMakeId(selectedMake.id);
    }
  }, [selectedMake, isOpen]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = "Model name is required";
    } else if (name.trim().length < 2) {
      newErrors.name = "Model name must be at least 2 characters";
    } else if (name.trim().length > 100) {
      newErrors.name = "Model name must be less than 100 characters";
    }

    if (!makeId) {
      newErrors.makeId = "Please select a make";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    createModelMutation.mutate({
      name: name.trim(),
      makeId,
    });
  };

  const handleClose = () => {
    setName("");
    setMakeId("");
    setSelectedYearIds([]);
    setErrors({});
    onClose();
  };

  const toggleYear = (yearId: string) => {
    setSelectedYearIds((prev) =>
      prev.includes(yearId)
        ? prev.filter((id) => id !== yearId)
        : [...prev, yearId],
    );
  };

  const selectAllYears = () => {
    if (yearsResponse?.data) {
      setSelectedYearIds(yearsResponse.data.map((year) => year.id));
    }
  };

  const clearAllYears = () => {
    setSelectedYearIds([]);
  };

  // Common model names for quick selection (based on selected make)
  const getModelSuggestions = (makeName: string): string[] => {
    const suggestions: Record<string, string[]> = {
      "Rockwood GeoPro": ["G19FD", "G20BHS", "G22S", "G24RK"],
      "Forest River Cherokee": ["16BH", "17BH", "19RR", "22RR", "26DBH"],
      "Keystone Passport": ["175BH", "195RB", "216RD", "239ML"],
      "Jayco Jay Flight": ["21QB", "24RBS", "26BH", "28BHBE"],
      "Grand Design Imagine": ["17MKE", "19RLE", "21BHE", "24MPE"],
    };

    return suggestions[makeName] || [];
  };

  const selectedMakeName =
    makesResponse?.data.find((make) => make.id === makeId)?.name || "";
  const modelSuggestions = getModelSuggestions(selectedMakeName);

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Create New Model"
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex items-center space-x-3 text-gray-600 mb-4">
          <Settings className="h-5 w-5" />
          <span>Add a new vehicle model to the hierarchy</span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left Column */}
          <div className="space-y-4">
            <FormField label="Make" error={errors.makeId} required>
              <select
                value={makeId}
                onChange={(e) => setMakeId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                disabled={!!selectedMake}
              >
                <option value="">Select a make...</option>
                {makesResponse?.data.map((make) => (
                  <option key={make.id} value={make.id}>
                    {make.name}
                  </option>
                ))}
              </select>
            </FormField>

            <FormField label="Model Name" error={errors.name} required>
              <Input
                type="text"
                placeholder="e.g., G20BHS"
                value={name}
                onChange={(e) => setName(e.target.value)}
                error={errors.name}
                maxLength={100}
              />
            </FormField>

            {/* Model Suggestions */}
            {modelSuggestions.length > 0 && (
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Common Models for {selectedMakeName}
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {modelSuggestions.map((suggestion) => (
                    <Button
                      key={suggestion}
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setName(suggestion)}
                      className="text-xs"
                    >
                      {suggestion}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Right Column - Year Selection */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Associate with Years (Optional)
              </label>
              <p className="text-xs text-gray-500 mb-3">
                Select which years this model was available
              </p>

              <div className="flex items-center space-x-2 mb-3">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={selectAllYears}
                >
                  Select All
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={clearAllYears}
                >
                  Clear All
                </Button>
                <Badge variant="secondary">
                  {selectedYearIds.length} selected
                </Badge>
              </div>

              <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-lg p-3 space-y-2">
                {yearsResponse?.data
                  .sort((a, b) => b.year - a.year)
                  .map((year) => (
                    <label
                      key={year.id}
                      className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 p-1 rounded"
                    >
                      <input
                        type="checkbox"
                        checked={selectedYearIds.includes(year.id)}
                        onChange={() => toggleYear(year.id)}
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      <span className="text-sm">{year.year}</span>
                    </label>
                  ))}
              </div>
            </div>
          </div>
        </div>

        {createModelMutation.error && (
          <Alert variant="error">
            {createModelMutation.error.message || "Failed to create model"}
          </Alert>
        )}

        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={createModelMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={createModelMutation.isPending}
            className="flex items-center space-x-2"
          >
            {createModelMutation.isPending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Creating...</span>
              </>
            ) : (
              <>
                <Settings className="h-4 w-4" />
                <span>Create Model</span>
              </>
            )}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
