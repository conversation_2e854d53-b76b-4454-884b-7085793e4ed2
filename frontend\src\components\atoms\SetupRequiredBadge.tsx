import React from "react";
import { Tooltip } from "./Tooltip";

export interface SetupRequiredBadgeProps {
  className?: string;
  showTooltip?: boolean;
  tooltipContent?: string;
}

const defaultTooltipContent = "This feature requires a one-time setup fee";

export const SetupRequiredBadge: React.FC<SetupRequiredBadgeProps> = ({
  className,
  showTooltip = true,
  tooltipContent = defaultTooltipContent,
}) => {
  const asterisk = <span className={`text-gray-500 ${className}`}>*</span>;

  if (showTooltip) {
    return (
      <Tooltip content={tooltipContent} position="top">
        {asterisk}
      </Tooltip>
    );
  }

  return asterisk;
};
