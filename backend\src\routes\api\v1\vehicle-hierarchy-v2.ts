import { Router, NextFunction, Request, Response } from 'express';
import { z } from 'zod';

import { MiddlewareFactory } from '../../../middleware/middleware-factory.js';
import { createValidationMiddleware } from '../../../middleware/validation.middleware.js';
import { VehicleHierarchyV2Service } from '../../../services/vehicle-hierarchy-v2.service.js';
import { Logger } from '../../../utils/logger.js';
import { getRequestUser } from '../../../utils/request-types.js';

interface ServiceDependencies {
  vehicleHierarchyV2Service: VehicleHierarchyV2Service;
  middlewareFactory: MiddlewareFactory;
  logger: Logger;
}

// Validation schemas
const createBrandSchema = z.object({
  name: z.string().min(1).max(100).trim(),
  isActive: z.boolean().optional(),
});

const createSubBrandSchema = z.object({
  name: z.string().min(1).max(100).trim(),
  brandId: z.string().cuid(),
  isActive: z.boolean().optional(),
});

const createModelV2Schema = z.object({
  name: z.string().min(1).max(100).trim(),
  subBrandId: z.string().cuid(),
  isActive: z.boolean().optional(),
});

const associateModelYearsV2Schema = z.object({
  yearIds: z.array(z.string().cuid()).min(1),
});

const idParamSchema = z.object({
  id: z.string().cuid(),
});

const yearParamSchema = z.object({
  year: z.string().transform(Number).pipe(z.number().int().min(1900).max(2050)),
});

const updateBrandSchema = z.object({
  name: z.string().min(1).max(100).trim().optional(),
  isActive: z.boolean().optional(),
});

const updateSubBrandSchema = z.object({
  name: z.string().min(1).max(100).trim().optional(),
  isActive: z.boolean().optional(),
});

const updateModelV2Schema = z.object({
  name: z.string().min(1).max(100).trim().optional(),
  isActive: z.boolean().optional(),
});

export function createVehicleHierarchyV2Router(
  dependencies: ServiceDependencies
): Router {
  const { vehicleHierarchyV2Service, middlewareFactory, logger } = dependencies;
  const router = Router();

  const validate = createValidationMiddleware({ logger });

  // ===== BRAND ROUTES =====

  /**
   * GET /api/v1/vehicle-hierarchy-v2/brands
   * Get all brands for tenant
   */
  router.get(
    '/brands',
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const brands = await vehicleHierarchyV2Service.getBrandsByTenant(
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: brands,
          meta: {
            count: brands.length,
            tenantId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch brands', {
          error,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy-v2/brands
   * Create a new brand
   */
  router.post(
    '/brands',
    validate({ body: createBrandSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const brandData = req.body;

        const brand = await vehicleHierarchyV2Service.createBrand(
          brandData,
          tenantId,
          getRequestUser(req)!
        );

        res.status(201).json({
          data: brand,
          message: 'Brand created successfully',
        });
      } catch (error) {
        logger.error('Failed to create brand', {
          error,
          brandData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * PUT /api/v1/vehicle-hierarchy-v2/brands/:id
   * Update a brand
   */
  router.put(
    '/brands/:id',
    validate({
      params: idParamSchema,
      body: updateBrandSchema,
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id } = req.params;
        const updateData = req.body;

        const brand = await vehicleHierarchyV2Service.updateBrand(
          id,
          updateData,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: brand,
          message: 'Brand updated successfully',
        });
      } catch (error) {
        logger.error('Failed to update brand', {
          error,
          brandId: req.params.id,
          updateData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * DELETE /api/v1/vehicle-hierarchy-v2/brands/:id
   * Delete a brand
   */
  router.delete(
    '/brands/:id',
    validate({ params: idParamSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id } = req.params;

        await vehicleHierarchyV2Service.deleteBrand(
          id,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Brand deleted successfully',
        });
      } catch (error) {
        logger.error('Failed to delete brand', {
          error,
          brandId: req.params.id,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  // ===== SUB-BRAND ROUTES =====

  /**
   * GET /api/v1/vehicle-hierarchy-v2/sub-brands
   * Get all sub-brands for tenant (optionally filtered by brand)
   */
  router.get(
    '/sub-brands',
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { brandId } = req.query;

        const subBrands = await vehicleHierarchyV2Service.getSubBrandsByTenant(
          tenantId,
          brandId && typeof brandId === 'string' ? brandId : undefined,
          getRequestUser(req)!
        );

        res.json({
          data: subBrands,
          meta: {
            count: subBrands.length,
            tenantId,
            ...(brandId && { brandId }),
          },
        });
      } catch (error) {
        logger.error('Failed to fetch sub-brands', {
          error,
          tenantId: getRequestUser(req)?.tenantId,
          brandId: req.query.brandId,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/vehicle-hierarchy-v2/brands/:id/sub-brands
   * Get sub-brands by brand ID
   */
  router.get(
    '/brands/:id/sub-brands',
    validate({ params: idParamSchema }),
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id: brandId } = req.params;

        const subBrands = await vehicleHierarchyV2Service.getSubBrandsByBrand(
          brandId,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: subBrands,
          meta: {
            count: subBrands.length,
            brandId,
            tenantId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch sub-brands by brand', {
          error,
          brandId: req.params.id,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy-v2/sub-brands
   * Create a new sub-brand
   */
  router.post(
    '/sub-brands',
    validate({ body: createSubBrandSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const subBrandData = req.body;

        const subBrand = await vehicleHierarchyV2Service.createSubBrand(
          subBrandData,
          tenantId,
          getRequestUser(req)!
        );

        res.status(201).json({
          data: subBrand,
          message: 'Sub-brand created successfully',
        });
      } catch (error) {
        logger.error('Failed to create sub-brand', {
          error,
          subBrandData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * PUT /api/v1/vehicle-hierarchy-v2/sub-brands/:id
   * Update a sub-brand
   */
  router.put(
    '/sub-brands/:id',
    validate({
      params: idParamSchema,
      body: updateSubBrandSchema,
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id } = req.params;
        const updateData = req.body;

        const subBrand = await vehicleHierarchyV2Service.updateSubBrand(
          id,
          updateData,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: subBrand,
          message: 'Sub-brand updated successfully',
        });
      } catch (error) {
        logger.error('Failed to update sub-brand', {
          error,
          subBrandId: req.params.id,
          updateData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * DELETE /api/v1/vehicle-hierarchy-v2/sub-brands/:id
   * Delete a sub-brand
   */
  router.delete(
    '/sub-brands/:id',
    validate({ params: idParamSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id } = req.params;

        await vehicleHierarchyV2Service.deleteSubBrand(
          id,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Sub-brand deleted successfully',
        });
      } catch (error) {
        logger.error('Failed to delete sub-brand', {
          error,
          subBrandId: req.params.id,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  // ===== MODEL ROUTES =====

  /**
   * GET /api/v1/vehicle-hierarchy-v2/models
   * Get all models for tenant (optionally filtered by sub-brand)
   */
  router.get(
    '/models',
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { subBrandId } = req.query;

        const models = await vehicleHierarchyV2Service.getModelsByTenant(
          tenantId,
          subBrandId && typeof subBrandId === 'string' ? subBrandId : undefined,
          getRequestUser(req)!
        );

        res.json({
          data: models,
          meta: {
            count: models.length,
            tenantId,
            ...(subBrandId && { subBrandId }),
          },
        });
      } catch (error) {
        logger.error('Failed to fetch models', {
          error,
          tenantId: getRequestUser(req)?.tenantId,
          subBrandId: req.query.subBrandId,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/vehicle-hierarchy-v2/sub-brands/:id/models
   * Get models by sub-brand ID
   */
  router.get(
    '/sub-brands/:id/models',
    validate({ params: idParamSchema }),
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id: subBrandId } = req.params;

        const models = await vehicleHierarchyV2Service.getModelsBySubBrand(
          subBrandId,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: models,
          meta: {
            count: models.length,
            subBrandId,
            tenantId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch models by sub-brand', {
          error,
          subBrandId: req.params.id,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy-v2/models
   * Create a new model
   */
  router.post(
    '/models',
    validate({ body: createModelV2Schema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const modelData = req.body;

        const model = await vehicleHierarchyV2Service.createModel(
          modelData,
          tenantId,
          getRequestUser(req)!
        );

        res.status(201).json({
          data: model,
          message: 'Model created successfully',
        });
      } catch (error) {
        logger.error('Failed to create model', {
          error,
          modelData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * PUT /api/v1/vehicle-hierarchy-v2/models/:id
   * Update a model
   */
  router.put(
    '/models/:id',
    validate({
      params: idParamSchema,
      body: updateModelV2Schema,
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id } = req.params;
        const updateData = req.body;

        const model = await vehicleHierarchyV2Service.updateModel(
          id,
          updateData,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: model,
          message: 'Model updated successfully',
        });
      } catch (error) {
        logger.error('Failed to update model', {
          error,
          modelId: req.params.id,
          updateData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * DELETE /api/v1/vehicle-hierarchy-v2/models/:id
   * Delete a model
   */
  router.delete(
    '/models/:id',
    validate({ params: idParamSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id } = req.params;

        await vehicleHierarchyV2Service.deleteModel(
          id,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Model deleted successfully',
        });
      } catch (error) {
        logger.error('Failed to delete model', {
          error,
          modelId: req.params.id,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  // ===== MODEL-YEAR ASSOCIATION ROUTES =====

  /**
   * GET /api/v1/vehicle-hierarchy-v2/models/:id/years
   * Get years associated with a model
   */
  router.get(
    '/models/:id/years',
    validate({ params: idParamSchema }),
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id: modelId } = req.params;

        const years = await vehicleHierarchyV2Service.getYearsByModel(
          modelId,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: years,
          meta: {
            count: years.length,
            modelId,
            tenantId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch years for model', {
          error,
          modelId: req.params.id,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy-v2/models/:id/years/associate
   * Associate a model with years
   */
  router.post(
    '/models/:id/years/associate',
    validate({
      params: idParamSchema,
      body: associateModelYearsV2Schema,
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id: modelId } = req.params;
        const { yearIds } = req.body;

        await vehicleHierarchyV2Service.associateModelWithYears(
          modelId,
          yearIds,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Model-year associations created successfully',
          meta: {
            modelId,
            yearCount: yearIds.length,
          },
        });
      } catch (error) {
        logger.error('Failed to associate model with years', {
          error,
          modelId: req.params.id,
          yearIds: req.body.yearIds,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * DELETE /api/v1/vehicle-hierarchy-v2/models/:id/years/remove
   * Remove year associations from a model
   */
  router.delete(
    '/models/:id/years/remove',
    validate({
      params: idParamSchema,
      body: associateModelYearsV2Schema,
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id: modelId } = req.params;
        const { yearIds } = req.body;

        await vehicleHierarchyV2Service.removeModelYearAssociations(
          modelId,
          yearIds,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Model-year associations removed successfully',
          meta: {
            modelId,
            yearCount: yearIds.length,
          },
        });
      } catch (error) {
        logger.error('Failed to remove model-year associations', {
          error,
          modelId: req.params.id,
          yearIds: req.body.yearIds,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  // ===== HIERARCHY QUERY ROUTES =====

  /**
   * GET /api/v1/vehicle-hierarchy-v2/full
   * Get full hierarchy tree for tenant
   */
  router.get(
    '/full',
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const hierarchy = await vehicleHierarchyV2Service.getFullHierarchy(
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: hierarchy,
          meta: {
            tenantId,
            yearCount: hierarchy.years.length,
            brandCount: hierarchy.years.reduce(
              (total, year) => total + year.brands.length,
              0
            ),
          },
        });
      } catch (error) {
        logger.error('Failed to fetch full hierarchy', {
          error,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/vehicle-hierarchy-v2/by-year/:year
   * Get hierarchy for a specific year
   */
  router.get(
    '/by-year/:year',
    validate({ params: yearParamSchema }),
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { year } = req.params;

        const hierarchy = await vehicleHierarchyV2Service.getHierarchyByYear(
          Number(year),
          tenantId,
          getRequestUser(req)!
        );

        if (!hierarchy) {
          return res.status(404).json({
            error: 'Not Found',
            message: `No data found for year ${year}`,
            statusCode: 404,
            timestamp: new Date().toISOString(),
          });
        }

        res.json({
          data: hierarchy,
          meta: {
            tenantId,
            year,
            brandCount: hierarchy.brands.length,
            subBrandCount: hierarchy.brands.reduce(
              (total, brand) => total + brand.subBrands.length,
              0
            ),
            modelCount: hierarchy.brands.reduce(
              (total, brand) =>
                total +
                brand.subBrands.reduce(
                  (subTotal, subBrand) => subTotal + subBrand.models.length,
                  0
                ),
              0
            ),
          },
        });
      } catch (error) {
        logger.error('Failed to fetch hierarchy by year', {
          error,
          year: req.params.year,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/vehicle-hierarchy-v2/models/by-year/:yearId
   * Get models for a specific year
   */
  router.get(
    '/models/by-year/:yearId',
    validate({ params: idParamSchema }),
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { yearId } = req.params;

        const models = await vehicleHierarchyV2Service.getModelsByYear(
          yearId,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: models,
          meta: {
            count: models.length,
            yearId,
            tenantId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch models by year', {
          error,
          yearId: req.params.yearId,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  return router;
}
