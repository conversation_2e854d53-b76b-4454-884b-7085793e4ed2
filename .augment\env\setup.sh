#!/bin/bash
set -e

echo "=== Tech Notes Development Environment Setup ==="

# Update system packages
sudo apt-get update

# Install Node.js 20 (LTS)
echo "Installing Node.js 20..."
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify Node.js and npm installation
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"

# Navigate to workspace
cd /mnt/persist/workspace

# Clean any existing installations to ensure fresh install
echo "Cleaning existing installations..."
rm -rf node_modules package-lock.json
rm -rf frontend/node_modules frontend/package-lock.json
rm -rf backend/node_modules backend/package-lock.json

# Install dependencies using yarn workspaces
echo "Installing project dependencies..."
yarn install

# Verify installations by checking if binaries exist
echo "Verifying test tool installations..."
if [ -f "frontend/node_modules/.bin/vitest" ]; then
    echo "Frontend vitest: $(cd frontend && ./node_modules/.bin/vitest --version)"
else
    echo "Frontend vitest: NOT FOUND"
fi

if [ -f "backend/node_modules/.bin/jest" ]; then
    echo "Backend jest: $(cd backend && ./node_modules/.bin/jest --version)"
else
    echo "Backend jest: NOT FOUND"
fi

# Install PostgreSQL for testing
echo "Installing PostgreSQL..."
sudo apt-get install -y postgresql postgresql-contrib

# Start PostgreSQL service
sudo service postgresql start

# Create test database and user
echo "Setting up test database..."
sudo -u postgres psql -c "CREATE USER \"user\" WITH PASSWORD 'password';"
sudo -u postgres psql -c "CREATE DATABASE tech_notes_test OWNER \"user\";"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE tech_notes_test TO \"user\";"

# Create admin user for test database management (for ephemeral databases)
sudo -u postgres psql -c "CREATE USER \"admin_user\" WITH PASSWORD 'admin_password' SUPERUSER;"

# Set up environment variables for testing
echo "Setting up test environment..."
cat > .env.test << 'EOF'
NODE_ENV=test
CLERK_SECRET_KEY=sk_test_mock_key_for_tests
CLERK_PUBLISHABLE_KEY=pk_test_mock_key_for_tests
VITE_CLERK_PUBLISHABLE_KEY=pk_test_mock_key_for_tests
VITE_API_URL=http://localhost:8080
LOG_LEVEL=error
DATABASE_URL=postgresql://user:password@localhost:5432/tech_notes_test
TEST_DATABASE_URL=postgresql://user:password@localhost:5432/tech_notes_test
TEST_DATABASE_ADMIN_URL=postgresql://admin_user:admin_password@localhost:5432/postgres
EOF

# Also create .env.local for the backend tests to find
cat > .env.local << 'EOF'
NODE_ENV=test
CLERK_SECRET_KEY=sk_test_mock_key_for_tests
CLERK_PUBLISHABLE_KEY=pk_test_mock_key_for_tests
LOG_LEVEL=error
DATABASE_URL=postgresql://user:password@localhost:5432/tech_notes_test
TEST_DATABASE_URL=postgresql://user:password@localhost:5432/tech_notes_test
TEST_DATABASE_ADMIN_URL=postgresql://admin_user:admin_password@localhost:5432/postgres
EOF

# Generate Prisma client
echo "Generating Prisma client..."
cd backend
npm run db:generate

# Set up test database schema
echo "Setting up test database schema..."
DATABASE_URL="postgresql://user:password@localhost:5432/tech_notes_test" npm run db:push

# Return to root directory
cd /mnt/persist/workspace

echo "=== Setup Complete ==="
echo "Environment is ready for testing!"