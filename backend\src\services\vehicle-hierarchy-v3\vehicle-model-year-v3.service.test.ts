import { BackendAuthContext } from '@tech-notes/shared';

import { ConflictError, NotFoundError } from '../../types/error.types.js';

import { VehicleModelV3Service } from './vehicle-model-v3.service.js';
import { VehicleModelYearV3Service } from './vehicle-model-year-v3.service.js';
import { VehicleYearV3Service } from './vehicle-year-v3.service.js';

describe('VehicleModelYearV3Service', () => {
  let vehicleModelYearV3Service: VehicleModelYearV3Service;
  let mockServices: any;
  let mockModelService: jest.Mocked<VehicleModelV3Service>;
  let mockYearService: jest.Mocked<VehicleYearV3Service>;
  let mockAuthContext: BackendAuthContext;

  beforeEach(() => {
    // Create mocks directly in test following project patterns
    mockServices = {
      prismaService: {
        prisma: {
          tenant: {
            findUnique: jest.fn().mockResolvedValue({ id: 'test-tenant-id' }),
          },
          user: {
            findFirst: jest.fn().mockResolvedValue({ id: 'test-user-id' }),
          },
          vehicleModelYearV3: {
            findMany: jest.fn(),
            findFirst: jest.fn(),
            create: jest.fn(),
            delete: jest.fn(),
            deleteMany: jest.fn(),
          },
          $transaction: jest.fn(),
        },
        healthCheck: jest.fn().mockResolvedValue(true),
      },
      logger: {
        error: jest.fn(),
        warn: jest.fn(),
        info: jest.fn(),
        debug: jest.fn(),
        verbose: jest.fn(),
        silly: jest.fn(),
        log: jest.fn(),
        query: jest.fn(),
      },
    };

    mockModelService = {
      validateModelOwnership: jest.fn(),
    } as any;

    mockYearService = {
      validateYearOwnership: jest.fn(),
    } as any;

    mockAuthContext = {
      id: 'test-user-id',
      clerkId: 'test-clerk-id',
      tenantId: 'test-tenant-id',
      email: '<EMAIL>',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    vehicleModelYearV3Service = new VehicleModelYearV3Service(
      mockServices.prismaService,
      mockServices.logger,
      mockModelService,
      mockYearService
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getYearsByModel', () => {
    it('should return years associated with a model', async () => {
      const mockModelYears = [
        {
          id: 'model-year-1',
          modelId: 'model-1',
          yearId: 'year-1',
          tenantId: 'test-tenant-id',
          createdAt: new Date(),
          updatedAt: new Date(),
          year: {
            id: 'year-1',
            name: '2020',
            tenantId: 'test-tenant-id',
            isActive: true,
            displayOrder: 1,
            deletedAt: null,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        },
      ];

      mockModelService.validateModelOwnership.mockResolvedValue();
      mockServices.prismaService.prisma.vehicleModelYearV3.findMany.mockResolvedValue(
        mockModelYears
      );

      const result = await vehicleModelYearV3Service.getYearsByModel(
        'model-1',
        'test-tenant-id',
        mockAuthContext
      );

      expect(result).toEqual(mockModelYears);
      expect(mockModelService.validateModelOwnership).toHaveBeenCalledWith(
        'model-1',
        'test-tenant-id'
      );
      expect(
        mockServices.prismaService.prisma.vehicleModelYearV3.findMany
      ).toHaveBeenCalledWith({
        where: {
          modelId: 'model-1',
          tenantId: 'test-tenant-id',
        },
        include: {
          year: true,
        },
        orderBy: {
          year: {
            displayOrder: 'asc',
          },
        },
      });
    });
  });

  describe('getModelsByYear', () => {
    it('should return models associated with a year', async () => {
      const mockYearModels = [
        {
          id: 'model-year-1',
          modelId: 'model-1',
          yearId: 'year-1',
          tenantId: 'test-tenant-id',
          createdAt: new Date(),
          updatedAt: new Date(),
          model: {
            id: 'model-1',
            name: 'XLT',
            subBrandId: 'sub-brand-1',
            tenantId: 'test-tenant-id',
            isActive: true,
            displayOrder: 1,
            deletedAt: null,
            createdAt: new Date(),
            updatedAt: new Date(),
            subBrand: {
              id: 'sub-brand-1',
              name: 'F-150',
              brandId: 'brand-1',
              tenantId: 'test-tenant-id',
              isActive: true,
              displayOrder: 1,
              deletedAt: null,
              createdAt: new Date(),
              updatedAt: new Date(),
              brand: {
                id: 'brand-1',
                name: 'Ford',
                tenantId: 'test-tenant-id',
                isActive: true,
                displayOrder: 1,
                deletedAt: null,
                createdAt: new Date(),
                updatedAt: new Date(),
              },
            },
          },
        },
      ];

      mockYearService.validateYearOwnership.mockResolvedValue();
      mockServices.prismaService.prisma.vehicleModelYearV3.findMany.mockResolvedValue(
        mockYearModels
      );

      const result = await vehicleModelYearV3Service.getModelsByYear(
        'year-1',
        'test-tenant-id',
        mockAuthContext
      );

      expect(result).toEqual(mockYearModels);
      expect(mockYearService.validateYearOwnership).toHaveBeenCalledWith(
        'year-1',
        'test-tenant-id'
      );
    });
  });

  describe('createModelYear', () => {
    it('should create a new model-year association', async () => {
      const associationData = {
        modelId: 'model-1',
        yearId: 'year-1',
      };
      const mockCreatedAssociation = {
        id: 'model-year-1',
        modelId: 'model-1',
        yearId: 'year-1',
        tenantId: 'test-tenant-id',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockModelService.validateModelOwnership.mockResolvedValue();
      mockYearService.validateYearOwnership.mockResolvedValue();
      mockServices.prismaService.prisma.vehicleModelYearV3.findFirst.mockResolvedValue(
        null
      );
      mockServices.prismaService.prisma.vehicleModelYearV3.create.mockResolvedValue(
        mockCreatedAssociation
      );

      const result = await vehicleModelYearV3Service.createModelYear(
        associationData,
        'test-tenant-id',
        mockAuthContext
      );

      expect(result).toEqual(mockCreatedAssociation);
      expect(mockModelService.validateModelOwnership).toHaveBeenCalledWith(
        'model-1',
        'test-tenant-id'
      );
      expect(mockYearService.validateYearOwnership).toHaveBeenCalledWith(
        'year-1',
        'test-tenant-id'
      );
      expect(
        mockServices.prismaService.prisma.vehicleModelYearV3.create
      ).toHaveBeenCalledWith({
        data: {
          modelId: 'model-1',
          yearId: 'year-1',
          tenantId: 'test-tenant-id',
        },
      });
    });

    it('should throw ConflictError if association already exists', async () => {
      const associationData = {
        modelId: 'model-1',
        yearId: 'year-1',
      };
      const existingAssociation = {
        id: 'existing-model-year',
        modelId: 'model-1',
        yearId: 'year-1',
        tenantId: 'test-tenant-id',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockModelService.validateModelOwnership.mockResolvedValue();
      mockYearService.validateYearOwnership.mockResolvedValue();
      mockServices.prismaService.prisma.vehicleModelYearV3.findFirst.mockResolvedValue(
        existingAssociation
      );

      await expect(
        vehicleModelYearV3Service.createModelYear(
          associationData,
          'test-tenant-id',
          mockAuthContext
        )
      ).rejects.toThrow(ConflictError);
    });
  });

  describe('deleteModelYear', () => {
    it('should delete a model-year association', async () => {
      const mockAssociation = {
        id: 'model-year-1',
        modelId: 'model-1',
        yearId: 'year-1',
        tenantId: 'test-tenant-id',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockServices.prismaService.prisma.vehicleModelYearV3.findFirst.mockResolvedValue(
        mockAssociation
      );
      mockServices.prismaService.prisma.vehicleModelYearV3.delete.mockResolvedValue(
        mockAssociation
      );

      await vehicleModelYearV3Service.deleteModelYear(
        'model-1',
        'year-1',
        'test-tenant-id',
        mockAuthContext
      );

      expect(
        mockServices.prismaService.prisma.vehicleModelYearV3.delete
      ).toHaveBeenCalledWith({
        where: {
          id: 'model-year-1',
        },
      });
    });

    it('should throw NotFoundError if association does not exist', async () => {
      mockServices.prismaService.prisma.vehicleModelYearV3.findFirst.mockResolvedValue(
        null
      );

      await expect(
        vehicleModelYearV3Service.deleteModelYear(
          'model-1',
          'year-1',
          'test-tenant-id',
          mockAuthContext
        )
      ).rejects.toThrow(NotFoundError);
    });
  });

  describe('associationExists', () => {
    it('should return true if association exists', async () => {
      const mockAssociation = { id: 'model-year-1' };
      mockServices.prismaService.prisma.vehicleModelYearV3.findFirst.mockResolvedValue(
        mockAssociation
      );

      const result = await vehicleModelYearV3Service.associationExists(
        'model-1',
        'year-1',
        'test-tenant-id'
      );

      expect(result).toBe(true);
    });

    it('should return false if association does not exist', async () => {
      mockServices.prismaService.prisma.vehicleModelYearV3.findFirst.mockResolvedValue(
        null
      );

      const result = await vehicleModelYearV3Service.associationExists(
        'model-1',
        'year-1',
        'test-tenant-id'
      );

      expect(result).toBe(false);
    });
  });
});
