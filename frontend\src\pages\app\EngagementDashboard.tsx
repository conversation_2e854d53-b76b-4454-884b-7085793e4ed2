import React, { useState } from "react";
import { EngagementMetrics } from "../../components/organisms/EngagementMetrics";
import { UserActivityTable } from "../../components/organisms/UserActivityTable";
import { InactiveUsersReport } from "../../components/organisms/InactiveUsersReport";
import { Card, Alert } from "../../components";
import { usePermissions } from "../../hooks/usePermissions";
import { BarChart3, Users, AlertTriangle } from "lucide-react";

type TabType = "overview" | "activity" | "inactive";

export const EngagementDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>("overview");
  const { isCompanyAdmin, isSystemAdmin } = usePermissions();

  // Only Company Admins and System Admins can access engagement dashboard
  const canAccessDashboard = isCompanyAdmin || isSystemAdmin;

  if (!canAccessDashboard) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <Alert variant="warning">
          <h2 className="text-lg font-semibold mb-2">Access Restricted</h2>
          <p>
            You don't have permission to view the engagement dashboard. This
            feature is only available to Company Administrators and System
            Administrators.
          </p>
        </Alert>
      </div>
    );
  }

  const tabs = [
    {
      id: "overview" as const,
      label: "Overview",
      icon: <BarChart3 className="h-4 w-4" />,
      description: "Key engagement metrics and trends",
    },
    {
      id: "activity" as const,
      label: "User Activity",
      icon: <Users className="h-4 w-4" />,
      description: "Detailed user activity and login history",
    },
    {
      id: "inactive" as const,
      label: "Inactive Users",
      icon: <AlertTriangle className="h-4 w-4" />,
      description: "Users who need re-engagement",
    },
  ];

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          User Engagement Dashboard
        </h1>
        <p className="text-gray-600 mt-2">
          Monitor user activity, engagement metrics, and identify opportunities
          for re-engagement.
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="mb-8">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm
                  ${
                    activeTab === tab.id
                      ? "border-primary-500 text-primary-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }
                `}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Description */}
        <div className="mt-4">
          <p className="text-sm text-gray-600">
            {tabs.find((tab) => tab.id === activeTab)?.description}
          </p>
        </div>
      </div>

      {/* Tab Content */}
      <div className="space-y-8">
        {activeTab === "overview" && (
          <Card className="p-6">
            <EngagementMetrics />
          </Card>
        )}

        {activeTab === "activity" && (
          <Card className="p-6">
            <UserActivityTable />
          </Card>
        )}

        {activeTab === "inactive" && (
          <Card className="p-6">
            <InactiveUsersReport />
          </Card>
        )}
      </div>

      {/* Help Section */}
      <div className="mt-12 bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Understanding Engagement Metrics
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Active Users</h4>
            <ul className="text-gray-600 space-y-1">
              <li>
                <strong>DAU:</strong> Users active in the last 24 hours
              </li>
              <li>
                <strong>WAU:</strong> Users active in the last 7 days
              </li>
              <li>
                <strong>MAU:</strong> Users active in the last 30 days
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">
              Activity Tracking
            </h4>
            <ul className="text-gray-600 space-y-1">
              <li>Login times are tracked automatically</li>
              <li>Activity is updated during app usage</li>
              <li>Data is refreshed every hour</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Re-engagement</h4>
            <ul className="text-gray-600 space-y-1">
              <li>Identify users who need attention</li>
              <li>Customize inactive user thresholds</li>
              <li>Export data for outreach campaigns</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
