import React from "react";
import { clsx } from "clsx";
import {
  TestimonialCard,
  type TestimonialCardProps,
} from "../atoms/TestimonialCard";
import { Badge } from "../atoms/Badge";
import { ChevronLeft, ChevronRight } from "lucide-react";

export interface TestimonialSectionProps
  extends React.HTMLAttributes<HTMLDivElement> {
  /** Section layout variant */
  layout?: "grid" | "carousel" | "single" | "masonry";
  /** Number of columns for grid layout */
  columns?: 1 | 2 | 3 | 4;
  /** Section spacing */
  spacing?: "tight" | "normal" | "loose";
  /** Optional section title */
  title?: string;
  /** Optional section subtitle */
  subtitle?: string;
  /** Optional section badge/announcement */
  badge?: {
    text: string;
    variant?: "primary" | "success" | "warning";
    icon?: React.ReactNode;
  };
  /** Array of testimonials to display */
  testimonials: Array<
    TestimonialCardProps & {
      id: string;
    }
  >;
  /** Testimonial card variant to apply to all cards */
  cardVariant?: TestimonialCardProps["variant"];
  /** Testimonial card size to apply to all cards */
  cardSize?: TestimonialCardProps["size"];
  /** Testimonial card layout to apply to all cards */
  cardLayout?: TestimonialCardProps["layout"];
  /** Whether testimonial cards should be interactive */
  interactive?: boolean;
  /** Carousel options */
  carousel?: {
    autoPlay?: boolean;
    autoPlayInterval?: number;
    showControls?: boolean;
    showDots?: boolean;
    itemsPerView?: number;
  };
  /** Custom render function for individual testimonials */
  renderTestimonial?: (
    testimonial: TestimonialSectionProps["testimonials"][0],
    index: number,
  ) => React.ReactNode;
}

const layoutClasses = {
  grid: "grid gap-6",
  carousel:
    "flex gap-6 overflow-x-auto pb-4 snap-x snap-mandatory scrollbar-hide",
  single: "flex justify-center",
  masonry: "columns-1 md:columns-2 lg:columns-3 gap-6 space-y-6",
};

const columnClasses = {
  1: "grid-cols-1",
  2: "grid-cols-1 md:grid-cols-2",
  3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
  4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
};

const spacingClasses = {
  tight: "gap-4",
  normal: "gap-6",
  loose: "gap-8",
};

export const TestimonialSection: React.FC<TestimonialSectionProps> = ({
  layout = "grid",
  columns = 3,
  spacing = "normal",
  title,
  subtitle,
  badge,
  testimonials,
  cardVariant = "default",
  cardSize = "md",
  cardLayout = "vertical",
  interactive = false,
  carousel,
  renderTestimonial,
  className,
  ...props
}) => {
  const [currentIndex, setCurrentIndex] = React.useState(0);
  const carouselRef = React.useRef<HTMLDivElement>(null);

  // Auto-play functionality
  React.useEffect(() => {
    if (
      layout === "carousel" &&
      carousel?.autoPlay &&
      testimonials.length > 1
    ) {
      const interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % testimonials.length);
      }, carousel.autoPlayInterval || 5000);

      return () => clearInterval(interval);
    }
  }, [
    layout,
    carousel?.autoPlay,
    carousel?.autoPlayInterval,
    testimonials.length,
  ]);

  // Scroll to current testimonial in carousel
  React.useEffect(() => {
    if (
      layout === "carousel" &&
      carouselRef.current &&
      typeof carouselRef.current.scrollTo === "function"
    ) {
      const container = carouselRef.current;
      const itemWidth = container.scrollWidth / testimonials.length;
      container.scrollTo({
        left: currentIndex * itemWidth,
        behavior: "smooth",
      });
    }
  }, [currentIndex, layout, testimonials.length]);

  const renderHeader = () => {
    if (!title && !subtitle && !badge) return null;

    return (
      <div className="text-center mb-12">
        {badge && (
          <div className="mb-4 flex justify-center">
            <Badge
              variant={badge.variant || "primary"}
              size="md"
              icon={badge.icon}
              className="px-4 py-2 text-sm font-medium"
            >
              {badge.text}
            </Badge>
          </div>
        )}

        {title && (
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            {title}
          </h2>
        )}

        {subtitle && (
          <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
            {subtitle}
          </p>
        )}
      </div>
    );
  };

  const renderTestimonialCard = (
    testimonial: TestimonialSectionProps["testimonials"][0],
    index: number,
  ) => {
    if (renderTestimonial) {
      return renderTestimonial(testimonial, index);
    }

    const cardProps = {
      ...testimonial,
      variant: testimonial.variant || cardVariant,
      size: testimonial.size || cardSize,
      layout: testimonial.layout || cardLayout,
      interactive:
        testimonial.interactive !== undefined
          ? testimonial.interactive
          : interactive,
    };

    return (
      <div
        key={testimonial.id}
        className={clsx(
          layout === "carousel" && "flex-shrink-0 w-80 snap-start",
          layout === "masonry" && "break-inside-avoid",
          layout === "single" && "max-w-2xl",
        )}
      >
        <TestimonialCard {...cardProps} />
      </div>
    );
  };

  const renderCarouselControls = () => {
    if (
      layout !== "carousel" ||
      !carousel?.showControls ||
      testimonials.length <= 1
    )
      return null;

    const handlePrevious = () => {
      setCurrentIndex(
        (prev) => (prev - 1 + testimonials.length) % testimonials.length,
      );
    };

    const handleNext = () => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    };

    return (
      <div className="flex justify-center items-center gap-4 mt-8">
        <button
          onClick={handlePrevious}
          className="p-2 rounded-full bg-white border border-gray-300 text-gray-600 hover:bg-gray-50 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-all duration-200"
          aria-label="Previous testimonial"
        >
          <ChevronLeft className="h-5 w-5" />
        </button>

        <span className="text-sm text-gray-500">
          {currentIndex + 1} of {testimonials.length}
        </span>

        <button
          onClick={handleNext}
          className="p-2 rounded-full bg-white border border-gray-300 text-gray-600 hover:bg-gray-50 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-all duration-200"
          aria-label="Next testimonial"
        >
          <ChevronRight className="h-5 w-5" />
        </button>
      </div>
    );
  };

  const renderCarouselDots = () => {
    if (
      layout !== "carousel" ||
      !carousel?.showDots ||
      testimonials.length <= 1
    )
      return null;

    return (
      <div className="flex justify-center gap-2 mt-6">
        {testimonials.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={clsx(
              "w-2 h-2 rounded-full transition-all duration-200",
              index === currentIndex
                ? "bg-primary-600"
                : "bg-gray-300 hover:bg-gray-400",
            )}
            aria-label={`Go to testimonial ${index + 1}`}
          />
        ))}
      </div>
    );
  };

  const containerClasses = clsx(
    layout === "grid" && layoutClasses.grid,
    layout === "grid" && columnClasses[columns],
    layout === "grid" && spacingClasses[spacing],
    layout === "carousel" && layoutClasses.carousel,
    layout === "single" && layoutClasses.single,
    layout === "masonry" && layoutClasses.masonry,
  );

  return (
    <div className={clsx("w-full", className)} {...props}>
      {renderHeader()}

      <div
        ref={carouselRef}
        className={containerClasses}
        data-testid="testimonial-container"
      >
        {testimonials.map((testimonial, index) =>
          renderTestimonialCard(testimonial, index),
        )}
      </div>

      {renderCarouselControls()}
      {renderCarouselDots()}
    </div>
  );
};
