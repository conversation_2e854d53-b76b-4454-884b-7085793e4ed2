import React, { useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { Modal, Button, Input, FormField, Alert } from "../index";
import { useTypedApi, type VehicleBrandV2 } from "../../services/api-client";
import { Tag } from "lucide-react";

interface CreateSubBrandV2ModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedBrand?: VehicleBrandV2 | null;
}

export const CreateSubBrandV2Modal: React.FC<CreateSubBrandV2ModalProps> = ({
  isOpen,
  onClose,
  selectedBrand,
}) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [name, setName] = useState("");
  const [brandId, setBrandId] = useState(selectedBrand?.id || "");
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch brands for selection
  const { data: brandsResponse } = useQuery({
    queryKey: ["vehicle-hierarchy-v2", "brands"],
    queryFn: () => api.vehicleHierarchyV2.getBrands(),
    enabled: isOpen,
  });

  const createSubBrandMutation = useMutation({
    mutationFn: (subBrandData: { name: string; brandId: string }) =>
      api.vehicleHierarchyV2.createSubBrand(subBrandData),
    onSuccess: () => {
      toast.success("Sub-brand created successfully");
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "sub-brands"],
      });
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "brands"],
      });
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "full"],
      });
      handleClose();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create sub-brand");
    },
  });

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = "Sub-brand name is required";
    } else if (name.trim().length < 2) {
      newErrors.name = "Sub-brand name must be at least 2 characters";
    } else if (name.trim().length > 100) {
      newErrors.name = "Sub-brand name must be less than 100 characters";
    }

    if (!brandId) {
      newErrors.brandId = "Please select a brand";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    createSubBrandMutation.mutate({
      name: name.trim(),
      brandId,
    });
  };

  const handleClose = () => {
    setName("");
    setBrandId(selectedBrand?.id || "");
    setErrors({});
    onClose();
  };

  // Update brandId when selectedBrand changes
  React.useEffect(() => {
    if (selectedBrand?.id) {
      setBrandId(selectedBrand.id);
    }
  }, [selectedBrand]);

  const brands = brandsResponse?.data || [];
  const selectedBrandName = brands.find((b) => b.id === brandId)?.name;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Create New Sub-Brand"
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex items-center space-x-3 text-gray-600 mb-4">
          <Tag className="h-5 w-5" />
          <span>Add a new sub-brand to the hierarchy</span>
        </div>

        <FormField label="Brand" error={errors.brandId} required>
          <select
            value={brandId}
            onChange={(e) => setBrandId(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Select a brand...</option>
            {brands.map((brand) => (
              <option key={brand.id} value={brand.id}>
                {brand.name}
              </option>
            ))}
          </select>
          {errors.brandId && (
            <p className="mt-1 text-sm text-red-600">{errors.brandId}</p>
          )}
        </FormField>

        <FormField label="Sub-Brand Name" error={errors.name} required>
          <Input
            type="text"
            placeholder="e.g., F-150, Camry, GeoPro"
            value={name}
            onChange={(e) => setName(e.target.value)}
            error={errors.name}
            maxLength={100}
          />
        </FormField>

        {selectedBrandName && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              This sub-brand will be added under{" "}
              <strong>{selectedBrandName}</strong>
            </p>
          </div>
        )}

        {createSubBrandMutation.isError && (
          <Alert variant="error">
            <p>Failed to create sub-brand. Please try again.</p>
          </Alert>
        )}

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={createSubBrandMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={
              createSubBrandMutation.isPending || !name.trim() || !brandId
            }
          >
            {createSubBrandMutation.isPending
              ? "Creating..."
              : "Create Sub-Brand"}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
