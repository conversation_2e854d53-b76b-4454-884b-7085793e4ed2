#!/usr/bin/env tsx

/**
 * S3 Connectivity Test Script
 * 
 * This script validates AWS S3 connectivity and presigned URL generation
 * Can be run independently to verify S3 configuration before integration
 * 
 * Usage:
 *   tsx scripts/test-s3-connectivity.ts
 */

import { S3Service } from '../src/services/s3.service';
import { logger } from '../src/utils/logger';
import { env } from '../src/utils/env-validation';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Test data
const TEST_DOCUMENT_ID = 'test-doc-123';
const TEST_TENANT_ID = 'test-tenant-456';
const TEST_FILE_NAME = 'test-document.pdf';
const TEST_FILE_SIZE = 1024 * 1024; // 1MB
const TEST_MIME_TYPE = 'application/pdf';

async function testS3Connectivity(): Promise<void> {
  console.log('🔍 Starting S3 Connectivity Test...\n');

  // Initialize S3 service with logger
  const s3Service = new S3Service(logger);

  try {
    // Test 1: Basic connectivity
    console.log('📡 Test 1: Basic S3 Connectivity');
    console.log('─'.repeat(50));
    
    const connectivityResult = await s3Service.testConnectivity();
    
    if (connectivityResult.healthy) {
      console.log('✅ S3 connectivity: HEALTHY');
    } else {
      console.log('❌ S3 connectivity: FAILED');
      console.log(`   Error: ${connectivityResult.error}`);
      throw new Error('S3 connectivity test failed');
    }

    // Test 2: Generate presigned upload URL
    console.log('\n📤 Test 2: Presigned Upload URL Generation');
    console.log('─'.repeat(50));
    
    const uploadRequest = {
      fileName: TEST_FILE_NAME,
      fileSize: TEST_FILE_SIZE,
      mimeType: TEST_MIME_TYPE,
      tenantId: TEST_TENANT_ID,
      documentId: TEST_DOCUMENT_ID,
    };

    const uploadUrlResponse = await s3Service.generatePresignedUploadUrl(uploadRequest);
    
    console.log('✅ Upload URL generated successfully');
    console.log(`   S3 Key: ${uploadUrlResponse.s3Key}`);
    console.log(`   Expires in: ${uploadUrlResponse.expiresIn} seconds`);
    console.log(`   URL length: ${uploadUrlResponse.uploadUrl.length} characters`);

    // Validate S3 key format
    const expectedKeyPattern = `tenant-${TEST_TENANT_ID}/${TEST_DOCUMENT_ID}-`;
    if (uploadUrlResponse.s3Key.startsWith(expectedKeyPattern)) {
      console.log('✅ S3 key format: VALID (tenant isolation confirmed)');
    } else {
      console.log('❌ S3 key format: INVALID');
      throw new Error('S3 key does not follow expected tenant isolation pattern');
    }

    // Test 3: Generate presigned download URL
    console.log('\n📥 Test 3: Presigned Download URL Generation');
    console.log('─'.repeat(50));
    
    const downloadRequest = {
      s3Key: uploadUrlResponse.s3Key,
      fileName: TEST_FILE_NAME,
    };

    const downloadUrlResponse = await s3Service.generatePresignedDownloadUrl(downloadRequest);
    
    console.log('✅ Download URL generated successfully');
    console.log(`   Expires in: ${downloadUrlResponse.expiresIn} seconds`);
    console.log(`   URL length: ${downloadUrlResponse.downloadUrl.length} characters`);

    // Test 4: File validation
    console.log('\n🔒 Test 4: File Validation');
    console.log('─'.repeat(50));
    
    // Test invalid file type
    try {
      await s3Service.generatePresignedUploadUrl({
        ...uploadRequest,
        mimeType: 'application/x-malware',
      });
      console.log('❌ File type validation: FAILED (should have rejected invalid MIME type)');
    } catch (error) {
      console.log('✅ File type validation: PASSED (correctly rejected invalid MIME type)');
    }

    // Test oversized file
    try {
      await s3Service.generatePresignedUploadUrl({
        ...uploadRequest,
        fileSize: 100 * 1024 * 1024, // 100MB (exceeds 50MB PDF limit)
      });
      console.log('❌ File size validation: FAILED (should have rejected oversized file)');
    } catch (error) {
      console.log('✅ File size validation: PASSED (correctly rejected oversized file)');
    }

    // Test 5: Environment configuration
    console.log('\n⚙️  Test 5: Environment Configuration');
    console.log('─'.repeat(50));
    
    console.log(`✅ AWS Region: ${env.AWS_REGION}`);
    console.log(`✅ S3 Bucket: ${env.AWS_S3_BUCKET_NAME}`);
    console.log(`✅ Access Key ID: ${env.AWS_ACCESS_KEY_ID.substring(0, 8)}...`);
    console.log(`✅ Secret Key: ${'*'.repeat(20)}... (hidden)`);

    // Test 6: Real File Upload and Download
    console.log('\n📁 Test 6: Real File Upload and Download');
    console.log('─'.repeat(50));

    // Get the directory of the current script
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    const testFilePath = path.join(__dirname, 'test.csv');

    // Check if test file exists
    if (!fs.existsSync(testFilePath)) {
      console.log('❌ Test file not found: backend/scripts/test.csv');
      throw new Error('Test file not found');
    }

    // Read the test file
    const fileBuffer = fs.readFileSync(testFilePath);
    const fileSize = fileBuffer.length;

    console.log(`📄 Test file: test.csv (${fileSize} bytes)`);

    // Generate upload URL for CSV file
    const csvUploadRequest = {
      fileName: 'test.csv',
      fileSize: fileSize,
      mimeType: 'text/csv',
      tenantId: TEST_TENANT_ID,
      documentId: 'real-test-doc-456',
    };

    const csvUploadResponse = await s3Service.generatePresignedUploadUrl(csvUploadRequest);
    console.log('✅ Upload URL generated for test.csv');
    console.log(`   S3 Key: ${csvUploadResponse.s3Key}`);

    // Upload the file to S3 using the presigned URL
    console.log('📤 Uploading file to S3...');

    const s3UploadResponse = await fetch(csvUploadResponse.uploadUrl, {
      method: 'PUT',
      body: fileBuffer,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Length': fileSize.toString(),
      },
    });

    if (s3UploadResponse.ok) {
      console.log('✅ File uploaded successfully to S3');
      console.log(`   HTTP Status: ${s3UploadResponse.status}`);
    } else {
      console.log('❌ File upload failed');
      console.log(`   HTTP Status: ${s3UploadResponse.status}`);
      console.log(`   Response: ${await s3UploadResponse.text()}`);
      throw new Error('File upload failed');
    }

    // Generate download URL
    console.log('📥 Generating download URL...');
    const csvDownloadRequest = {
      s3Key: csvUploadResponse.s3Key,
      fileName: 'test.csv',
    };

    const csvDownloadResponse = await s3Service.generatePresignedDownloadUrl(csvDownloadRequest);
    console.log('✅ Download URL generated successfully');
    console.log(`   Download URL: ${csvDownloadResponse.downloadUrl}`);
    console.log(`   Expires in: ${csvDownloadResponse.expiresIn} seconds`);

    // Test the download URL by fetching the file
    console.log('📥 Testing download URL...');
    const s3DownloadResponse = await fetch(csvDownloadResponse.downloadUrl);

    if (s3DownloadResponse.ok) {
      const downloadedContent = await s3DownloadResponse.text();
      const originalContent = fileBuffer.toString();

      if (downloadedContent === originalContent) {
        console.log('✅ Downloaded file content matches original');
        console.log(`   Original size: ${originalContent.length} bytes`);
        console.log(`   Downloaded size: ${downloadedContent.length} bytes`);
      } else {
        console.log('❌ Downloaded file content does not match original');
        console.log(`   Original: ${originalContent.substring(0, 50)}...`);
        console.log(`   Downloaded: ${downloadedContent.substring(0, 50)}...`);
        throw new Error('File content mismatch');
      }
    } else {
      console.log('❌ Download test failed');
      console.log(`   HTTP Status: ${s3DownloadResponse.status}`);
      throw new Error('Download test failed');
    }

    // Summary
    console.log('\n🎉 S3 Connectivity Test Summary');
    console.log('═'.repeat(50));
    console.log('✅ All tests passed successfully!');
    console.log('✅ S3 service is ready for document storage');
    console.log('✅ Tenant isolation is properly configured');
    console.log('✅ File validation is working correctly');
    console.log('✅ Presigned URLs are generating properly');
    console.log('✅ Real file upload and download working correctly');
    
    console.log('\n📋 Next Steps:');
    console.log('   1. Ensure your S3 bucket has proper CORS configuration');
    console.log('   2. Verify IAM permissions for your AWS credentials');
    console.log('   3. Test actual file upload/download in your application');
    
  } catch (error) {
    console.log('\n❌ S3 Connectivity Test Failed');
    console.log('═'.repeat(50));
    console.error('Error:', error instanceof Error ? error.message : error);
    
    if (error instanceof Error && error.stack) {
      console.log('\nStack trace:');
      console.log(error.stack);
    }
    
    console.log('\n🔧 Troubleshooting Tips:');
    console.log('   1. Check your AWS credentials in .env.local');
    console.log('   2. Verify your S3 bucket exists and is accessible');
    console.log('   3. Ensure your AWS region is correct');
    console.log('   4. Check IAM permissions for S3 operations');
    
    process.exit(1);
  }
}

// Handle script execution
testS3Connectivity().catch((error) => {
  console.error('Unhandled error:', error);
  process.exit(1);
});

export { testS3Connectivity };
