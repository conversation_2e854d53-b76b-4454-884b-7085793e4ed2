#!/usr/bin/env tsx

/**
 * Test script to check the current user's authentication context
 * This simulates what happens when the frontend calls /api/v1/users
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '../src/utils/logger';

const prisma = new PrismaClient();

async function testCurrentUserAuth() {
  try {
    logger.info('🔍 Testing current user authentication context...');

    // You'll need to provide your actual Clerk ID here
    // Check the browser's localStorage or network tab to get your current Clerk token
    console.log('\n📋 To test your current user:');
    console.log('1. Open browser dev tools');
    console.log('2. Go to Application/Storage > Local Storage');
    console.log('3. Look for Clerk session data');
    console.log(
      '4. Or check Network tab for Authorization header in API calls'
    );
    console.log('5. Find your Clerk user ID (starts with "user_")');

    // For now, let's test with the known System Admin users
    const systemAdminUsers = [
      'test-clerk-id',
      'user_2xnKdw3E9eBEh8Ep5mDqlymGtyl',
    ];

    for (const clerkId of systemAdminUsers) {
      logger.info(`\n🧪 Testing System Admin user: ${clerkId}`);

      // Simulate the auth middleware process
      const user = await prisma.user.findUnique({
        where: { clerkId },
        include: {
          tenant: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          userRoles: {
            include: {
              role: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                  isSystemRole: true,
                },
              },
            },
          },
        },
      });

      if (!user) {
        logger.warn(`❌ User not found for Clerk ID: ${clerkId}`);
        continue;
      }

      logger.info(`👤 User: ${user.email}`);
      logger.info(`   Tenant: ${user.tenant.name}`);

      // Determine if user can bypass tenant scope (System Admin)
      const canBypassTenantScope = user.userRoles.some(
        (userRole) => userRole.role.isSystemRole
      );

      logger.info(`   Can Bypass Tenant Scope: ${canBypassTenantScope}`);

      // Simulate the getUsersByTenantWithRoles call
      const whereClause = canBypassTenantScope
        ? {} // No tenant restriction for System Admins
        : { tenantId: user.tenantId }; // Tenant-scoped for Company Admins

      const users = await prisma.user.findMany({
        where: whereClause,
        include: {
          userRoles: {
            include: {
              role: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                  isSystemRole: true,
                },
              },
            },
            where: canBypassTenantScope
              ? {} // No role restriction for System Admins
              : {
                  OR: [
                    { tenantId: user.tenantId },
                    { tenantId: null }, // System roles
                  ],
                },
          },
          tenant: canBypassTenantScope
            ? {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                },
              }
            : undefined,
        },
        orderBy: { createdAt: 'desc' },
      });

      logger.info(`   API would return ${users.length} users:`);
      for (const u of users) {
        logger.info(
          `     - ${u.email} (${u.tenant?.name || 'No tenant info'})`
        );
      }

      // Simulate the API response
      const apiResponse = {
        data: users,
        meta: {
          count: users.length,
          tenantId: user.tenantId,
        },
      };

      logger.info(`   Meta: ${JSON.stringify(apiResponse.meta)}`);
    }

    // Also test the Company Admin user to show the difference
    logger.info(`\n🧪 Testing Company Admin user for comparison...`);
    const companyAdminUser = await prisma.user.findFirst({
      where: {
        userRoles: {
          some: {
            role: {
              type: 'COMPANY_ADMIN',
            },
          },
        },
      },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        userRoles: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                type: true,
                isSystemRole: true,
              },
            },
          },
        },
      },
    });

    if (companyAdminUser) {
      logger.info(`👤 Company Admin: ${companyAdminUser.email}`);
      logger.info(`   Tenant: ${companyAdminUser.tenant.name}`);

      const canBypassTenantScope = companyAdminUser.userRoles.some(
        (userRole) => userRole.role.isSystemRole
      );

      logger.info(`   Can Bypass Tenant Scope: ${canBypassTenantScope}`);

      const whereClause = canBypassTenantScope
        ? {}
        : { tenantId: companyAdminUser.tenantId };

      const users = await prisma.user.findMany({
        where: whereClause,
        include: {
          tenant: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      logger.info(`   API would return ${users.length} users:`);
      for (const u of users) {
        logger.info(
          `     - ${u.email} (${u.tenant?.name || 'No tenant info'})`
        );
      }
    }
  } catch (error) {
    logger.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testCurrentUserAuth().catch(console.error);
