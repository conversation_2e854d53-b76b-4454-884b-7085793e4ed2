import {
  BrowserRouter,
  Routes,
  Route,
  Navigate,
  useLocation,
} from "react-router-dom";
import { SignedIn, SignedOut } from "@clerk/clerk-react";
import { Suspense, lazy } from "react";
import { PublicLayout } from "./layouts/PublicLayout";
import { AppLayout } from "./layouts/AppLayout";
import { TechLayout } from "./layouts/TechLayout";

// Lazy load marketing pages
const MarketingHome = lazy(() =>
  import("./pages/marketing/MarketingHome").then((m) => ({
    default: m.MarketingHome,
  })),
);
const PricingPage = lazy(() =>
  import("./pages/marketing/PricingPage").then((m) => ({
    default: m.PricingPage,
  })),
);
// Lazy load admin/app pages
const AdminDashboard = lazy(() =>
  import("./pages/app/AdminDashboard").then((m) => ({
    default: m.AdminDashboard,
  })),
);
const HealthDashboardPage = lazy(() =>
  import("./pages/app/HealthDashboard").then((m) => ({
    default: m.HealthDashboardPage,
  })),
);
const ComponentShowcasePage = lazy(() =>
  import("./pages/app/ComponentShowcase").then((m) => ({
    default: m.ComponentShowcasePage,
  })),
);
const UsersPage = lazy(() =>
  import("./pages/app/UsersPage").then((m) => ({ default: m.UsersPage })),
);
const SettingsPage = lazy(() =>
  import("./pages/app/SettingsPage").then((m) => ({ default: m.SettingsPage })),
);
const AdminPage = lazy(() =>
  import("./pages/app/AdminPage").then((m) => ({ default: m.AdminPage })),
);
const RoleManagementPage = lazy(() =>
  import("./pages/app/RoleManagementPage").then((m) => ({
    default: m.RoleManagementPage,
  })),
);
const VehicleHierarchyPage = lazy(() =>
  import("./pages/app/VehicleHierarchyPage").then((m) => ({
    default: m.VehicleHierarchyPage,
  })),
);
const VehicleHierarchyV2Page = lazy(() =>
  import("./pages/VehicleHierarchyV2Page").then((m) => ({
    default: m.VehicleHierarchyV2Page,
  })),
);
const VehicleHierarchyV3Page = lazy(() =>
  import("./pages/VehicleHierarchyV3Page").then((m) => ({
    default: m.VehicleHierarchyV3Page,
  })),
);
const TestingPage = lazy(() =>
  import("./pages/app/TestingPage").then((m) => ({ default: m.TestingPage })),
);
const InvitationsPage = lazy(() =>
  import("./pages/app/InvitationsPage").then((m) => ({
    default: m.InvitationsPage,
  })),
);
const EngagementDashboard = lazy(() =>
  import("./pages/app/EngagementDashboard").then((m) => ({
    default: m.EngagementDashboard,
  })),
);
const DocumentsPage = lazy(() =>
  import("./pages/app/DocumentsPage").then((m) => ({
    default: m.DocumentsPage,
  })),
);
// Keep these as static imports (small components used in guards/routing)
import { InvitationAcceptance } from "./components/organisms/InvitationAcceptance";
import { AuthGuard } from "./components/guards/AuthGuard";
import { RoleBasedRedirect } from "./components/routing/RoleBasedRedirect";
import { LoadingSpinner } from "./components/atoms/LoadingSpinner";

// Lazy load auth and tech pages
const SignInPage = lazy(() =>
  import("./pages/auth/SignInPage").then((m) => ({ default: m.SignInPage })),
);
const TechDashboard = lazy(() =>
  import("./pages/tech/TechDashboard").then((m) => ({
    default: m.TechDashboard,
  })),
);
const VinScannerPage = lazy(() =>
  import("./pages/tech/VinScannerPage").then((m) => ({
    default: m.VinScannerPage,
  })),
);
const WorkOrderPage = lazy(() =>
  import("./pages/tech/WorkOrderPage").then((m) => ({
    default: m.WorkOrderPage,
  })),
);
const QuickActionsPage = lazy(() =>
  import("./pages/tech/QuickActionsPage").then((m) => ({
    default: m.QuickActionsPage,
  })),
);

function App() {
  return (
    <BrowserRouter>
      <AppRouter />
    </BrowserRouter>
  );
}

function AppRouter() {
  const location = useLocation();
  const isPublicRoute = [
    "/",
    "/pricing",
    "/accept-invitation",
    "/sign-in",
  ].includes(location.pathname);

  if (isPublicRoute) {
    return <PublicSite />;
  }

  return <AuthenticatedApp />;
}

function PublicSite() {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <LoadingSpinner size="lg" text="Loading..." />
        </div>
      }
    >
      <Routes>
        <Route path="/" element={<PublicLayout />}>
          <Route index element={<MarketingHome />} />
          <Route path="pricing" element={<PricingPage />} />
        </Route>
        <Route path="/accept-invitation" element={<InvitationAcceptance />} />
        <Route path="/sign-in" element={<SignInPage />} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Suspense>
  );
}

function AuthenticatedApp() {
  return (
    <>
      <SignedOut>
        <Navigate to="/" replace />
      </SignedOut>
      <SignedIn>
        <AuthGuard>
          <Suspense
            fallback={
              <div className="flex items-center justify-center min-h-screen">
                <LoadingSpinner size="lg" text="Loading..." />
              </div>
            }
          >
            <Routes>
              {/* Admin Experience - Desktop Optimized */}
              <Route path="/app" element={<AppLayout />}>
                <Route index element={<AdminDashboard />} />
                <Route path="health" element={<HealthDashboardPage />} />
                <Route path="showcase" element={<ComponentShowcasePage />} />
                <Route path="users" element={<UsersPage />} />
                <Route path="settings" element={<SettingsPage />} />
                <Route path="admin" element={<AdminPage />} />
                <Route path="roles" element={<RoleManagementPage />} />
                <Route path="vehicles" element={<VehicleHierarchyPage />} />
                <Route
                  path="vehicles-v2"
                  element={<VehicleHierarchyV2Page />}
                />
                <Route
                  path="vehicles-v3"
                  element={<VehicleHierarchyV3Page />}
                />
                <Route path="testing" element={<TestingPage />} />
                <Route path="invitations" element={<InvitationsPage />} />
                <Route path="engagement" element={<EngagementDashboard />} />
                <Route path="documents" element={<DocumentsPage />} />
              </Route>

              {/* Tech Experience - Mobile Optimized */}
              <Route path="/tech" element={<TechLayout />}>
                <Route index element={<TechDashboard />} />
                <Route path="scan" element={<VinScannerPage />} />
                <Route path="work-orders" element={<WorkOrderPage />} />
                <Route path="quick-actions" element={<QuickActionsPage />} />
              </Route>

              {/* Smart Redirect based on user role */}
              <Route path="/dashboard" element={<RoleBasedRedirect />} />
              <Route path="/" element={<RoleBasedRedirect />} />

              {/* Redirect public routes for authenticated users */}
              <Route path="/pricing" element={<RoleBasedRedirect />} />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Suspense>
        </AuthGuard>
      </SignedIn>
    </>
  );
}

export default App;
