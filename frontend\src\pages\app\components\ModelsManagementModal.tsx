import React, { useState, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
  <PERSON>ge,
  <PERSON>ert,
  <PERSON><PERSON><PERSON>ner,
  FormField,
  CompanyAdminOnly,
} from "../../../components";
import { useTypedApi } from "../../../services/api-client";
import type { VehicleModel } from "../../../services/api-client";
import {
  Settings,
  Plus,
  Edit,
  Trash2,
  Search,
  AlertTriangle,
  Calendar,
} from "lucide-react";

interface ModelsManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  onManageModelYears: (model: VehicleModel) => void;
}

export const ModelsManagementModal: React.FC<ModelsManagementModalProps> = ({
  isOpen,
  onClose,
  onManageModelYears,
}) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [searchQuery, setSearchQuery] = useState("");
  const [isCreateMode, setIsCreateMode] = useState(false);
  const [editingModel, setEditingModel] = useState<VehicleModel | null>(null);
  const [newModelName, setNewModelName] = useState("");
  const [newModelMakeId, setNewModelMakeId] = useState("");
  const [editModelName, setEditModelName] = useState("");
  const [deleteConfirmModel, setDeleteConfirmModel] =
    useState<VehicleModel | null>(null);

  // Fetch models
  const { data: modelsResponse, isLoading: isLoadingModels } = useQuery({
    queryKey: ["vehicle-models"],
    queryFn: () => api.vehicleHierarchy.getAllModels(),
    enabled: isOpen,
  });

  // Fetch makes for create form
  const { data: makesResponse, isLoading: isLoadingMakes } = useQuery({
    queryKey: ["vehicle-makes"],
    queryFn: () => api.vehicleHierarchy.getMakes(),
    enabled: isOpen,
  });

  // Fetch hierarchy for model details
  const { data: hierarchyResponse } = useQuery({
    queryKey: ["vehicle-hierarchy-full"],
    queryFn: () => api.vehicleHierarchy.getFullHierarchy(),
    enabled: isOpen,
  });

  // Create model mutation
  const createModelMutation = useMutation({
    mutationFn: ({ makeId, name }: { makeId: string; name: string }) =>
      api.vehicleHierarchy.createModel({ makeId, name }),
    onSuccess: () => {
      toast.success("Model created successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-models"] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      setIsCreateMode(false);
      setNewModelName("");
      setNewModelMakeId("");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create model");
    },
  });

  // Update model mutation
  const updateModelMutation = useMutation({
    mutationFn: ({ id, name }: { id: string; name: string }) =>
      api.vehicleHierarchy.updateModel(id, { name }),
    onSuccess: () => {
      toast.success("Model updated successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-models"] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      setEditingModel(null);
      setEditModelName("");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update model");
    },
  });

  // Delete model mutation
  const deleteModelMutation = useMutation({
    mutationFn: (modelId: string) => api.vehicleHierarchy.deleteModel(modelId),
    onSuccess: () => {
      toast.success("Model deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-models"] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      setDeleteConfirmModel(null);
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete model");
    },
  });

  // Filter models based on search
  const filteredModels = useMemo(() => {
    if (!modelsResponse?.data) return [];

    return modelsResponse.data
      .filter(
        (model) =>
          model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (model.make?.name || "")
            .toLowerCase()
            .includes(searchQuery.toLowerCase()),
      )
      .sort((a, b) => {
        const makeCompare = (a.make?.name || "").localeCompare(
          b.make?.name || "",
        );
        return makeCompare !== 0 ? makeCompare : a.name.localeCompare(b.name);
      });
  }, [modelsResponse?.data, searchQuery]);

  // Get model statistics
  const getModelStats = (model: VehicleModel) => {
    if (!hierarchyResponse?.data) return { years: [] };

    const years: number[] = [];

    hierarchyResponse.data.years.forEach((year) => {
      const makeData = year.makes.find((m) => m.id === model.makeId);
      if (makeData) {
        const modelData = makeData.models.find((m) => m.id === model.id);
        if (modelData) {
          years.push(year.year);
        }
      }
    });

    return { years: years.sort((a, b) => b - a) };
  };

  const handleCreateModel = () => {
    if (!newModelName.trim() || !newModelMakeId) {
      toast.error("Please enter a model name and select a make");
      return;
    }
    createModelMutation.mutate({
      makeId: newModelMakeId,
      name: newModelName.trim(),
    });
  };

  const handleEditModel = (model: VehicleModel) => {
    setEditingModel(model);
    setEditModelName(model.name);
  };

  const handleUpdateModel = () => {
    if (!editingModel || !editModelName.trim()) {
      toast.error("Please enter a model name");
      return;
    }
    updateModelMutation.mutate({
      id: editingModel.id,
      name: editModelName.trim(),
    });
  };

  const handleDeleteModel = (model: VehicleModel) => {
    setDeleteConfirmModel(model);
  };

  const confirmDelete = () => {
    if (deleteConfirmModel) {
      deleteModelMutation.mutate(deleteConfirmModel.id);
    }
  };

  const handleClose = () => {
    setSearchQuery("");
    setIsCreateMode(false);
    setEditingModel(null);
    setNewModelName("");
    setNewModelMakeId("");
    setEditModelName("");
    setDeleteConfirmModel(null);
    onClose();
  };

  const isPending =
    createModelMutation.isPending ||
    updateModelMutation.isPending ||
    deleteModelMutation.isPending;
  const isLoading = isLoadingModels || isLoadingMakes;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Manage Vehicle Models"
      size="xl"
    >
      <div className="space-y-6">
        {/* Header with search and add button */}
        <div className="flex items-center justify-between">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search models or makes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <CompanyAdminOnly>
            <Button
              onClick={() => setIsCreateMode(true)}
              className="ml-4 flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Add Model</span>
            </Button>
          </CompanyAdminOnly>
        </div>

        {/* Create model form */}
        {isCreateMode && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="text-lg font-medium text-blue-900 mb-3">
              Add New Model
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <FormField label="Make">
                <select
                  value={newModelMakeId}
                  onChange={(e) => setNewModelMakeId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select a make</option>
                  {makesResponse?.data.map((make) => (
                    <option key={make.id} value={make.id}>
                      {make.name}
                    </option>
                  ))}
                </select>
              </FormField>
              <FormField label="Model Name">
                <Input
                  type="text"
                  placeholder="e.g., F-150, Camry, etc."
                  value={newModelName}
                  onChange={(e) => setNewModelName(e.target.value)}
                />
              </FormField>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                onClick={handleCreateModel}
                disabled={isPending || !newModelName.trim() || !newModelMakeId}
                className="flex items-center space-x-2"
              >
                {createModelMutation.isPending ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Plus className="h-4 w-4" />
                )}
                <span>Create</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setIsCreateMode(false);
                  setNewModelName("");
                  setNewModelMakeId("");
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        {/* Models list */}
        <div className="space-y-3">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner size="md" />
            </div>
          ) : filteredModels.length > 0 ? (
            filteredModels.map((model) => {
              const stats = getModelStats(model);
              const isEditing = editingModel?.id === model.id;

              return (
                <div
                  key={model.id}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-4 flex-1">
                    <Settings className="h-5 w-5 text-primary-600" />
                    <div className="flex-1">
                      {isEditing ? (
                        <div className="flex items-center space-x-3">
                          <Input
                            type="text"
                            value={editModelName}
                            onChange={(e) => setEditModelName(e.target.value)}
                            className="flex-1"
                          />
                          <Button
                            size="sm"
                            onClick={handleUpdateModel}
                            disabled={isPending || !editModelName.trim()}
                          >
                            Save
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setEditingModel(null);
                              setEditModelName("");
                            }}
                          >
                            Cancel
                          </Button>
                        </div>
                      ) : (
                        <>
                          <h3 className="font-medium text-gray-900">
                            {model.make?.name || "Unknown Make"} {model.name}
                          </h3>
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>
                              Created:{" "}
                              {new Date(model.createdAt).toLocaleDateString()}
                            </span>
                            <Badge variant="secondary">
                              {stats.years.length} years
                            </Badge>
                            {stats.years.length > 0 && (
                              <span className="text-xs">
                                Years: {stats.years.slice(0, 3).join(", ")}
                                {stats.years.length > 3 &&
                                  ` +${stats.years.length - 3} more`}
                              </span>
                            )}
                            <Badge
                              variant={model.isActive ? "success" : "secondary"}
                            >
                              {model.isActive ? "Active" : "Inactive"}
                            </Badge>
                          </div>
                        </>
                      )}
                    </div>
                  </div>

                  {!isEditing && (
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onManageModelYears(model)}
                        className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                        title="Manage year associations"
                      >
                        <Calendar className="h-4 w-4" />
                      </Button>

                      <CompanyAdminOnly>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditModel(model)}
                          disabled={isPending}
                          className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteModel(model)}
                          disabled={isPending}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </CompanyAdminOnly>
                    </div>
                  )}
                </div>
              );
            })
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Settings className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p>No models found</p>
              {searchQuery && (
                <p className="text-sm">Try adjusting your search</p>
              )}
            </div>
          )}
        </div>

        {/* Delete confirmation */}
        {deleteConfirmModel && (
          <Alert variant="warning" className="border-orange-200 bg-orange-50">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
              <div className="flex-1">
                <h3 className="font-medium text-orange-900">
                  Confirm Deletion
                </h3>
                <p className="text-orange-800 mt-1">
                  Are you sure you want to delete model "
                  {deleteConfirmModel.make?.name || "Unknown Make"}{" "}
                  {deleteConfirmModel.name}"? This action cannot be undone.
                </p>
                <div className="flex items-center space-x-3 mt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setDeleteConfirmModel(null)}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={confirmDelete}
                    disabled={isPending}
                    className="bg-red-600 text-white hover:bg-red-700"
                  >
                    {deleteModelMutation.isPending ? "Deleting..." : "Delete"}
                  </Button>
                </div>
              </div>
            </div>
          </Alert>
        )}

        {(createModelMutation.error ||
          updateModelMutation.error ||
          deleteModelMutation.error) && (
          <Alert variant="error">
            {createModelMutation.error?.message ||
              updateModelMutation.error?.message ||
              deleteModelMutation.error?.message ||
              "Operation failed"}
          </Alert>
        )}
      </div>
    </Modal>
  );
};
