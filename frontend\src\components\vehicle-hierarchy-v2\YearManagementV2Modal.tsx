import React, { useState, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import {
  <PERSON>dal,
  Button,
  Input,
  <PERSON>ert,
  <PERSON>ading<PERSON><PERSON>ner,
  FormField,
  CompanyAdminOnly,
} from "../index";
import { useTypedApi } from "../../services/api-client";
import type { VehicleYear } from "../../services/api-client";
import { Calendar, Plus, Trash2, Search, AlertTriangle, X } from "lucide-react";

interface YearManagementV2ModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const YearManagementV2Modal: React.FC<YearManagementV2ModalProps> = ({
  isOpen,
  onClose,
}) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [searchQuery, setSearchQuery] = useState("");
  const [isCreateMode, setIsCreateMode] = useState(false);
  const [newYear, setNewYear] = useState("");
  // Years are immutable - no editing functionality needed
  const [deleteConfirmYear, setDeleteConfirmYear] =
    useState<VehicleYear | null>(null);

  // Fetch years
  const { data: yearsResponse, isLoading } = useQuery({
    queryKey: ["vehicle-years"],
    queryFn: () => api.vehicleHierarchy.getYears(),
    enabled: isOpen,
  });

  // Fetch model counts for each year
  const yearIds = yearsResponse?.data?.map((year) => year.id) || [];
  const { data: yearStatsData } = useQuery({
    queryKey: ["vehicle-hierarchy-v2", "year-stats", yearIds],
    queryFn: async () => {
      if (!yearsResponse?.data) return {};

      const stats: Record<string, { models: number }> = {};

      // Fetch model count for each year
      await Promise.all(
        yearsResponse.data.map(async (year) => {
          try {
            const modelsResponse = await api.vehicleHierarchyV2.getModelsByYear(
              year.id,
            );
            stats[year.id] = { models: modelsResponse.data?.length || 0 };
          } catch (error) {
            console.warn(
              `Failed to fetch models for year ${year.year}:`,
              error,
            );
            stats[year.id] = { models: 0 };
          }
        }),
      );

      return stats;
    },
    enabled: isOpen && !!yearsResponse?.data && yearIds.length > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Create year mutation
  const createYearMutation = useMutation({
    mutationFn: (year: number) => api.vehicleHierarchy.createYear({ year }),
    onSuccess: () => {
      toast.success("Year created successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-years"] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-v2"] });
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "year-stats"],
      });
      setNewYear("");
      setIsCreateMode(false);
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create year");
    },
  });

  // Note: Years are immutable in this system - they can only be created or deleted
  // If editing is needed, we would delete the old year and create a new one

  // Delete year mutation
  const deleteYearMutation = useMutation({
    mutationFn: (yearId: string) => api.vehicleHierarchy.deleteYear(yearId),
    onSuccess: () => {
      toast.success("Year deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-years"] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-v2"] });
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "year-stats"],
      });
      setDeleteConfirmYear(null);
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete year");
    },
  });

  const isPending =
    createYearMutation.isPending || deleteYearMutation.isPending;

  // Filter years based on search
  const filteredYears = useMemo(() => {
    if (!yearsResponse?.data) return [];

    return yearsResponse.data
      .filter((year) => year.year.toString().includes(searchQuery))
      .sort((a, b) => b.year - a.year); // Sort by year descending
  }, [yearsResponse?.data, searchQuery]);

  // Get year statistics for V2 system
  const getYearStats = (yearId: string) => {
    return yearStatsData?.[yearId] || { models: 0 };
  };

  const handleCreateYear = () => {
    const yearNum = parseInt(newYear);
    if (isNaN(yearNum) || yearNum < 1900 || yearNum > 2050) {
      toast.error("Please enter a valid year between 1900 and 2050");
      return;
    }
    createYearMutation.mutate(yearNum);
  };

  // Years are immutable - no update functionality

  const handleDeleteYear = (year: VehicleYear) => {
    const stats = getYearStats(year.id);
    if (stats.models > 0) {
      setDeleteConfirmYear(year);
    } else {
      deleteYearMutation.mutate(year.id);
    }
  };

  const confirmDelete = () => {
    if (deleteConfirmYear) {
      deleteYearMutation.mutate(deleteConfirmYear.id);
    }
  };

  const handleClose = () => {
    setSearchQuery("");
    setIsCreateMode(false);
    setNewYear("");
    setDeleteConfirmYear(null);
    onClose();
  };

  const currentYear = new Date().getFullYear();
  const suggestedYears = [
    currentYear + 1,
    currentYear,
    currentYear - 1,
    currentYear - 2,
    currentYear - 3,
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Manage Vehicle Years"
      size="lg"
    >
      <div className="space-y-6">
        {/* Header with search and add button */}
        <div className="flex items-center justify-between">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search years..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <CompanyAdminOnly>
            <Button
              onClick={() => setIsCreateMode(true)}
              className="ml-4 flex items-center space-x-2"
              disabled={isPending}
            >
              <Plus className="h-4 w-4" />
              <span>Add Year</span>
            </Button>
          </CompanyAdminOnly>
        </div>

        {/* Create year form */}
        {isCreateMode && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-medium text-blue-900">
                Add New Year
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsCreateMode(false)}
                className="h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-3">
              <div className="flex items-end space-x-3">
                <FormField label="Year" className="flex-1">
                  <Input
                    type="number"
                    placeholder="e.g., 2024"
                    value={newYear}
                    onChange={(e) => setNewYear(e.target.value)}
                    min="1900"
                    max="2050"
                  />
                </FormField>
                <Button
                  onClick={handleCreateYear}
                  disabled={isPending || !newYear}
                  className="flex items-center space-x-2"
                >
                  {createYearMutation.isPending ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <Plus className="h-4 w-4" />
                  )}
                  <span>Add</span>
                </Button>
              </div>

              {/* Quick add suggestions */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">Quick add:</span>
                {suggestedYears.map((year) => (
                  <Button
                    key={year}
                    variant="outline"
                    size="sm"
                    onClick={() => setNewYear(year.toString())}
                    disabled={isPending}
                  >
                    {year}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Years list */}
        {isLoading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : (
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {filteredYears.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                {searchQuery
                  ? "No years found matching your search."
                  : "No years found."}
              </div>
            ) : (
              filteredYears.map((year) => {
                const stats = getYearStats(year.id);

                return (
                  <div
                    key={year.id}
                    className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center space-x-3">
                      <Calendar className="h-5 w-5 text-gray-400" />
                      <div>
                        <span className="font-medium">{year.year}</span>
                        <div className="text-sm text-gray-500">
                          {stats.models} model{stats.models !== 1 ? "s" : ""}{" "}
                          associated
                        </div>
                      </div>
                    </div>

                    <CompanyAdminOnly>
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteYear(year)}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CompanyAdminOnly>
                  </div>
                );
              })
            )}
          </div>
        )}

        {/* Delete confirmation */}
        {deleteConfirmYear && (
          <Alert variant="warning" className="border-orange-200 bg-orange-50">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
              <div className="flex-1">
                <h3 className="font-medium text-orange-900">
                  Confirm Deletion
                </h3>
                <p className="text-orange-800 mt-1">
                  Are you sure you want to delete year {deleteConfirmYear.year}?
                  {(() => {
                    const stats = getYearStats(deleteConfirmYear.id);
                    if (stats.models > 0) {
                      return (
                        <span className="block mt-2 font-medium">
                          ⚠️ This will also remove {stats.models} model
                          association{stats.models !== 1 ? "s" : ""}.
                        </span>
                      );
                    }
                    return null;
                  })()}
                  <span className="block mt-2">
                    This action cannot be undone.
                  </span>
                </p>
                <div className="flex items-center space-x-3 mt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setDeleteConfirmYear(null)}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={confirmDelete}
                    disabled={isPending}
                    className="border-red-300 text-red-700 hover:bg-red-50"
                  >
                    {deleteYearMutation.isPending
                      ? "Deleting..."
                      : "Delete Year"}
                  </Button>
                </div>
              </div>
            </div>
          </Alert>
        )}

        <div className="flex justify-end pt-4 border-t border-gray-200">
          <Button variant="outline" onClick={handleClose}>
            Close
          </Button>
        </div>
      </div>
    </Modal>
  );
};
