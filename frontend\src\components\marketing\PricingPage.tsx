import React, { useEffect, useState } from "react";
import { PricingTiers } from "./PricingTiers";
import { FeatureMatrix } from "./FeatureMatrix";
import { PricingCTA } from "./PricingCTA";
import { Badge } from "../atoms/Badge";
import type { FeatureGroup } from "../../types/pricing.types";
import { parseFeatureGroups } from "../../services/pricing-data.service";
import { PRICING_TIERS, PRICING_CONFIG } from "../../data/pricing-data";

export const PricingPage: React.FC = () => {
  const [features, setFeatures] = useState<FeatureGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use hard-coded data for tiers and config
  const tiers = PRICING_TIERS;
  const config = PRICING_CONFIG;

  useEffect(() => {
    const loadFeatureData = async () => {
      try {
        setLoading(true);
        const featuresData = await parseFeatureGroups();
        setFeatures(featuresData);
      } catch (err) {
        console.error("Error loading feature data:", err);
        setError("Failed to load feature information. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    loadFeatureData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading feature information...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Something went wrong
          </h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white">
      {/* Header Section */}
      <div className="bg-gradient-to-b from-gray-50 to-white py-16 sm:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl lg:text-6xl">
              {config.companyName} Pricing
            </h1>
            <p className="mt-6 text-xl text-gray-600 max-w-3xl mx-auto">
              {config.tagline}
            </p>
            <div className="mt-8 flex justify-center">
              <Badge variant="primary" size="md">
                {config.pricingNote}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Tiers */}
      <div className="py-16 sm:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <PricingTiers tiers={tiers} />
        </div>
      </div>

      {/* Feature Matrix */}
      <div className="bg-gray-50 py-16 sm:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Compare Features
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              See what's included in each plan
            </p>
          </div>
          <FeatureMatrix features={features} tiers={tiers} />
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-16 sm:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <PricingCTA />
        </div>
      </div>
    </div>
  );
};
