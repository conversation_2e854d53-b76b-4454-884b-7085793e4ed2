import type {
  PricingTier,
  FeatureGroup,
  PricingConfig,
  Feature,
} from "../types/pricing.types";
import {
  FEATURE_GROUPS,
  PRICING_CONFIG,
  PRICING_TIERS,
} from "../data/pricing-data";

// Simple service that uses build-time generated data

export const parsePricingTiers = async (): Promise<PricingTier[]> => {
  return PRICING_TIERS;
};

export const parseFeatureGroups = async (): Promise<FeatureGroup[]> => {
  return FEATURE_GROUPS;
};

export const parsePricingConfig = async (): Promise<PricingConfig> => {
  return PRICING_CONFIG;
};

// Get features for a specific tier that should be included on the pricing card
export const getFeaturesForTier = async (tier: string): Promise<Feature[]> => {
  const allFeatures = FEATURE_GROUPS;
  const tierFeatures: Feature[] = [];

  allFeatures.forEach((group) => {
    group.features.forEach((feature) => {
      const tierKey = tier.toLowerCase() as
        | "basic"
        | "plus"
        | "pro"
        | "enterprise";
      const tierValue = feature[tierKey];
      // Check if feature is available for this tier (true or string value, but not false)
      const isAvailable =
        tierValue === true ||
        (typeof tierValue === "string" && tierValue.length > 0);
      if (isAvailable && feature.includeOnCard) {
        tierFeatures.push(feature);
      }
    });
  });

  return tierFeatures;
};

// Legacy function for compatibility - no longer needed with build-time data
export const clearPricingCache = (): void => {
  // No-op since we don't use caching anymore
};
