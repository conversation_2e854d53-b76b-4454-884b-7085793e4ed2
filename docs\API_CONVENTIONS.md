# API Development Guide

AI-focused reference for Express.js APIs with TypeScript, emphasizing security, RBAC, and multi-tenant architecture.

---

## 🏗️ Core Patterns

### Dependency Injection Pattern

```typescript
// Service with constructor injection
export class UserService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly logger: Logger,
  ) {}
}

// Route factory accepting dependencies
export function createUsersRouter(userService: UserService): Router {
  const router = Router();

  router.get(
    "/me",
    ...middlewareFactory.createAuthWithPermission(CommonPermissions.USER_READ),
    async (req: AuthenticatedRequest, res): Promise<void> => {
      const user = await userService.getUserByClerkId(req.user!.clerkId);
      res.json({ data: user, timestamp: new Date().toISOString() });
    },
  );

  return router;
}

// Bootstrap: Create services → Inject into routes
const userService = new UserService(prismaService, logger);
const usersRouter = createUsersRouter(userService);
app.use("/api/v1/users", usersRouter);
```

---

## 🛡️ Security & RBAC Patterns

### 3-Tier RBAC System

- **System Admin** (`RoleType.SYSTEM_ADMIN`): Global access, `canBypassTenantScope: true`
- **Company Admin** (`RoleType.COMPANY_ADMIN`): Full tenant access, create/update/delete
- **Company Tech** (`RoleType.COMPANY_TECH`): Limited tenant access, mostly read-only

### RBAC Decision Matrix

- **Permission-based**: Use `createAuthWithPermission()` for granular access control
- **Role-based**: Use `createAuthWithRole()` for hierarchical access
- **System Admin only**: Use `createAuthWithSystemAdmin()` for global operations

### Tenant-Scoped Operations

**Critical Security Rules:**

- All operations must be tenant-scoped unless `canBypassTenantScope: true`
- Database `User.tenantId` is source of truth (NOT Clerk metadata)
- Always validate tenant access in service layer
- System Admins can bypass tenant scope for global operations

```typescript
// ✅ CORRECT - Tenant-scoped with System Admin bypass
async (req: AuthenticatedRequest, res): Promise<void> => {
  const { tenantId, canBypassTenantScope } = req.user!;

  const users = canBypassTenantScope
    ? await userService.getAllUsers(req.user!)
    : await userService.getUsersByTenant(tenantId, req.user!);

  res.json({ data: users, timestamp: new Date().toISOString() });
};
```

### Input Validation

**Required Order: Authentication → Validation → Handler**

```typescript
// ✅ CORRECT - Auth first, then validation
router.post(
  "/users",
  ...middlewareFactory.createAuthWithPermission(CommonPermissions.USER_WRITE),
  validate({
    body: z.object({
      email: z.string().email(),
      firstName: z.string().min(1).max(50),
      lastName: z.string().min(1).max(50),
    }),
  }),
  handler,
);

// ❌ WRONG - Validation before auth (security risk)
// router.post("/users", validate({ body: schema }), middlewareFactory.createAuth(), handler);
```

---

## 🔧 Request/Response Standards

### Response Format

```typescript
interface SuccessResponse<T> {
  data: T;
  message?: string;
  timestamp: string;
}

interface ErrorResponse {
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
}
```

### HTTP Status Codes

- **200**: Success (GET, PUT) | **201**: Created (POST) | **204**: No Content (DELETE)
- **400**: Bad Request | **401**: Unauthorized | **403**: Forbidden | **404**: Not Found
- **409**: Conflict | **500**: Internal Server Error

---

## 🔄 Error Handling

### Essential Error Classes

```typescript
export abstract class BaseError extends Error {
  abstract statusCode: number;
  constructor(message: string) {
    super(message);
    this.name = this.constructor.name;
  }
}

export class ValidationError extends BaseError {
  statusCode = 400;
}
export class NotFoundError extends BaseError {
  statusCode = 404;
}
export class ForbiddenError extends BaseError {
  statusCode = 403;
}
```

**Key Principles:**

- Use `BaseError` subclasses for consistent error responses
- Global error handler logs context and returns standardized format
- Always call `next(error)` in route handlers to trigger error middleware

---

## 🧪 Testing Patterns

```typescript
// Direct mocking with proper typing
import { UserService } from "./user.service";
jest.mock("./user.service");

const mockUserService = {
  getUsersByTenant: jest.fn(),
  createUser: jest.fn(),
} as jest.Mocked<UserService>;

// Always verify tenant isolation in tests
expect(mockUserService.getUsersByTenant).toHaveBeenCalledWith(
  "tenant-123",
  expect.any(Object),
);
```

---

## 📚 Quick Reference

### Essential Imports

```typescript
import { Router, Request, Response, NextFunction } from "express";
import { z } from "zod";
import { RoleType } from "@prisma/client";
import {
  MiddlewareFactory,
  CommonPermissions,
} from "../middleware/middleware-factory";
import { AuthenticatedRequest } from "../types/auth.types";
```

### Complete Route Template

```typescript
router.get(
  "/resource/:id",
  validate({ params: z.object({ id: z.string().cuid() }) }),
  ...middlewareFactory.createAuthWithPermission(CommonPermissions.USER_READ),
  async (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction,
  ): Promise<void> => {
    try {
      const { tenantId, canBypassTenantScope } = req.user!;
      const { id } = req.params;

      const result = canBypassTenantScope
        ? await service.getResource(id, req.user!)
        : await service.getResourceByTenant(id, tenantId, req.user!);

      res.json({ data: result, timestamp: new Date().toISOString() });
    } catch (error) {
      next(error);
    }
  },
);
```

### AuthContext Interface

```typescript
interface AuthContext {
  id: string; // User database ID
  clerkId: string; // Clerk user ID
  tenantId: string; // Source of truth for tenant
  email: string;
  roles?: UserRoleWithContext[];
  permissions?: string[];
  canBypassTenantScope?: boolean; // True for System Admins
}

interface AuthenticatedRequest extends Request {
  user?: AuthContext;
}
```

### RBAC Middleware Patterns

```typescript
// ✅ PREFERRED - Permission-based (granular control)
router.get(
  "/users",
  ...middlewareFactory.createAuthWithPermission(CommonPermissions.USER_READ),
  handler,
);

// ✅ Multiple permissions (user must have ALL)
router.post(
  "/users",
  ...middlewareFactory.createAuthWithPermission([
    CommonPermissions.USER_WRITE,
    CommonPermissions.USER_MANAGE,
  ]),
  handler,
);

// ✅ Role-based (hierarchical access)
router.get("/admin", ...middlewareFactory.createAuthWithSystemAdmin(), handler);

// ⚠️ AVOID - Basic auth without permission checking
// router.get("/users", middlewareFactory.createAuth(), handler);
```

### Critical Security Rules

- **Never skip tenant validation** in protected routes
- **Database User.tenantId is source of truth** (NOT Clerk metadata)
- **Authentication order**: Auth → Validation → Handler
- **Always use permission-based middleware** over basic auth
