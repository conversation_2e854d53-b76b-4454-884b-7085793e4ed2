import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen } from "@testing-library/react";
import { BrowserRouter } from "react-router-dom";
import { TechDashboard } from "../../../src/pages/tech/TechDashboard";
import { useAuth } from "../../../src/hooks/useAuth";
import { type IAuthService } from "../../../src/services/auth-api.service";

// Mock the hooks
vi.mock("../../../src/hooks/useAuth");

const mockUseAuth = vi.mocked(useAuth);

const mockApiService: IAuthService = {
  checkAuthStatus: vi.fn(),
  getCurrentUser: vi.fn(),
  completeOnboarding: vi.fn(),
  acceptInvitation: vi.fn(),
  makeRequest: vi.fn(),
  getToken: vi.fn(),
};

const renderTechDashboard = () => {
  return render(
    <BrowserRouter>
      <TechDashboard />
    </BrowserRouter>,
  );
};

describe("TechDashboard", () => {
  beforeEach(() => {
    mockUseAuth.mockReturnValue({
      userProfile: {
        id: "test-user-id",
        clerkId: "test-clerk-id",
        email: "<EMAIL>",
        firstName: "John",
        lastName: "Doe",
        tenantId: "test-tenant-id",
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        roles: [],
        permissions: [],
        canBypassTenantScope: false,
      },
      isLoading: false,
      isAuthenticated: true,
      isOnboarded: true,
      needsOnboarding: false,
      isServiceUnavailable: false,
      clerkUser: null,
      authStatus: undefined,
      signOut: vi.fn(),
      signIn: vi.fn(),
      refreshAuth: vi.fn(),
      getToken: vi.fn(),
      api: mockApiService,
      statusError: null,
      profileError: null,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("renders welcome message with user name", () => {
    renderTechDashboard();

    expect(screen.getByText("Welcome, John!")).toBeInTheDocument();
    expect(screen.getByText("Ready to get to work?")).toBeInTheDocument();
  });

  it("renders welcome message without user name when not available", () => {
    mockUseAuth.mockReturnValue({
      userProfile: {
        id: "test-user-id",
        clerkId: "test-clerk-id",
        email: "<EMAIL>",
        firstName: null,
        lastName: "Doe",
        tenantId: "test-tenant-id",
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        roles: [],
        permissions: [],
        canBypassTenantScope: false,
      },
      isLoading: false,
      isAuthenticated: true,
      isOnboarded: true,
      needsOnboarding: false,
      isServiceUnavailable: false,
      clerkUser: null,
      authStatus: undefined,
      signOut: vi.fn(),
      signIn: vi.fn(),
      refreshAuth: vi.fn(),
      getToken: vi.fn(),
      api: mockApiService,
      statusError: null,
      profileError: null,
    });

    renderTechDashboard();

    expect(screen.getByText("Welcome!")).toBeInTheDocument();
  });

  it("renders quick stats cards", () => {
    renderTechDashboard();

    expect(screen.getByText("Today")).toBeInTheDocument();
    expect(screen.getByText("Complete")).toBeInTheDocument();
    expect(screen.getByText("Status")).toBeInTheDocument();
    expect(screen.getByText("Active")).toBeInTheDocument();
  });

  it("renders all quick action cards", () => {
    renderTechDashboard();

    expect(screen.getByText("Scan VIN/QR")).toBeInTheDocument();
    expect(screen.getByText("Scan vehicle codes")).toBeInTheDocument();

    expect(screen.getByText("Work Orders")).toBeInTheDocument();
    expect(screen.getByText("View active orders")).toBeInTheDocument();

    // Use getAllByText for multiple "Quick Actions" elements
    const quickActionsElements = screen.getAllByText("Quick Actions");
    expect(quickActionsElements).toHaveLength(2); // Section heading and card title
    expect(screen.getByText("Common tasks")).toBeInTheDocument();
  });

  it("has correct links for quick actions", () => {
    renderTechDashboard();

    const scanLink = screen.getByRole("link", {
      name: /scan vin\/qr scan vehicle codes/i,
    });
    expect(scanLink).toHaveAttribute("href", "/tech/scan");

    const workOrdersLink = screen.getByRole("link", {
      name: /work orders view active orders/i,
    });
    expect(workOrdersLink).toHaveAttribute("href", "/tech/work-orders");

    const quickActionsLink = screen.getByRole("link", {
      name: /quick actions common tasks/i,
    });
    expect(quickActionsLink).toHaveAttribute("href", "/tech/quick-actions");
  });

  it("renders recent activity section", () => {
    renderTechDashboard();

    expect(screen.getByText("Recent Activity")).toBeInTheDocument();
    expect(screen.getByText("No recent activity")).toBeInTheDocument();
    expect(
      screen.getByText("Your completed work will appear here"),
    ).toBeInTheDocument();
  });

  it("has proper mobile-first design structure", () => {
    renderTechDashboard();

    // Check for proper spacing
    const container = screen.getByText("Welcome, John!").closest(".space-y-6");
    expect(container).toBeInTheDocument();

    // Check for grid layout in stats
    const statsGrid = screen.getByText("Today").closest(".grid-cols-3");
    expect(statsGrid).toBeInTheDocument();
  });

  it("has proper card hover effects", () => {
    renderTechDashboard();

    const scanCard = screen.getByRole("link", {
      name: /scan vin\/qr scan vehicle codes/i,
    });
    const cardElement = scanCard.querySelector(".hover\\:shadow-md");
    expect(cardElement).toBeInTheDocument();
  });

  it("displays proper icons for each action", () => {
    const { container } = renderTechDashboard();

    // Check that SVG icons are present by querying for SVG elements
    const svgElements = container.querySelectorAll("svg");
    expect(svgElements.length).toBeGreaterThan(0);

    // Verify specific lucide icon classes are present
    const lucideIcons = container.querySelectorAll(".lucide");
    expect(lucideIcons.length).toBeGreaterThan(0);
  });

  it("has proper color coding for action cards", () => {
    renderTechDashboard();

    const scanCard = screen.getByText("Scan VIN/QR").closest(".p-6");
    const scanIcon = scanCard?.querySelector(".bg-blue-500");
    expect(scanIcon).toBeInTheDocument();

    const workOrderCard = screen.getByText("Work Orders").closest(".p-6");
    const workOrderIcon = workOrderCard?.querySelector(".bg-green-500");
    expect(workOrderIcon).toBeInTheDocument();

    // Use the link to find the specific Quick Actions card (not the section heading)
    const quickActionsLink = screen.getByRole("link", {
      name: /quick actions common tasks/i,
    });
    const quickActionsCard = quickActionsLink.querySelector(".p-6");
    const quickActionsIcon = quickActionsCard?.querySelector(".bg-orange-500");
    expect(quickActionsIcon).toBeInTheDocument();
  });

  it("handles missing user profile gracefully", () => {
    mockUseAuth.mockReturnValue({
      userProfile: null,
      isLoading: false,
      isAuthenticated: false,
      isOnboarded: false,
      needsOnboarding: false,
      isServiceUnavailable: false,
      clerkUser: null,
      authStatus: undefined,
      signOut: vi.fn(),
      signIn: vi.fn(),
      refreshAuth: vi.fn(),
      getToken: vi.fn(),
      api: mockApiService,
      statusError: null,
      profileError: null,
    });

    renderTechDashboard();

    expect(screen.getByText("Welcome!")).toBeInTheDocument();
    expect(screen.getByText("Ready to get to work?")).toBeInTheDocument();
  });

  it("has proper accessibility structure", () => {
    renderTechDashboard();

    // Check for proper heading hierarchy
    const mainHeading = screen.getByRole("heading", { level: 1 });
    expect(mainHeading).toHaveTextContent("Welcome, John!");

    const sectionHeadings = screen.getAllByRole("heading", { level: 2 });
    expect(sectionHeadings).toHaveLength(2); // "Quick Actions" and "Recent Activity"

    // Check that action cards are properly linked
    const links = screen.getAllByRole("link");
    expect(links).toHaveLength(3); // Three quick action links
  });

  it("displays correct order counts in stats", () => {
    renderTechDashboard();

    // Check that stats show 0 orders (default state) - there are multiple instances
    const orderStats = screen.getAllByText("0 orders");
    expect(orderStats).toHaveLength(2); // Today and Complete cards both show "0 orders"
    expect(orderStats[0]).toBeInTheDocument();
  });
});
