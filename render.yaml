# Render Blueprint for Tech Notes Application
# Multi-tenant SaaS with Express backend, React frontend, and PostgreSQL database

databases:
  - name: tech-notes-db
    databaseName: tech_notes_prod
    user: tech_notes_user
    plan: basic-256mb # Cheapest new plan option

services:
  # Backend API Service
  - type: web
    name: tech-notes-backend
    runtime: docker
    plan: starter # Can upgrade to standard/pro as needed
    dockerfilePath: ./infrastructure/docker/backend/Dockerfile
    dockerContext: .
    region: oregon # Choose region closest to your users
    branch: main # Deploy from main branch
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 8080
      - key: CORS_ORIGIN
        sync: false # Set manually in Render dashboard to include custom domain
      - key: DATABASE_URL
        fromDatabase:
          name: tech-notes-db
          property: connectionString
      - key: DIRECT_URL
        fromDatabase:
          name: tech-notes-db
          property: connectionString
      - key: CLERK_SECRET_KEY
        sync: false # Set manually in Render dashboard
      - key: CLERK_PUBLISHABLE_KEY
        sync: false # Set manually in Render dashboard
      - key: STRIPE_SECRET_KEY
        sync: false # Set manually in Render dashboard
      - key: STRIPE_WEBHOOK_SECRET
        sync: false # Set manually in Render dashboard
      - key: FRONTEND_URL
        sync: false # Set manually in Render dashboard to frontend URL
      # AWS S3 Configuration (for document storage)
      - key: AWS_REGION
        sync: false # Set manually in Render dashboard
      - key: AWS_S3_BUCKET_NAME
        sync: false # Set manually in Render dashboard
      - key: AWS_ACCESS_KEY_ID
        sync: false # Set manually in Render dashboard
      - key: AWS_SECRET_ACCESS_KEY
        sync: false # Set manually in Render dashboard

  # Frontend Web Service
  - type: web
    name: tech-notes-frontend
    runtime: docker
    plan: starter # Can upgrade to standard/pro as needed
    dockerfilePath: ./infrastructure/docker/frontend/Dockerfile
    dockerContext: .
    region: oregon # Same region as backend
    branch: main # Deploy from main branch
    healthCheckPath: /health
    envVars:
      - key: VITE_API_URL
        sync: false # Set manually in Render dashboard to backend URL
      - key: VITE_CLERK_PUBLISHABLE_KEY
        sync: false # Set manually in Render dashboard
      - key: VITE_STRIPE_PUBLISHABLE_KEY
        sync: false # Set manually in Render dashboard

# Optional: Background worker for async tasks (if needed in the future)
# - type: worker
#   name: tech-notes-worker
#   runtime: docker
#   dockerfilePath: ./infrastructure/docker/backend/Dockerfile
#   dockerContext: .
#   region: oregon
#   branch: main
#   startCommand: npm run worker # You'd need to create this script
#   envVars:
#     - key: NODE_ENV
#       value: production
#     - key: DATABASE_URL
#       fromDatabase:
#         name: tech-notes-db
#         property: connectionString
#     - key: CLERK_SECRET_KEY
#       sync: false
