import { RoleType } from '@prisma/client';
import { RequestHandler } from 'express';

import { IAuthCacheService } from '../services/auth-cache.service.js';
import { PermissionService } from '../services/permission.service.js';
import { PrismaService } from '../services/prisma.service.js';
import { UserEngagementService } from '../services/user-engagement.service.js';
import { UserService } from '../services/user.service.js';
import { Logger } from '../utils/logger.js';

import {
  createActivityTrackingMiddleware,
  ActivityTrackingDependencies,
} from './activity-tracking.middleware.js';
import {
  AuthMiddlewareDependencies,
  createClerkAuthMiddleware,
} from './auth.middleware.js';
import {
  PermissionMiddlewareDependencies,
  createPermissionMiddleware,
  createRoleMiddleware,
  createSystemAdminMiddleware,
  createCompanyAdminMiddleware,
  createCompanyUserMiddleware,
  PermissionRequirement,
} from './permission.middleware.js';

export interface MiddlewareFactoryDependencies {
  userService: UserService;
  permissionService: PermissionService;
  userEngagementService: UserEngagementService;
  prismaService: PrismaService;
  authCacheService: IAuthCacheService;
  logger: Logger;
}

/**
 * Factory class for creating all middleware with proper dependency injection
 */
export class MiddlewareFactory {
  private authDependencies: AuthMiddlewareDependencies;
  private permissionDependencies: PermissionMiddlewareDependencies;
  private activityTrackingDependencies: ActivityTrackingDependencies;

  constructor(dependencies: MiddlewareFactoryDependencies) {
    this.authDependencies = {
      userService: dependencies.userService,
      prismaService: dependencies.prismaService,
      permissionService: dependencies.permissionService,
      userEngagementService: dependencies.userEngagementService,
      authCacheService: dependencies.authCacheService,
      logger: dependencies.logger,
    };

    this.permissionDependencies = {
      permissionService: dependencies.permissionService,
      logger: dependencies.logger,
    };

    this.activityTrackingDependencies = {
      userEngagementService: dependencies.userEngagementService,
      logger: dependencies.logger,
    };
  }

  /**
   * Create authentication middleware
   */
  createAuth(required: boolean = true): RequestHandler {
    const authMiddleware = createClerkAuthMiddleware(this.authDependencies);
    return authMiddleware({ required }) as unknown as RequestHandler;
  }

  /**
   * Create permission checking middleware
   */
  createPermission(
    requirement: PermissionRequirement | PermissionRequirement[]
  ): RequestHandler {
    const permissionMiddleware = createPermissionMiddleware(
      this.permissionDependencies
    );
    return permissionMiddleware(requirement) as unknown as RequestHandler;
  }

  /**
   * Create role checking middleware
   */
  createRole(roleTypes: RoleType | RoleType[]): RequestHandler {
    const roleMiddleware = createRoleMiddleware(this.permissionDependencies);
    return roleMiddleware(roleTypes) as unknown as RequestHandler;
  }

  /**
   * Create System Admin only middleware
   */
  createSystemAdmin(): RequestHandler {
    return createSystemAdminMiddleware(
      this.permissionDependencies
    ) as unknown as RequestHandler;
  }

  /**
   * Create Company Admin middleware (includes System Admin)
   */
  createCompanyAdmin(): RequestHandler {
    return createCompanyAdminMiddleware(
      this.permissionDependencies
    ) as unknown as RequestHandler;
  }

  /**
   * Create Company User middleware (any company role)
   */
  createCompanyUser(): RequestHandler {
    return createCompanyUserMiddleware(
      this.permissionDependencies
    ) as unknown as RequestHandler;
  }

  /**
   * Create activity tracking middleware
   */
  createActivityTracking(): RequestHandler {
    return createActivityTrackingMiddleware(
      this.activityTrackingDependencies
    ) as unknown as RequestHandler;
  }

  /**
   * Convenience method: Auth + Permission
   */
  createAuthWithPermission(
    requirement: PermissionRequirement | PermissionRequirement[],
    authRequired: boolean = true
  ): RequestHandler[] {
    return [this.createAuth(authRequired), this.createPermission(requirement)];
  }

  /**
   * Convenience method: Auth + Role
   */
  createAuthWithRole(
    roleTypes: RoleType | RoleType[],
    authRequired: boolean = true
  ): RequestHandler[] {
    return [this.createAuth(authRequired), this.createRole(roleTypes)];
  }

  /**
   * Convenience method: Auth + System Admin
   */
  createAuthWithSystemAdmin(): RequestHandler[] {
    return [this.createAuth(true), this.createSystemAdmin()];
  }

  /**
   * Convenience method: Auth + Company Admin
   */
  createAuthWithCompanyAdmin(): RequestHandler[] {
    return [this.createAuth(true), this.createCompanyAdmin()];
  }

  /**
   * Convenience method: Auth + Company User
   */
  createAuthWithCompanyUser(): RequestHandler[] {
    return [this.createAuth(true), this.createCompanyUser()];
  }
}

/**
 * Common permission requirements for easy reuse
 */
export const CommonPermissions = {
  // User management
  USER_READ: { resource: 'USER' as const, action: 'READ' as const },
  USER_WRITE: { resource: 'USER' as const, action: 'WRITE' as const },
  USER_DELETE: { resource: 'USER' as const, action: 'DELETE' as const },
  USER_MANAGE: { resource: 'USER' as const, action: 'MANAGE' as const },

  // Tenant management
  TENANT_READ: { resource: 'TENANT' as const, action: 'READ' as const },
  TENANT_WRITE: { resource: 'TENANT' as const, action: 'WRITE' as const },
  TENANT_MANAGE: { resource: 'TENANT' as const, action: 'MANAGE' as const },

  // System management
  SYSTEM_MANAGE: { resource: 'SYSTEM' as const, action: 'MANAGE' as const },

  // Data access
  DATA_READ: { resource: 'DATA' as const, action: 'read' as const },
  DATA_WRITE: { resource: 'DATA' as const, action: 'WRITE' as const },

  // Document management
  DOCUMENT_READ: { resource: 'DOCUMENT' as const, action: 'READ' as const },
  DOCUMENT_WRITE: { resource: 'DOCUMENT' as const, action: 'WRITE' as const },
  DOCUMENT_DELETE: { resource: 'DOCUMENT' as const, action: 'DELETE' as const },
} as const;

/**
 * Helper function to create middleware factory with dependencies
 */
export function createMiddlewareFactory(
  dependencies: MiddlewareFactoryDependencies
): MiddlewareFactory {
  return new MiddlewareFactory(dependencies);
}
