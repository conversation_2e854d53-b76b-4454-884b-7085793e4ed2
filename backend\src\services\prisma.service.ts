import { PrismaClient } from '@prisma/client';

import { env } from '../utils/env-validation.js';
import { Logger } from '../utils/logger.js';

export class PrismaService {
  private static instance: PrismaService;
  private client: PrismaClient;
  private logger: Logger;

  private constructor(logger: Logger) {
    this.logger = logger;

    // For test environment, read DATABASE_URL directly from process.env
    // since it's set dynamically by the test setup script after env validation
    // This bypasses the cached env object which was validated before test setup
    // Prioritize TEST_DATABASE_URL in test mode to avoid conflicts with dev env
    const databaseUrl =
      process.env.NODE_ENV === 'test'
        ? process.env.TEST_DATABASE_URL || process.env.DATABASE_URL
        : env.DATABASE_URL;

    // Log the database URL being used for debugging
    if (process.env.NODE_ENV === 'test') {
      this.logger.info(
        `PrismaService: Using test database URL: ${databaseUrl?.split('@')[1] || 'unknown'}`
      );
    }

    this.client = new PrismaClient({
      datasources: {
        db: {
          url: databaseUrl,
        },
      },
    });

    this.setupLogging();
  }

  public static getInstance(logger: Logger): PrismaService {
    if (!PrismaService.instance) {
      PrismaService.instance = new PrismaService(logger);
    }
    return PrismaService.instance;
  }

  /**
   * Reset the singleton instance - for testing purposes only
   */
  public static resetInstance(): void {
    if (PrismaService.instance?.client) {
      try {
        if (typeof PrismaService.instance.client.$disconnect === 'function') {
          PrismaService.instance.client.$disconnect().catch(() => {
            // Ignore disconnect errors during reset
          });
        }
      } catch {
        // Ignore errors during reset
      }
    }
    PrismaService.instance = null as unknown as PrismaService;
  }

  public get prisma(): PrismaClient {
    return this.client;
  }

  private setupLogging(): void {
    // Simplified logging setup - can be enhanced later if needed
    this.logger.info('Prisma client initialized');
  }

  public async connect(): Promise<void> {
    try {
      await this.client.$connect();
      this.logger.info('Database connected successfully');
    } catch (error) {
      this.logger.error('Failed to connect to database', { error });
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    try {
      await this.client.$disconnect();
      this.logger.info('Database disconnected successfully');
    } catch (error) {
      this.logger.error('Failed to disconnect from database', { error });
      throw error;
    }
  }

  public async healthCheck(): Promise<boolean> {
    try {
      await this.client.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      this.logger.error('Database health check failed', { error });
      return false;
    }
  }

  /**
   * Comprehensive database connectivity check for authentication
   * Includes connection test and basic table access verification
   */
  public async authHealthCheck(): Promise<{
    healthy: boolean;
    error?: string;
  }> {
    try {
      // Test basic connectivity
      await this.client.$queryRaw`SELECT 1`;

      // Test table access (users table is critical for auth)
      await this.client.user.findFirst({
        where: { id: 'non-existent-id' },
        select: { id: true },
      });

      return { healthy: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown database error';
      this.logger.error('Database auth health check failed', {
        error: errorMessage,
        stack: error instanceof Error ? error.stack : undefined,
      });

      return {
        healthy: false,
        error: errorMessage,
      };
    }
  }
}
