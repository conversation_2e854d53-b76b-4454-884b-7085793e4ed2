/**
 * Common permission constants for use with RBAC hooks and components
 */
export const PermissionConstants = {
  // User permissions
  USER_READ: { resource: "USER", action: "read" },
  USER_WRITE: { resource: "USER", action: "write" },
  USER_DELETE: { resource: "USER", action: "delete" },
  USER_MANAGE: { resource: "USER", action: "manage" },

  // Tenant permissions
  TENANT_READ: { resource: "TENANT", action: "read" },
  TENANT_WRITE: { resource: "TENANT", action: "write" },
  TENANT_MANAGE: { resource: "TENANT", action: "manage" },

  // Data permissions
  DATA_READ: { resource: "DATA", action: "read" },
  DATA_WRITE: { resource: "DATA", action: "write" },

  // System permissions
  SYSTEM_MANAGE: { resource: "SYSTEM", action: "manage" },
} as const;

/**
 * Role type constants - imported from shared workspace
 */
export { ROLE_TYPES as RoleConstants } from "@tech-notes/shared";
