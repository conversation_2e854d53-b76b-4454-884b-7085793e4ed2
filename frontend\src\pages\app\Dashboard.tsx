import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import HealthDashboard from "../../components/organisms/HealthDashboard";
import { ComponentShowcase } from "../../components/organisms/ComponentShowcase";
import { healthService } from "../../services/api";
import {
  Button,
  StatCard,
  Card,
  CardHeader,
  CardContent,
  Alert,
} from "../../components";
import { useTypedApi } from "../../services/api-client";
import { useAuth } from "../../hooks/useAuth";
import { usePermissions } from "../../hooks/usePermissions";
import { Users, Building2, Activity, TrendingUp } from "lucide-react";

type View = "dashboard" | "health" | "showcase";

export const Dashboard: React.FC = () => {
  const [currentView, setCurrentView] = useState<View>("dashboard");
  const api = useTypedApi();
  const { userProfile } = useAuth();
  const { canViewUsers, isSystemAdmin } = usePermissions();

  // Fetch users data for statistics (only if user has permission)
  const {
    data: usersResponse,
    isLoading: isLoadingUsers,
    error: usersError,
  } = useQuery({
    queryKey: ["users"],
    queryFn: () => api.users.getAll(),
    enabled: canViewUsers,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Note: Authentication, service availability, and onboarding checks
  // are now handled by AuthGuard component in App.tsx

  return (
    <>
      {/* Enhanced View Toggle with Salient styling */}
      <div className="flex justify-center mb-8">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-1.5 shadow-sm border border-gray-200/60">
          <div className="flex space-x-1">
            <Button
              variant={currentView === "dashboard" ? "primary" : "ghost"}
              size="sm"
              onClick={() => setCurrentView("dashboard")}
              className={currentView === "dashboard" ? "shadow-sm" : ""}
            >
              Dashboard
            </Button>
            <Button
              variant={currentView === "health" ? "primary" : "ghost"}
              size="sm"
              onClick={() => setCurrentView("health")}
              className={currentView === "health" ? "shadow-sm" : ""}
            >
              Health
            </Button>
            <Button
              variant={currentView === "showcase" ? "primary" : "ghost"}
              size="sm"
              onClick={() => setCurrentView("showcase")}
              className={currentView === "showcase" ? "shadow-sm" : ""}
            >
              Showcase
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      {currentView === "dashboard" && (
        <div className="space-y-8">
          {/* Enhanced Welcome Section */}
          <div className="text-center bg-gradient-to-r from-primary-50 to-blue-50 rounded-2xl p-8 border border-primary-100/50">
            <div className="max-w-2xl mx-auto">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">
                Welcome back
                {userProfile?.firstName ? `, ${userProfile.firstName}` : ""}!
              </h1>
              <p className="mt-4 text-lg text-gray-600 leading-relaxed">
                Here's an overview of your system performance and key metrics
              </p>
              {userProfile?.tenantId && (
                <div className="mt-4 inline-flex items-center px-3 py-1 rounded-full bg-white/60 border border-primary-200/60">
                  <Building2 className="h-4 w-4 text-primary-500 mr-2" />
                  <span className="text-sm font-medium text-gray-700">
                    Tenant: {userProfile.tenantId.slice(-8)}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Enhanced Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Users Statistics */}
            {canViewUsers ? (
              <StatCard
                title="Total Users"
                value={
                  isLoadingUsers
                    ? "--"
                    : usersResponse?.meta.count?.toString() || "0"
                }
                change={{
                  value: isLoadingUsers ? "Loading..." : "Active",
                  trend: "neutral",
                  period: "",
                }}
                icon={<Users className="h-6 w-6 text-primary-600" />}
              />
            ) : (
              <StatCard
                title="Users"
                value="--"
                change={{ value: "No Access", trend: "neutral", period: "" }}
                icon={<Users className="h-6 w-6 text-gray-400" />}
              />
            )}

            {/* Tenant Information */}
            <StatCard
              title="Current Tenant"
              value={userProfile?.tenantId ? "1" : "--"}
              change={{
                value: userProfile?.tenantId ? "Active" : "Unknown",
                trend: userProfile?.tenantId ? "up" : "neutral",
                period: "",
              }}
              icon={<Building2 className="h-6 w-6 text-primary-600" />}
            />

            {/* User Status */}
            <StatCard
              title="Your Status"
              value={userProfile?.isActive ? "Active" : "Inactive"}
              change={{
                value: userProfile?.roles?.length
                  ? `${userProfile.roles.length} roles`
                  : "No roles",
                trend: userProfile?.isActive ? "up" : "down",
                period: "",
              }}
              icon={<Activity className="h-6 w-6 text-primary-600" />}
            />

            {/* System Health */}
            <StatCard
              title="System Health"
              value="Online"
              change={{ value: "Operational", trend: "up", period: "" }}
              icon={<TrendingUp className="h-6 w-6 text-primary-600" />}
            />
          </div>

          {/* Enhanced Quick Actions */}
          <Card className="bg-gradient-to-br from-white to-gray-50/50 border-gray-200/60 shadow-sm">
            <CardHeader
              title="Quick Actions"
              subtitle="Access frequently used features and tools"
            />
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {canViewUsers && (
                  <Button
                    variant="outline"
                    className="h-24 flex flex-col items-center justify-center space-y-3 bg-white/80 hover:bg-primary-50 hover:border-primary-200 transition-all duration-200 group"
                    onClick={() => (window.location.href = "/app/users")}
                  >
                    <Users className="h-7 w-7 text-primary-600 group-hover:text-primary-700" />
                    <span className="font-medium">Manage Users</span>
                  </Button>
                )}

                <Button
                  variant="outline"
                  className="h-24 flex flex-col items-center justify-center space-y-3 bg-white/80 hover:bg-primary-50 hover:border-primary-200 transition-all duration-200 group"
                  onClick={() => setCurrentView("health")}
                >
                  <Activity className="h-7 w-7 text-primary-600 group-hover:text-primary-700" />
                  <span className="font-medium">System Health</span>
                </Button>

                {isSystemAdmin && (
                  <Button
                    variant="outline"
                    className="h-24 flex flex-col items-center justify-center space-y-3 bg-white/80 hover:bg-primary-50 hover:border-primary-200 transition-all duration-200 group"
                    onClick={() => (window.location.href = "/app/admin")}
                  >
                    <Building2 className="h-7 w-7 text-primary-600 group-hover:text-primary-700" />
                    <span className="font-medium">Admin Panel</span>
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Error Display */}
          {usersError && (
            <Alert variant="warning">
              <p>
                Unable to load user statistics. Some data may be unavailable.
              </p>
            </Alert>
          )}
        </div>
      )}

      {currentView === "health" && (
        <div>
          <div className="text-center mb-8 bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-100/50">
            <h2 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
              System Health
            </h2>
            <p className="mt-3 text-lg text-gray-600 leading-relaxed">
              Monitor system status and performance metrics in real-time
            </p>
          </div>
          <HealthDashboard healthService={healthService} />
        </div>
      )}

      {currentView === "showcase" && <ComponentShowcase />}
    </>
  );
};
