import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Stat<PERSON>ard, Alert, LoadingSpinner } from "../index";
import { useTypedApi } from "../../services/api-client";
import { usePermissions } from "../../hooks/usePermissions";
import { Users, Activity, TrendingUp, Clock } from "lucide-react";

interface EngagementMetricsProps {
  tenantId?: string; // For System Admins to view specific tenant
  className?: string;
}

export const EngagementMetrics: React.FC<EngagementMetricsProps> = ({
  tenantId,
  className,
}) => {
  const api = useTypedApi();
  const { isCompanyAdmin, isSystemAdmin } = usePermissions();

  // Only Company Admins and System Admins can view engagement metrics
  const canViewMetrics = isCompanyAdmin || isSystemAdmin;

  const {
    data: metricsResponse,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["engagement-metrics", tenantId],
    queryFn: () => api.engagement.getMetrics(tenantId),
    enabled: canViewMetrics,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
  });

  if (!canViewMetrics) {
    return (
      <Alert variant="warning">
        <p>You don't have permission to view engagement metrics.</p>
      </Alert>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size="lg" />
        <span className="ml-2 text-gray-600">
          Loading engagement metrics...
        </span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="error">
        <p>Failed to load engagement metrics. Please try again.</p>
        <button
          onClick={() => refetch()}
          className="mt-2 text-sm underline hover:no-underline"
        >
          Retry
        </button>
      </Alert>
    );
  }

  if (!metricsResponse?.data) {
    return (
      <Alert variant="info">
        <p>No engagement data available.</p>
      </Alert>
    );
  }

  const metrics = metricsResponse.data;

  return (
    <div className={className}>
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900">
          User Engagement Overview
        </h2>
        <p className="text-sm text-gray-600 mt-1">
          Track user activity and engagement across your platform
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Daily Active Users */}
        <StatCard
          title="Daily Active Users"
          value={metrics.dau}
          icon={<Activity className="h-6 w-6 text-primary-600" />}
          change={{
            value: `${((metrics.dau / metrics.totalUsers) * 100).toFixed(1)}%`,
            trend: "neutral",
            period: "of total users",
          }}
        />

        {/* Weekly Active Users */}
        <StatCard
          title="Weekly Active Users"
          value={metrics.wau}
          icon={<TrendingUp className="h-6 w-6 text-blue-600" />}
          change={{
            value: `${((metrics.wau / metrics.totalUsers) * 100).toFixed(1)}%`,
            trend: "neutral",
            period: "of total users",
          }}
        />

        {/* Monthly Active Users */}
        <StatCard
          title="Monthly Active Users"
          value={metrics.mau}
          icon={<Users className="h-6 w-6 text-green-600" />}
          change={{
            value: `${((metrics.mau / metrics.totalUsers) * 100).toFixed(1)}%`,
            trend: "neutral",
            period: "of total users",
          }}
        />

        {/* Total Users */}
        <StatCard
          title="Total Users"
          value={metrics.totalUsers}
          icon={<Users className="h-6 w-6 text-gray-600" />}
        />
      </div>

      {/* Inactive Users Section */}
      <div className="mt-8">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Inactive Users
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <StatCard
            title="Inactive (1 Week)"
            value={metrics.inactiveOneWeek}
            icon={<Clock className="h-6 w-6 text-yellow-600" />}
            change={{
              value: `${((metrics.inactiveOneWeek / metrics.totalUsers) * 100).toFixed(1)}%`,
              trend:
                metrics.inactiveOneWeek > metrics.totalUsers * 0.3
                  ? "up"
                  : "neutral",
              period: "of total users",
            }}
          />

          <StatCard
            title="Inactive (1 Month)"
            value={metrics.inactiveOneMonth}
            icon={<Clock className="h-6 w-6 text-red-600" />}
            change={{
              value: `${((metrics.inactiveOneMonth / metrics.totalUsers) * 100).toFixed(1)}%`,
              trend:
                metrics.inactiveOneMonth > metrics.totalUsers * 0.5
                  ? "up"
                  : "neutral",
              period: "of total users",
            }}
          />
        </div>
      </div>

      {/* Metadata */}
      <div className="mt-6 text-xs text-gray-500">
        Last updated:{" "}
        {new Date(metricsResponse.meta.timestamp).toLocaleString()}
        {tenantId && ` • Tenant: ${metricsResponse.meta.tenantId}`}
      </div>
    </div>
  );
};
