# Testing Conventions - Tech Notes

**Simplified testing approach designed specifically for AI agents working on MVP-stage projects.**

---

## 🎯 Core Philosophy

- **Simple over sophisticated** - AI agents work better with straightforward patterns
- **Reliable over comprehensive** - Tests should rarely break when unrelated code changes
- **Fast feedback** - No Docker, no complex setup, just `npm test` and go
- **Clear types** - No `any` types in tests, AI agents need type guidance

---

## 🚨 Critical Rules for AI Agents

1. **Always run `npm run test` before and after changes** - Establish baseline, verify nothing broke
2. **All backend operations must be tenant-scoped** - Every test must verify tenant isolation
3. **Use dependency injection** - Inject mocks, never create services directly in tests
4. **Never use `any` types** - Always provide proper TypeScript interfaces
5. **Only modify tests related to your changes** - Don't touch unrelated test files
6. **If a test breaks, fix it, don't skip it** - Maintain test reliability
7. **Keep tests simple and focused** - One thing per test, direct mocking only
8. **Test behavior, not implementation** - Focus on what the code does, not how

---

## 📁 Test Structure

```
backend/src/
├── services/user.service.test.ts          # Co-located unit tests
├── middleware/auth.middleware.test.ts     # Security-critical tests
├── routes/api/v1/health.test.ts          # Simple API tests
└── __tests__/setup.ts                     # Minimal test setup
```

## ✅ Test Patterns

### Service Unit Tests

```typescript
import { UserService } from "./user.service";
jest.mock("./prisma.service");

describe("UserService", () => {
  let userService: UserService;
  let mockPrisma: jest.Mocked<PrismaService>;

  beforeEach(() => {
    mockPrisma = { prisma: { user: { findMany: jest.fn() } } } as any;
    userService = new UserService(mockPrisma, console);
  });

  it("should return users for tenant", async () => {
    const mockUsers = [{ id: "1", tenantId: "tenant1" }];
    mockPrisma.prisma.user.findMany.mockResolvedValue(mockUsers);

    const result = await userService.getUsersByTenant("tenant1");

    expect(result).toEqual(mockUsers);
    // ✅ Critical: Verify tenant isolation
    expect(mockPrisma.prisma.user.findMany).toHaveBeenCalledWith({
      where: { tenantId: "tenant1" },
    });
  });
});
```

### Route Tests

```typescript
import request from "supertest";
import { healthRouter } from "./health";

describe("Health Route", () => {
  const app = express();
  app.use("/health", healthRouter);

  it("should return health status", async () => {
    const response = await request(app).get("/health").expect(200);
    expect(response.body).toHaveProperty("status", "healthy");
  });
});
```

## ❌ Avoid These Patterns

- **Complex mock factories** - Use direct `jest.mock()` instead
- **Docker/integration setup** - Keep tests simple and fast
- **Any types** - Always provide proper TypeScript interfaces

---

## 📋 Test Coverage Strategy

### Must Test:

- **Service business logic** - Core functionality
- **Authentication middleware** - Security critical
- **Route handlers** - API behavior
- **Tenant isolation** - Every backend operation
- **Error handling** - Edge cases

### Don't Over-Test:

- **Database queries** - Trust Prisma
- **Third-party integrations** - Mock them
- **Complex workflows** - Test components separately

---

## 📝 Quick Reference

### Test Commands

```bash
npm test                    # Run all tests
npm run test:watch         # Watch mode for development
```

### Test Naming Convention

```typescript
it("should [expected behavior] when [condition]", async () => {});
// Example: it("should return tenant-scoped users when valid tenant ID provided", async () => {});
```

### Essential Imports

```typescript
// Backend (Jest)
import { describe, it, expect, beforeEach } from "@jest/globals";
import request from "supertest";

// Frontend (Vitest)
import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
```

### Adding New Tests:

1. **Co-locate** test files next to source files
2. **Use direct mocking** - `jest.mock('./dependency')`
3. **Follow naming convention** - `should [behavior] when [condition]`
4. **Verify tenant isolation** - All backend tests must check tenant scoping
5. **Mock external dependencies** - Database, APIs, etc.

### When Tests Break:

1. **If your change should affect the test** - Update the test
2. **If not** - The test was too brittle, simplify it
3. **Never skip tests** - Fix them instead

---

**Remember: Simple tests that run reliably are infinitely better than complex tests that break constantly.**
