import React from "react";
import { renderHook, waitFor } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach } from "vitest";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useAuth } from "../../src/hooks/useAuth";
import {
  createMockAuthService,
  setupMockAuthService,
  createMockClerkHooks,
  setupClerkMocks,
  type MockAuthService,
} from "../__tests__/helpers/service-factory.helper";

// Create mock Clerk hooks
const clerkMocks = createMockClerkHooks();
const { mockUseUser, mockUseAuth, mockUseClerk, mockUseSignUp } = clerkMocks;

vi.mock("@clerk/clerk-react", () => ({
  useUser: () => mockUseUser(),
  useAuth: () => mockUseAuth(),
  useClerk: () => mockUseClerk(),
  useSignUp: () => mockUseSignUp(),
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 0,
        gcTime: 0,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe("useAuth Hook", () => {
  let mockAuthService: MockAuthService;

  beforeEach(() => {
    vi.clearAllMocks();

    // Create fresh mock service for each test
    mockAuthService = createMockAuthService();

    // Setup default Clerk mocks
    setupClerkMocks(clerkMocks, "signed-out");
  });

  describe("Service Unavailable State", () => {
    it("should detect service unavailable from auth status", async () => {
      // Arrange
      setupClerkMocks(clerkMocks, "signed-in");
      setupMockAuthService(mockAuthService, "service-unavailable");

      // Act
      const { result } = renderHook(
        () => useAuth({ authService: mockAuthService }),
        {
          wrapper: createWrapper(),
        },
      );

      // Assert
      await waitFor(() => {
        expect(result.current.isServiceUnavailable).toBe(true);
        expect(result.current.isAuthenticated).toBe(false);
        expect(result.current.isOnboarded).toBe(false);
      });
    });

    it("should not load user profile when service is unavailable", async () => {
      // Arrange
      setupClerkMocks(clerkMocks, "signed-in");
      setupMockAuthService(mockAuthService, "service-unavailable");

      // Act
      const { result } = renderHook(
        () => useAuth({ authService: mockAuthService }),
        {
          wrapper: createWrapper(),
        },
      );

      // Assert
      await waitFor(() => {
        expect(result.current.isServiceUnavailable).toBe(true);
        expect(result.current.isAuthenticated).toBe(false);
        expect(result.current.isOnboarded).toBe(false);
      });

      // Wait a bit more to ensure no profile loading is attempted
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Verify profile was not loaded
      expect(mockAuthService.getCurrentUser).not.toHaveBeenCalled();
    });

    it("should handle 503 errors in auth status check", async () => {
      // Arrange
      setupClerkMocks(clerkMocks, "signed-in");
      setupMockAuthService(mockAuthService, "error");

      // Act
      const { result } = renderHook(
        () => useAuth({ authService: mockAuthService }),
        {
          wrapper: createWrapper(),
        },
      );

      // Assert
      await waitFor(
        () => {
          expect(result.current.isServiceUnavailable).toBe(true);
          expect(result.current.isAuthenticated).toBe(false);
        },
        { timeout: 5000 },
      );
    });
  });

  describe("Authentication State Logic", () => {
    it("should be authenticated when all conditions are met", async () => {
      // Arrange
      setupClerkMocks(clerkMocks, "signed-in");
      setupMockAuthService(mockAuthService, "authenticated");

      // Act
      const { result } = renderHook(
        () => useAuth({ authService: mockAuthService }),
        {
          wrapper: createWrapper(),
        },
      );

      // Assert
      await waitFor(() => {
        expect(result.current.isAuthenticated).toBe(true);
        expect(result.current.isOnboarded).toBe(true);
        expect(result.current.needsOnboarding).toBe(false);
        expect(result.current.isServiceUnavailable).toBe(false);
      });
    });

    it("should not be authenticated when Clerk token is invalid", async () => {
      // Arrange
      setupClerkMocks(clerkMocks, "signed-out");

      // Act
      const { result } = renderHook(
        () => useAuth({ authService: mockAuthService }),
        {
          wrapper: createWrapper(),
        },
      );

      // Assert
      await waitFor(() => {
        expect(result.current.isAuthenticated).toBe(false);
        expect(result.current.isOnboarded).toBe(false);
      });
    });

    it("should not be authenticated when backend auth fails", async () => {
      // Arrange
      setupClerkMocks(clerkMocks, "signed-in");
      setupMockAuthService(mockAuthService, "unauthenticated");

      // Act
      const { result } = renderHook(
        () => useAuth({ authService: mockAuthService }),
        {
          wrapper: createWrapper(),
        },
      );

      // Assert
      await waitFor(() => {
        expect(result.current.isAuthenticated).toBe(false);
        expect(result.current.isOnboarded).toBe(false);
      });
    });

    it("should not be authenticated when service is unavailable", async () => {
      // Arrange
      setupClerkMocks(clerkMocks, "signed-in");
      setupMockAuthService(mockAuthService, "service-unavailable");

      // Act
      const { result } = renderHook(
        () => useAuth({ authService: mockAuthService }),
        {
          wrapper: createWrapper(),
        },
      );

      // Assert
      await waitFor(() => {
        expect(result.current.isAuthenticated).toBe(false);
        expect(result.current.isOnboarded).toBe(false);
        expect(result.current.isServiceUnavailable).toBe(true);
      });
    });
  });

  describe("Onboarding State", () => {
    it("should need onboarding when authenticated but not onboarded", async () => {
      // Arrange
      setupClerkMocks(clerkMocks, "signed-in");
      setupMockAuthService(mockAuthService, "onboarding");

      // Act
      const { result } = renderHook(
        () => useAuth({ authService: mockAuthService }),
        {
          wrapper: createWrapper(),
        },
      );

      // Assert
      await waitFor(() => {
        expect(result.current.isAuthenticated).toBe(true);
        expect(result.current.isOnboarded).toBe(false);
        expect(result.current.needsOnboarding).toBe(true);
      });
    });

    it("should not need onboarding when service is unavailable", async () => {
      // Arrange
      setupClerkMocks(clerkMocks, "signed-in");
      setupMockAuthService(mockAuthService, "service-unavailable");

      // Act
      const { result } = renderHook(
        () => useAuth({ authService: mockAuthService }),
        {
          wrapper: createWrapper(),
        },
      );

      // Assert
      await waitFor(() => {
        expect(result.current.needsOnboarding).toBe(false);
        expect(result.current.isServiceUnavailable).toBe(true);
      });
    });
  });

  describe("Loading State", () => {
    it("should be loading when Clerk is not loaded", () => {
      // Arrange
      setupClerkMocks(clerkMocks, "loading");

      // Act
      const { result } = renderHook(
        () => useAuth({ authService: mockAuthService }),
        {
          wrapper: createWrapper(),
        },
      );

      // Assert
      expect(result.current.isLoading).toBe(true);
    });

    it("should be loading when auth status is being checked", () => {
      // Arrange
      setupClerkMocks(clerkMocks, "signed-in");
      // Don't resolve the auth status check immediately
      mockAuthService.checkAuthStatus.mockImplementation(
        () => new Promise(() => {}),
      );

      // Act
      const { result } = renderHook(
        () => useAuth({ authService: mockAuthService }),
        {
          wrapper: createWrapper(),
        },
      );

      // Assert
      expect(result.current.isLoading).toBe(true);
    });

    it("should be loading when user profile is being loaded", async () => {
      // Arrange
      setupClerkMocks(clerkMocks, "signed-in");
      setupMockAuthService(mockAuthService, "authenticated");
      // Don't resolve the profile check immediately
      mockAuthService.getCurrentUser.mockImplementation(
        () => new Promise(() => {}),
      );

      // Act
      const { result } = renderHook(
        () => useAuth({ authService: mockAuthService }),
        {
          wrapper: createWrapper(),
        },
      );

      // Assert
      await waitFor(() => {
        expect(result.current.isLoading).toBe(true);
      });
    });
  });

  describe("Error Handling", () => {
    it("should handle auth status check errors gracefully", async () => {
      // Arrange
      setupClerkMocks(clerkMocks, "signed-in");
      mockAuthService.checkAuthStatus.mockRejectedValue(
        new Error("Network error"),
      );

      // Act
      const { result } = renderHook(
        () => useAuth({ authService: mockAuthService }),
        {
          wrapper: createWrapper(),
        },
      );

      // Assert
      await waitFor(() => {
        expect(result.current.isAuthenticated).toBe(false);
        expect(result.current.isServiceUnavailable).toBe(false);
      });
    });

    it("should handle profile loading errors gracefully", async () => {
      // Arrange
      setupClerkMocks(clerkMocks, "signed-in");
      setupMockAuthService(mockAuthService, "authenticated");
      mockAuthService.getCurrentUser.mockRejectedValue(
        new Error("Profile load error"),
      );

      // Act
      const { result } = renderHook(
        () => useAuth({ authService: mockAuthService }),
        {
          wrapper: createWrapper(),
        },
      );

      // Wait for auth status to resolve first
      await waitFor(() => {
        expect(result.current.isAuthenticated).toBe(true);
      });

      // Then wait for profile error to be set
      await waitFor(
        () => {
          expect(result.current.profileError).toBeTruthy();
        },
        { timeout: 3000 },
      );
    });
  });

  describe("Auth Actions", () => {
    it("should provide sign out functionality", async () => {
      // Arrange
      const mockSignOut = vi.fn();
      clerkMocks.mockUseAuth.mockReturnValue({
        signOut: mockSignOut,
        getToken: vi.fn().mockResolvedValue("mock-token"),
      });

      // Act
      const { result } = renderHook(
        () => useAuth({ authService: mockAuthService }),
        {
          wrapper: createWrapper(),
        },
      );
      await result.current.signOut();

      // Assert
      expect(mockSignOut).toHaveBeenCalled();
    });

    it("should provide token access", async () => {
      // Arrange
      const mockGetToken = vi.fn().mockResolvedValue("mock-token");
      clerkMocks.mockUseAuth.mockReturnValue({
        signOut: vi.fn(),
        getToken: mockGetToken,
      });

      // Act
      const { result } = renderHook(
        () => useAuth({ authService: mockAuthService }),
        {
          wrapper: createWrapper(),
        },
      );
      const token = await result.current.getToken();

      // Assert
      expect(token).toBe("mock-token");
      expect(mockGetToken).toHaveBeenCalled();
    });
  });
});
