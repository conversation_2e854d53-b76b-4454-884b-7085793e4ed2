# Database Patterns - Quick Reference

Essential database patterns for multi-tenant TypeScript applications using Prisma.

---

## 🏗️ Architecture

```
Controllers → Domain Services → Prisma → PostgreSQL
```

**Service Layer:**

- **PrismaService**: Connection management, infrastructure
- **BaseTenantService**: Abstract base with tenant scoping
- **Domain Services**: UserService, TenantService extending base

**Core Rule**: All operations must be tenant-scoped unless explicitly global.

---

## 📋 Prisma Model Patterns

### Required Model Structure

```prisma
model User {
  id        String   @id @default(cuid())
  clerkId   String   @unique
  email     String
  tenantId  String   // REQUIRED for multi-tenancy
  isActive  Boolean  @default(true)  // User activation status
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  tenant    Tenant   @relation(fields: [tenantId], references: [id])

  @@index([tenantId])           // Required for tenant isolation
  @@index([clerkId])            // Required for auth lookups
  @@index([tenantId, email])    // For user search within tenant
  @@index([tenantId, isActive]) // For filtering active users
  @@map("users")
}

model Tenant {
  id        String   @id @default(cuid())
  name      String
  slug      String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  users     User[]

  @@map("tenants")
}
```

**Required Fields:**

- All models: `id`, `createdAt`, `updatedAt`
- Tenant-scoped models: `tenantId`
- Use `@@map("table_name")` for snake_case tables
- Use `@default(cuid())` for primary keys

---

## 🔧 Service Implementation

### BaseTenantService

```typescript
export abstract class BaseTenantService {
  protected prismaService: PrismaService;
  protected logger: Logger;

  constructor(prismaService: PrismaService, logger: Logger) {
    this.prismaService = prismaService;
    this.logger = logger;
  }

  protected get prisma(): PrismaClient {
    return this.prismaService.prisma;
  }

  // Execute operation with automatic tenant scope validation
  protected async withTenantScope<T>(
    tenantId: string,
    operation: () => Promise<T>,
    userId?: string,
  ): Promise<T> {
    await this.validateTenantAccess(tenantId, userId);
    return await operation();
  }

  protected async validateTenantAccess(
    tenantId: string,
    userId?: string,
  ): Promise<void> {
    const tenant = await this.prisma.tenant.findUnique({
      where: { id: tenantId },
      select: { id: true },
    });

    if (!tenant) {
      throw new NotFoundError(`Tenant ${tenantId} not found`);
    }

    if (userId) {
      const user = await this.prisma.user.findFirst({
        where: { clerkId: userId, tenantId },
        select: { id: true },
      });

      if (!user) {
        throw new ForbiddenError(
          `User ${userId} does not have access to tenant ${tenantId}`,
        );
      }
    }
  }

  protected async transaction<T>(fn: (prisma: any) => Promise<T>): Promise<T> {
    return await this.prisma.$transaction(fn);
  }
}
```

### Domain Service Pattern

```typescript
export class UserService extends BaseTenantService {
  async getUsersByTenant(tenantId: string): Promise<User[]> {
    return await this.withTenantScope(tenantId, async () => {
      return await this.prisma.user.findMany({
        where: { tenantId },
        orderBy: { createdAt: "desc" },
      });
    });
  }

  async getUserById(userId: string, tenantId: string): Promise<User | null> {
    return await this.withTenantScope(tenantId, async () => {
      return await this.prisma.user.findFirst({
        where: { id: userId, tenantId },
      });
    });
  }

  // Transaction example
  async createUserWithProfile(
    userData: CreateUserData,
    tenantId: string,
  ): Promise<User> {
    return await this.withTenantScope(tenantId, async () => {
      return await this.transaction(async (tx) => {
        const user = await tx.user.create({
          data: { ...userData, tenantId },
        });

        await tx.userProfile.create({
          data: { userId: user.id, tenantId },
        });

        return user;
      });
    });
  }
}
```

---

## 🛡️ Security Patterns

### Tenant Isolation

```typescript
// ✅ CORRECT - Tenant-scoped query
const users = await prisma.user.findMany({
  where: { tenantId: currentTenantId },
});

// ❌ WRONG - Global query (security risk)
const users = await prisma.user.findMany();
```

### Input Validation

```typescript
import { z } from "zod";

const CreateUserSchema = z.object({
  email: z.string().email(),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  tenantId: z.string().cuid(),
});

export const validateCreateUser = (data: unknown) => {
  return CreateUserSchema.parse(data);
};
```

---

## 🔄 Migration Commands

```bash
# Development
yarn prisma migrate dev --name add_user_profiles
yarn prisma migrate reset
yarn prisma generate

# Production
yarn prisma migrate deploy
yarn prisma migrate status
```

**Migration Rules:**

- Always review generated SQL
- Use descriptive names: `add_user_profiles`, `update_tenant_constraints`
- Avoid destructive operations without planning
- Migrations run automatically on backend start

---

## ⚡ Performance Patterns

### Query Optimization

```typescript
// ✅ Selective field loading
const users = await prisma.user.findMany({
  where: { tenantId },
  select: { id: true, email: true, firstName: true, lastName: true },
});

// ✅ Efficient pagination
const users = await prisma.user.findMany({
  where: { tenantId },
  take: 20,
  skip: page * 20,
  orderBy: { createdAt: "desc" },
});

// ✅ Include related data efficiently
const usersWithProfiles = await prisma.user.findMany({
  where: { tenantId },
  include: { profile: true },
});
```

### Connection Management

```typescript
// Singleton pattern
let prisma: PrismaClient;

if (process.env.NODE_ENV === "production") {
  prisma = new PrismaClient();
} else {
  if (!global.prisma) {
    global.prisma = new PrismaClient();
  }
  prisma = global.prisma;
}

export { prisma };
```

---

## 🚨 Common Pitfalls

**Security:**

- Never query without tenant scope (unless explicitly global)
- Don't trust client-provided tenantId - get from authenticated context
- Validate foreign key relationships respect tenant boundaries

**Performance:**

- Don't use `findMany()` without limits on large datasets
- Avoid N+1 queries - use `include` or `select`
- Always index `tenantId` fields

**Data Integrity:**

- Always use transactions for multi-table operations
- Handle unique constraint violations gracefully
- Validate required relationships before deletion

---

## 📚 Quick Reference

### Service Template

```typescript
export class CustomService extends BaseTenantService {
  async getCustomData(tenantId: string): Promise<CustomData[]> {
    return await this.withTenantScope(tenantId, async () => {
      return await this.prisma.customModel.findMany({
        where: { tenantId },
        orderBy: { createdAt: "desc" },
      });
    });
  }

  async createWithTransaction(
    data: CreateData,
    tenantId: string,
  ): Promise<Result> {
    return await this.withTenantScope(tenantId, async () => {
      return await this.transaction(async (tx) => {
        const result1 = await tx.model1.create({ data: { ...data, tenantId } });
        const result2 = await tx.model2.create({
          data: { model1Id: result1.id, tenantId },
        });
        return { result1, result2 };
      });
    });
  }
}
```

### Common Operations

```typescript
// Tenant-scoped operations (automatic validation)
const users = await userService.getUsersByTenant(tenantId);
const user = await userService.getUserById(userId, tenantId);

// Create operations
const newUser = await userService.createUser({
  clerkId: "clerk_123",
  email: "<EMAIL>",
  tenantId,
});

// Clerk integration
const syncedUser = await userService.syncUserFromClerk(clerkUserData, tenantId);
```

---

**Core Principle**: Every database operation must respect tenant boundaries. Use `withTenantScope()` for automatic validation.

**For detailed testing patterns**: See `TESTING_CONVENTIONS.md`
**For API integration**: See `API_CONVENTIONS.md`
