import { Router, Request, Response } from 'express';
import { z } from 'zod';

import {
  MiddlewareFactory,
  CommonPermissions,
} from '../../../middleware/middleware-factory.js';
import { CreateTenantData } from '../../../services/tenant.service.js';
import { TenantService } from '../../../services/tenant.service.js';
import { Logger } from '../../../utils/logger.js';
import { getRequestUser } from '../../../utils/request-types.js';

interface ServiceDependencies {
  tenantService: TenantService;
  middlewareFactory: MiddlewareFactory;
  logger: Logger;
}

// Validation schemas
const createTenantSchema = z.object({
  name: z
    .string()
    .min(1, 'Tenant name is required')
    .max(100, 'Tenant name too long'),
  slug: z
    .string()
    .min(1, 'Tenant slug is required')
    .max(50, 'Tenant slug too long')
    .regex(
      /^[a-z0-9-]+$/,
      'Slug must contain only lowercase letters, numbers, and hyphens'
    ),
  isActive: z.boolean().optional(),
});

const updateTenantSchema = z.object({
  name: z
    .string()
    .min(1, 'Tenant name is required')
    .max(100, 'Tenant name too long')
    .optional(),
  slug: z
    .string()
    .min(1, 'Tenant slug is required')
    .max(50, 'Tenant slug too long')
    .regex(
      /^[a-z0-9-]+$/,
      'Slug must contain only lowercase letters, numbers, and hyphens'
    )
    .optional(),
  isActive: z.boolean().optional(),
});

const tenantIdSchema = z.object({
  tenantId: z.string().cuid('Invalid tenant ID format'),
});

export function createTenantsRouter(dependencies: ServiceDependencies): Router {
  const { tenantService, middlewareFactory, logger } = dependencies;
  const router = Router();

  /**
   * GET /api/v1/tenants
   * Get all tenants (requires TENANT_READ permission)
   * System Admins can see all tenants, Company Admins see only their own
   */
  router.get(
    '/',
    ...middlewareFactory.createAuthWithPermission(
      CommonPermissions.TENANT_READ
    ),
    async (req: Request, res: Response) => {
      try {
        const user = getRequestUser(req);

        // System Admins can access all tenants
        if (user!.canBypassTenantScope) {
          const tenants = await tenantService.getAllTenants();
          return res.json({
            data: tenants,
            meta: { count: tenants.length, scope: 'all' },
            timestamp: new Date().toISOString(),
          });
        }

        // Company Admins can only see their own tenant
        const tenant = await tenantService.getTenantById(user!.tenantId, user!);
        res.json({
          data: [tenant],
          meta: { count: 1, scope: 'tenant' },
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        logger.error('Failed to get tenants', {
          error: error instanceof Error ? error.message : 'Unknown error',
          userId: getRequestUser(req)?.id,
          tenantId: getRequestUser(req)?.tenantId,
        });

        res.status(500).json({
          error: 'Internal Server Error',
          message: 'Failed to retrieve tenants',
          statusCode: 500,
          timestamp: new Date().toISOString(),
        });
      }
    }
  );

  /**
   * GET /api/v1/tenants/:tenantId
   * Get tenant by ID (requires TENANT_READ permission)
   * System Admins can access any tenant, others restricted to their own
   */
  router.get(
    '/:tenantId',
    ...middlewareFactory.createAuthWithPermission(
      CommonPermissions.TENANT_READ
    ),
    async (req: Request, res: Response) => {
      try {
        const { tenantId } = tenantIdSchema.parse(req.params);
        const user = getRequestUser(req);

        // System Admins can access any tenant
        if (!user!.canBypassTenantScope && tenantId !== user!.tenantId) {
          return res.status(403).json({
            error: 'Forbidden',
            message: 'Access denied: Cannot view other tenants',
            statusCode: 403,
            timestamp: new Date().toISOString(),
          });
        }

        const tenant = await tenantService.getTenantById(tenantId, user!);

        if (!tenant) {
          return res.status(404).json({
            error: 'Not Found',
            message: 'Tenant not found',
            statusCode: 404,
            timestamp: new Date().toISOString(),
          });
        }

        res.json({
          data: tenant,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'Invalid tenant ID format',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        logger.error('Failed to get tenant', {
          error: error instanceof Error ? error.message : 'Unknown error',
          tenantId: req.params.tenantId,
          userId: getRequestUser(req)?.id,
        });

        res.status(500).json({
          error: 'Internal Server Error',
          message: 'Failed to retrieve tenant',
          statusCode: 500,
          timestamp: new Date().toISOString(),
        });
      }
    }
  );

  /**
   * POST /api/v1/tenants
   * Create new tenant (System Admin only)
   */
  router.post(
    '/',
    ...middlewareFactory.createAuthWithSystemAdmin(),
    async (req: Request, res: Response) => {
      try {
        const tenantData = createTenantSchema.parse(req.body);
        const user = getRequestUser(req);

        const tenant = await tenantService.createTenant(
          tenantData as CreateTenantData,
          user!
        );

        logger.info('Tenant created successfully', {
          tenantId: tenant.id,
          tenantName: tenant.name,
          createdBy: user!.id,
        });

        res.status(201).json({
          data: tenant,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return res.status(400).json({
            error: 'Bad Request',
            message: error.errors.map((e) => e.message).join(', '),
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        logger.error('Failed to create tenant', {
          error: error instanceof Error ? error.message : 'Unknown error',
          tenantData: req.body,
          userId: getRequestUser(req)?.id,
        });

        const statusCode =
          error instanceof Error && 'statusCode' in error
            ? (error as Error & { statusCode: number }).statusCode
            : 500;

        res.status(statusCode).json({
          error: statusCode === 409 ? 'Conflict' : 'Internal Server Error',
          message:
            error instanceof Error ? error.message : 'Failed to create tenant',
          statusCode,
          timestamp: new Date().toISOString(),
        });
      }
    }
  );

  /**
   * PUT /api/v1/tenants/:tenantId
   * Update tenant (requires TENANT_WRITE permission)
   * System Admins can update any tenant, Company Admins only their own
   */
  router.put(
    '/:tenantId',
    ...middlewareFactory.createAuthWithPermission(
      CommonPermissions.TENANT_WRITE
    ),
    async (req: Request, res: Response) => {
      try {
        const { tenantId } = tenantIdSchema.parse(req.params);
        const tenantData = updateTenantSchema.parse(req.body);
        const user = getRequestUser(req);

        // System Admins can update any tenant
        if (!user!.canBypassTenantScope && tenantId !== user!.tenantId) {
          return res.status(403).json({
            error: 'Forbidden',
            message: 'Access denied: Cannot modify other tenants',
            statusCode: 403,
            timestamp: new Date().toISOString(),
          });
        }

        const tenant = await tenantService.updateTenant(
          tenantId,
          tenantData,
          user!
        );

        logger.info('Tenant updated successfully', {
          tenantId,
          updatedBy: user!.id,
          changes: Object.keys(tenantData),
        });

        res.json({
          data: tenant,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return res.status(400).json({
            error: 'Bad Request',
            message: error.errors.map((e) => e.message).join(', '),
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        logger.error('Failed to update tenant', {
          error: error instanceof Error ? error.message : 'Unknown error',
          tenantId: req.params.tenantId,
          tenantData: req.body,
          userId: getRequestUser(req)?.id,
        });

        const statusCode =
          error instanceof Error && 'statusCode' in error
            ? (error as Error & { statusCode: number }).statusCode
            : 500;

        res.status(statusCode).json({
          error:
            statusCode === 404
              ? 'Not Found'
              : statusCode === 409
                ? 'Conflict'
                : 'Internal Server Error',
          message:
            error instanceof Error ? error.message : 'Failed to update tenant',
          statusCode,
          timestamp: new Date().toISOString(),
        });
      }
    }
  );

  /**
   * DELETE /api/v1/tenants/:tenantId
   * Delete tenant (System Admin only)
   */
  router.delete(
    '/:tenantId',
    ...middlewareFactory.createAuthWithSystemAdmin(),
    async (req: Request, res: Response) => {
      try {
        const { tenantId } = tenantIdSchema.parse(req.params);
        const user = getRequestUser(req);

        await tenantService.deleteTenant(tenantId, user!);

        logger.warn('Tenant deleted', {
          tenantId,
          deletedBy: user!.id,
        });

        res.status(204).send();
      } catch (error) {
        if (error instanceof z.ZodError) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'Invalid tenant ID format',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        logger.error('Failed to delete tenant', {
          error: error instanceof Error ? error.message : 'Unknown error',
          tenantId: req.params.tenantId,
          userId: getRequestUser(req)?.id,
        });

        const statusCode =
          error instanceof Error && 'statusCode' in error
            ? (error as Error & { statusCode: number }).statusCode
            : 500;

        res.status(statusCode).json({
          error: statusCode === 404 ? 'Not Found' : 'Internal Server Error',
          message:
            error instanceof Error ? error.message : 'Failed to delete tenant',
          statusCode,
          timestamp: new Date().toISOString(),
        });
      }
    }
  );

  return router;
}
