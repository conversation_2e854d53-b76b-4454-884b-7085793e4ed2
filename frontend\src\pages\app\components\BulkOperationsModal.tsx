import React, { useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { Modal, Button, Input, FormField, Alert } from "../../../components";
import { useTypedApi } from "../../../services/api-client";
import { Upload, Calendar, Settings, Plus } from "lucide-react";

interface BulkOperationsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

type OperationType = "years" | "models" | "associations";

export const BulkOperationsModal: React.FC<BulkOperationsModalProps> = ({
  isOpen,
  onClose,
}) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [operationType, setOperationType] = useState<OperationType>("years");
  const [startYear, setStartYear] = useState("");
  const [endYear, setEndYear] = useState("");
  const [selectedMakeId, setSelectedMakeId] = useState("");
  const [modelNames, setModelNames] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch makes for model creation
  const { data: makesResponse } = useQuery({
    queryKey: ["vehicle-makes"],
    queryFn: () => api.vehicleHierarchy.getMakes(),
    enabled: isOpen && operationType === "models",
  });

  // Bulk create years mutation
  const bulkCreateYearsMutation = useMutation({
    mutationFn: (data: { startYear: number; endYear: number }) =>
      api.vehicleHierarchy.bulkCreateYears(data),
    onSuccess: (response) => {
      toast.success(`Created ${response.data.length} years successfully`);
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-years"] });
      handleClose();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create years");
    },
  });

  // Bulk create models mutation
  const bulkCreateModelsMutation = useMutation({
    mutationFn: (data: { makeId: string; modelNames: string[] }) =>
      api.vehicleHierarchy.bulkCreateModels(data.makeId, {
        modelNames: data.modelNames,
      }),
    onSuccess: (response) => {
      toast.success(`Created ${response.data.length} models successfully`);
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      handleClose();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create models");
    },
  });

  const validateYearsForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!startYear.trim()) {
      newErrors.startYear = "Start year is required";
    } else {
      const startYearNum = parseInt(startYear);
      if (isNaN(startYearNum) || startYearNum < 1900 || startYearNum > 2050) {
        newErrors.startYear = "Start year must be between 1900 and 2050";
      }
    }

    if (!endYear.trim()) {
      newErrors.endYear = "End year is required";
    } else {
      const endYearNum = parseInt(endYear);
      if (isNaN(endYearNum) || endYearNum < 1900 || endYearNum > 2050) {
        newErrors.endYear = "End year must be between 1900 and 2050";
      }
    }

    if (startYear && endYear) {
      const startYearNum = parseInt(startYear);
      const endYearNum = parseInt(endYear);
      if (startYearNum > endYearNum) {
        newErrors.endYear =
          "End year must be greater than or equal to start year";
      }
      if (endYearNum - startYearNum > 20) {
        newErrors.endYear = "Year range cannot exceed 20 years";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateModelsForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!selectedMakeId) {
      if (!makesResponse?.data.length) {
        newErrors.selectedMakeId = "No makes available. Create a make first.";
      } else {
        newErrors.selectedMakeId = "Please select a make";
      }
    }

    if (!modelNames.trim()) {
      newErrors.modelNames = "Please enter at least one model name";
    } else {
      const names = modelNames.split("\n").filter((name) => name.trim());
      if (names.length === 0) {
        newErrors.modelNames = "Please enter at least one model name";
      } else if (names.length > 100) {
        newErrors.modelNames = "Cannot create more than 100 models at once";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (operationType === "years") {
      if (!validateYearsForm()) return;

      bulkCreateYearsMutation.mutate({
        startYear: parseInt(startYear),
        endYear: parseInt(endYear),
      });
    } else if (operationType === "models") {
      if (!validateModelsForm()) return;

      const names = modelNames
        .split("\n")
        .map((name) => name.trim())
        .filter((name) => name.length > 0);

      bulkCreateModelsMutation.mutate({
        makeId: selectedMakeId,
        modelNames: names,
      });
    }
  };

  const handleClose = () => {
    setOperationType("years");
    setStartYear("");
    setEndYear("");
    setSelectedMakeId("");
    setModelNames("");
    setErrors({});
    onClose();
  };

  const isPending =
    bulkCreateYearsMutation.isPending || bulkCreateModelsMutation.isPending;
  const error = bulkCreateYearsMutation.error || bulkCreateModelsMutation.error;

  // Quick year range suggestions
  const currentYear = new Date().getFullYear();
  const yearRangeSuggestions = [
    { label: "Last 5 years", start: currentYear - 4, end: currentYear },
    { label: "Next 5 years", start: currentYear, end: currentYear + 4 },
    { label: "2020-2024", start: 2020, end: 2024 },
    { label: "2015-2024", start: 2015, end: 2024 },
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Bulk Operations"
      size="lg"
    >
      <div className="space-y-6">
        <div className="flex items-center space-x-3 text-gray-600">
          <Upload className="h-5 w-5" />
          <span>
            Perform bulk operations to quickly populate your hierarchy
          </span>
        </div>

        {/* Operation Type Selection */}
        <div className="space-y-3">
          <label className="block text-sm font-medium text-gray-700">
            Operation Type
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <button
              type="button"
              onClick={() => setOperationType("years")}
              className={`p-4 border-2 rounded-lg text-left transition-colors ${
                operationType === "years"
                  ? "border-primary-500 bg-primary-50"
                  : "border-gray-200 hover:border-gray-300"
              }`}
            >
              <div className="flex items-center space-x-3">
                <Calendar className="h-5 w-5 text-primary-600" />
                <div>
                  <h3 className="font-medium">Bulk Create Years</h3>
                  <p className="text-sm text-gray-600">
                    Create multiple years in a range
                  </p>
                </div>
              </div>
            </button>

            <button
              type="button"
              onClick={() => setOperationType("models")}
              className={`p-4 border-2 rounded-lg text-left transition-colors ${
                operationType === "models"
                  ? "border-primary-500 bg-primary-50"
                  : "border-gray-200 hover:border-gray-300"
              }`}
            >
              <div className="flex items-center space-x-3">
                <Settings className="h-5 w-5 text-primary-600" />
                <div>
                  <h3 className="font-medium">Bulk Create Models</h3>
                  <p className="text-sm text-gray-600">
                    Create multiple models for a make
                  </p>
                </div>
              </div>
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Years Form */}
          {operationType === "years" && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField label="Start Year" error={errors.startYear} required>
                  <Input
                    type="number"
                    placeholder="2020"
                    value={startYear}
                    onChange={(e) => setStartYear(e.target.value)}
                    min="1900"
                    max="2050"
                    error={errors.startYear}
                  />
                </FormField>

                <FormField label="End Year" error={errors.endYear} required>
                  <Input
                    type="number"
                    placeholder="2024"
                    value={endYear}
                    onChange={(e) => setEndYear(e.target.value)}
                    min="1900"
                    max="2050"
                    error={errors.endYear}
                  />
                </FormField>
              </div>

              {/* Quick Range Selection */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Quick Select Range
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {yearRangeSuggestions.map((suggestion) => (
                    <Button
                      key={suggestion.label}
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setStartYear(suggestion.start.toString());
                        setEndYear(suggestion.end.toString());
                      }}
                      className="text-xs"
                    >
                      {suggestion.label}
                    </Button>
                  ))}
                </div>
              </div>

              {startYear && endYear && !errors.startYear && !errors.endYear && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-800">
                    This will create{" "}
                    {parseInt(endYear) - parseInt(startYear) + 1} years:{" "}
                    {startYear} to {endYear}
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Models Form */}
          {operationType === "models" && (
            <div className="space-y-4">
              {makesResponse?.data.length === 0 && (
                <Alert variant="warning">
                  <p>
                    No makes available. You need to create at least one make
                    before creating models.
                  </p>
                  <p className="mt-2 text-sm">
                    Switch to "Years" tab to create years first, then create
                    makes using the "Add Make" button on the main page.
                  </p>
                </Alert>
              )}

              <FormField label="Make" error={errors.selectedMakeId} required>
                <select
                  value={selectedMakeId}
                  onChange={(e) => setSelectedMakeId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  disabled={!makesResponse?.data.length}
                >
                  <option value="">
                    {makesResponse?.data.length === 0
                      ? "No makes available"
                      : "Select a make..."}
                  </option>
                  {makesResponse?.data.map((make) => (
                    <option key={make.id} value={make.id}>
                      {make.name}
                    </option>
                  ))}
                </select>
              </FormField>

              <FormField label="Model Names" error={errors.modelNames} required>
                <textarea
                  placeholder="Enter model names, one per line:&#10;G19FD&#10;G20BHS&#10;G22S&#10;G24RK"
                  value={modelNames}
                  onChange={(e) => setModelNames(e.target.value)}
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Enter one model name per line. Maximum 100 models.
                </p>
              </FormField>

              {modelNames && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-800">
                    This will create{" "}
                    {
                      modelNames.split("\n").filter((name) => name.trim())
                        .length
                    }{" "}
                    models
                  </p>
                </div>
              )}
            </div>
          )}

          {error && (
            <Alert variant="error">{error.message || "Operation failed"}</Alert>
          )}

          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                isPending ||
                (operationType === "models" && !makesResponse?.data.length)
              }
              className="flex items-center space-x-2"
            >
              {isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Processing...</span>
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4" />
                  <span>
                    {operationType === "years"
                      ? "Create Years"
                      : "Create Models"}
                  </span>
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
};
