import React from "react";
import {
  MarketingHero,
  FeatureGrid,
  TestimonialSection,
} from "../../components";
import {
  ArrowRight,
  Users,
  Zap,
  Shield,
  Star,
  CheckCircle,
  Smartphone,
  BarChart3,
  Globe,
} from "lucide-react";

export const MarketingHome: React.FC = () => {
  // Hero section data
  const heroData = {
    title: "Tech Notes",
    description:
      "An all-in-one tool for making sure your service technicians have the exact content they need right when they need it.",
    announcement: {
      text: "🚀 New Mobile App Available",
      variant: "primary" as const,
      icon: <Star className="h-4 w-4" />,
    },
    features: [
      "Mobile-first design",
      "Real-time sync",
      "Offline access",
      "Enterprise security",
    ],
    primaryCta: {
      text: "Get Started Free",
      href: "/sign-in",
      icon: <ArrowRight className="h-5 w-5" />,
    },
    secondaryCta: {
      text: "View Pricing",
      href: "/pricing",
      variant: "outline" as const,
    },
    socialProof: {
      text: "Trusted by leading service organizations",
      stats: [
        { value: "10,000+", label: "Active Technicians" },
        { value: "500+", label: "Companies" },
        { value: "99.9%", label: "Uptime" },
      ],
    },
  };

  // Features section data
  const featuresData = {
    title: "Everything you need to manage technical content",
    subtitle:
      "Streamline your operations with powerful tools designed for service teams",
    badge: {
      text: "Core Features",
      variant: "primary" as const,
      icon: <CheckCircle className="h-4 w-4" />,
    },
    features: [
      {
        id: "user-management",
        title: "User Management",
        description:
          "Complete control over user access with admin-managed accounts, invite codes, and approval workflows.",
        icon: <Users className="h-6 w-6" />,
        variant: "elevated" as const,
        interactive: true,
      },
      {
        id: "smart-analytics",
        title: "Smart Analytics",
        description:
          "Deep insights into user engagement, login patterns, and content usage to optimize your operations.",
        icon: <BarChart3 className="h-6 w-6" />,
        variant: "elevated" as const,
        interactive: true,
      },
      {
        id: "enterprise-security",
        title: "Enterprise Security",
        description:
          "Bank-level security with role-based access control, audit logs, and compliance-ready data protection.",
        icon: <Shield className="h-6 w-6" />,
        variant: "elevated" as const,
        interactive: true,
      },
      {
        id: "mobile-first",
        title: "Mobile-First Design",
        description:
          "Optimized for technicians in the field with offline access and real-time synchronization.",
        icon: <Smartphone className="h-6 w-6" />,
        variant: "elevated" as const,
        interactive: true,
      },
      {
        id: "real-time-sync",
        title: "Real-Time Sync",
        description:
          "Instant updates across all devices ensure your team always has the latest information.",
        icon: <Zap className="h-6 w-6" />,
        variant: "elevated" as const,
        interactive: true,
      },
      {
        id: "global-access",
        title: "Global Access",
        description:
          "Access your technical documentation from anywhere in the world with 99.9% uptime guarantee.",
        icon: <Globe className="h-6 w-6" />,
        variant: "elevated" as const,
        interactive: true,
      },
    ],
  };

  // Testimonials section data
  const testimonialsData = {
    title: "Trusted by service professionals worldwide",
    subtitle: "See what our customers have to say about Tech Notes",
    badge: {
      text: "Customer Stories",
      variant: "success" as const,
      icon: <Star className="h-4 w-4" />,
    },
    testimonials: [
      {
        id: "testimonial-1",
        quote:
          "Tech Notes has completely transformed how our technicians access and use documentation. The mobile-first design is perfect for field work.",
        author: {
          name: "Sarah Johnson",
          title: "Operations Manager",
          company: "ServicePro Solutions",
        },
        rating: 5,
        variant: "elevated" as const,
      },
      {
        id: "testimonial-2",
        quote:
          "The real-time sync feature ensures our entire team is always working with the latest information. It's been a game-changer for our efficiency.",
        author: {
          name: "Mike Chen",
          title: "Field Service Director",
          company: "TechFlow Industries",
        },
        rating: 5,
        variant: "elevated" as const,
      },
      {
        id: "testimonial-3",
        quote:
          "Implementation was seamless and the support team was fantastic. Our technicians adopted it immediately.",
        author: {
          name: "Emily Rodriguez",
          title: "IT Manager",
          company: "Global Maintenance Corp",
        },
        rating: 5,
        variant: "elevated" as const,
      },
    ],
  };

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <MarketingHero {...heroData} />

      {/* Features Section */}
      <section className="py-20 sm:py-32" id="features">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FeatureGrid {...featuresData} />
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 sm:py-32 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <TestimonialSection {...testimonialsData} />
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 sm:py-32" id="contact">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <MarketingHero
            layout="minimal"
            title="Ready to streamline your operations?"
            description="Join thousands of teams already using Tech Notes to improve their service delivery."
            primaryCta={{
              text: "Start Free Trial",
              href: "/sign-in",
              icon: <ArrowRight className="h-5 w-5" />,
            }}
            secondaryCta={{
              text: "See Pricing",
              href: "/pricing",
              variant: "outline",
            }}
            socialProof={{
              text: "Join 500+ companies already using Tech Notes",
              stats: [
                { value: "14-day", label: "Free Trial" },
                { value: "No", label: "Credit Card Required" },
                { value: "24/7", label: "Support" },
              ],
            }}
          />
        </div>
      </section>
    </div>
  );
};
