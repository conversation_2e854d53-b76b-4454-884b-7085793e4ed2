import { AuthenticatedRequestBase } from '@tech-notes/shared';
import { Request, Response, NextFunction } from 'express';

/**
 * Backend-specific authenticated request type using proper intersection
 * This avoids the Express compatibility issues by intersecting types
 */
export type AuthenticatedRequest = Request & AuthenticatedRequestBase;

/**
 * Type-safe request handler that works with Express router
 * while providing access to authenticated user context
 */
export type AuthenticatedRequestHandler = (
  req: Request,
  res: Response,
  next: NextFunction
) => Promise<void> | void;

/**
 * Helper function to safely cast request to AuthenticatedRequest
 * Use this when you need to access req.user in route handlers
 */
export function getAuthenticatedRequest(req: Request): AuthenticatedRequest {
  return req as unknown as AuthenticatedRequest;
}

/**
 * Helper function to get user from request with type safety
 * This is the recommended way to access user context in route handlers
 */
export function getRequestUser(req: Request) {
  return (req as unknown as AuthenticatedRequest).user;
}
