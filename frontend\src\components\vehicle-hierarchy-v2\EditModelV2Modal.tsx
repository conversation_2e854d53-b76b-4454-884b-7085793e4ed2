import React, { useState, useEffect } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { Modal, Button, Input, FormField, Alert } from "../index";
import { useTypedApi, type VehicleModelV2 } from "../../services/api-client";
import { Car } from "lucide-react";

interface EditModelV2ModalProps {
  isOpen: boolean;
  onClose: () => void;
  model: VehicleModelV2 | null;
}

export const EditModelV2Modal: React.FC<EditModelV2ModalProps> = ({
  isOpen,
  onClose,
  model,
}) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [name, setName] = useState("");
  const [isActive, setIsActive] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch sub-brands for reference (read-only in edit mode)
  const { data: subBrandsResponse } = useQuery({
    queryKey: ["vehicle-hierarchy-v2", "sub-brands"],
    queryFn: () => api.vehicleHierarchyV2.getSubBrands(),
    enabled: isOpen && !!model,
  });

  // Populate form when model changes
  useEffect(() => {
    if (model) {
      setName(model.name);
      setIsActive(model.isActive);
    }
  }, [model]);

  const updateModelMutation = useMutation({
    mutationFn: (modelData: { name?: string; isActive?: boolean }) => {
      if (!model) throw new Error("No model selected");
      return api.vehicleHierarchyV2.updateModel(model.id, modelData);
    },
    onSuccess: () => {
      toast.success("Model updated successfully");
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "models"],
      });
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "sub-brands"],
      });
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "full"],
      });
      handleClose();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update model");
    },
  });

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = "Model name is required";
    } else if (name.trim().length < 2) {
      newErrors.name = "Model name must be at least 2 characters";
    } else if (name.trim().length > 100) {
      newErrors.name = "Model name must be less than 100 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Only send changed fields
    const updateData: { name?: string; isActive?: boolean } = {};

    if (name.trim() !== model?.name) {
      updateData.name = name.trim();
    }

    if (isActive !== model?.isActive) {
      updateData.isActive = isActive;
    }

    // If nothing changed, just close
    if (Object.keys(updateData).length === 0) {
      handleClose();
      return;
    }

    updateModelMutation.mutate(updateData);
  };

  const handleClose = () => {
    setName("");
    setIsActive(true);
    setErrors({});
    onClose();
  };

  if (!model) return null;

  const subBrands = subBrandsResponse?.data || [];
  const parentSubBrand =
    subBrands.find((sb) => sb.id === model.subBrandId) || model.subBrand;

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Edit Model" size="md">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex items-center space-x-3 text-gray-600 mb-4">
          <Car className="h-5 w-5" />
          <span>Edit model details</span>
        </div>

        {parentSubBrand && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Hierarchy:</strong> {parentSubBrand.brand?.name} →{" "}
              {parentSubBrand.name} → {model.name}
            </p>
            <p className="text-xs text-blue-600 mt-1">
              Note: To change the parent sub-brand, you'll need to create a new
              model.
            </p>
          </div>
        )}

        <FormField label="Model Name" error={errors.name} required>
          <Input
            type="text"
            placeholder="e.g., Regular Cab, LE, G20BHS"
            value={name}
            onChange={(e) => setName(e.target.value)}
            error={errors.name}
            maxLength={100}
          />
        </FormField>

        <FormField label="Status">
          <div className="flex items-center space-x-3">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="isActive"
                checked={isActive}
                onChange={() => setIsActive(true)}
                className="text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm">Active</span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="isActive"
                checked={!isActive}
                onChange={() => setIsActive(false)}
                className="text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm">Inactive</span>
            </label>
          </div>
        </FormField>

        {updateModelMutation.isError && (
          <Alert variant="error">
            <p>Failed to update model. Please try again.</p>
          </Alert>
        )}

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={updateModelMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={updateModelMutation.isPending || !name.trim()}
          >
            {updateModelMutation.isPending ? "Updating..." : "Update Model"}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
