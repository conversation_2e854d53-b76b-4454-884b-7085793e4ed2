import { describe, it, expect, vi } from "vitest";
import { render, screen } from "../../__tests__/helpers/test-utils";
import { ErrorBoundary } from "./ErrorBoundary";

// Test component that throws an error
const ThrowError = ({ shouldThrow = false, message = "Test error" }) => {
  if (shouldThrow) {
    throw new Error(message);
  }
  return <div>No error</div>;
};

// Test component for successful rendering
const SuccessfulComponent = () => <div>Component rendered successfully</div>;

describe("ErrorBoundary", () => {
  // Mock console.error to avoid noise in test output
  const originalConsoleError = console.error;

  beforeEach(() => {
    console.error = vi.fn();
  });

  afterEach(() => {
    console.error = originalConsoleError;
  });

  describe("Normal Operation", () => {
    it("should render children when no error occurs", () => {
      // Arrange & Act
      render(
        <ErrorBoundary>
          <SuccessfulComponent />
        </ErrorBoundary>,
      );

      // Assert
      expect(
        screen.getByText("Component rendered successfully"),
      ).toBeInTheDocument();
    });

    it("should render multiple children when no error occurs", () => {
      // Arrange & Act
      render(
        <ErrorBoundary>
          <div>First child</div>
          <div>Second child</div>
        </ErrorBoundary>,
      );

      // Assert
      expect(screen.getByText("First child")).toBeInTheDocument();
      expect(screen.getByText("Second child")).toBeInTheDocument();
    });
  });

  describe("Error Handling", () => {
    it("should catch and display error when child component throws", () => {
      // Arrange & Act
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} message="Component crashed" />
        </ErrorBoundary>,
      );

      // Assert
      expect(screen.getByText("Something went wrong")).toBeInTheDocument();
      expect(screen.getByText("Component crashed")).toBeInTheDocument();
      expect(screen.queryByText("No error")).not.toBeInTheDocument();
    });

    it("should display generic message when error has no message", () => {
      // Arrange - Component that throws error without message
      const ThrowEmptyError = () => {
        throw new Error("");
      };

      // Act
      render(
        <ErrorBoundary>
          <ThrowEmptyError />
        </ErrorBoundary>,
      );

      // Assert
      expect(screen.getByText("Something went wrong")).toBeInTheDocument();
      expect(
        screen.getByText("An unexpected error occurred"),
      ).toBeInTheDocument();
    });

    it("should log error to console when error occurs", () => {
      // Arrange
      const consoleSpy = vi.spyOn(console, "error");

      // Act
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} message="Test error for logging" />
        </ErrorBoundary>,
      );

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith(
        "ErrorBoundary caught an error:",
        expect.any(Error),
        expect.any(Object),
      );
    });
  });

  describe("Custom Fallback", () => {
    it("should render custom fallback when provided and error occurs", () => {
      // Arrange
      const customFallback = (
        <div className="custom-error">
          <h3>Custom Error UI</h3>
          <p>Please try again later</p>
        </div>
      );

      // Act
      render(
        <ErrorBoundary fallback={customFallback}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>,
      );

      // Assert
      expect(screen.getByText("Custom Error UI")).toBeInTheDocument();
      expect(screen.getByText("Please try again later")).toBeInTheDocument();
      expect(
        screen.queryByText("Something went wrong"),
      ).not.toBeInTheDocument();
    });

    it("should render children normally when custom fallback is provided but no error occurs", () => {
      // Arrange
      const customFallback = <div>Custom Error UI</div>;

      // Act
      render(
        <ErrorBoundary fallback={customFallback}>
          <SuccessfulComponent />
        </ErrorBoundary>,
      );

      // Assert
      expect(
        screen.getByText("Component rendered successfully"),
      ).toBeInTheDocument();
      expect(screen.queryByText("Custom Error UI")).not.toBeInTheDocument();
    });
  });

  describe("Error Recovery", () => {
    it("should maintain error state after error occurs", () => {
      // Arrange
      const { rerender } = render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>,
      );

      // Verify error state
      expect(screen.getByText("Something went wrong")).toBeInTheDocument();

      // Act - Rerender with same error
      rerender(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>,
      );

      // Assert - Should still show error
      expect(screen.getByText("Something went wrong")).toBeInTheDocument();
    });

    it("should reset when new ErrorBoundary instance is created", () => {
      // Arrange - First render with error
      const { unmount } = render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>,
      );

      expect(screen.getByText("Something went wrong")).toBeInTheDocument();
      unmount();

      // Act - New ErrorBoundary instance with working component
      render(
        <ErrorBoundary>
          <SuccessfulComponent />
        </ErrorBoundary>,
      );

      // Assert - Should render successfully
      expect(
        screen.getByText("Component rendered successfully"),
      ).toBeInTheDocument();
      expect(
        screen.queryByText("Something went wrong"),
      ).not.toBeInTheDocument();
    });
  });

  describe("Styling and Accessibility", () => {
    it("should apply correct CSS classes to default error UI", () => {
      // Arrange & Act
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>,
      );

      // Assert
      const errorContainer = screen.getByText(
        "Something went wrong",
      ).parentElement;
      expect(errorContainer).toHaveClass(
        "p-4",
        "bg-red-50",
        "border",
        "border-red-200",
        "rounded-md",
      );
    });

    it("should have proper semantic structure for error UI", () => {
      // Arrange & Act
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} message="Accessibility test error" />
        </ErrorBoundary>,
      );

      // Assert
      expect(
        screen.getByRole("heading", { name: "Something went wrong" }),
      ).toBeInTheDocument();
      expect(screen.getByText("Accessibility test error")).toBeInTheDocument();
    });

    it("should have appropriate text styling for error messages", () => {
      // Arrange & Act
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} message="Styled error message" />
        </ErrorBoundary>,
      );

      // Assert
      const heading = screen.getByRole("heading", {
        name: "Something went wrong",
      });
      expect(heading).toHaveClass("text-lg", "font-semibold", "text-red-800");

      const message = screen.getByText("Styled error message");
      expect(message).toHaveClass("text-red-600", "mt-2");
    });
  });

  describe("Edge Cases", () => {
    it("should handle null children gracefully", () => {
      // Arrange & Act
      render(<ErrorBoundary>{null}</ErrorBoundary>);

      // Assert - Should not crash
      expect(document.body).toBeInTheDocument();
    });

    it("should handle undefined children gracefully", () => {
      // Arrange & Act
      render(<ErrorBoundary>{undefined}</ErrorBoundary>);

      // Assert - Should not crash
      expect(document.body).toBeInTheDocument();
    });

    it("should handle errors in nested components", () => {
      // Arrange
      const NestedComponent = () => (
        <div>
          <div>
            <ThrowError shouldThrow={true} message="Nested error" />
          </div>
        </div>
      );

      // Act
      render(
        <ErrorBoundary>
          <NestedComponent />
        </ErrorBoundary>,
      );

      // Assert
      expect(screen.getByText("Something went wrong")).toBeInTheDocument();
      expect(screen.getByText("Nested error")).toBeInTheDocument();
    });
  });
});
