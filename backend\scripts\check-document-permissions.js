import { PrismaClient, PermissionResource } from '@prisma/client';

const prisma = new PrismaClient();

async function checkDocumentPermissions() {
  console.log('🔍 Checking document-related permissions in database...\n');

  // Check if DOCUMENT permissions exist
  const documentPermissions = await prisma.permission.findMany({
    where: {
      resource: PermissionResource.DOCUMENT
    },
    orderBy: {
      action: 'asc'
    }
  });

  console.log('📋 Document Permissions Found:');
  if (documentPermissions.length === 0) {
    console.log('❌ No DOCUMENT permissions found in database!');
  } else {
    documentPermissions.forEach(perm => {
      console.log(`✅ ${perm.name} (${perm.resource}:${perm.action}) - ${perm.description}`);
    });
  }

  console.log('\n🔗 Role-Permission Assignments for Document Permissions:');
  
  // Check role assignments for document permissions
  const rolePermissions = await prisma.rolePermission.findMany({
    where: {
      permission: {
        resource: PermissionResource.DOCUMENT
      }
    },
    include: {
      role: true,
      permission: true
    }
  });

  if (rolePermissions.length === 0) {
    console.log('❌ No roles have been assigned DOCUMENT permissions!');
  } else {
    rolePermissions.forEach(rp => {
      console.log(`✅ ${rp.role.name} (${rp.role.type}) → ${rp.permission.name}`);
    });
  }

  console.log('\n📊 Summary of All Roles and Their Permissions:');
  const roles = await prisma.role.findMany({
    include: {
      permissions: {
        include: {
          permission: true
        }
      }
    }
  });

  roles.forEach(role => {
    console.log(`\n🎭 ${role.name} (${role.type}):`);
    const docPerms = role.permissions.filter(rp => rp.permission.resource === PermissionResource.DOCUMENT);
    if (docPerms.length === 0) {
      console.log('  ❌ No DOCUMENT permissions assigned');
    } else {
      docPerms.forEach(rp => {
        console.log(`  ✅ ${rp.permission.name}`);
      });
    }
  });

  await prisma.$disconnect();
}

checkDocumentPermissions().catch(console.error);
