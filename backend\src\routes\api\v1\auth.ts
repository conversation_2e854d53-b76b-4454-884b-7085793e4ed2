import { clerkClient } from '@clerk/clerk-sdk-node';
import { Router, Request, Response } from 'express';
import { z } from 'zod';

import { InvitationService } from '../../../services/invitation.service.js';
import { OnboardingService } from '../../../services/onboarding.service.js';
import { PrismaService } from '../../../services/prisma.service.js';
import { TenantService } from '../../../services/tenant.service.js';
import { UserService } from '../../../services/user.service.js';
import { ClerkAuthMiddleware } from '../../../types/clerk.types.js';
import { AcceptInvitationData } from '../../../types/invitation.types.js';
import { Logger } from '../../../utils/logger.js';
import { getRequestUser } from '../../../utils/request-types.js';

interface ServiceDependencies {
  userService: UserService;
  tenantService: TenantService;
  onboardingService: OnboardingService;
  invitationService: InvitationService;
  prismaService: PrismaService;
  clerkAuth: ClerkAuthMiddleware;
  logger: Logger;
}

// Validation schemas for invitation acceptance
const acceptInvitationSchema = z.object({
  invitationToken: z.string().min(1, 'Invitation token is required'),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  password: z.string().optional(),
});

// Validation schema for onboarding
const onboardingSchema = z.object({
  tenantName: z.string().min(1, 'Tenant name is required'),
  tenantSlug: z.string().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
});

export function createAuthRouter(dependencies: ServiceDependencies): Router {
  const {
    userService,
    onboardingService,
    invitationService,
    prismaService,
    clerkAuth,
    logger,
  } = dependencies;
  const router = Router();

  /**
   * GET /api/v1/auth/me
   * Get current user profile
   */
  router.get('/me', clerkAuth(), async (req: Request, res: Response) => {
    const user = getRequestUser(req);
    try {
      if (!user) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Authentication required',
          statusCode: 401,
          timestamp: new Date().toISOString(),
        });
      }

      res.json({
        data: user,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('Failed to get user profile', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: user?.id,
      });

      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to retrieve user profile',
        statusCode: 500,
        timestamp: new Date().toISOString(),
      });
    }
  });

  /**
   * POST /api/v1/auth/onboard
   * Complete user onboarding - create tenant and user
   * Handles authentication manually to provide better error handling during onboarding
   */
  router.post('/onboard', async (req, res: Response) => {
    try {
      // Extract token manually for better error handling during onboarding
      const authHeader = req.headers.authorization;

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Authentication token is required',
          statusCode: 401,
          timestamp: new Date().toISOString(),
        });
      }

      const token = authHeader.substring(7);

      // Verify token with Clerk
      const session = await clerkClient.verifyToken(token);

      if (!session || !session.sub) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Invalid authentication token',
          statusCode: 401,
          timestamp: new Date().toISOString(),
        });
      }

      const clerkId = session.sub;

      // Check if user already exists in database (already onboarded)
      const existingUser = await userService.getUserByClerkId(clerkId);
      if (existingUser) {
        return res.status(409).json({
          error: 'User Already Onboarded',
          message: 'User has already completed onboarding.',
          statusCode: 409,
          timestamp: new Date().toISOString(),
        });
      }

      // Validate request data
      const onboardingData = onboardingSchema.parse(req.body);

      // Get user details from Clerk
      const clerkUser = await clerkClient.users.getUser(clerkId);
      const email = clerkUser.emailAddresses[0]?.emailAddress;

      if (!email) {
        return res.status(400).json({
          error: 'Bad Request',
          message: 'User email not found in Clerk',
          statusCode: 400,
          timestamp: new Date().toISOString(),
        });
      }

      // Generate slug from tenant name if not provided
      const tenantSlug =
        onboardingData.tenantSlug ||
        onboardingData.tenantName
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim();

      // Create tenant and user through onboarding service
      const result = await onboardingService.createTenantAndUser(
        {
          name: onboardingData.tenantName,
          slug: tenantSlug,
        },
        {
          clerkId,
          email,
          firstName:
            onboardingData.firstName || clerkUser.firstName || undefined,
          lastName: onboardingData.lastName || clerkUser.lastName || undefined,
          imageUrl: clerkUser.imageUrl || undefined,
        }
      );

      logger.info('User onboarding completed', {
        userId: result.user.id,
        tenantId: result.tenant.id,
        email: result.user.email,
        tenantSlug: result.tenant.slug,
      });

      res.status(201).json({
        data: result,
        message: 'Onboarding completed successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('Failed to complete onboarding', {
        error: error instanceof Error ? error.message : 'Unknown error',
        requestData: req.body,
      });

      // Check if this is a database connectivity error
      if (
        error instanceof Error &&
        (error.message.includes('connect') ||
          error.message.includes('ECONNREFUSED') ||
          error.message.includes('timeout') ||
          error.message.includes('database'))
      ) {
        return res.status(500).json({
          error: 'Internal Server Error',
          message: 'Database connectivity error during onboarding',
          statusCode: 500,
          timestamp: new Date().toISOString(),
        });
      }

      const statusCode =
        error instanceof Error && 'statusCode' in error
          ? (error as Error & { statusCode: number }).statusCode
          : 500;

      res.status(statusCode).json({
        error:
          statusCode === 500 ? 'Internal Server Error' : 'Onboarding Failed',
        message:
          error instanceof Error
            ? error.message
            : 'Failed to complete onboarding',
        statusCode,
        timestamp: new Date().toISOString(),
      });
    }
  });

  /**
   * POST /api/v1/auth/accept-invitation
   * Complete invitation-based signup - create user record from invitation
   * This route is called after Clerk signup is completed via invitation
   */
  router.post('/accept-invitation', async (req, res: Response) => {
    try {
      // Extract token manually since user might not exist in database yet
      const authHeader = req.headers.authorization;

      logger.info('Accept invitation request received', {
        hasAuthHeader: !!authHeader,
        authHeaderPrefix: authHeader?.substring(0, 10),
        bodyKeys: Object.keys(req.body),
      });

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        logger.warn('Accept invitation: Missing or invalid auth header', {
          authHeader: authHeader?.substring(0, 20),
        });
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Authentication token is required',
          statusCode: 401,
          timestamp: new Date().toISOString(),
        });
      }

      const token = authHeader.substring(7);
      logger.info('Accept invitation: Verifying Clerk token', {
        tokenLength: token.length,
        tokenPrefix: token.substring(0, 10),
      });

      const session = await clerkClient.verifyToken(token);

      if (!session || !session.sub) {
        logger.warn('Accept invitation: Invalid Clerk session', {
          hasSession: !!session,
          sessionSub: session?.sub,
        });
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Invalid authentication token',
          statusCode: 401,
          timestamp: new Date().toISOString(),
        });
      }

      logger.info('Accept invitation: Clerk token verified successfully', {
        clerkUserId: session.sub,
      });

      // Check if user already exists
      const existingUser = await userService.getUserByClerkId(session.sub);
      if (existingUser) {
        return res.status(409).json({
          error: 'User Already Exists',
          message: 'User account already exists.',
          statusCode: 409,
          timestamp: new Date().toISOString(),
        });
      }

      // Validate request data
      const invitationData = acceptInvitationSchema.parse(req.body);

      // Accept invitation and create user
      const result = await invitationService.acceptInvitation(
        invitationData as AcceptInvitationData,
        session.sub
      );

      const { user, invitation } = result;

      logger.info('Invitation accepted and user created', {
        userId: user.id,
        tenantId: user.tenantId,
        email: user.email,
        invitationId: invitation.id,
        roleType: invitation.role.type,
      });

      res.status(201).json({
        data: {
          user: {
            id: user.id,
            clerkId: user.clerkId,
            tenantId: user.tenantId,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            imageUrl: user.imageUrl,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          },
          invitation: {
            id: invitation.id,
            email: invitation.email,
            status: invitation.status,
            role: invitation.role,
            tenant: invitation.tenant,
          },
        },
        message: 'Invitation accepted successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('Failed to accept invitation', {
        error: error instanceof Error ? error.message : 'Unknown error',
        requestData: req.body,
      });

      const statusCode =
        error instanceof Error && 'statusCode' in error
          ? (error as Error & { statusCode: number }).statusCode
          : 500;

      res.status(statusCode).json({
        error: statusCode === 409 ? 'Conflict' : 'Internal Server Error',
        message:
          error instanceof Error
            ? error.message
            : 'Failed to accept invitation',
        statusCode,
        timestamp: new Date().toISOString(),
      });
    }
  });

  /**
   * GET /api/v1/auth/status
   * Check authentication status and onboarding state
   */
  router.get('/status', async (req, res: Response) => {
    try {
      const authHeader = req.headers.authorization;

      // Temporary debug logging
      logger.info('Auth status check', {
        hasAuthHeader: !!authHeader,
        authHeaderPrefix: authHeader?.substring(0, 20) + '...',
        userAgent: req.headers['user-agent']?.substring(0, 50),
      });

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        logger.info('No valid auth header, returning unauthenticated');
        return res.json({
          data: {
            authenticated: false,
            onboarded: false,
          },
          timestamp: new Date().toISOString(),
        });
      }

      const token = authHeader.substring(7);
      logger.info('Attempting to verify token', { tokenLength: token.length });

      const session = await clerkClient.verifyToken(token);
      logger.info('Token verification result', {
        hasSession: !!session,
        hasSub: !!session?.sub,
        sub: session?.sub?.substring(0, 10) + '...',
      });

      if (!session || !session.sub) {
        return res.json({
          data: {
            authenticated: false,
            onboarded: false,
          },
          timestamp: new Date().toISOString(),
        });
      }

      // First check database connectivity
      const dbHealthResult = await prismaService.authHealthCheck();
      if (!dbHealthResult.healthy) {
        logger.error(
          'Database connectivity check failed during auth status check',
          {
            error: dbHealthResult.error,
          }
        );
        return res.status(503).json({
          error: 'Service Unavailable',
          message: 'Authentication service requires database connectivity',
          statusCode: 503,
          timestamp: new Date().toISOString(),
        });
      }

      // Database is healthy, proceed with user lookup
      const user = await userService.getUserByClerkId(session.sub);

      // If user doesn't exist in database but is authenticated with Clerk,
      // log this for debugging purposes
      if (!user) {
        logger.warn('Authenticated Clerk user not found in database', {
          clerkId: session.sub,
        });
      }

      res.json({
        data: {
          authenticated: true,
          onboarded: !!user,
          user: user
            ? {
                id: user.id,
                tenantId: user.tenantId,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
              }
            : null,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('Failed to check auth status', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        errorType: error?.constructor?.name,
      });

      // Check if this is a database connectivity error
      if (
        error instanceof Error &&
        (error.message.includes('connect') ||
          error.message.includes('ECONNREFUSED') ||
          error.message.includes('timeout') ||
          error.message.includes('database'))
      ) {
        return res.status(503).json({
          error: 'Service Unavailable',
          message: 'Authentication service requires database connectivity',
          statusCode: 503,
          timestamp: new Date().toISOString(),
        });
      }

      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to check authentication status',
        statusCode: 500,
        timestamp: new Date().toISOString(),
      });
    }
  });

  return router;
}
