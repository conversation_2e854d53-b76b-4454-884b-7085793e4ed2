import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Button,
  Badge,
  Al<PERSON>,
} from "../../components";
import { useTypedApi } from "../../services/api-client";
import { useAuth } from "../../hooks/useAuth";
import {
  Play,
  Database,
  CheckCircle,
  XCircle,
  Clock,
  Trash2,
  Edit,
  Plus,
  RotateCcw,
  ArrowUpDown,
} from "lucide-react";

interface TestResult {
  endpoint: string;
  method: string;
  status: "pending" | "success" | "error";
  statusCode?: number;
  data?: unknown;
  error?: string;
  timestamp: Date;
}

interface Brand {
  id: string;
  name: string;
  tenantId: string;
  isActive: boolean;
  displayOrder: number;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

interface SubBrand {
  id: string;
  name: string;
  brandId: string;
  tenantId: string;
  isActive: boolean;
  displayOrder: number;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

interface Model {
  id: string;
  name: string;
  subBrandId: string;
  tenantId: string;
  isActive: boolean;
  displayOrder: number;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

interface Year {
  id: string;
  name: string;
  tenantId: string;
  isActive: boolean;
  displayOrder: number;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

interface ModelYear {
  id: string;
  modelId: string;
  yearId: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  year?: Year;
}

export const TestingPage: React.FC = () => {
  const api = useTypedApi();
  const { userProfile } = useAuth();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [subBrands, setSubBrands] = useState<SubBrand[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [years, setYears] = useState<Year[]>([]);
  const [modelYears, setModelYears] = useState<ModelYear[]>([]);
  const [selectedBrandId, setSelectedBrandId] = useState<string>("");
  const [selectedSubBrandId, setSelectedSubBrandId] = useState<string>("");
  const [selectedModelId, setSelectedModelId] = useState<string>("");
  const [selectedYearId, setSelectedYearId] = useState<string>("");

  const addTestResult = (result: Omit<TestResult, "timestamp">) => {
    setTestResults((prev) => [...prev, { ...result, timestamp: new Date() }]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  // Test GET /brands
  const testGetBrands = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/v1/vehicle-hierarchy-v3/brands", {
        headers: {
          Authorization: `Bearer ${await api.getToken()}`,
        },
      });

      const data = await response.json();

      if (response.ok) {
        setBrands(data.data || []);
        addTestResult({
          endpoint: "/brands",
          method: "GET",
          status: "success",
          statusCode: response.status,
          data: data,
        });
      } else {
        addTestResult({
          endpoint: "/brands",
          method: "GET",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: "/brands",
        method: "GET",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test POST /brands
  const testCreateBrand = async (name: string) => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/v1/vehicle-hierarchy-v3/brands", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${await api.getToken()}`,
        },
        body: JSON.stringify({ name }),
      });

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: "/brands",
          method: "POST",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh brands list
        await testGetBrands();
      } else {
        addTestResult({
          endpoint: "/brands",
          method: "POST",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: "/brands",
        method: "POST",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test GET /brands/:id
  const testGetBrandById = async (brandId: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}`,
        {
          headers: {
            Authorization: `Bearer ${await api.getToken()}`,
          },
        },
      );

      const data = await response.json();

      addTestResult({
        endpoint: `/brands/${brandId}`,
        method: "GET",
        status: response.ok ? "success" : "error",
        statusCode: response.status,
        data: response.ok ? data : undefined,
        error: response.ok ? undefined : data.message || "Unknown error",
      });
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}`,
        method: "GET",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test PUT /brands/:id - Update name
  const testUpdateBrandName = async (brandId: string, newName: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await api.getToken()}`,
          },
          body: JSON.stringify({ name: newName }),
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}`,
          method: "PUT (name)",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh brands list
        await testGetBrands();
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}`,
          method: "PUT (name)",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}`,
        method: "PUT (name)",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test PUT /brands/:id - Toggle active status
  const testToggleBrandActive = async (
    brandId: string,
    currentActive: boolean,
  ) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await api.getToken()}`,
          },
          body: JSON.stringify({ isActive: !currentActive }),
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}`,
          method: "PUT (active)",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh brands list
        await testGetBrands();
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}`,
          method: "PUT (active)",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}`,
        method: "PUT (active)",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test PUT /brands/:id - Update display order
  const testUpdateBrandOrder = async (brandId: string, newOrder: number) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await api.getToken()}`,
          },
          body: JSON.stringify({ displayOrder: newOrder }),
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}`,
          method: "PUT (order)",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh brands list
        await testGetBrands();
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}`,
          method: "PUT (order)",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}`,
        method: "PUT (order)",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test POST /brands/reorder
  const testReorderBrands = async () => {
    if (brands.length < 2) {
      alert("Need at least 2 brands to test reordering");
      return;
    }

    setIsLoading(true);
    try {
      // Reverse the current order
      const brandOrders = brands.map((brand, index) => ({
        id: brand.id,
        displayOrder: brands.length - index,
      }));

      const response = await fetch(
        "/api/v1/vehicle-hierarchy-v3/brands/reorder",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await api.getToken()}`,
          },
          body: JSON.stringify({ brandOrders }),
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: "/brands/reorder",
          method: "POST",
          status: "success",
          statusCode: response.status,
          data: { ...data, brandOrders },
        });
        // Refresh brands list
        await testGetBrands();
      } else {
        addTestResult({
          endpoint: "/brands/reorder",
          method: "POST",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: "/brands/reorder",
        method: "POST",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test DELETE /brands/:id
  const testDeleteBrand = async (brandId: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${await api.getToken()}`,
          },
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}`,
          method: "DELETE",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh brands list
        await testGetBrands();
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}`,
          method: "DELETE",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}`,
        method: "DELETE",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // ===== SUB-BRAND TEST FUNCTIONS =====

  // Test GET /brands/:brandId/sub-brands
  const testGetSubBrands = async (brandId: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands`,
        {
          headers: {
            Authorization: `Bearer ${await api.getToken()}`,
          },
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands`,
          method: "GET",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        setSubBrands(data.data || []);
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands`,
          method: "GET",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}/sub-brands`,
        method: "GET",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test POST /brands/:brandId/sub-brands
  const testCreateSubBrand = async (brandId: string) => {
    const name = prompt("Enter sub-brand name:");
    if (!name) return;

    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await api.getToken()}`,
          },
          body: JSON.stringify({ name }),
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands`,
          method: "POST",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh sub-brands list
        try {
          await testGetSubBrands(brandId);
        } catch (refreshError) {
          console.error(
            "Failed to refresh sub-brands after creation:",
            refreshError,
          );
        }
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands`,
          method: "POST",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}/sub-brands`,
        method: "POST",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test GET /brands/:brandId/sub-brands/:subBrandId
  const testGetSubBrandById = async (brandId: string, subBrandId: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}`,
        {
          headers: {
            Authorization: `Bearer ${await api.getToken()}`,
          },
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}`,
          method: "GET",
          status: "success",
          statusCode: response.status,
          data: data,
        });
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}`,
          method: "GET",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}/sub-brands/${subBrandId}`,
        method: "GET",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test PUT /brands/:brandId/sub-brands/:subBrandId - Update name
  const testUpdateSubBrandName = async (
    brandId: string,
    subBrandId: string,
    currentName: string,
  ) => {
    const newName = prompt("Enter new sub-brand name:", currentName);
    if (!newName || newName === currentName) return;

    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await api.getToken()}`,
          },
          body: JSON.stringify({ name: newName }),
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}`,
          method: "PUT (name)",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh sub-brands list
        try {
          await testGetSubBrands(brandId);
        } catch (refreshError) {
          console.error(
            "Failed to refresh sub-brands after update:",
            refreshError,
          );
        }
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}`,
          method: "PUT (name)",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}/sub-brands/${subBrandId}`,
        method: "PUT (name)",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test PUT /brands/:brandId/sub-brands/:subBrandId - Toggle active status
  const testToggleSubBrandActive = async (
    brandId: string,
    subBrandId: string,
    currentActive: boolean,
  ) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await api.getToken()}`,
          },
          body: JSON.stringify({ isActive: !currentActive }),
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}`,
          method: "PUT (active)",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh sub-brands list
        try {
          await testGetSubBrands(brandId);
        } catch (refreshError) {
          console.error(
            "Failed to refresh sub-brands after toggle:",
            refreshError,
          );
        }
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}`,
          method: "PUT (active)",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}/sub-brands/${subBrandId}`,
        method: "PUT (active)",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test DELETE /brands/:brandId/sub-brands/:subBrandId
  const testDeleteSubBrand = async (brandId: string, subBrandId: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${await api.getToken()}`,
          },
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}`,
          method: "DELETE",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh sub-brands list
        try {
          await testGetSubBrands(brandId);
        } catch (refreshError) {
          console.error(
            "Failed to refresh sub-brands after deletion:",
            refreshError,
          );
        }
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}`,
          method: "DELETE",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}/sub-brands/${subBrandId}`,
        method: "DELETE",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test POST /brands/:brandId/sub-brands/reorder
  const testReorderSubBrands = async (brandId: string) => {
    if (subBrands.length < 2) {
      alert("Need at least 2 sub-brands to test reordering");
      return;
    }

    setIsLoading(true);
    try {
      // Reverse the current order
      const subBrandOrders = subBrands.map((subBrand, index) => ({
        id: subBrand.id,
        displayOrder: subBrands.length - index,
      }));

      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/reorder`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await api.getToken()}`,
          },
          body: JSON.stringify({ subBrandOrders }),
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/reorder`,
          method: "POST",
          status: "success",
          statusCode: response.status,
          data: { ...data, subBrandOrders },
        });
        // Refresh sub-brands list
        try {
          await testGetSubBrands(brandId);
        } catch (refreshError) {
          console.error(
            "Failed to refresh sub-brands after reorder:",
            refreshError,
          );
        }
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/reorder`,
          method: "POST",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}/sub-brands/reorder`,
        method: "POST",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // ===== MODEL TEST FUNCTIONS =====

  // Test GET /brands/:brandId/sub-brands/:subBrandId/models
  const testGetModels = async (brandId: string, subBrandId: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}/models`,
        {
          headers: {
            Authorization: `Bearer ${await api.getToken()}`,
          },
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models`,
          method: "GET",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        setModels(data.data || []);
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models`,
          method: "GET",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models`,
        method: "GET",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test POST /brands/:brandId/sub-brands/:subBrandId/models
  const testCreateModel = async (brandId: string, subBrandId: string) => {
    const name = prompt("Enter model name:");
    if (!name) return;

    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}/models`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await api.getToken()}`,
          },
          body: JSON.stringify({ name }),
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models`,
          method: "POST",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh models list
        try {
          await testGetModels(brandId, subBrandId);
        } catch (refreshError) {
          console.error(
            "Failed to refresh models after creation:",
            refreshError,
          );
        }
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models`,
          method: "POST",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models`,
        method: "POST",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test GET /brands/:brandId/sub-brands/:subBrandId/models/:modelId
  const testGetModelById = async (
    brandId: string,
    subBrandId: string,
    modelId: string,
  ) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
        {
          headers: {
            Authorization: `Bearer ${await api.getToken()}`,
          },
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
          method: "GET",
          status: "success",
          statusCode: response.status,
          data: data,
        });
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
          method: "GET",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
        method: "GET",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test PUT /brands/:brandId/sub-brands/:subBrandId/models/:modelId (name)
  const testUpdateModelName = async (
    brandId: string,
    subBrandId: string,
    modelId: string,
    currentName: string,
  ) => {
    const newName = prompt("Enter new model name:", currentName);
    if (!newName || newName === currentName) return;

    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await api.getToken()}`,
          },
          body: JSON.stringify({ name: newName }),
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
          method: "PUT",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh models list
        try {
          await testGetModels(brandId, subBrandId);
        } catch (refreshError) {
          console.error("Failed to refresh models after update:", refreshError);
        }
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
          method: "PUT",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
        method: "PUT",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test PUT /brands/:brandId/sub-brands/:subBrandId/models/:modelId (active)
  const testToggleModelActive = async (
    brandId: string,
    subBrandId: string,
    modelId: string,
    currentActive: boolean,
  ) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await api.getToken()}`,
          },
          body: JSON.stringify({ isActive: !currentActive }),
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
          method: "PUT",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh models list
        try {
          await testGetModels(brandId, subBrandId);
        } catch (refreshError) {
          console.error("Failed to refresh models after toggle:", refreshError);
        }
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
          method: "PUT",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
        method: "PUT",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test DELETE /brands/:brandId/sub-brands/:subBrandId/models/:modelId
  const testDeleteModel = async (
    brandId: string,
    subBrandId: string,
    modelId: string,
  ) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${await api.getToken()}`,
          },
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
          method: "DELETE",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh models list and reset selection
        setSelectedModelId("");
        try {
          await testGetModels(brandId, subBrandId);
        } catch (refreshError) {
          console.error(
            "Failed to refresh models after deletion:",
            refreshError,
          );
        }
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
          method: "DELETE",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
        method: "DELETE",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test POST /brands/:brandId/sub-brands/:subBrandId/models/reorder
  const testReorderModels = async (brandId: string, subBrandId: string) => {
    if (models.length < 2) {
      alert("Need at least 2 models to test reordering");
      return;
    }

    // Create a simple reorder: reverse the current order
    const reorderData = models.map((model, index) => ({
      id: model.id,
      displayOrder: models.length - index,
    }));

    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}/models/reorder`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await api.getToken()}`,
          },
          body: JSON.stringify({ modelOrders: reorderData }),
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models/reorder`,
          method: "POST",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh models list
        try {
          await testGetModels(brandId, subBrandId);
        } catch (refreshError) {
          console.error(
            "Failed to refresh models after reorder:",
            refreshError,
          );
        }
      } else {
        addTestResult({
          endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models/reorder`,
          method: "POST",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/brands/${brandId}/sub-brands/${subBrandId}/models/reorder`,
        method: "POST",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // ===== YEAR TESTING FUNCTIONS =====

  // Test GET /years
  const testGetYears = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/v1/vehicle-hierarchy-v3/years", {
        headers: {
          Authorization: `Bearer ${await api.getToken()}`,
        },
      });

      const data = await response.json();

      if (response.ok) {
        setYears(data.data || []);
        addTestResult({
          endpoint: "/years",
          method: "GET",
          status: "success",
          statusCode: response.status,
          data: data,
        });
      } else {
        addTestResult({
          endpoint: "/years",
          method: "GET",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: "/years",
        method: "GET",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test POST /years
  const testCreateYear = async () => {
    const yearName = prompt('Enter year name (e.g., "2024"):');
    if (!yearName) return;

    setIsLoading(true);
    try {
      const response = await fetch("/api/v1/vehicle-hierarchy-v3/years", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${await api.getToken()}`,
        },
        body: JSON.stringify({ name: yearName }),
      });

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: "/years",
          method: "POST",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh years list
        try {
          await testGetYears();
        } catch (refreshError) {
          console.error("Failed to refresh years after create:", refreshError);
        }
      } else {
        addTestResult({
          endpoint: "/years",
          method: "POST",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: "/years",
        method: "POST",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test PUT /years/:yearId
  const testUpdateYear = async (yearId: string) => {
    const newName = prompt("Enter new year name:");
    if (!newName) return;

    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/years/${yearId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await api.getToken()}`,
          },
          body: JSON.stringify({ name: newName }),
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/years/${yearId}`,
          method: "PUT",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh years list
        try {
          await testGetYears();
        } catch (refreshError) {
          console.error("Failed to refresh years after update:", refreshError);
        }
      } else {
        addTestResult({
          endpoint: `/years/${yearId}`,
          method: "PUT",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/years/${yearId}`,
        method: "PUT",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test DELETE /years/:yearId
  const testDeleteYear = async (yearId: string) => {
    if (!confirm("Are you sure you want to delete this year?")) return;

    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/years/${yearId}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${await api.getToken()}`,
          },
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/years/${yearId}`,
          method: "DELETE",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh years list
        try {
          await testGetYears();
        } catch (refreshError) {
          console.error("Failed to refresh years after delete:", refreshError);
        }
      } else {
        addTestResult({
          endpoint: `/years/${yearId}`,
          method: "DELETE",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/years/${yearId}`,
        method: "DELETE",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // ===== MODEL-YEAR ASSOCIATION TESTING FUNCTIONS =====

  // Test GET /models/:modelId/years
  const testGetYearsByModel = async (modelId: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/models/${modelId}/years`,
        {
          headers: {
            Authorization: `Bearer ${await api.getToken()}`,
          },
        },
      );

      const data = await response.json();

      if (response.ok) {
        setModelYears(data.data || []);
        addTestResult({
          endpoint: `/models/${modelId}/years`,
          method: "GET",
          status: "success",
          statusCode: response.status,
          data: data,
        });
      } else {
        addTestResult({
          endpoint: `/models/${modelId}/years`,
          method: "GET",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/models/${modelId}/years`,
        method: "GET",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test POST /models/:modelId/years
  const testCreateModelYear = async (modelId: string, yearId: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/models/${modelId}/years`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await api.getToken()}`,
          },
          body: JSON.stringify({ modelId, yearId }),
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/models/${modelId}/years`,
          method: "POST",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh model-years list
        try {
          await testGetYearsByModel(modelId);
        } catch (refreshError) {
          console.error(
            "Failed to refresh model-years after create:",
            refreshError,
          );
        }
      } else {
        addTestResult({
          endpoint: `/models/${modelId}/years`,
          method: "POST",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/models/${modelId}/years`,
        method: "POST",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  // Test DELETE /models/:modelId/years/:yearId
  const testDeleteModelYear = async (modelId: string, yearId: string) => {
    if (
      !confirm("Are you sure you want to delete this model-year association?")
    )
      return;

    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/v1/vehicle-hierarchy-v3/models/${modelId}/years/${yearId}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${await api.getToken()}`,
          },
        },
      );

      const data = await response.json();

      if (response.ok) {
        addTestResult({
          endpoint: `/models/${modelId}/years/${yearId}`,
          method: "DELETE",
          status: "success",
          statusCode: response.status,
          data: data,
        });
        // Refresh model-years list
        try {
          await testGetYearsByModel(modelId);
        } catch (refreshError) {
          console.error(
            "Failed to refresh model-years after delete:",
            refreshError,
          );
        }
      } else {
        addTestResult({
          endpoint: `/models/${modelId}/years/${yearId}`,
          method: "DELETE",
          status: "error",
          statusCode: response.status,
          error: data.message || "Unknown error",
        });
      }
    } catch (error) {
      addTestResult({
        endpoint: `/models/${modelId}/years/${yearId}`,
        method: "DELETE",
        status: "error",
        error: error instanceof Error ? error.message : "Network error",
      });
    }
    setIsLoading(false);
  };

  const getStatusIcon = (status: TestResult["status"]) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />;
    }
  };

  // Helper functions for sub-brand selection
  const getSelectedSubBrand = () => {
    return subBrands.find((sb) => sb.id === selectedSubBrandId);
  };

  const handleSubBrandSelection = (subBrandId: string) => {
    setSelectedSubBrandId(subBrandId);
    setSelectedModelId(""); // Reset model selection
    setModels([]); // Clear models list
  };

  // Helper functions for model selection
  const getSelectedModel = () => {
    return models.find((m) => m.id === selectedModelId);
  };

  const handleModelSelection = (modelId: string) => {
    setSelectedModelId(modelId);
  };

  // Reset sub-brand selection when brand changes
  const handleBrandSelection = (brandId: string) => {
    setSelectedBrandId(brandId);
    setSelectedSubBrandId(""); // Reset sub-brand selection
    setSelectedModelId(""); // Reset model selection
    setSubBrands([]); // Clear sub-brands list
    setModels([]); // Clear models list
  };

  // Helper functions for year selection removed - were unused

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            API Testing Dashboard
          </h1>
          <p className="text-gray-600">
            Test Vehicle Hierarchy V3 endpoints with live data
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="secondary">{userProfile?.email}</Badge>
          <Badge variant="outline">
            Tenant: {userProfile?.tenantId?.slice(0, 8)}...
          </Badge>
        </div>
      </div>

      {/* Vehicle Brand V3 Testing Section */}
      <Card>
        <CardHeader
          title="Vehicle Brand V3 Testing"
          subtitle="Test all CRUD operations for the Brand endpoints"
        >
          <div className="mt-2">
            <Button
              onClick={clearResults}
              variant="outline"
              size="sm"
              disabled={testResults.length === 0}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear Results
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* GET /brands */}
            <Button
              onClick={testGetBrands}
              disabled={isLoading}
              className="h-20 flex flex-col items-center justify-center"
              variant="outline"
            >
              <Database className="h-5 w-5 mb-1" />
              <span className="text-sm">GET /brands</span>
              <span className="text-xs text-gray-500">List all brands</span>
            </Button>

            {/* POST /brands */}
            <Button
              onClick={() => testCreateBrand(`Test Brand ${Date.now()}`)}
              disabled={isLoading}
              className="h-20 flex flex-col items-center justify-center"
              variant="outline"
            >
              <Plus className="h-5 w-5 mb-1" />
              <span className="text-sm">POST /brands</span>
              <span className="text-xs text-gray-500">Create test brand</span>
            </Button>

            {/* GET /brands/:id */}
            <Button
              onClick={() => {
                if (selectedBrandId) {
                  testGetBrandById(selectedBrandId);
                } else if (brands.length > 0) {
                  testGetBrandById(brands[0].id);
                } else {
                  alert("No brands available. Create one first.");
                }
              }}
              disabled={isLoading}
              className="h-20 flex flex-col items-center justify-center"
              variant="outline"
            >
              <Play className="h-5 w-5 mb-1" />
              <span className="text-sm">GET /brands/:id</span>
              <span className="text-xs text-gray-500">Get brand by ID</span>
            </Button>

            {/* PUT /brands/:id - Update name */}
            <Button
              onClick={() => {
                if (selectedBrandId) {
                  testUpdateBrandName(
                    selectedBrandId,
                    `Updated Brand ${Date.now()}`,
                  );
                } else if (brands.length > 0) {
                  testUpdateBrandName(
                    brands[0].id,
                    `Updated Brand ${Date.now()}`,
                  );
                } else {
                  alert("No brands available. Create one first.");
                }
              }}
              disabled={isLoading}
              className="h-20 flex flex-col items-center justify-center"
              variant="outline"
            >
              <Edit className="h-5 w-5 mb-1" />
              <span className="text-sm">PUT name</span>
              <span className="text-xs text-gray-500">Update brand name</span>
            </Button>

            {/* PUT /brands/:id - Toggle active */}
            <Button
              onClick={() => {
                const brand = selectedBrandId
                  ? brands.find((b) => b.id === selectedBrandId)
                  : brands[0];

                if (brand) {
                  testToggleBrandActive(brand.id, brand.isActive);
                } else {
                  alert("No brands available. Create one first.");
                }
              }}
              disabled={isLoading}
              className="h-20 flex flex-col items-center justify-center"
              variant="outline"
            >
              <Edit className="h-5 w-5 mb-1" />
              <span className="text-sm">PUT active</span>
              <span className="text-xs text-gray-500">
                Toggle active status
              </span>
            </Button>

            {/* PUT /brands/:id - Update display order */}
            <Button
              onClick={() => {
                const brand = selectedBrandId
                  ? brands.find((b) => b.id === selectedBrandId)
                  : brands[0];

                if (brand) {
                  const newOrder = brand.displayOrder === 1 ? 999 : 1;
                  testUpdateBrandOrder(brand.id, newOrder);
                } else {
                  alert("No brands available. Create one first.");
                }
              }}
              disabled={isLoading}
              className="h-20 flex flex-col items-center justify-center"
              variant="outline"
            >
              <Edit className="h-5 w-5 mb-1" />
              <span className="text-sm">PUT order</span>
              <span className="text-xs text-gray-500">
                Change display order
              </span>
            </Button>

            {/* POST /brands/reorder */}
            <Button
              onClick={testReorderBrands}
              disabled={isLoading || brands.length < 2}
              className="h-20 flex flex-col items-center justify-center"
              variant="outline"
            >
              <ArrowUpDown className="h-5 w-5 mb-1" />
              <span className="text-sm">POST reorder</span>
              <span className="text-xs text-gray-500">Reverse brand order</span>
            </Button>

            {/* DELETE /brands/:id */}
            <Button
              onClick={() => {
                if (selectedBrandId) {
                  testDeleteBrand(selectedBrandId);
                } else if (brands.length > 0) {
                  testDeleteBrand(brands[0].id);
                } else {
                  alert("No brands available. Create one first.");
                }
              }}
              disabled={isLoading}
              className="h-20 flex flex-col items-center justify-center"
              variant="outline"
            >
              <Trash2 className="h-5 w-5 mb-1" />
              <span className="text-sm">DELETE /brands/:id</span>
              <span className="text-xs text-gray-500">Soft delete brand</span>
            </Button>
          </div>

          {/* Brand Selection */}
          {brands.length > 0 && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Brand for ID-based operations:
              </label>
              <select
                value={selectedBrandId}
                onChange={(e) => setSelectedBrandId(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">Use first brand</option>
                {brands.map((brand) => (
                  <option key={brand.id} value={brand.id}>
                    {brand.name} (Order: {brand.displayOrder})
                  </option>
                ))}
              </select>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Vehicle Sub-Brand V3 Testing Section */}
      <Card>
        <CardHeader
          title="Vehicle Sub-Brand V3 Testing"
          subtitle="Test all CRUD operations for the Sub-Brand endpoints (requires Brand selection)"
        />
        <CardContent>
          {/* Brand Selection for Sub-Brand Operations */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Brand for Sub-Brand Operations:
            </label>
            <select
              value={selectedBrandId}
              onChange={(e) => handleBrandSelection(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Select a brand...</option>
              {brands.map((brand) => (
                <option key={brand.id} value={brand.id}>
                  {brand.name} (Order: {brand.displayOrder})
                </option>
              ))}
            </select>
            {selectedBrandId && (
              <p className="text-sm text-gray-600 mt-2">
                Selected: {brands.find((b) => b.id === selectedBrandId)?.name}
              </p>
            )}
          </div>

          {/* Sub-Brand Selection for Specific Operations */}
          {selectedBrandId && subBrands.length > 0 && (
            <div className="mb-6 p-4 bg-green-50 rounded-lg">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Sub-Brand for Update/Delete/Get by ID Operations:
              </label>
              <select
                value={selectedSubBrandId}
                onChange={(e) => handleSubBrandSelection(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">Select a sub-brand...</option>
                {subBrands.map((subBrand) => (
                  <option key={subBrand.id} value={subBrand.id}>
                    {subBrand.name} (Order: {subBrand.displayOrder},{" "}
                    {subBrand.isActive ? "Active" : "Inactive"})
                  </option>
                ))}
              </select>
              {selectedSubBrandId && (
                <p className="text-sm text-gray-600 mt-2">
                  Selected: {getSelectedSubBrand()?.name}
                </p>
              )}
            </div>
          )}

          {selectedBrandId ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* GET /brands/:brandId/sub-brands */}
              <Button
                onClick={() => testGetSubBrands(selectedBrandId)}
                disabled={isLoading}
                className="flex items-center justify-center"
              >
                <Database className="h-4 w-4 mr-2" />
                GET Sub-Brands
              </Button>

              {/* POST /brands/:brandId/sub-brands */}
              <Button
                onClick={() => testCreateSubBrand(selectedBrandId)}
                disabled={isLoading}
                className="flex items-center justify-center"
              >
                <Plus className="h-4 w-4 mr-2" />
                CREATE Sub-Brand
              </Button>

              {/* GET /brands/:brandId/sub-brands/:subBrandId */}
              <Button
                onClick={() => {
                  if (!selectedSubBrandId) {
                    alert("Please select a sub-brand first.");
                    return;
                  }
                  testGetSubBrandById(selectedBrandId, selectedSubBrandId);
                }}
                disabled={isLoading || !selectedSubBrandId}
                className="flex items-center justify-center"
              >
                <Database className="h-4 w-4 mr-2" />
                GET Sub-Brand by ID
              </Button>

              {/* PUT /brands/:brandId/sub-brands/:subBrandId (name) */}
              <Button
                onClick={() => {
                  const selectedSubBrand = getSelectedSubBrand();
                  if (!selectedSubBrand) {
                    alert("Please select a sub-brand first.");
                    return;
                  }
                  testUpdateSubBrandName(
                    selectedBrandId,
                    selectedSubBrand.id,
                    selectedSubBrand.name,
                  );
                }}
                disabled={isLoading || !selectedSubBrandId}
                className="flex items-center justify-center"
              >
                <Edit className="h-4 w-4 mr-2" />
                UPDATE Name
              </Button>

              {/* PUT /brands/:brandId/sub-brands/:subBrandId (active) */}
              <Button
                onClick={() => {
                  const selectedSubBrand = getSelectedSubBrand();
                  if (!selectedSubBrand) {
                    alert("Please select a sub-brand first.");
                    return;
                  }
                  testToggleSubBrandActive(
                    selectedBrandId,
                    selectedSubBrand.id,
                    selectedSubBrand.isActive,
                  );
                }}
                disabled={isLoading || !selectedSubBrandId}
                className="flex items-center justify-center"
              >
                <Edit className="h-4 w-4 mr-2" />
                TOGGLE Active
              </Button>

              {/* DELETE /brands/:brandId/sub-brands/:subBrandId */}
              <Button
                onClick={() => {
                  const selectedSubBrand = getSelectedSubBrand();
                  if (!selectedSubBrand) {
                    alert("Please select a sub-brand first.");
                    return;
                  }
                  if (
                    confirm(
                      `Are you sure you want to delete "${selectedSubBrand.name}"?`,
                    )
                  ) {
                    testDeleteSubBrand(selectedBrandId, selectedSubBrand.id);
                  }
                }}
                disabled={isLoading || !selectedSubBrandId}
                variant="danger"
                className="flex items-center justify-center"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                DELETE Sub-Brand
              </Button>

              {/* POST /brands/:brandId/sub-brands/reorder */}
              <Button
                onClick={() => testReorderSubBrands(selectedBrandId)}
                disabled={isLoading || subBrands.length < 2}
                className="flex items-center justify-center"
              >
                <ArrowUpDown className="h-4 w-4 mr-2" />
                REORDER Sub-Brands
              </Button>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              Please select a brand above to test Sub-Brand operations
            </div>
          )}
        </CardContent>
      </Card>

      {/* Vehicle Model V3 Testing Section */}
      <Card>
        <CardHeader
          title="Vehicle Model V3 Testing"
          subtitle="Test all CRUD operations for the Model endpoints (requires Brand and Sub-Brand selection)"
        />
        <CardContent>
          {/* Brand Selection for Model Operations */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Brand for Model Operations:
            </label>
            <select
              value={selectedBrandId}
              onChange={(e) => handleBrandSelection(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Select a brand...</option>
              {brands.map((brand) => (
                <option key={brand.id} value={brand.id}>
                  {brand.name} (Order: {brand.displayOrder})
                </option>
              ))}
            </select>
            {selectedBrandId && (
              <p className="text-sm text-gray-600 mt-2">
                Selected: {brands.find((b) => b.id === selectedBrandId)?.name}
              </p>
            )}
          </div>

          {/* Sub-Brand Selection for Model Operations */}
          {selectedBrandId && subBrands.length > 0 && (
            <div className="mb-6 p-4 bg-green-50 rounded-lg">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Sub-Brand for Model Operations:
              </label>
              <select
                value={selectedSubBrandId}
                onChange={(e) => handleSubBrandSelection(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">Select a sub-brand...</option>
                {subBrands.map((subBrand) => (
                  <option key={subBrand.id} value={subBrand.id}>
                    {subBrand.name} (Order: {subBrand.displayOrder},{" "}
                    {subBrand.isActive ? "Active" : "Inactive"})
                  </option>
                ))}
              </select>
              {selectedSubBrandId && (
                <p className="text-sm text-gray-600 mt-2">
                  Selected: {getSelectedSubBrand()?.name}
                </p>
              )}
            </div>
          )}

          {/* Model Selection for Specific Operations */}
          {selectedSubBrandId && models.length > 0 && (
            <div className="mb-6 p-4 bg-purple-50 rounded-lg">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Model for Update/Delete/Get by ID Operations:
              </label>
              <select
                value={selectedModelId}
                onChange={(e) => handleModelSelection(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">Select a model...</option>
                {models.map((model) => (
                  <option key={model.id} value={model.id}>
                    {model.name} (Order: {model.displayOrder},{" "}
                    {model.isActive ? "Active" : "Inactive"})
                  </option>
                ))}
              </select>
              {selectedModelId && (
                <p className="text-sm text-gray-600 mt-2">
                  Selected: {getSelectedModel()?.name}
                </p>
              )}
            </div>
          )}

          {selectedBrandId && selectedSubBrandId ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* GET /brands/:brandId/sub-brands/:subBrandId/models */}
              <Button
                onClick={() =>
                  testGetModels(selectedBrandId, selectedSubBrandId)
                }
                disabled={isLoading}
                className="flex items-center justify-center"
              >
                <Database className="h-4 w-4 mr-2" />
                GET Models
              </Button>

              {/* POST /brands/:brandId/sub-brands/:subBrandId/models */}
              <Button
                onClick={() =>
                  testCreateModel(selectedBrandId, selectedSubBrandId)
                }
                disabled={isLoading}
                className="flex items-center justify-center"
              >
                <Plus className="h-4 w-4 mr-2" />
                CREATE Model
              </Button>

              {/* GET /brands/:brandId/sub-brands/:subBrandId/models/:modelId */}
              <Button
                onClick={() => {
                  if (!selectedModelId) {
                    alert("Please select a model first.");
                    return;
                  }
                  testGetModelById(
                    selectedBrandId,
                    selectedSubBrandId,
                    selectedModelId,
                  );
                }}
                disabled={isLoading || !selectedModelId}
                className="flex items-center justify-center"
              >
                <Database className="h-4 w-4 mr-2" />
                GET Model by ID
              </Button>

              {/* PUT /brands/:brandId/sub-brands/:subBrandId/models/:modelId (name) */}
              <Button
                onClick={() => {
                  const selectedModel = getSelectedModel();
                  if (!selectedModel) {
                    alert("Please select a model first.");
                    return;
                  }
                  testUpdateModelName(
                    selectedBrandId,
                    selectedSubBrandId,
                    selectedModel.id,
                    selectedModel.name,
                  );
                }}
                disabled={isLoading || !selectedModelId}
                className="flex items-center justify-center"
              >
                <Edit className="h-4 w-4 mr-2" />
                UPDATE Name
              </Button>

              {/* PUT /brands/:brandId/sub-brands/:subBrandId/models/:modelId (active) */}
              <Button
                onClick={() => {
                  const selectedModel = getSelectedModel();
                  if (!selectedModel) {
                    alert("Please select a model first.");
                    return;
                  }
                  testToggleModelActive(
                    selectedBrandId,
                    selectedSubBrandId,
                    selectedModel.id,
                    selectedModel.isActive,
                  );
                }}
                disabled={isLoading || !selectedModelId}
                className="flex items-center justify-center"
              >
                <Edit className="h-4 w-4 mr-2" />
                TOGGLE Active
              </Button>

              {/* DELETE /brands/:brandId/sub-brands/:subBrandId/models/:modelId */}
              <Button
                onClick={() => {
                  const selectedModel = getSelectedModel();
                  if (!selectedModel) {
                    alert("Please select a model first.");
                    return;
                  }
                  if (
                    confirm(
                      `Are you sure you want to delete "${selectedModel.name}"?`,
                    )
                  ) {
                    testDeleteModel(
                      selectedBrandId,
                      selectedSubBrandId,
                      selectedModel.id,
                    );
                  }
                }}
                disabled={isLoading || !selectedModelId}
                variant="danger"
                className="flex items-center justify-center"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                DELETE Model
              </Button>

              {/* POST /brands/:brandId/sub-brands/:subBrandId/models/reorder */}
              <Button
                onClick={() =>
                  testReorderModels(selectedBrandId, selectedSubBrandId)
                }
                disabled={isLoading || models.length < 2}
                className="flex items-center justify-center"
              >
                <ArrowUpDown className="h-4 w-4 mr-2" />
                REORDER Models
              </Button>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              {!selectedBrandId
                ? "Please select a brand above to test Model operations"
                : "Please select a sub-brand above to test Model operations"}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Current Models Display */}
      {selectedSubBrandId && models.length > 0 && (
        <Card>
          <CardHeader
            title={`Current Models for ${getSelectedSubBrand()?.name}`}
          />
          <CardContent>
            <div className="space-y-2">
              {models.map((model) => (
                <div
                  key={model.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div>
                    <span className="font-medium">{model.name}</span>
                    <span className="text-sm text-gray-500 ml-2">
                      Order: {model.displayOrder}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={model.isActive ? "success" : "secondary"}>
                      {model.isActive ? "Active" : "Inactive"}
                    </Badge>
                    {model.deletedAt && <Badge variant="error">Deleted</Badge>}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* ===== YEAR TESTING SECTION ===== */}
      <Card>
        <CardHeader title="Year Operations Testing" />
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {/* GET /years */}
            <Button
              onClick={testGetYears}
              disabled={isLoading}
              className="flex items-center justify-center"
            >
              <Database className="h-4 w-4 mr-2" />
              GET Years
            </Button>

            {/* POST /years */}
            <Button
              onClick={testCreateYear}
              disabled={isLoading}
              className="flex items-center justify-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              CREATE Year
            </Button>

            {/* PUT /years/:yearId */}
            <Button
              onClick={() => {
                if (!selectedYearId) {
                  alert("Please select a year first");
                  return;
                }
                testUpdateYear(selectedYearId);
              }}
              disabled={isLoading || !selectedYearId}
              className="flex items-center justify-center"
            >
              <Edit className="h-4 w-4 mr-2" />
              UPDATE Year
            </Button>

            {/* DELETE /years/:yearId */}
            <Button
              onClick={() => {
                if (!selectedYearId) {
                  alert("Please select a year first");
                  return;
                }
                testDeleteYear(selectedYearId);
              }}
              disabled={isLoading || !selectedYearId}
              className="flex items-center justify-center"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              DELETE Year
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Current Years Display */}
      {years.length > 0 && (
        <Card>
          <CardHeader title="Current Years" />
          <CardContent>
            <div className="space-y-2">
              {years.map((year) => (
                <div
                  key={year.id}
                  className={`flex items-center justify-between p-3 rounded-lg cursor-pointer ${
                    selectedYearId === year.id
                      ? "bg-blue-100 border-blue-300"
                      : "bg-gray-50"
                  }`}
                  onClick={() => setSelectedYearId(year.id)}
                >
                  <div>
                    <span className="font-medium">{year.name}</span>
                    <span className="text-sm text-gray-500 ml-2">
                      Order: {year.displayOrder}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={year.isActive ? "success" : "secondary"}>
                      {year.isActive ? "Active" : "Inactive"}
                    </Badge>
                    {year.deletedAt && <Badge variant="error">Deleted</Badge>}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* ===== MODEL-YEAR ASSOCIATION TESTING SECTION ===== */}
      <Card>
        <CardHeader title="Model-Year Association Testing" />
        <CardContent>
          {selectedModelId ? (
            <div className="space-y-4">
              <div className="text-sm text-gray-600 mb-4">
                Testing associations for model:{" "}
                <strong>{getSelectedModel()?.name}</strong>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* GET /models/:modelId/years */}
                <Button
                  onClick={() => testGetYearsByModel(selectedModelId)}
                  disabled={isLoading}
                  className="flex items-center justify-center"
                >
                  <Database className="h-4 w-4 mr-2" />
                  GET Model Years
                </Button>

                {/* POST /models/:modelId/years */}
                <Button
                  onClick={() => {
                    if (!selectedYearId) {
                      alert("Please select a year first");
                      return;
                    }
                    testCreateModelYear(selectedModelId, selectedYearId);
                  }}
                  disabled={isLoading || !selectedYearId}
                  className="flex items-center justify-center"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  ADD Year to Model
                </Button>

                {/* DELETE /models/:modelId/years/:yearId */}
                <Button
                  onClick={() => {
                    if (!selectedYearId) {
                      alert("Please select a year first");
                      return;
                    }
                    testDeleteModelYear(selectedModelId, selectedYearId);
                  }}
                  disabled={isLoading || !selectedYearId}
                  className="flex items-center justify-center"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  REMOVE Year from Model
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              Please select a model above to test Model-Year association
              operations
            </div>
          )}
        </CardContent>
      </Card>

      {/* Current Model-Years Display */}
      {selectedModelId && modelYears.length > 0 && (
        <Card>
          <CardHeader title={`Years for ${getSelectedModel()?.name}`} />
          <CardContent>
            <div className="space-y-2">
              {modelYears.map((modelYear) => (
                <div
                  key={modelYear.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div>
                    <span className="font-medium">{modelYear.year?.name}</span>
                    <span className="text-sm text-gray-500 ml-2">
                      Associated:{" "}
                      {new Date(modelYear.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Current Sub-Brands Display */}
      {selectedBrandId && subBrands.length > 0 && (
        <Card>
          <CardHeader
            title={`Current Sub-Brands for ${brands.find((b) => b.id === selectedBrandId)?.name}`}
          />
          <CardContent>
            <div className="space-y-2">
              {subBrands.map((subBrand) => (
                <div
                  key={subBrand.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div>
                    <span className="font-medium">{subBrand.name}</span>
                    <span className="text-sm text-gray-500 ml-2">
                      Order: {subBrand.displayOrder}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant={subBrand.isActive ? "success" : "secondary"}
                    >
                      {subBrand.isActive ? "Active" : "Inactive"}
                    </Badge>
                    {subBrand.deletedAt && (
                      <Badge variant="error">Deleted</Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Current Brands Display */}
      {brands.length > 0 && (
        <Card>
          <CardHeader title="Current Brands in Database" />
          <CardContent>
            <div className="space-y-2">
              {brands.map((brand) => (
                <div
                  key={brand.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div>
                    <span className="font-medium">{brand.name}</span>
                    <span className="text-sm text-gray-500 ml-2">
                      Order: {brand.displayOrder}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={brand.isActive ? "success" : "secondary"}>
                      {brand.isActive ? "Active" : "Inactive"}
                    </Badge>
                    {brand.deletedAt && <Badge variant="error">Deleted</Badge>}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader
            title="Test Results"
            subtitle="Latest results from API calls"
          />
          <CardContent>
            <div className="space-y-3">
              {testResults
                .slice()
                .reverse()
                .map((result, index) => (
                  <div
                    key={index}
                    className="border border-gray-200 rounded-lg p-4"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(result.status)}
                        <span className="font-medium">
                          {result.method} {result.endpoint}
                        </span>
                        {result.statusCode && (
                          <Badge variant="outline">{result.statusCode}</Badge>
                        )}
                      </div>
                      <span className="text-sm text-gray-500">
                        {result.timestamp.toLocaleTimeString()}
                      </span>
                    </div>

                    {result.error && (
                      <Alert variant="error" className="mb-2">
                        <p className="text-sm">{result.error}</p>
                      </Alert>
                    )}

                    {result.data ? (
                      <details className="mt-2">
                        <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                          View Response Data
                        </summary>
                        <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </details>
                    ) : null}
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}

      {testResults.length === 0 && (
        <Card>
          <CardContent>
            <div className="text-center py-8">
              <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No tests run yet
              </h3>
              <p className="text-gray-600">
                Click the buttons above to test the Vehicle Brand V3 API
                endpoints. Results will appear here with full request/response
                details.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
