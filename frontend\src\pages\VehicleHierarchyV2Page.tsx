import { useAuth } from "@clerk/clerk-react";
import { Navigate } from "react-router-dom";
import { VehicleHierarchyV2Management } from "../components/vehicle-hierarchy-v2/VehicleHierarchyV2Management";
import { usePermissions } from "../hooks/usePermissions";
import { LoadingSpinner, Alert } from "../components";

export function VehicleHierarchyV2Page() {
  const { isLoaded, isSignedIn } = useAuth();
  const { isCompanyAdmin, isCompanyTech } = usePermissions();

  // Show loading while auth is being determined
  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  // Redirect to sign-in if not authenticated
  if (!isSignedIn) {
    return <Navigate to="/sign-in" replace />;
  }

  // Check if user has access (Company Admin or Company Tech)
  const hasAccess = isCompanyAdmin || isCompanyTech;

  if (!hasAccess) {
    return (
      <div className="space-y-8 p-6">
        <div className="text-center bg-gradient-to-r from-amber-50 to-orange-50 rounded-2xl p-8 border border-amber-100/50">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
            Vehicle Hierarchy V2
          </h1>
          <div className="mt-6">
            <Alert variant="warning" className="bg-white/80 border-amber-200">
              You don't have permission to access vehicle hierarchy management.
              Contact your administrator for access.
            </Alert>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="container mx-auto px-4 py-8">
        <VehicleHierarchyV2Management />
      </div>
    </div>
  );
}
