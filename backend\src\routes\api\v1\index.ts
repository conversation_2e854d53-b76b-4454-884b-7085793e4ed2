import { Router } from 'express';
import { Logger } from 'winston';

import { MiddlewareFactory } from '../../../middleware/middleware-factory.js';
import { DocumentService } from '../../../services/document.service.js';
import { InvitationService } from '../../../services/invitation.service.js';
import { OnboardingService } from '../../../services/onboarding.service.js';
import { PermissionService } from '../../../services/permission.service.js';
import { PrismaService } from '../../../services/prisma.service.js';
import { RoleService } from '../../../services/role.service.js';
import { S3Service } from '../../../services/s3.service.js';
import { TenantService } from '../../../services/tenant.service.js';
import { UserEngagementService } from '../../../services/user-engagement.service.js';
import { UserService } from '../../../services/user.service.js';
import { VehicleHierarchyV2Service } from '../../../services/vehicle-hierarchy-v2.service.js';
import { VehicleHierarchyV3CoordinatorService } from '../../../services/vehicle-hierarchy-v3/vehicle-hierarchy-v3-coordinator.service.js';
import { VehicleHierarchyService } from '../../../services/vehicle-hierarchy.service.js';
import { ClerkAuthMiddleware } from '../../../types/clerk.types.js';

import { createAuthRouter } from './auth.js';
import { createDocumentsRouter } from './documents.js';
import { createEngagementRouter } from './engagement.js';
import { createHealthRouter } from './health.js';
import { createInvitationsRouter } from './invitations.js';
import { createPermissionsRouter } from './permissions.js';
import { createRolesRouter } from './roles.js';
import { createTenantsRouter } from './tenants.js';
import { createUsersRouter } from './users.js';
import { createVehicleHierarchyV2Router } from './vehicle-hierarchy-v2.js';
import { createVehicleHierarchyV3Router } from './vehicle-hierarchy-v3.js';
import { createVehicleHierarchyRouter } from './vehicle-hierarchy.js';

interface ServiceDependencies {
  prismaService: PrismaService;
  userService: UserService;
  tenantService: TenantService;
  onboardingService: OnboardingService;
  invitationService: InvitationService;
  roleService: RoleService;
  permissionService: PermissionService;
  userEngagementService: UserEngagementService;
  vehicleHierarchyService: VehicleHierarchyService;
  vehicleHierarchyV2Service: VehicleHierarchyV2Service;
  vehicleHierarchyV3Service: VehicleHierarchyV3CoordinatorService;
  documentService: DocumentService;
  s3Service: S3Service;
  middlewareFactory: MiddlewareFactory;
  clerkAuth: ClerkAuthMiddleware;
  logger: Logger;
}

export function createApiV1Routes(dependencies: ServiceDependencies): Router {
  const router = Router();

  // Mount health routes at /api/v1/health
  router.use(
    '/health',
    createHealthRouter({
      prismaService: dependencies.prismaService,
      middlewareFactory: dependencies.middlewareFactory,
      logger: dependencies.logger,
    })
  );

  // Mount users routes at /api/v1/users
  router.use(
    '/users',
    createUsersRouter({
      userService: dependencies.userService,
      middlewareFactory: dependencies.middlewareFactory,
      logger: dependencies.logger,
    })
  );

  // Mount tenants routes at /api/v1/tenants
  router.use(
    '/tenants',
    createTenantsRouter({
      tenantService: dependencies.tenantService,
      middlewareFactory: dependencies.middlewareFactory,
      logger: dependencies.logger,
    })
  );

  // Mount auth routes at /api/v1/auth
  router.use(
    '/auth',
    createAuthRouter({
      userService: dependencies.userService,
      tenantService: dependencies.tenantService,
      onboardingService: dependencies.onboardingService,
      invitationService: dependencies.invitationService,
      prismaService: dependencies.prismaService,
      clerkAuth: dependencies.clerkAuth,
      logger: dependencies.logger,
    })
  );

  // Mount documents routes at /api/v1/documents
  router.use(
    '/documents',
    createDocumentsRouter({
      documentService: dependencies.documentService,
      middlewareFactory: dependencies.middlewareFactory,
      logger: dependencies.logger,
    })
  );

  // Mount invitation routes at /api/v1/invitations
  router.use(
    '/invitations',
    createInvitationsRouter({
      invitationService: dependencies.invitationService,
      middlewareFactory: dependencies.middlewareFactory,
      prismaService: dependencies.prismaService,
      logger: dependencies.logger,
    })
  );

  // Mount roles routes at /api/v1/roles
  router.use(
    '/roles',
    createRolesRouter({
      roleService: dependencies.roleService,
      permissionService: dependencies.permissionService,
      middlewareFactory: dependencies.middlewareFactory,
      logger: dependencies.logger,
    })
  );

  // Mount permissions routes at /api/v1/permissions
  router.use(
    '/permissions',
    createPermissionsRouter({
      permissionService: dependencies.permissionService,
      middlewareFactory: dependencies.middlewareFactory,
      logger: dependencies.logger,
    })
  );

  // Mount vehicle hierarchy routes at /api/v1/vehicle-hierarchy
  router.use(
    '/vehicle-hierarchy',
    createVehicleHierarchyRouter({
      vehicleHierarchyService: dependencies.vehicleHierarchyService,
      middlewareFactory: dependencies.middlewareFactory,
      logger: dependencies.logger,
    })
  );

  // Mount vehicle hierarchy V2 routes at /api/v1/vehicle-hierarchy-v2
  router.use(
    '/vehicle-hierarchy-v2',
    createVehicleHierarchyV2Router({
      vehicleHierarchyV2Service: dependencies.vehicleHierarchyV2Service,
      middlewareFactory: dependencies.middlewareFactory,
      logger: dependencies.logger,
    })
  );

  // Mount vehicle hierarchy V3 routes at /api/v1/vehicle-hierarchy-v3
  router.use(
    '/vehicle-hierarchy-v3',
    createVehicleHierarchyV3Router({
      vehicleHierarchyV3Service: dependencies.vehicleHierarchyV3Service,
      middlewareFactory: dependencies.middlewareFactory,
      logger: dependencies.logger,
    })
  );

  // Mount engagement routes at /api/v1/engagement
  router.use(
    '/engagement',
    createEngagementRouter({
      userEngagementService: dependencies.userEngagementService,
      middlewareFactory: dependencies.middlewareFactory,
      logger: dependencies.logger,
    })
  );

  return router;
}
