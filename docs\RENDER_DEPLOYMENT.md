# Render Deployment Guide

This guide walks you through deploying the Tech Notes application to <PERSON>der using the blueprint file.

## Prerequisites

1. **Render Account**: Sign up at [render.com](https://render.com)
2. **GitHub Repository**: Your code should be pushed to GitHub
3. **Environment Variables**: Have your <PERSON> and <PERSON>e keys ready

## Deployment Steps

### 1. Deploy via Blueprint

1. **Connect Repository**:
   - Go to your Render dashboard
   - Click "New" → "Blueprint"
   - Connect your GitHub repository
   - Select the repository containing your tech-notes project

2. **Blueprint Configuration**:
   - Render will automatically detect the `render.yaml` file
   - Review the services that will be created:
     - `tech-notes-db` (PostgreSQL database)
     - `tech-notes-backend` (Express API)
     - `tech-notes-frontend` (React app)

3. **Deploy**:
   - Click "Apply" to start the deployment
   - Render will create all services simultaneously

### 2. Configure Environment Variables

After deployment, you need to manually set sensitive environment variables:

#### Backend Service (`tech-notes-backend`)

Navigate to the backend service settings and add:

```bash
CLERK_SECRET_KEY=sk_live_your_clerk_secret_key
CLERK_PUBLISHABLE_KEY=pk_live_your_clerk_publishable_key
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
FRONTEND_URL=https://your-frontend-url.onrender.com
```

#### Frontend Service (`tech-notes-frontend`)

Navigate to the frontend service settings and add build args:

```bash
VITE_API_URL=https://your-backend-url.onrender.com
VITE_CLERK_PUBLISHABLE_KEY=pk_live_your_clerk_publishable_key
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
```

**Important**: Replace `your-backend-url.onrender.com` with your actual backend service URL from Render.

### 3. Database Setup

1. **Database Migration**:
   - The database will be created automatically
   - Migrations run automatically during deployment via the `start` script
   - If you encounter migration conflicts, see [Migration Troubleshooting](#migration-troubleshooting)
   - For manual migration management, use the service shell:

   ```bash
   # Check migration status
   npx prisma migrate status

   # Deploy pending migrations
   npx prisma migrate deploy

   # Note: Production seeding is handled by migrations, not separate seed scripts
   ```

2. **Database Access**:
   - Connection details are automatically injected as `DATABASE_URL`
   - The database is accessible only from your Render services (secure by default)

### 4. Custom Domain (Optional)

1. **Backend API**:
   - Go to backend service settings
   - Add custom domain (e.g., `api.yourdomain.com`)
   - Update CORS_ORIGIN if needed

2. **Frontend**:
   - Go to frontend service settings
   - Add custom domain (e.g., `yourdomain.com`)
   - Update VITE_API_URL to point to your custom backend domain

### 5. Stripe Webhook Configuration

1. **Get Webhook URL**:
   - Your webhook endpoint will be: `https://your-backend-url.onrender.com/webhooks/stripe`

2. **Configure in Stripe**:
   - Go to Stripe Dashboard → Webhooks
   - Add endpoint with your Render backend URL
   - Select events you need (subscription updates, payment events, etc.)
   - Copy the webhook secret to your backend environment variables

### 6. Clerk Configuration

1. **Update Clerk Settings**:
   - Go to Clerk Dashboard
   - Update allowed origins to include your Render frontend URL
   - Update redirect URLs if using custom domains

## Monitoring & Scaling

### Health Checks

- Both services have health check endpoints configured
- Backend: `/health`
- Frontend: `/health`

### Scaling Options

- **Starter Plan**: Good for development/small apps
- **Standard Plan**: Better performance, custom domains
- **Pro Plan**: High availability, advanced features

### Logs & Monitoring

- View logs in Render dashboard
- Set up log drains for external monitoring
- Configure alerts for service failures

## Cost Optimization

### Estimated Monthly Cost

- **Development**: ~$21/month (Starter DB + 2 Starter services)
- **Production**: ~$65/month (Standard DB + 2 Standard services)

**Plans**: Starter ($7/month), Standard ($25/month), Pro ($85/month) per service

## Troubleshooting

### Migration Troubleshooting

If you encounter Prisma migration errors during deployment:

**Error: "relation already exists" or P3018/P3009**
This happens when the database has partial schema from a previous failed deployment.

**Solution 1: Reset Migration State (Recommended)**

1. Connect to your Render database using the connection details from the dashboard
2. Run the following SQL to reset the migration state:
   ```sql
   DROP TABLE IF EXISTS "_prisma_migrations";
   -- Optionally, for complete reset:
   DROP TABLE IF EXISTS "role_permissions" CASCADE;
   DROP TABLE IF EXISTS "user_roles" CASCADE;
   DROP TABLE IF EXISTS "permissions" CASCADE;
   DROP TABLE IF EXISTS "roles" CASCADE;
   DROP TABLE IF EXISTS "users" CASCADE;
   DROP TABLE IF EXISTS "tenants" CASCADE;
   ```
3. Redeploy your service - migrations will run from scratch

**Solution 2: Use Service Shell**

1. Go to your backend service in Render dashboard
2. Open the "Shell" tab
3. Run migration resolution commands:
   ```bash
   npx prisma migrate status
   npx prisma migrate resolve --rolled-back MIGRATION_NAME
   npx prisma migrate deploy
   ```

For detailed migration troubleshooting, see [Render Deployment Issues](troubleshooting/render-deployment-issues.md#prisma-migration-conflicts).

### Common Issues

1. **Build Failures**:
   - Check Docker build logs
   - Ensure all dependencies are in package.json
   - Verify Dockerfile paths are correct

2. **Database Connection Issues**:
   - Verify DATABASE_URL is set correctly
   - Check if migrations have been run
   - Ensure database is in same region as services

3. **CORS Issues**:
   - Update CORS_ORIGIN to match frontend URL
   - Check if custom domains are configured correctly

4. **Environment Variable Issues**:
   - Sensitive vars must be set manually (not in blueprint)
   - Build args for frontend must be set before build
   - Restart services after changing environment variables

### Getting Help

- Render has excellent documentation and support
- Check service logs for detailed error messages
- Use Render's community forum for specific issues

## Migration from GCP

**Database**: Export from Cloud SQL → Import to Render PostgreSQL
**Environment**: Update API URLs to point to Render services
**CI/CD**: Remove GCP-specific GitHub Actions, keep testing workflows

## Next Steps

After successful deployment:

1. **Test all functionality**:
   - User registration/login
   - API endpoints
   - Payment processing
   - Multi-tenant isolation

2. **Set up monitoring**:
   - Configure uptime monitoring
   - Set up error tracking
   - Monitor performance metrics

3. **Security review**:
   - Verify all secrets are properly configured
   - Test authentication flows
   - Review CORS settings

4. **Performance optimization**:
   - Monitor response times
   - Consider upgrading plans if needed
   - Optimize database queries
