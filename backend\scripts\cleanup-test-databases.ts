#!/usr/bin/env ts-node

import { Client } from 'pg';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from root directory
dotenv.config({ path: path.resolve(__dirname, '../../.env') });
dotenv.config({
  path: path.resolve(__dirname, '../../.env.local'),
  override: true,
});

// Parse command line arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');
const isForce = args.includes('--force');

async function cleanupTestDatabases() {
  const adminUrl = process.env.TEST_DATABASE_ADMIN_URL;

  if (!adminUrl) {
    console.error(
      '❌ TEST_DATABASE_ADMIN_URL environment variable is required'
    );
    process.exit(1);
  }

  const client = new Client({
    connectionString: adminUrl,
    ssl: {
      rejectUnauthorized: false,
    },
  });

  try {
    console.log('🔗 Connecting to database...');
    await client.connect();

    // Get all test databases
    const result = await client.query(`
      SELECT datname
      FROM pg_database
      WHERE datname LIKE 'tech_notes_test_%'
      AND datname != 'tech_notes_test'
      ORDER BY datname
    `);

    const testDatabases = result.rows.map((row) => row.datname);

    if (testDatabases.length === 0) {
      console.log('✨ No old test databases found to clean up');
      return;
    }

    console.log(`📋 Found ${testDatabases.length} test databases to clean up:`);
    testDatabases.forEach((db) => console.log(`  - ${db}`));

    if (isDryRun) {
      console.log('\n🔍 DRY RUN MODE - No databases will be deleted');
      return;
    }

    console.log(`\n🧹 Starting cleanup... ${isForce ? '(FORCE MODE)' : ''}`);

    let successCount = 0;
    let failureCount = 0;

    for (const dbName of testDatabases) {
      try {
        console.log(`🗑️  Dropping ${dbName}...`);

        // First, try to drop directly
        try {
          await client.query(`DROP DATABASE IF EXISTS "${dbName}"`);
          console.log(`✅ Dropped ${dbName}`);
          successCount++;
          continue;
        } catch (dropError: any) {
          // If it fails due to active connections, try force termination if enabled
          if (dropError.code === '55006') {
            console.log(`⚠️  ${dbName} has active connections...`);

            if (isForce) {
              try {
                console.log('🔥 Force terminating connections...');

                // Terminate all connections to this database
                await client.query(
                  `
                  SELECT pg_terminate_backend(pid)
                  FROM pg_stat_activity
                  WHERE datname = $1
                  AND pid <> pg_backend_pid()
                `,
                  [dbName]
                );

                // Wait a moment for connections to close
                await new Promise((resolve) => setTimeout(resolve, 1000));

                // Try dropping again
                await client.query(`DROP DATABASE IF EXISTS "${dbName}"`);
                console.log(`✅ Dropped ${dbName} after force termination`);
                successCount++;
                continue;
              } catch (forceError) {
                console.error(
                  `❌ Force termination failed for ${dbName}:`,
                  forceError instanceof Error ? forceError.message : forceError
                );
              }
            } else {
              // Show connection details without force mode
              try {
                const connectionsResult = await client.query(
                  `
                  SELECT pid, usename, application_name, state, query_start
                  FROM pg_stat_activity
                  WHERE datname = $1
                  AND pid <> pg_backend_pid()
                `,
                  [dbName]
                );

                if (connectionsResult.rows.length > 0) {
                  console.log(
                    `   Found ${connectionsResult.rows.length} active connections:`
                  );
                  connectionsResult.rows.forEach((row) => {
                    console.log(
                      `   - PID ${row.pid}: ${row.usename} (${row.application_name || 'unknown'}) - ${row.state}`
                    );
                  });
                  console.log(
                    `   Use --force to terminate connections and drop database`
                  );
                }
              } catch (queryError) {
                console.log(`   Could not query connections for ${dbName}`);
              }
            }
          }
          throw dropError;
        }
      } catch (error) {
        console.error(
          `❌ Failed to drop ${dbName}:`,
          error instanceof Error ? error.message : error
        );
        failureCount++;
      }
    }

    console.log(`\n📊 Cleanup Summary:`);
    console.log(`  ✅ Successfully dropped: ${successCount}`);
    console.log(`  ❌ Failed to drop: ${failureCount}`);

    if (failureCount > 0) {
      console.log(
        `\n⚠️  Some databases couldn't be dropped. They may have active connections.`
      );
      if (!isForce) {
        console.log(
          `   Try running with --force to terminate connections: npm run cleanup:test-dbs:force`
        );
      } else {
        console.log(
          `   Even force termination failed. Consider contacting Render support.`
        );
      }
    }
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run the cleanup
cleanupTestDatabases().catch(console.error);
