#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to systematically fix AuthenticatedRequest usage in route files
 * This script applies the pattern changes needed to resolve TypeScript compilation errors
 */

import fs from 'fs';
import path from 'path';

// Files that need to be updated
const routeFiles = [
  'src/routes/api/v1/engagement.ts',
  'src/routes/api/v1/invitations.ts',
  'src/routes/api/v1/permissions.ts',
  'src/routes/api/v1/roles.ts',
  'src/routes/api/v1/tenants.ts',
  'src/routes/api/v1/vehicle-hierarchy.ts',
  'src/routes/api/v1/vehicle-hierarchy-v2.ts',
  'src/routes/api/v1/vehicle-hierarchy-v3.ts'
];

// Middleware files that also need to be updated
const middlewareFiles = [
  'src/middleware/auth.middleware.ts',
  'src/middleware/activity-tracking.middleware.ts',
  'src/middleware/permission.middleware.ts',
  'src/middleware/error-handler.ts'
];

const allFiles = [...routeFiles, ...middlewareFiles];

function fixRouteFile(filePath) {
  console.log(`Fixing ${filePath}...`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // 1. Fix imports - remove AuthenticatedRequest import and add getRequestUser
  if (content.includes("import { AuthenticatedRequest } from '@tech-notes/shared';")) {
    content = content.replace(
      /import { AuthenticatedRequest } from '@tech-notes\/shared';\s*\n/g,
      ''
    );
    modified = true;
  }

  // Add getRequestUser import if not present
  if (!content.includes("import { getRequestUser } from '../../../utils/request-types.js';")) {
    // Find the first import and add after it
    const importMatch = content.match(/^import .+;\s*$/m);
    if (importMatch) {
      const insertIndex = content.indexOf(importMatch[0]) + importMatch[0].length;
      content = content.slice(0, insertIndex) + 
                "\nimport { getRequestUser } from '../../../utils/request-types.js';" +
                content.slice(insertIndex);
      modified = true;
    }
  }

  // 2. Add Request and Response imports if missing
  content = content.replace(
    /import { ([^}]*) } from 'express';/,
    (match, imports) => {
      const importList = imports.split(',').map(s => s.trim());
      if (!importList.includes('Request')) importList.push('Request');
      if (!importList.includes('Response')) importList.push('Response');
      return `import { ${importList.join(', ')} } from 'express';`;
    }
  );
  modified = true;

  // 3. Fix route handler signatures and middleware function signatures
  content = content.replace(
    /async \(req: AuthenticatedRequest, res(?:, next: NextFunction)?\) => {/g,
    'async (req: Request, res: Response, next: NextFunction) => {'
  );

  content = content.replace(
    /async \(req: AuthenticatedRequest, res: Response(?:, next: NextFunction)?\) => {/g,
    'async (req: Request, res: Response, next: NextFunction) => {'
  );

  // Fix middleware function signatures
  content = content.replace(
    /\(req: AuthenticatedRequest, res: Response, next: NextFunction\)/g,
    '(req: Request, res: Response, next: NextFunction)'
  );

  // 4. Replace req.user with getRequestUser(req) - but be careful about scoping
  // This is a simple replacement - for complex cases, manual review may be needed
  content = content.replace(/req\.user!/g, 'getRequestUser(req)!');
  content = content.replace(/req\.user\?/g, 'getRequestUser(req)?');
  
  // Handle cases where req.user is used without ! or ?
  content = content.replace(/req\.user([^!?])/g, 'getRequestUser(req)$1');

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed ${filePath}`);
  } else {
    console.log(`ℹ️  No changes needed for ${filePath}`);
  }
}

// Process all files
allFiles.forEach(fixRouteFile);

console.log('\n🎉 Route file fixes completed!');
console.log('\nNext steps:');
console.log('1. Review the changes manually');
console.log('2. Run yarn run build to check for remaining errors');
console.log('3. Fix any remaining issues manually');
