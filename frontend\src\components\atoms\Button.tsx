import React from "react";
import { clsx } from "clsx";

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline" | "ghost" | "danger";
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  asChild?: boolean;
  children: React.ReactNode;
}

const sizeClasses = {
  xs: "px-2.5 py-1.5 text-xs font-medium",
  sm: "px-3 py-2 text-sm font-medium",
  md: "px-4 py-2 text-sm font-semibold",
  lg: "px-4 py-2 text-base font-semibold",
  xl: "px-6 py-3 text-base font-semibold",
};

const variantClasses = {
  primary:
    "bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-xs hover:from-primary-700 hover:to-primary-800 hover:shadow-button-hover focus:ring-primary-500 active:shadow-inner",
  secondary:
    "bg-gray-100 text-gray-800 shadow-xs hover:bg-gray-200 hover:shadow-button-hover focus:ring-gray-500 active:shadow-inner border border-gray-200",
  outline:
    "border-2 border-gray-300 bg-white text-gray-700 shadow-xs hover:bg-gray-50 hover:border-gray-400 hover:shadow-button-hover focus:ring-primary-500 active:shadow-inner",
  ghost:
    "text-gray-700 hover:bg-gray-100 hover:shadow-xs focus:ring-gray-500 active:bg-gray-200",
  danger:
    "bg-gradient-to-r from-error-600 to-error-700 text-white shadow-xs hover:from-error-700 hover:to-error-800 hover:shadow-button-hover focus:ring-error-500 active:shadow-inner border border-error-700",
};

export const Button: React.FC<ButtonProps> = ({
  variant = "primary",
  size = "md",
  asChild = false,
  className,
  children,
  ...props
}) => {
  const baseClasses = clsx(
    "inline-flex items-center justify-center rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-[1.02] active:scale-[0.98]",
    variantClasses[variant],
    sizeClasses[size],
    className,
  );

  if (asChild) {
    // When asChild is true, we expect children to be a single React element
    // We clone it and add our button classes to it
    const child = React.Children.only(children) as React.ReactElement<
      React.HTMLAttributes<HTMLElement>
    >;
    return React.cloneElement(child, {
      className: clsx(baseClasses, child.props?.className),
      ...props,
    });
  }

  return (
    <button className={baseClasses} {...props}>
      {children}
    </button>
  );
};
