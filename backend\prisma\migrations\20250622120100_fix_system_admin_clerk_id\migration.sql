-- Fix System Admin Assignment with Correct Clerk ID
-- This migration corrects the previous migration by using the correct Clerk ID
-- The previous migration used the database user ID instead of the Clerk ID

-- Assign System Admin role to the user with the CORRECT Clerk ID
INSERT INTO "user_roles" ("id", "userId", "roleId", "tenantId", "assignedBy", "createdAt", "updatedAt")
SELECT 
  'ur_prod_system_admin_' || u.id,  -- Generate unique ID
  u.id,                             -- User ID from database
  'role_system_admin',              -- System Admin role ID
  NULL,                             -- System roles have no tenant scope
  u.id,                             -- Self-assigned for initial setup
  NOW(),                            -- Created timestamp
  NOW()                             -- Updated timestamp
FROM "users" u
WHERE u."clerkId" = 'user_2yqMjrYXAekqQVzPeusn3YFLB8X'  -- CORRECT Clerk ID
  AND NOT EXISTS (
    -- Only insert if user doesn't already have System Admin role
    SELECT 1 FROM "user_roles" ur
    JOIN "roles" r ON ur."roleId" = r.id
    WHERE ur."userId" = u.id 
      AND r."type" = 'SYSTEM_ADMIN'
  );
