/**
 * Model Management V3 Modal Component
 * Wrapper component that follows the established pattern for v3 management modals
 */

import React from "react";
import { ModelManagementModal } from "./ModelManagementModal";

interface ModelManagementV3ModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ModelManagementV3Modal: React.FC<ModelManagementV3ModalProps> = ({
  isOpen,
  onClose,
}) => {
  return (
    <ModelManagementModal
      isOpen={isOpen}
      onClose={onClose}
    />
  );
};
