{"compilerOptions": {"target": "ES2020", "module": "ES2020", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": false, "noEmitOnError": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "isolatedModules": true, "baseUrl": "./src", "paths": {"@/*": ["*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}