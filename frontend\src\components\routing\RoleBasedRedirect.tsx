import React from "react";
import { Navigate } from "react-router-dom";
import { usePermissions } from "../../hooks/usePermissions";
import { LoadingSpinner } from "../atoms/LoadingSpinner";
import { useAuth } from "../../hooks/useAuth";

/**
 * Smart routing component that redirects users based on their roles:
 * - Company Tech (and not admin) → /tech (mobile-optimized experience)
 * - Company Admin or System Admin → /app (desktop-optimized experience)
 * - Multi-role users default to admin experience with option to switch
 */
export const RoleBasedRedirect: React.FC = () => {
  const { userProfile, isLoading } = useAuth();
  const { isCompanyTech, isAdmin } = usePermissions();

  // Show loading while auth is being determined
  if (isLoading || !userProfile) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Logic for role-based routing:
  // 1. If user is Company Tech but NOT an admin → Tech experience
  // 2. If user is any type of admin → Admin experience
  // 3. Multi-role users (tech + admin) → Admin experience (can switch later)

  if (isCompanyTech && !isAdmin) {
    // Pure tech user - redirect to mobile-optimized experience
    return <Navigate to="/tech" replace />;
  }

  if (isAdmin) {
    // Admin user (System Admin or Company Admin) - redirect to desktop experience
    return <Navigate to="/app" replace />;
  }

  // Fallback: if user has no recognized roles, default to admin experience
  // This handles edge cases and ensures users aren't stuck
  return <Navigate to="/app" replace />;
};
