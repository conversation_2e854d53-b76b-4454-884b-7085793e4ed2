import { Response, NextFunction, Request } from 'express';

import { UserEngagementService } from '../services/user-engagement.service.js';
import { Logger } from '../utils/logger.js';
import { getRequestUser } from '../utils/request-types.js';

export interface ActivityTrackingDependencies {
  userEngagementService: UserEngagementService;
  logger: Logger;
}

/**
 * In-memory cache for activity tracking throttling
 * In production, this should be moved to Redis for multi-instance deployments
 */
class ActivityThrottleCache {
  private cache = new Map<string, number>();
  private readonly THROTTLE_DURATION = 60 * 60 * 1000; // 1 hour in milliseconds

  /**
   * Check if user activity should be tracked (not throttled)
   */
  shouldTrackActivity(userId: string): boolean {
    const lastUpdate = this.cache.get(userId);
    const now = Date.now();

    if (!lastUpdate || now - lastUpdate > this.THROTTLE_DURATION) {
      this.cache.set(userId, now);
      return true;
    }

    return false;
  }

  /**
   * Clear cache entry for user (useful for testing)
   */
  clearUser(userId: string): void {
    this.cache.delete(userId);
  }

  /**
   * Get cache size (useful for monitoring)
   */
  getCacheSize(): number {
    return this.cache.size;
  }

  /**
   * Clear old entries to prevent memory leaks
   * Should be called periodically
   */
  cleanup(): void {
    const now = Date.now();
    const cutoff = now - this.THROTTLE_DURATION * 2; // Keep entries for 2x throttle duration

    for (const [userId, timestamp] of this.cache.entries()) {
      if (timestamp < cutoff) {
        this.cache.delete(userId);
      }
    }
  }
}

// Global instance for activity throttling
const activityCache = new ActivityThrottleCache();

/**
 * Factory function to create activity tracking middleware
 * Tracks user activity with throttling to prevent excessive database writes
 */
export function createActivityTrackingMiddleware(
  dependencies: ActivityTrackingDependencies
) {
  const { userEngagementService, logger } = dependencies;

  return async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      // Only track activity for authenticated users
      if (!getRequestUser(req)) {
        return next();
      }

      const { id: userId, tenantId } = getRequestUser(req);

      // Check if we should track activity (throttled)
      if (activityCache.shouldTrackActivity(userId)) {
        // Track activity asynchronously to not block the request
        userEngagementService
          .updateLastActivity(userId, tenantId, getRequestUser(req))
          .catch((error) => {
            logger.error('Failed to update user activity', {
              error: error instanceof Error ? error.message : 'Unknown error',
              userId,
              tenantId,
              path: req.path,
              method: req.method,
            });
          });

        logger.debug('User activity tracked', {
          userId,
          tenantId,
          path: req.path,
          method: req.method,
        });
      }

      next();
    } catch (error) {
      logger.error('Activity tracking middleware error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        path: req.path,
        method: req.method,
        userId: getRequestUser(req)?.id,
      });

      // Don't block the request if activity tracking fails
      next();
    }
  };
}

/**
 * Cleanup function to remove old cache entries
 * Should be called periodically (e.g., via cron job)
 */
export function cleanupActivityCache(): void {
  activityCache.cleanup();
}

/**
 * Get activity cache statistics (for monitoring)
 */
export function getActivityCacheStats(): { size: number } {
  return {
    size: activityCache.getCacheSize(),
  };
}

/**
 * Clear activity cache for a specific user (useful for testing)
 */
export function clearUserActivityCache(userId: string): void {
  activityCache.clearUser(userId);
}
