import { clerkClient } from '@clerk/clerk-sdk-node';
import { Logger } from 'winston';

import { ClerkInvitationResponse } from '../types/invitation.types.js';

export class ClerkInvitationService {
  constructor(private logger: Logger) {}

  /**
   * Create an invitation using Clerk's API
   */
  async createInvitation(
    email: string,
    redirectUrl?: string,
    publicMetadata?: Record<string, unknown>
  ): Promise<ClerkInvitationResponse> {
    this.logger.info('Creating Clerk invitation', {
      email,
      redirectUrl,
      hasMetadata: !!publicMetadata,
    });

    try {
      // Create invitation with redirect URL for proper flow
      const invitationParams: {
        emailAddress: string;
        publicMetadata: Record<string, unknown>;
        redirectUrl?: string;
      } = {
        emailAddress: email,
        publicMetadata: publicMetadata || {},
      };

      // Add redirect URL if provided - this is where <PERSON> will redirect after invitation acceptance
      if (redirectUrl) {
        invitationParams.redirectUrl = redirectUrl;
      }

      const invitation =
        await clerkClient.invitations.createInvitation(invitationParams);

      this.logger.info('Clerk invitation created successfully', {
        invitationId: invitation.id,
        email: invitation.emailAddress,
        status: invitation.status,
      });

      return {
        id: invitation.id,
        email_address: invitation.emailAddress,
        public_metadata: invitation.publicMetadata || {},
        created_at: invitation.createdAt,
        updated_at: invitation.updatedAt,
        status: invitation.status as 'pending' | 'accepted' | 'revoked',
        url: (invitation as { url?: string }).url || undefined,
      };
    } catch (error) {
      this.logger.error('Failed to create Clerk invitation', {
        error: error instanceof Error ? error.message : 'Unknown error',
        email,
        redirectUrl,
      });
      throw error;
    }
  }

  /**
   * Revoke an invitation using Clerk's API
   */
  async revokeInvitation(clerkInvitationId: string): Promise<void> {
    this.logger.info('Revoking Clerk invitation', {
      clerkInvitationId,
    });

    try {
      await clerkClient.invitations.revokeInvitation(clerkInvitationId);

      this.logger.info('Clerk invitation revoked successfully', {
        clerkInvitationId,
      });
    } catch (error) {
      this.logger.error('Failed to revoke Clerk invitation', {
        error: error instanceof Error ? error.message : 'Unknown error',
        clerkInvitationId,
      });
      throw error;
    }
  }

  /**
   * Get invitation details from Clerk
   */
  async getInvitation(
    clerkInvitationId: string
  ): Promise<ClerkInvitationResponse> {
    this.logger.info('Fetching Clerk invitation', {
      clerkInvitationId,
    });

    try {
      // Note: Clerk SDK may not have getInvitation method, using getInvitationList as fallback
      const invitations = await clerkClient.invitations.getInvitationList();
      const invitation = invitations.find(
        (inv) => inv.id === clerkInvitationId
      );

      if (!invitation) {
        throw new Error(`Invitation not found: ${clerkInvitationId}`);
      }

      return {
        id: invitation.id,
        email_address: invitation.emailAddress,
        public_metadata: invitation.publicMetadata || {},
        created_at: invitation.createdAt,
        updated_at: invitation.updatedAt,
        status: invitation.status as 'pending' | 'accepted' | 'revoked',
        url: (invitation as { url?: string }).url || undefined,
      };
    } catch (error) {
      this.logger.error('Failed to fetch Clerk invitation', {
        error: error instanceof Error ? error.message : 'Unknown error',
        clerkInvitationId,
      });
      throw error;
    }
  }

  /**
   * List all invitations from Clerk
   */
  async listInvitations(
    limit = 20,
    offset = 0
  ): Promise<{ data: ClerkInvitationResponse[]; totalCount: number }> {
    this.logger.info('Listing Clerk invitations', {
      limit,
      offset,
    });

    try {
      const response = await clerkClient.invitations.getInvitationList();

      const invitations = response.map(
        (invitation: {
          id: string;
          emailAddress: string;
          publicMetadata?: Record<string, unknown> | null;
          createdAt: number;
          updatedAt: number;
          status: string;
          url?: string;
        }) => ({
          id: invitation.id,
          email_address: invitation.emailAddress,
          public_metadata: invitation.publicMetadata ?? {},
          created_at: invitation.createdAt,
          updated_at: invitation.updatedAt,
          status: invitation.status as 'pending' | 'accepted' | 'revoked',
          url: invitation.url || undefined,
        })
      );

      return {
        data: invitations,
        totalCount: invitations.length,
      };
    } catch (error) {
      this.logger.error('Failed to list Clerk invitations', {
        error: error instanceof Error ? error.message : 'Unknown error',
        limit,
        offset,
      });
      throw error;
    }
  }

  /**
   * Generate metadata for invitation context
   */
  generateInvitationMetadata(
    tenantId: string,
    roleId: string,
    invitationId: string
  ): Record<string, string> {
    return {
      tenantId,
      roleId,
      invitationId,
      invitationType: 'tenant_invitation',
    };
  }

  /**
   * Generate redirect URL for invitation acceptance
   * This URL is where Clerk will redirect AFTER the user accepts the invitation
   * Clerk will append __clerk_ticket parameter automatically
   */
  generateRedirectUrl(baseUrl: string, invitationId: string): string {
    // Clerk will append __clerk_ticket parameter to this URL
    // We include our invitation ID for reference but the main flow uses the ticket
    return `${baseUrl}/accept-invitation?invitation=${invitationId}`;
  }
}
