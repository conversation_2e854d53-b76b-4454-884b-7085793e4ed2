/**
 * Sub-Brand Management V3 Modal Component
 * Wrapper component that follows the established pattern for v3 management modals
 */

import React from "react";
import { SubBrandManagementModal } from "./SubBrandManagementModal";

interface SubBrandManagementV3ModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const SubBrandManagementV3Modal: React.FC<SubBrandManagementV3ModalProps> = ({
  isOpen,
  onClose,
}) => {
  return (
    <SubBrandManagementModal
      isOpen={isOpen}
      onClose={onClose}
    />
  );
};
