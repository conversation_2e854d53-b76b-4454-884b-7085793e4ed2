#!/usr/bin/env ts-node

/**
 * Verification script to check if the production system admin assignment was successful
 * This script verifies that the specified user has been assigned the System Admin role
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifySystemAdminAssignment() {
  try {
    console.log('🔍 Verifying System Admin assignment...');

    const targetClerkId = 'user_2yqMjrYXAekqQVzPeusn3YFLB8X';

    // Find the user and their roles
    const user = await prisma.user.findUnique({
      where: { clerkId: targetClerkId },
      include: {
        userRoles: {
          include: {
            role: true,
            tenant: true,
          },
        },
      },
    });

    if (!user) {
      console.log('❌ User not found with Clerk ID:', targetClerkId);
      console.log(
        '   Make sure the user exists in the database before running the migration.'
      );
      return;
    }

    console.log('✅ User found:');
    console.log(`   Name: ${user.firstName} ${user.lastName}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Clerk ID: ${user.clerkId}`);
    console.log(`   Database ID: ${user.id}`);
    console.log(`   Tenant ID: ${user.tenantId}`);

    // Check for System Admin role
    const systemAdminRole = user.userRoles.find(
      (ur) => ur.role.type === 'SYSTEM_ADMIN'
    );

    if (systemAdminRole) {
      console.log('✅ System Admin role assigned successfully!');
      console.log(`   Role: ${systemAdminRole.role.name}`);
      console.log(`   Role Type: ${systemAdminRole.role.type}`);
      console.log(
        `   Tenant Scope: ${systemAdminRole.tenantId || 'Global (System-level)'}`
      );
      console.log(`   Assigned At: ${systemAdminRole.createdAt}`);
    } else {
      console.log('❌ System Admin role NOT found for this user');
      console.log('   Current roles:');
      user.userRoles.forEach((ur) => {
        console.log(
          `   - ${ur.role.name} (${ur.role.type}) - Tenant: ${ur.tenantId || 'Global'}`
        );
      });
    }

    // Show all system admins for reference
    console.log('\n📋 All System Admins in the system:');
    const allSystemAdmins = await prisma.userRole.findMany({
      where: {
        role: {
          type: 'SYSTEM_ADMIN',
        },
      },
      include: {
        user: true,
        role: true,
      },
    });

    if (allSystemAdmins.length === 0) {
      console.log('   No System Admins found in the system');
    } else {
      allSystemAdmins.forEach((sa) => {
        console.log(
          `   - ${sa.user.email} (${sa.user.firstName} ${sa.user.lastName})`
        );
        console.log(`     Clerk ID: ${sa.user.clerkId}`);
        console.log(`     Assigned: ${sa.createdAt}`);
      });
    }
  } catch (error) {
    console.error('❌ Error during verification:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the verification
verifySystemAdminAssignment().catch(console.error);
