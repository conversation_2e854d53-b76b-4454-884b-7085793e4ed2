import { PrismaClient } from '@prisma/client';

import { NotFoundError, ForbiddenError } from '../types/error.types.js';
import { Logger } from '../utils/logger.js';

import { PrismaService } from './prisma.service.js';

export abstract class BaseTenantService {
  protected prismaService: PrismaService;
  protected logger: Logger;

  constructor(prismaService: PrismaService, logger: Logger) {
    this.prismaService = prismaService;
    this.logger = logger;
  }

  protected get prisma(): PrismaClient {
    return this.prismaService.prisma;
  }

  /**
   * Validates that a tenant exists and optionally that a user has access to it
   */
  protected async validateTenantAccess(
    tenantId: string,
    userId?: string
  ): Promise<void> {
    const tenant = await this.prisma.tenant.findUnique({
      where: { id: tenantId },
      select: { id: true },
    });

    if (!tenant) {
      this.logger.warn('Tenant access validation failed - tenant not found', {
        tenantId,
      });
      throw new NotFoundError(`Tenant ${tenantId} not found`);
    }

    if (userId) {
      const user = await this.prisma.user.findFirst({
        where: { clerkId: userId, tenantId },
        select: { id: true },
      });

      if (!user) {
        this.logger.warn(
          'Tenant access validation failed - user access denied',
          {
            userId,
            tenantId,
          }
        );
        throw new ForbiddenError(
          `User ${userId} does not have access to tenant ${tenantId}`
        );
      }
    }

    this.logger.debug('Tenant access validation successful', {
      tenantId,
      userId,
    });
  }

  /**
   * Execute operation with automatic tenant scope validation
   * @param tenantId - The tenant ID to validate access for
   * @param operation - The operation to execute
   * @param userId - Optional user ID for additional validation
   * @param bypassTenantScope - If true, skips tenant validation (for System Admins)
   */
  protected async withTenantScope<T>(
    tenantId: string,
    operation: () => Promise<T>,
    userId?: string,
    bypassTenantScope?: boolean
  ): Promise<T> {
    if (bypassTenantScope) {
      this.logger.info('Tenant scope validation bypassed for System Admin', {
        tenantId,
        userId,
        service: this.constructor.name,
      });
    } else {
      await this.validateTenantAccess(tenantId, userId);
    }
    return await operation();
  }

  /**
   * Execute operation with tenant scope validation that can be bypassed for System Admins
   * This is a convenience method that automatically determines bypass based on BackendAuthContext
   */
  protected async withTenantScopeForUser<T>(
    tenantId: string,
    operation: () => Promise<T>,
    BackendAuthContext?: { canBypassTenantScope?: boolean; clerkId?: string }
  ): Promise<T> {
    const bypassTenantScope = BackendAuthContext?.canBypassTenantScope || false;
    const userId = BackendAuthContext?.clerkId;

    return await this.withTenantScope(
      tenantId,
      operation,
      userId,
      bypassTenantScope
    );
  }

  /**
   * Built-in transaction support
   */
  protected async transaction<T>(
    fn: (
      prisma: Omit<
        PrismaClient,
        | '$connect'
        | '$disconnect'
        | '$on'
        | '$transaction'
        | '$use'
        | '$extends'
      >
    ) => Promise<T>
  ): Promise<T> {
    return await this.prisma.$transaction(fn);
  }

  /**
   * Log service operation with context
   */
  protected logOperation(
    operation: string,
    context: Record<string, unknown> = {}
  ): void {
    this.logger.info(`${this.constructor.name}: ${operation}`, context);
  }

  /**
   * Log service error with context
   */
  protected logError(
    operation: string,
    error: Error,
    context: Record<string, unknown> = {}
  ): void {
    this.logger.error(`${this.constructor.name}: ${operation} failed`, {
      error: error.message,
      stack: error.stack,
      ...context,
    });
  }
}
