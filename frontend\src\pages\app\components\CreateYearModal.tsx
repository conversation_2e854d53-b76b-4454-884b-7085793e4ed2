import React, { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { Modal, Button, Input, FormField, Alert } from "../../../components";
import { useTypedApi } from "../../../services/api-client";
import { Calendar } from "lucide-react";

interface CreateYearModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const CreateYearModal: React.FC<CreateYearModalProps> = ({
  isOpen,
  onClose,
}) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [year, setYear] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});

  const createYearMutation = useMutation({
    mutationFn: (yearData: { year: number }) =>
      api.vehicleHierarchy.createYear(yearData),
    onSuccess: () => {
      toast.success("Year created successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-years"] });
      handleClose();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create year");
    },
  });

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!year.trim()) {
      newErrors.year = "Year is required";
    } else {
      const yearNum = parseInt(year);
      if (isNaN(yearNum)) {
        newErrors.year = "Year must be a valid number";
      } else if (yearNum < 1900 || yearNum > 2050) {
        newErrors.year = "Year must be between 1900 and 2050";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const yearNum = parseInt(year);
    createYearMutation.mutate({ year: yearNum });
  };

  const handleClose = () => {
    setYear("");
    setErrors({});
    onClose();
  };

  const currentYear = new Date().getFullYear();
  const suggestedYears = [
    currentYear + 1,
    currentYear,
    currentYear - 1,
    currentYear - 2,
    currentYear - 3,
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Create New Year"
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex items-center space-x-3 text-gray-600 mb-4">
          <Calendar className="h-5 w-5" />
          <span>Add a new year to the vehicle hierarchy</span>
        </div>

        <FormField label="Year" error={errors.year} required>
          <Input
            type="number"
            placeholder="e.g., 2024"
            value={year}
            onChange={(e) => setYear(e.target.value)}
            min="1900"
            max="2050"
            error={errors.year}
          />
        </FormField>

        {/* Quick Select Buttons */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Quick Select
          </label>
          <div className="flex flex-wrap gap-2">
            {suggestedYears.map((suggestedYear) => (
              <Button
                key={suggestedYear}
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setYear(suggestedYear.toString())}
                className="text-xs"
              >
                {suggestedYear}
              </Button>
            ))}
          </div>
        </div>

        {createYearMutation.error && (
          <Alert variant="error">
            {createYearMutation.error.message || "Failed to create year"}
          </Alert>
        )}

        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={createYearMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={createYearMutation.isPending}
            className="flex items-center space-x-2"
          >
            {createYearMutation.isPending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Creating...</span>
              </>
            ) : (
              <>
                <Calendar className="h-4 w-4" />
                <span>Create Year</span>
              </>
            )}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
