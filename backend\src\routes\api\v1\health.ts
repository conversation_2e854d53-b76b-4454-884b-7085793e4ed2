import { formatDateTime } from '@tech-notes/shared';
import { Router } from 'express';
import { Logger } from 'winston';

import { MiddlewareFactory } from '../../../middleware/middleware-factory.js';
import { PrismaService } from '../../../services/prisma.service.js';
import { env } from '../../../utils/env-validation.js';

interface ServiceDependencies {
  prismaService: PrismaService;
  middlewareFactory: MiddlewareFactory;
  logger: Logger;
}

export function createHealthRouter(dependencies: ServiceDependencies): Router {
  const { prismaService, middlewareFactory, logger } = dependencies;
  const router = Router();

  router.get(
    '/',
    middlewareFactory.createActivityTracking(),
    async (req, res) => {
      try {
        const dbHealthy = await prismaService.healthCheck();

        // Always return 200 OK for health check - let the response indicate database status
        res.json({
          data: {
            status: 'ok',
            environment: env.NODE_ENV,
            database: dbHealthy ? 'connected' : 'disconnected',
            version: '1.0.0', // Added to trigger deployment
          },
          timestamp: formatDateTime(new Date()),
        });
      } catch (error) {
        logger.error('Health check failed', { error });

        // Return 200 OK even if database check fails - service is still running
        res.json({
          data: {
            status: 'ok',
            environment: env.NODE_ENV,
            database: 'error',
          },
          timestamp: formatDateTime(new Date()),
        });
      }
    }
  );

  return router;
}
