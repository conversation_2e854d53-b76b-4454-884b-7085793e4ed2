import React from "react";
import { clsx } from "clsx";
import { Badge } from "../atoms/Badge";

export interface MarketingHeroProps
  extends React.HTMLAttributes<HTMLDivElement> {
  /** Hero title */
  title: string;
  /** Hero description */
  description: string;
  /** Hero layout variant for different marketing contexts */
  layout?: "centered" | "split" | "minimal" | "feature-rich";
  /** Optional badge/announcement above title */
  announcement?: {
    text: string;
    variant?: "primary" | "success" | "warning";
    icon?: React.ReactNode;
  };
  /** Optional feature highlights below description */
  features?: string[];
  /** Optional social proof elements */
  socialProof?: {
    text: string;
    logos?: Array<{
      src: string;
      alt: string;
      width?: number;
      height?: number;
    }>;
    stats?: Array<{
      value: string;
      label: string;
    }>;
  };
  /** Optional hero image/visual */
  heroVisual?: {
    src: string;
    alt: string;
    position?: "right" | "left" | "background";
  };
  /** Custom content area for additional elements */
  customContent?: React.ReactNode;
  /** Primary call-to-action button */
  primaryCta?: {
    text: string;
    href?: string;
    onClick?: () => void;
    icon?: React.ReactNode;
    variant?: "primary" | "secondary" | "outline" | "ghost";
  };
  /** Secondary call-to-action button */
  secondaryCta?: {
    text: string;
    href?: string;
    onClick?: () => void;
    icon?: React.ReactNode;
    variant?: "primary" | "secondary" | "outline" | "ghost";
  };
}

const layoutClasses = {
  centered: "text-center max-w-4xl mx-auto",
  split: "grid lg:grid-cols-2 gap-12 lg:gap-16 items-center",
  minimal: "text-center max-w-3xl mx-auto",
  "feature-rich": "text-center max-w-5xl mx-auto",
};

export const MarketingHero: React.FC<MarketingHeroProps> = ({
  layout = "centered",
  announcement,
  features,
  socialProof,
  heroVisual,
  customContent,
  title,
  description,
  primaryCta,
  secondaryCta,
  className,
  ...props
}) => {
  const renderAnnouncement = () => {
    if (!announcement) return null;

    return (
      <div className="mb-8 flex justify-center">
        <Badge
          variant={announcement.variant || "primary"}
          size="md"
          icon={announcement.icon}
          className="px-4 py-2 text-sm font-medium"
        >
          {announcement.text}
        </Badge>
      </div>
    );
  };

  const renderFeatures = () => {
    if (!features || features.length === 0) return null;

    return (
      <div className="mt-8">
        <ul className="flex flex-wrap justify-center gap-x-8 gap-y-2 text-sm text-gray-600">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center">
              <svg
                className="mr-2 h-4 w-4 text-primary-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
              {feature}
            </li>
          ))}
        </ul>
      </div>
    );
  };

  const renderSocialProof = () => {
    if (!socialProof) return null;

    return (
      <div className="mt-12 pt-8 border-t border-gray-200">
        <p className="text-sm text-gray-600 mb-6">{socialProof.text}</p>

        {socialProof.logos && socialProof.logos.length > 0 && (
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
            {socialProof.logos.map((logo, index) => (
              <img
                key={index}
                src={logo.src}
                alt={logo.alt}
                width={logo.width || 120}
                height={logo.height || 40}
                className="h-8 w-auto grayscale hover:grayscale-0 transition-all duration-300"
              />
            ))}
          </div>
        )}

        {socialProof.stats && socialProof.stats.length > 0 && (
          <div className="flex flex-wrap justify-center gap-8 mt-6">
            {socialProof.stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {stat.value}
                </div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  const renderHeroVisual = () => {
    if (!heroVisual) return null;

    return (
      <div className="relative">
        <img
          src={heroVisual.src}
          alt={heroVisual.alt}
          className="w-full h-auto rounded-2xl shadow-2xl"
        />
        <div className="absolute inset-0 rounded-2xl ring-1 ring-inset ring-gray-900/10" />
      </div>
    );
  };

  const renderContent = () => {
    const content = (
      <>
        {renderAnnouncement()}

        <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
          {title}
        </h1>

        <p className="mt-6 text-xl leading-8 text-gray-600">{description}</p>

        {renderFeatures()}

        <div className="mt-10 flex items-center justify-center gap-x-6">
          {primaryCta && (
            <div className="group">
              {primaryCta.href ? (
                <a
                  href={primaryCta.href}
                  className="inline-flex items-center justify-center px-6 py-3 text-base font-semibold text-white bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg shadow-xs hover:from-primary-700 hover:to-primary-800 hover:shadow-button-hover focus:ring-primary-500 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
                >
                  {primaryCta.text}
                  {primaryCta.icon && (
                    <span className="ml-2 transition-transform duration-200 group-hover:translate-x-1">
                      {primaryCta.icon}
                    </span>
                  )}
                </a>
              ) : (
                <button
                  onClick={primaryCta.onClick}
                  className="inline-flex items-center justify-center px-6 py-3 text-base font-semibold text-white bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg shadow-xs hover:from-primary-700 hover:to-primary-800 hover:shadow-button-hover focus:ring-primary-500 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
                >
                  {primaryCta.text}
                  {primaryCta.icon && (
                    <span className="ml-2 transition-transform duration-200 group-hover:translate-x-1">
                      {primaryCta.icon}
                    </span>
                  )}
                </button>
              )}
            </div>
          )}

          {secondaryCta && (
            <div>
              {secondaryCta.href ? (
                <a
                  href={secondaryCta.href}
                  className={clsx(
                    "inline-flex items-center justify-center px-6 py-3 text-base font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2",
                    secondaryCta.variant === "ghost"
                      ? "text-gray-700 hover:bg-gray-100 hover:shadow-xs focus:ring-gray-500"
                      : "border-2 border-gray-300 bg-white text-gray-700 shadow-xs hover:bg-gray-50 hover:border-gray-400 hover:shadow-button-hover focus:ring-primary-500",
                  )}
                >
                  {secondaryCta.text}
                </a>
              ) : (
                <button
                  onClick={secondaryCta.onClick}
                  className={clsx(
                    "inline-flex items-center justify-center px-6 py-3 text-base font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2",
                    secondaryCta.variant === "ghost"
                      ? "text-gray-700 hover:bg-gray-100 hover:shadow-xs focus:ring-gray-500"
                      : "border-2 border-gray-300 bg-white text-gray-700 shadow-xs hover:bg-gray-50 hover:border-gray-400 hover:shadow-button-hover focus:ring-primary-500",
                  )}
                >
                  {secondaryCta.text}
                </button>
              )}
            </div>
          )}
        </div>

        {customContent}
        {renderSocialProof()}
      </>
    );

    if (layout === "split") {
      return (
        <>
          <div className="lg:pr-8">{content}</div>
          {heroVisual && (
            <div className="mt-12 lg:mt-0">{renderHeroVisual()}</div>
          )}
        </>
      );
    }

    return content;
  };

  return (
    <div
      className={clsx(
        "bg-gradient-to-b from-gray-50 to-white py-20 sm:py-32",
        className,
      )}
      {...props}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className={layoutClasses[layout]}>{renderContent()}</div>
      </div>
    </div>
  );
};
