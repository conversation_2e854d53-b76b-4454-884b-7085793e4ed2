import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "../../__tests__/helpers/test-utils";
import HealthCard from "./HealthCard";
import { createMockProps } from "../../__tests__/helpers/mock-props.helper";

describe("HealthCard", () => {
  describe("Rendering", () => {
    it("should render health card with ok status", () => {
      // Arrange
      const props = createMockProps.healthCard({ status: "ok" });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      expect(screen.getByText("System Health")).toBeInTheDocument();
      expect(screen.getByText("Healthy")).toBeInTheDocument();
      expect(screen.getByRole("status")).toHaveAttribute(
        "aria-label",
        "Status: ok",
      );
    });

    it("should render health card with error status", () => {
      // Arrange
      const props = createMockProps.healthCard({
        status: "error",
        database: "disconnected",
      });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      expect(screen.getByText("Error")).toBeInTheDocument();
      expect(screen.getByText("disconnected")).toBeInTheDocument();
      expect(screen.getByRole("status")).toHaveAttribute(
        "aria-label",
        "Status: error",
      );
    });

    it("should render health card with loading status", () => {
      // Arrange
      const props = createMockProps.healthCard({ status: "loading" });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      expect(screen.getByText("Loading...")).toBeInTheDocument();
      expect(screen.getByRole("status")).toHaveAttribute(
        "aria-label",
        "Status: loading",
      );
    });
  });

  describe("Data Display", () => {
    it("should display environment information when provided", () => {
      // Arrange
      const props = createMockProps.healthCard({
        environment: "production",
      });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      expect(screen.getByText("Environment:")).toBeInTheDocument();
      expect(screen.getByText("production")).toBeInTheDocument();
    });

    it("should display database status when provided", () => {
      // Arrange
      const props = createMockProps.healthCard({
        database: "connected",
      });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      expect(screen.getByText("Database:")).toBeInTheDocument();
      expect(screen.getByText("connected")).toBeInTheDocument();
    });

    it("should not display environment section when not provided", () => {
      // Arrange
      const props = createMockProps.healthCard({
        environment: undefined,
      });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      expect(screen.queryByText("Environment:")).not.toBeInTheDocument();
    });

    it("should not display database section when not provided", () => {
      // Arrange
      const props = createMockProps.healthCard({
        database: undefined,
      });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      expect(screen.queryByText("Database:")).not.toBeInTheDocument();
    });
  });

  describe("Timestamp Formatting", () => {
    it("should format and display timestamp when provided", () => {
      // Arrange
      const timestamp = "2024-01-15T10:30:00.000Z";
      const props = createMockProps.healthCard({ timestamp });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      expect(screen.getByText("Last Updated:")).toBeInTheDocument();
      // The exact formatted date depends on locale, so just verify it's not "Unknown"
      const timestampElement =
        screen.getByText("Last Updated:").nextElementSibling;
      expect(timestampElement?.textContent).not.toBe("Unknown");
      expect(timestampElement?.textContent).toBeTruthy();
    });

    it('should display "Unknown" when timestamp is not provided', () => {
      // Arrange
      const props = createMockProps.healthCard({
        timestamp: undefined,
      });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      expect(screen.getByText("Last Updated:")).toBeInTheDocument();
      expect(screen.getByText("Unknown")).toBeInTheDocument();
    });

    it("should handle invalid timestamp gracefully", () => {
      // Arrange
      const props = createMockProps.healthCard({
        timestamp: "invalid-date",
      });

      // Act
      render(<HealthCard {...props} />);

      // Assert - Should not crash and display something
      expect(screen.getByText("Last Updated:")).toBeInTheDocument();
      const timestampElement =
        screen.getByText("Last Updated:").nextElementSibling;
      expect(timestampElement).toBeInTheDocument();
    });
  });

  describe("Status Styling", () => {
    it("should apply correct styling for ok status", () => {
      // Arrange
      const props = createMockProps.healthCard({ status: "ok" });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      const statusText = screen.getByText("Healthy");
      expect(statusText).toHaveClass("text-success-800");

      const statusIndicator = screen.getByRole("status");
      expect(statusIndicator).toHaveClass("bg-green-500");
    });

    it("should apply correct styling for error status", () => {
      // Arrange
      const props = createMockProps.healthCard({ status: "error" });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      const statusText = screen.getByText("Error");
      expect(statusText).toHaveClass("text-error-800");

      const statusIndicator = screen.getByRole("status");
      expect(statusIndicator).toHaveClass("bg-red-500");
    });

    it("should apply correct styling for loading status", () => {
      // Arrange
      const props = createMockProps.healthCard({ status: "loading" });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      const statusText = screen.getByText("Loading...");
      expect(statusText).toHaveClass("text-warning-800");

      const statusIndicator = screen.getByRole("status");
      expect(statusIndicator).toHaveClass("bg-yellow-500", "animate-pulse");
    });

    it("should apply correct database status styling", () => {
      // Arrange - Test connected database
      const connectedProps = createMockProps.healthCard({
        database: "connected",
      });

      // Act
      render(<HealthCard {...connectedProps} />);

      // Assert
      const connectedText = screen.getByText("connected");
      expect(connectedText).toHaveClass("text-success-800");
    });

    it("should apply error styling for disconnected database", () => {
      // Arrange
      const props = createMockProps.healthCard({
        database: "disconnected",
      });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      const disconnectedText = screen.getByText("disconnected");
      expect(disconnectedText).toHaveClass("text-error-800");
    });
  });

  describe("Refresh Functionality", () => {
    it("should render refresh button when onRefresh is provided", () => {
      // Arrange
      const mockRefresh = vi.fn();
      const props = createMockProps.healthCard({
        onRefresh: mockRefresh,
      });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      const refreshButton = screen.getByRole("button", { name: /refresh/i });
      expect(refreshButton).toBeInTheDocument();
    });

    it("should not render refresh button when onRefresh is not provided", () => {
      // Arrange
      const props = createMockProps.healthCard({
        onRefresh: undefined,
      });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      expect(
        screen.queryByRole("button", { name: /refresh/i }),
      ).not.toBeInTheDocument();
    });

    it("should call onRefresh when refresh button is clicked", () => {
      // Arrange
      const mockRefresh = vi.fn();
      const props = createMockProps.healthCard({
        onRefresh: mockRefresh,
      });

      // Act
      render(<HealthCard {...props} />);
      const refreshButton = screen.getByRole("button", { name: /refresh/i });
      fireEvent.click(refreshButton);

      // Assert
      expect(mockRefresh).toHaveBeenCalledTimes(1);
    });

    it("should handle multiple refresh button clicks", () => {
      // Arrange
      const mockRefresh = vi.fn();
      const props = createMockProps.healthCard({
        onRefresh: mockRefresh,
      });

      // Act
      render(<HealthCard {...props} />);
      const refreshButton = screen.getByRole("button", { name: /refresh/i });

      fireEvent.click(refreshButton);
      fireEvent.click(refreshButton);
      fireEvent.click(refreshButton);

      // Assert
      expect(mockRefresh).toHaveBeenCalledTimes(3);
    });
  });

  describe("Accessibility", () => {
    it("should have proper ARIA attributes for status indicator", () => {
      // Arrange
      const props = createMockProps.healthCard({ status: "ok" });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      const statusIndicator = screen.getByRole("status");
      expect(statusIndicator).toHaveAttribute("role", "status");
      expect(statusIndicator).toHaveAttribute("aria-label", "Status: ok");
    });

    it("should have accessible refresh button", () => {
      // Arrange
      const props = createMockProps.healthCard({
        onRefresh: vi.fn(),
      });

      // Act
      render(<HealthCard {...props} />);

      // Assert
      const refreshButton = screen.getByRole("button", { name: /refresh/i });
      expect(refreshButton).toHaveClass("focus:outline-none", "focus:ring-2");
    });

    it("should be keyboard accessible", () => {
      // Arrange
      const mockRefresh = vi.fn();
      const props = createMockProps.healthCard({
        onRefresh: mockRefresh,
      });

      // Act
      render(<HealthCard {...props} />);
      const refreshButton = screen.getByRole("button", { name: /refresh/i });

      // Simulate keyboard interaction
      refreshButton.focus();
      fireEvent.keyDown(refreshButton, { key: "Enter" });

      // Assert - Button should be focusable
      expect(refreshButton).toHaveFocus();
    });
  });

  describe("Component Structure", () => {
    it("should have proper semantic structure", () => {
      // Arrange
      const props = createMockProps.healthCard();

      // Act
      render(<HealthCard {...props} />);

      // Assert
      expect(
        screen.getByRole("heading", { name: "System Health" }),
      ).toBeInTheDocument();
      expect(screen.getByRole("status")).toBeInTheDocument();
    });

    it("should apply correct CSS classes for styling", () => {
      // Arrange
      const props = createMockProps.healthCard();

      // Act
      const { container } = render(<HealthCard {...props} />);

      // Assert
      const card = container.firstChild as HTMLElement;
      expect(card).toHaveClass(
        "bg-white",
        "rounded-2xl",
        "shadow-sm",
        "border",
        "border-gray-200",
      );
    });
  });
});
