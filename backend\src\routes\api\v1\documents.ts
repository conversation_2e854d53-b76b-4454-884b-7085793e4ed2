import { Router, Request, Response, NextFunction } from 'express';
import { Logger } from 'winston';

import {
  MiddlewareFactory,
  CommonPermissions,
} from '../../../middleware/middleware-factory.js';
import { DocumentService } from '../../../services/document.service.js';
import { getRequestUser } from '../../../utils/request-types.js';

interface ServiceDependencies {
  documentService: DocumentService;
  middlewareFactory: MiddlewareFactory;
  logger: Logger;
}

export function createDocumentsRouter(
  dependencies: ServiceDependencies
): Router {
  const { documentService, middlewareFactory, logger } = dependencies;
  const router = Router();

  /**
   * POST /api/v1/documents/upload-url
   * Generate presigned upload URL for document (requires document write permission)
   */
  router.post(
    '/upload-url',
    ...middlewareFactory.createAuthWithPermission(
      CommonPermissions.DOCUMENT_WRITE
    ),
    async (req: Request, res: Response, next: NextFunction) => {
      const user = getRequestUser(req);
      try {
        const {
          fileName,
          originalName,
          fileSize,
          mimeType,
          title,
          description,
        } = req.body;
        const { tenantId, id: createdBy } = user!;

        // Validate required fields
        if (!fileName || !originalName || !fileSize || !mimeType) {
          return res.status(400).json({
            error: 'Bad Request',
            message:
              'fileName, originalName, fileSize, and mimeType are required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        const documentWithUploadUrl = await documentService.createDocument(
          {
            fileName,
            originalName,
            fileSize,
            mimeType,
            title,
            description,
            tenantId,
            createdBy,
          },
          user!
        );

        res.status(201).json({
          data: documentWithUploadUrl,
          meta: {
            tenantId,
            expiresIn: documentWithUploadUrl.expiresIn,
          },
        });
      } catch (error) {
        logger.error('Failed to generate upload URL', {
          error,
          tenantId: user?.tenantId,
          userId: user?.id,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/documents
   * List documents for tenant with pagination (requires document read permission)
   */
  router.get(
    '/',
    ...middlewareFactory.createAuthWithPermission(
      CommonPermissions.DOCUMENT_READ
    ),
    async (req: Request, res: Response, next: NextFunction) => {
      const user = getRequestUser(req);
      try {
        const { tenantId } = user!;
        const {
          page = '1',
          limit = '20',
          search,
          sortBy = 'createdAt',
          sortOrder = 'desc',
        } = req.query;

        const options = {
          page: parseInt(page as string, 10),
          limit: parseInt(limit as string, 10),
          search: search as string,
          sortBy: sortBy as 'createdAt' | 'fileName' | 'fileSize',
          sortOrder: sortOrder as 'asc' | 'desc',
        };

        const result = await documentService.getDocuments(
          tenantId,
          options,
          user!
        );

        res.json({
          data: result.documents,
          meta: {
            pagination: result.pagination,
            tenantId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch documents', {
          error,
          tenantId: user?.tenantId,
          userId: user?.id,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/documents/:id/url
   * Generate presigned download URL for document (requires document read permission)
   */
  router.get(
    '/:id/url',
    ...middlewareFactory.createAuthWithPermission(
      CommonPermissions.DOCUMENT_READ
    ),
    async (req: Request, res: Response, next: NextFunction) => {
      const user = getRequestUser(req);
      try {
        const { id: documentId } = req.params;
        const { tenantId } = user!;

        if (!documentId || documentId.trim() === '') {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'Document ID is required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        // Generate presigned download URL (this will validate document exists and belongs to tenant)
        const downloadUrl = await documentService.generateDownloadUrl(
          documentId,
          tenantId,
          user!
        );

        res.json({
          data: {
            downloadUrl: downloadUrl.downloadUrl,
            expiresIn: downloadUrl.expiresIn,
          },
          meta: {
            tenantId,
            documentId,
          },
        });
      } catch (error) {
        logger.error('Failed to generate download URL', {
          error,
          tenantId: user?.tenantId,
          userId: user?.id,
          documentId: req.params.id,
        });
        next(error);
      }
    }
  );

  /**
   * DELETE /api/v1/documents/:id
   * Soft delete document (requires document delete permission)
   */
  router.delete(
    '/:id',
    ...middlewareFactory.createAuthWithPermission(
      CommonPermissions.DOCUMENT_DELETE
    ),
    async (req: Request, res: Response, next: NextFunction) => {
      const user = getRequestUser(req);
      try {
        const { id: documentId } = req.params;
        const { tenantId } = user!;

        if (!documentId || documentId.trim() === '') {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'Document ID is required',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          });
        }

        const deletedDocument = await documentService.deleteDocument(
          documentId,
          tenantId,
          user!
        );

        res.json({
          data: deletedDocument,
          meta: {
            tenantId,
            documentId,
            deletedAt: deletedDocument.deletedAt,
          },
        });
      } catch (error) {
        logger.error('Failed to delete document', {
          error,
          tenantId: user?.tenantId,
          userId: user?.id,
          documentId: req.params.id,
        });
        next(error);
      }
    }
  );

  return router;
}
