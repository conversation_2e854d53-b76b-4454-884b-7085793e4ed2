import React from "react";
import { usePermissions, type PermissionCheck } from "./usePermissions";
import type { RoleType } from "../types/auth.types";

/**
 * Hook for imperative permission checking with loading states
 */
export function usePermissionCheck(
  permission?: PermissionCheck,
  role?: RoleType,
) {
  const { hasPermission, hasRole } = usePermissions();

  const hasAccess = React.useMemo(() => {
    if (permission) {
      return hasPermission(permission.resource, permission.action);
    }
    if (role) {
      return hasRole(role);
    }
    return false;
  }, [permission, role, hasPermission, hasRole]);

  return { hasAccess };
}

// Note: withPermissionGuard HOC moved to PermissionGuard.tsx to avoid circular imports
