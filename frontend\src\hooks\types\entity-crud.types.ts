/**
 * Base types and interfaces for Entity CRUD operations
 * Provides type-safe foundation for both simple and complex entity management
 */

import type { ApiResponse } from "../../services/api-client";

// Base entity interface that all manageable entities must implement
export interface BaseEntity {
  id: string;
  name: string;
  isActive: boolean;
  displayOrder: number;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

// Simple entity type for entities without parent relationships
export type SimpleEntity = BaseEntity;

// Complex entity interface for entities with parent relationships
export interface ComplexEntity extends BaseEntity {
  // Complex entities may have additional relationship fields
  // This will be extended by specific entity types
  parentId?: string; // Optional parent relationship
}

// Base API methods interface that all entity APIs must implement
export interface BaseEntityApiMethods<T extends BaseEntity> {
  getAll: () => Promise<{ data: T[]; meta: { count: number; tenantId: string } }>;
  getById: (id: string) => Promise<ApiResponse<T>>;
  create: (data: { name: string }) => Promise<ApiResponse<T>>;
  update: (id: string, data: { name?: string; isActive?: boolean }) => Promise<ApiResponse<T>>;
  delete: (id: string) => Promise<ApiResponse<{ success: boolean }>>;
}

// Validation configuration interface
export interface ValidationConfig {
  maxLength: number;
  requiredMessage: string;
  maxLengthMessage: string;
}

// Base CRUD configuration interface
export interface BaseCRUDConfig<T extends BaseEntity> {
  entityName: string;
  queryKey: string;
  apiMethods: BaseEntityApiMethods<T>;
  validation: ValidationConfig;
}

// UI configuration for simple entities
export interface SimpleEntityUIConfig {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  colorScheme: 'blue' | 'green' | 'purple' | 'orange';
  placeholder: string;
  emptyStateMessage: string;
}

// Complete configuration for simple entities
export interface SimpleEntityConfig<T extends SimpleEntity> extends BaseCRUDConfig<T> {
  ui: SimpleEntityUIConfig;
}

// CRUD operation states
export type CRUDOperation = 'create' | 'update' | 'delete' | 'fetch';

// Common CRUD state interface
export interface CRUDState<T extends BaseEntity> {
  // Data state
  entities: T[];
  isLoading: boolean;
  error: string | null;
  
  // Form state
  newEntityName: string;
  editingEntity: T | null;
  editEntityName: string;
  editEntityActive: boolean;
  deleteConfirmEntity: T | null;
  
  // Operation state
  isPending: boolean;
  currentOperation: CRUDOperation | null;
}

// CRUD actions interface
export interface CRUDActions<T extends BaseEntity> {
  // State setters
  setNewEntityName: (name: string) => void;
  setEditingEntity: (entity: T | null) => void;
  setEditEntityName: (name: string) => void;
  setEditEntityActive: (active: boolean) => void;
  setDeleteConfirmEntity: (entity: T | null) => void;
  
  // Operation handlers
  handleCreate: () => void;
  handleEdit: (entity: T) => void;
  handleUpdate: () => void;
  handleDelete: (entity: T) => void;
  confirmDelete: () => void;
  handleClose: () => void;
  
  // Validation
  validateName: (name: string) => string | null;
}

// Complete CRUD hook return type
export interface CRUDHookReturn<T extends BaseEntity> extends CRUDState<T>, CRUDActions<T> {
  // Sorted entities for display
  sortedEntities: T[];
}

// Error handling types
export interface CRUDError {
  operation: CRUDOperation;
  message: string;
  originalError?: Error;
}

// Success handling types
export interface CRUDSuccess {
  operation: CRUDOperation;
  message: string;
  entity?: BaseEntity;
}
