import React, { useState, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
  <PERSON><PERSON>,
  <PERSON>ert,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FormField,
  CompanyAdminOnly,
} from "../../../components";
import { useTypedApi } from "../../../services/api-client";
import type { VehicleMake } from "../../../services/api-client";
import { Car, Plus, Edit, Trash2, Search, AlertTriangle } from "lucide-react";

interface MakesManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const MakesManagementModal: React.FC<MakesManagementModalProps> = ({
  isOpen,
  onClose,
}) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [searchQuery, setSearchQuery] = useState("");
  const [isCreateMode, setIsCreateMode] = useState(false);
  const [editingMake, setEditingMake] = useState<VehicleMake | null>(null);
  const [newMakeName, setNewMakeName] = useState("");
  const [editMakeName, setEditMakeName] = useState("");
  const [deleteConfirmMake, setDeleteConfirmMake] =
    useState<VehicleMake | null>(null);

  // Fetch makes
  const { data: makesResponse, isLoading } = useQuery({
    queryKey: ["vehicle-makes"],
    queryFn: () => api.vehicleHierarchy.getMakes(),
    enabled: isOpen,
  });

  // Fetch hierarchy for cascade warnings
  const { data: hierarchyResponse } = useQuery({
    queryKey: ["vehicle-hierarchy-full"],
    queryFn: () => api.vehicleHierarchy.getFullHierarchy(),
    enabled: isOpen,
  });

  // Create make mutation
  const createMakeMutation = useMutation({
    mutationFn: (name: string) => api.vehicleHierarchy.createMake({ name }),
    onSuccess: () => {
      toast.success("Make created successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-makes"] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      setIsCreateMode(false);
      setNewMakeName("");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create make");
    },
  });

  // Update make mutation
  const updateMakeMutation = useMutation({
    mutationFn: ({ id, name }: { id: string; name: string }) =>
      api.vehicleHierarchy.updateMake(id, { name }),
    onSuccess: () => {
      toast.success("Make updated successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-makes"] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      setEditingMake(null);
      setEditMakeName("");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update make");
    },
  });

  // Delete make mutation
  const deleteMakeMutation = useMutation({
    mutationFn: (makeId: string) => api.vehicleHierarchy.deleteMake(makeId),
    onSuccess: () => {
      toast.success("Make deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-makes"] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      setDeleteConfirmMake(null);
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete make");
    },
  });

  // Filter makes based on search
  const filteredMakes = useMemo(() => {
    if (!makesResponse?.data) return [];

    return makesResponse.data
      .filter((make) =>
        make.name.toLowerCase().includes(searchQuery.toLowerCase()),
      )
      .sort((a, b) => a.name.localeCompare(b.name));
  }, [makesResponse?.data, searchQuery]);

  // Get make statistics
  const getMakeStats = (make: VehicleMake) => {
    if (!hierarchyResponse?.data) return { models: 0, years: 0 };

    let models = 0;
    const years = new Set<number>();

    hierarchyResponse.data.years.forEach((year) => {
      const makeData = year.makes.find((m) => m.id === make.id);
      if (makeData) {
        models += makeData.models.length;
        years.add(year.year);
      }
    });

    return { models, years: years.size };
  };

  const handleCreateMake = () => {
    if (!newMakeName.trim()) {
      toast.error("Please enter a make name");
      return;
    }
    createMakeMutation.mutate(newMakeName.trim());
  };

  const handleEditMake = (make: VehicleMake) => {
    setEditingMake(make);
    setEditMakeName(make.name);
  };

  const handleUpdateMake = () => {
    if (!editingMake || !editMakeName.trim()) {
      toast.error("Please enter a make name");
      return;
    }
    updateMakeMutation.mutate({
      id: editingMake.id,
      name: editMakeName.trim(),
    });
  };

  const handleDeleteMake = (make: VehicleMake) => {
    const stats = getMakeStats(make);
    if (stats.models > 0) {
      setDeleteConfirmMake(make);
    } else {
      deleteMakeMutation.mutate(make.id);
    }
  };

  const confirmDelete = () => {
    if (deleteConfirmMake) {
      deleteMakeMutation.mutate(deleteConfirmMake.id);
    }
  };

  const handleClose = () => {
    setSearchQuery("");
    setIsCreateMode(false);
    setEditingMake(null);
    setNewMakeName("");
    setEditMakeName("");
    setDeleteConfirmMake(null);
    onClose();
  };

  const isPending =
    createMakeMutation.isPending ||
    updateMakeMutation.isPending ||
    deleteMakeMutation.isPending;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Manage Vehicle Makes"
      size="lg"
    >
      <div className="space-y-6">
        {/* Header with search and add button */}
        <div className="flex items-center justify-between">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search makes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <CompanyAdminOnly>
            <Button
              onClick={() => setIsCreateMode(true)}
              className="ml-4 flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Add Make</span>
            </Button>
          </CompanyAdminOnly>
        </div>

        {/* Create make form */}
        {isCreateMode && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="text-lg font-medium text-blue-900 mb-3">
              Add New Make
            </h3>
            <div className="flex items-end space-x-3">
              <FormField label="Make Name" className="flex-1">
                <Input
                  type="text"
                  placeholder="e.g., Ford, Toyota, etc."
                  value={newMakeName}
                  onChange={(e) => setNewMakeName(e.target.value)}
                />
              </FormField>
              <Button
                onClick={handleCreateMake}
                disabled={isPending || !newMakeName.trim()}
                className="flex items-center space-x-2"
              >
                {createMakeMutation.isPending ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Plus className="h-4 w-4" />
                )}
                <span>Create</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setIsCreateMode(false);
                  setNewMakeName("");
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        {/* Makes list */}
        <div className="space-y-3">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner size="md" />
            </div>
          ) : filteredMakes.length > 0 ? (
            filteredMakes.map((make) => {
              const stats = getMakeStats(make);
              const isEditing = editingMake?.id === make.id;

              return (
                <div
                  key={make.id}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-4 flex-1">
                    <Car className="h-5 w-5 text-primary-600" />
                    <div className="flex-1">
                      {isEditing ? (
                        <div className="flex items-center space-x-3">
                          <Input
                            type="text"
                            value={editMakeName}
                            onChange={(e) => setEditMakeName(e.target.value)}
                            className="flex-1"
                          />
                          <Button
                            size="sm"
                            onClick={handleUpdateMake}
                            disabled={isPending || !editMakeName.trim()}
                          >
                            Save
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setEditingMake(null);
                              setEditMakeName("");
                            }}
                          >
                            Cancel
                          </Button>
                        </div>
                      ) : (
                        <>
                          <h3 className="font-medium text-gray-900">
                            {make.name}
                          </h3>
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>
                              Created:{" "}
                              {new Date(make.createdAt).toLocaleDateString()}
                            </span>
                            <Badge variant="secondary">
                              {stats.models} models
                            </Badge>
                            <Badge variant="secondary">
                              {stats.years} years
                            </Badge>
                          </div>
                        </>
                      )}
                    </div>
                  </div>

                  {!isEditing && (
                    <CompanyAdminOnly>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditMake(make)}
                          disabled={isPending}
                          className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteMake(make)}
                          disabled={isPending}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CompanyAdminOnly>
                  )}
                </div>
              );
            })
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Car className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p>No makes found</p>
              {searchQuery && (
                <p className="text-sm">Try adjusting your search</p>
              )}
            </div>
          )}
        </div>

        {/* Delete confirmation */}
        {deleteConfirmMake && (
          <Alert variant="warning" className="border-orange-200 bg-orange-50">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
              <div className="flex-1">
                <h3 className="font-medium text-orange-900">
                  Confirm Deletion
                </h3>
                <p className="text-orange-800 mt-1">
                  Are you sure you want to delete make "{deleteConfirmMake.name}
                  "?
                  {(() => {
                    const stats = getMakeStats(deleteConfirmMake);
                    if (stats.models > 0) {
                      return (
                        <span className="block mt-2 font-medium">
                          ⚠️ This will also delete {stats.models} model(s)
                          associated with this make.
                        </span>
                      );
                    }
                    return null;
                  })()}
                </p>
                <div className="flex items-center space-x-3 mt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setDeleteConfirmMake(null)}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={confirmDelete}
                    disabled={isPending}
                    className="bg-red-600 text-white hover:bg-red-700"
                  >
                    {deleteMakeMutation.isPending ? "Deleting..." : "Delete"}
                  </Button>
                </div>
              </div>
            </div>
          </Alert>
        )}

        {(createMakeMutation.error ||
          updateMakeMutation.error ||
          deleteMakeMutation.error) && (
          <Alert variant="error">
            {createMakeMutation.error?.message ||
              updateMakeMutation.error?.message ||
              deleteMakeMutation.error?.message ||
              "Operation failed"}
          </Alert>
        )}
      </div>
    </Modal>
  );
};
