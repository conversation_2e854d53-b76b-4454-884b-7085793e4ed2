/**
 * Generic modal component for simple entity CRUD operations
 * Handles Year and Brand management through configuration
 */

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
  <PERSON>ge,
  <PERSON><PERSON>,
  <PERSON>ading<PERSON><PERSON>ner,
  FormField,
  CompanyAdminOnly,
} from "../index";
import { Plus, Trash2, <PERSON><PERSON><PERSON>riangle, Edit } from "lucide-react";

import type { SimpleEntity, CRUDHookReturn } from "../../hooks/types/entity-crud.types";

interface SimpleEntityModalProps<T extends SimpleEntity> {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  entityName: string;
  icon: React.ComponentType<{ className?: string }>;
  colorScheme: 'blue' | 'green' | 'purple' | 'orange';
  placeholder: string;
  emptyStateMessage: string;
  crud: CRUDHookReturn<T>;
}

export const SimpleEntityModal = <T extends SimpleEntity>({
  isOpen,
  onClose, // Used by crud.handleClose
  title,
  entityName,
  icon: Icon,
  colorScheme,
  placeholder,
  emptyStateMessage,
  crud,
}: SimpleEntityModalProps<T>) => {
  
  // Color scheme configuration
  const colorClasses = {
    blue: {
      container: 'bg-blue-50 border-blue-200',
      text: 'text-blue-900',
      header: 'text-blue-900',
    },
    green: {
      container: 'bg-green-50 border-green-200',
      text: 'text-green-900',
      header: 'text-green-900',
    },
    purple: {
      container: 'bg-purple-50 border-purple-200',
      text: 'text-purple-900',
      header: 'text-purple-900',
    },
    orange: {
      container: 'bg-orange-50 border-orange-200',
      text: 'text-orange-900',
      header: 'text-orange-900',
    },
  };

  const colors = colorClasses[colorScheme];

  // Handle close - combine external onClose with crud cleanup
  const handleClose = () => {
    crud.handleClose();
    onClose();
  };

  // Loading state
  if (crud.isLoading) {
    return (
      <Modal isOpen={isOpen} onClose={handleClose} title={title} size="xl">
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner size="lg" />
        </div>
      </Modal>
    );
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title={title} size="xl">
      <div className="space-y-6">
        {/* Add New Entity Form - Always Visible */}
        <CompanyAdminOnly>
          <div className={`p-4 rounded-lg ${colors.container}`}>
            <h3 className={`text-lg font-medium mb-3 ${colors.header}`}>
              Add New {entityName}
            </h3>
            <div className="flex items-center space-x-3">
              <FormField label={`${entityName} Name`} className="flex-1">
                <Input
                  type="text"
                  placeholder={placeholder}
                  value={crud.newEntityName}
                  onChange={(e) => crud.setNewEntityName(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !crud.isPending && crud.newEntityName.trim()) {
                      crud.handleCreate();
                    }
                  }}
                  maxLength={500}
                  disabled={crud.isPending}
                />
              </FormField>
              <Button
                onClick={crud.handleCreate}
                disabled={crud.isPending || !crud.newEntityName.trim()}
                className="flex items-center space-x-2 h-[46px] px-4 mt-6"
              >
                <Plus className="h-4 w-4" />
                <span>Add {entityName}</span>
              </Button>
            </div>
          </div>
        </CompanyAdminOnly>

        {/* Edit Entity Form */}
        {crud.editingEntity && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="text-lg font-medium text-yellow-900 mb-3">
              Edit {entityName}: {crud.editingEntity.name}
            </h3>
            <div className="space-y-4">
              <FormField label={`${entityName} Name`}>
                <Input
                  type="text"
                  value={crud.editEntityName}
                  onChange={(e) => crud.setEditEntityName(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !crud.isPending && crud.editEntityName.trim()) {
                      crud.handleUpdate();
                    }
                    if (e.key === "Escape") {
                      crud.setEditingEntity(null);
                    }
                  }}
                  maxLength={500}
                  disabled={crud.isPending}
                />
              </FormField>
              <FormField label="Status">
                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="entityActive"
                      checked={crud.editEntityActive}
                      onChange={() => crud.setEditEntityActive(true)}
                      disabled={crud.isPending}
                      className="mr-2"
                    />
                    Active
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="entityActive"
                      checked={!crud.editEntityActive}
                      onChange={() => crud.setEditEntityActive(false)}
                      disabled={crud.isPending}
                      className="mr-2"
                    />
                    Inactive
                  </label>
                </div>
              </FormField>
              <div className="flex items-center space-x-3">
                <Button
                  onClick={crud.handleUpdate}
                  disabled={crud.isPending || !crud.editEntityName.trim()}
                  variant="primary"
                >
                  Save Changes
                </Button>
                <Button
                  onClick={() => crud.setEditingEntity(null)}
                  disabled={crud.isPending}
                  variant="outline"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Entities List */}
        <div className="border border-gray-200 rounded-lg">
          <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
            <h3 className="text-lg font-medium text-gray-900">
              {title.replace('Manage ', '')} ({crud.entities.length})
            </h3>
          </div>

          {crud.sortedEntities.length === 0 ? (
            <div className="p-8 text-center">
              <Icon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No {entityName.toLowerCase()}s yet
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {emptyStateMessage}
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {crud.sortedEntities.map((entity) => (
                <div
                  key={entity.id}
                  className="px-4 py-4 flex items-center justify-between hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <Icon className="h-5 w-5 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-900">
                        {entity.name}
                      </span>
                      <Badge
                        variant={entity.isActive ? "success" : "secondary"}
                        size="sm"
                      >
                        {entity.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>

                  <CompanyAdminOnly>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => crud.handleEdit(entity)}
                        disabled={crud.isPending}
                        className="text-gray-600 hover:text-gray-900"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => crud.handleDelete(entity)}
                        disabled={crud.isPending}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CompanyAdminOnly>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Delete confirmation */}
        {crud.deleteConfirmEntity && (
          <Alert variant="warning" className="border-orange-200 bg-orange-50">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
              <div className="flex-1">
                <h3 className="font-medium text-orange-900">
                  Confirm Deletion
                </h3>
                <p className="text-orange-800 mt-1">
                  Are you sure you want to delete {entityName.toLowerCase()} "{crud.deleteConfirmEntity.name}"?
                  This action cannot be undone and may affect associated data.
                </p>
                <div className="flex items-center space-x-3 mt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => crud.setDeleteConfirmEntity(null)}
                    disabled={crud.isPending}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={crud.confirmDelete}
                    disabled={crud.isPending}
                    className="text-red-600 border-red-300 hover:bg-red-50"
                  >
                    {crud.currentOperation === 'delete' ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                    ) : (
                      `Delete ${entityName}`
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </Alert>
        )}
      </div>
    </Modal>
  );
};
