import React from "react";
import { clsx } from "clsx";
import { Button } from "./Button";

export interface HeroSectionProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Main hero title */
  title: string;
  /** Supporting description text */
  description: string;
  /** Primary call-to-action button */
  primaryCta?: {
    text: string;
    onClick?: () => void;
    href?: string;
    icon?: React.ReactNode;
  };
  /** Secondary call-to-action button */
  secondaryCta?: {
    text: string;
    onClick?: () => void;
    href?: string;
    variant?: "outline" | "ghost";
  };
  /** Background variant */
  variant?: "gradient" | "solid" | "image";
  /** Size variant for different use cases */
  size?: "sm" | "md" | "lg" | "xl";
  /** Text alignment */
  align?: "left" | "center" | "right";
  /** Background image URL (when variant="image") */
  backgroundImage?: string;
}

const sizeClasses = {
  sm: "py-12 sm:py-16",
  md: "py-16 sm:py-20",
  lg: "py-20 sm:py-32",
  xl: "py-24 sm:py-40",
} as const;

const titleSizeClasses = {
  sm: "text-3xl sm:text-4xl lg:text-5xl",
  md: "text-4xl sm:text-5xl lg:text-6xl",
  lg: "text-4xl sm:text-6xl lg:text-7xl",
  xl: "text-5xl sm:text-7xl lg:text-8xl",
} as const;

const descriptionSizeClasses = {
  sm: "text-lg",
  md: "text-xl",
  lg: "text-xl",
  xl: "text-2xl",
} as const;

const alignmentClasses = {
  left: "text-left",
  center: "text-center",
  right: "text-right",
} as const;

const backgroundClasses = {
  gradient: "bg-gradient-to-b from-primary-50 via-white to-gray-50",
  solid: "bg-white",
  image: "bg-cover bg-center bg-no-repeat relative",
} as const;

export const HeroSection: React.FC<HeroSectionProps> = ({
  title,
  description,
  primaryCta,
  secondaryCta,
  variant = "gradient",
  size = "lg",
  align = "center",
  backgroundImage,
  className,
  ...props
}) => {
  const backgroundStyle =
    variant === "image" && backgroundImage
      ? { backgroundImage: `url(${backgroundImage})` }
      : undefined;

  const renderCta = (
    cta: HeroSectionProps["primaryCta"] | HeroSectionProps["secondaryCta"],
    isPrimary = false,
  ) => {
    if (!cta) return null;

    const secondaryCta = cta as HeroSectionProps["secondaryCta"];
    const primaryCta = cta as HeroSectionProps["primaryCta"];

    const buttonProps = {
      size: (size === "xl" ? "xl" : "lg") as "xl" | "lg",
      variant: isPrimary
        ? ("primary" as const)
        : (secondaryCta?.variant as "outline" | "ghost") ||
          ("outline" as const),
      className: isPrimary ? "group" : undefined,
      onClick: cta.onClick,
    };

    const content = (
      <>
        {cta.text}
        {isPrimary && primaryCta?.icon && (
          <span className="ml-2 group-hover:translate-x-1 transition-transform">
            {primaryCta.icon}
          </span>
        )}
      </>
    );

    if (cta.href) {
      return (
        <Button {...buttonProps} asChild>
          <a
            href={cta.href}
            className="inline-flex items-center justify-center"
          >
            {content}
          </a>
        </Button>
      );
    }

    return <Button {...buttonProps}>{content}</Button>;
  };

  return (
    <div
      className={clsx(
        backgroundClasses[variant],
        sizeClasses[size],
        variant === "image" && "relative",
        className,
      )}
      style={backgroundStyle}
      data-testid="hero-section-container"
      {...props}
    >
      {/* Overlay for image backgrounds */}
      {variant === "image" && (
        <div className="absolute inset-0 bg-gray-900/40" />
      )}

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div
          className={clsx(
            alignmentClasses[align],
            align === "center" && "max-w-4xl mx-auto",
          )}
        >
          <h1
            className={clsx(
              "font-bold tracking-tight",
              titleSizeClasses[size],
              variant === "image" ? "text-white" : "text-gray-900",
            )}
          >
            {title}
          </h1>

          <p
            className={clsx(
              "mt-6 leading-relaxed max-w-3xl",
              descriptionSizeClasses[size],
              variant === "image" ? "text-gray-100" : "text-gray-600",
              align === "center" && "mx-auto",
            )}
          >
            {description}
          </p>

          {(primaryCta || secondaryCta) && (
            <div
              className={clsx(
                "mt-10 flex gap-4",
                align === "center" && "justify-center",
                align === "right" && "justify-end",
                "flex-col sm:flex-row",
              )}
            >
              {renderCta(primaryCta, true)}
              {renderCta(secondaryCta, false)}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
