/**
 * Design System Color Tokens
 * Inspired by Salient template design patterns
 *
 * Provides semantic color naming for maintainable theming
 * across marketing pages, authenticated app, and future React Native
 */

export const colors = {
  // Primary Blue Scale (Salient-inspired)
  primary: {
    50: "#eff6ff", // Light backgrounds, subtle highlights
    100: "#dbeafe", // Hover states for light elements
    200: "#bfdbfe", // Disabled states, borders
    300: "#93c5fd", // Muted text, secondary elements
    400: "#60a5fa", // Interactive elements
    500: "#3b82f6", // Primary brand color
    600: "#2563eb", // Primary buttons, links
    700: "#1d4ed8", // Primary hover states
    800: "#1e40af", // Primary active states
    900: "#1e3a8a", // Dark text, headings
    950: "#172554", // Darkest text, high contrast
  },

  // Neutral Gray Scale (Enhanced from current)
  gray: {
    50: "#f9fafb", // Body background, light surfaces
    100: "#f3f4f6", // Card backgrounds, subtle dividers
    200: "#e5e7eb", // Borders, dividers
    300: "#d1d5db", // Input borders, inactive elements
    400: "#9ca3af", // Placeholder text, icons
    500: "#6b7280", // Secondary text, captions
    600: "#4b5563", // Body text, labels
    700: "#374151", // Headings, emphasized text
    800: "#1f2937", // Dark headings, high contrast
    900: "#111827", // Primary text, maximum contrast
    950: "#030712", // Darkest possible text
  },

  // Semantic Colors
  semantic: {
    success: {
      50: "#ecfdf5",
      100: "#d1fae5",
      500: "#10b981", // Success states, positive feedback
      600: "#059669", // Success hover
      700: "#047857", // Success active
    },
    warning: {
      50: "#fffbeb",
      100: "#fef3c7",
      500: "#f59e0b", // Warning states, caution
      600: "#d97706", // Warning hover
      700: "#b45309", // Warning active
    },
    error: {
      50: "#fef2f2",
      100: "#fee2e2",
      500: "#ef4444", // Error states, destructive actions
      600: "#dc2626", // Error hover
      700: "#b91c1c", // Error active
    },
    info: {
      50: "#eff6ff",
      100: "#dbeafe",
      500: "#3b82f6", // Info states, neutral feedback
      600: "#2563eb", // Info hover
      700: "#1d4ed8", // Info active
    },
  },

  // Background Gradients (Salient-inspired)
  gradients: {
    primary: "linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",
    primarySubtle: "linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%)",
    hero: "linear-gradient(180deg, #f9fafb 0%, #ffffff 100%)",
    cta: "linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%)",
  },

  // Surface Colors
  surface: {
    primary: "#ffffff", // Main content backgrounds
    secondary: "#f9fafb", // Page backgrounds
    tertiary: "#f3f4f6", // Card backgrounds
    elevated: "#ffffff", // Modal, dropdown backgrounds
    overlay: "rgba(0, 0, 0, 0.5)", // Modal overlays
  },

  // Border Colors
  border: {
    light: "#f3f4f6", // Subtle dividers
    default: "#e5e7eb", // Standard borders
    medium: "#d1d5db", // Input borders
    dark: "#9ca3af", // Emphasized borders
  },

  // Text Colors (Semantic mapping)
  text: {
    primary: "#111827", // Main content text
    secondary: "#4b5563", // Supporting text
    tertiary: "#6b7280", // Captions, metadata
    inverse: "#ffffff", // Text on dark backgrounds
    muted: "#9ca3af", // Placeholder, disabled text
    link: "#2563eb", // Links, interactive text
    linkHover: "#1d4ed8", // Link hover states
  },
} as const;

// Type definitions for TypeScript support
export type ColorScale = typeof colors.primary;
export type SemanticColor = typeof colors.semantic.success;
export type ColorToken = keyof typeof colors;

// Utility function for accessing nested color values
export const getColor = (path: string): string => {
  const keys = path.split(".");
  let value: unknown = colors;

  for (const key of keys) {
    if (typeof value === "object" && value !== null && key in value) {
      value = (value as Record<string, unknown>)[key];
    } else {
      throw new Error(`Color token "${path}" not found`);
    }
  }

  if (typeof value !== "string") {
    throw new Error(`Color token "${path}" is not a string`);
  }

  return value;
};

// Export individual scales for convenience
export const { primary, gray, semantic, gradients, surface, border, text } =
  colors;
