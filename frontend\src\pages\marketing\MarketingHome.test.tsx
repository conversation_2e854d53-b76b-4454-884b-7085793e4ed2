/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import { <PERSON>rowserRouter } from "react-router-dom";
import { MarketingHome } from "./MarketingHome";

// Mock the organisms
vi.mock("../../components", () => ({
  MarketingHero: ({
    title,
    description,
    primaryCta,
    secondaryCta,
    announcement,
    features,
    socialProof,
  }: any) => (
    <div data-testid="marketing-hero">
      {announcement && (
        <div data-testid="hero-announcement">{announcement.text}</div>
      )}
      <h1>{title}</h1>
      <p>{description}</p>
      {features && (
        <ul data-testid="hero-features">
          {features.map((feature: string, index: number) => (
            <li key={index}>{feature}</li>
          ))}
        </ul>
      )}
      {primaryCta && (
        <button data-testid="hero-primary-cta">{primaryCta.text}</button>
      )}
      {secondaryCta && (
        <button data-testid="hero-secondary-cta">{secondaryCta.text}</button>
      )}
      {socialProof && (
        <div data-testid="hero-social-proof">
          <span>{socialProof.text}</span>
          {socialProof.stats && (
            <div data-testid="hero-stats">
              {socialProof.stats.map((stat: any, index: number) => (
                <div key={index}>
                  {stat.value} {stat.label}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  ),
  FeatureGrid: ({ title, subtitle, badge, features }: any) => (
    <div data-testid="feature-grid">
      {badge && <div data-testid="features-badge">{badge.text}</div>}
      <h2>{title}</h2>
      <p>{subtitle}</p>
      <div data-testid="features-list">
        {features.map((feature: any) => (
          <div key={feature.id} data-testid={`feature-${feature.id}`}>
            <h3>{feature.title}</h3>
            <p>{feature.description}</p>
          </div>
        ))}
      </div>
    </div>
  ),
  TestimonialSection: ({ title, subtitle, badge, testimonials }: any) => (
    <div data-testid="testimonial-section">
      {badge && <div data-testid="testimonials-badge">{badge.text}</div>}
      <h2>{title}</h2>
      <p>{subtitle}</p>
      <div data-testid="testimonials-list">
        {testimonials.map((testimonial: any) => (
          <div
            key={testimonial.id}
            data-testid={`testimonial-${testimonial.id}`}
          >
            <blockquote>{testimonial.quote}</blockquote>
            <div>
              {testimonial.author.name} - {testimonial.author.title}
            </div>
            <div>{testimonial.author.company}</div>
          </div>
        ))}
      </div>
    </div>
  ),
}));

const renderWithRouter = (component: React.ReactElement) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe("MarketingHome", () => {
  describe("Page Structure", () => {
    it("should render all main sections", () => {
      renderWithRouter(<MarketingHome />);

      const heroSections = screen.getAllByTestId("marketing-hero");
      expect(heroSections).toHaveLength(2); // Main hero + CTA hero
      expect(screen.getByTestId("feature-grid")).toBeInTheDocument();
      expect(screen.getByTestId("testimonial-section")).toBeInTheDocument();
    });

    it("should have proper semantic structure", () => {
      renderWithRouter(<MarketingHome />);

      const sections = document.querySelectorAll("section");
      expect(sections.length).toBe(3); // Features, Testimonials, CTA

      // Check for section IDs for navigation
      expect(document.querySelector("#features")).toBeInTheDocument();
      expect(document.querySelector("#contact")).toBeInTheDocument();
    });

    it("should have proper background styling", () => {
      renderWithRouter(<MarketingHome />);

      // Check the main container has bg-white class
      const mainContainer = document.querySelector(".bg-white");
      expect(mainContainer).toBeInTheDocument();
      expect(mainContainer).toHaveClass("bg-white");
    });
  });

  describe("Hero Section", () => {
    it("should render main hero with correct content", () => {
      renderWithRouter(<MarketingHome />);

      expect(screen.getByText("Tech Notes")).toBeInTheDocument();
      expect(
        screen.getByText(
          /An all-in-one tool for making sure your service technicians/,
        ),
      ).toBeInTheDocument();
    });

    it("should render hero announcement", () => {
      renderWithRouter(<MarketingHome />);

      expect(screen.getByTestId("hero-announcement")).toBeInTheDocument();
      expect(
        screen.getByText("🚀 New Mobile App Available"),
      ).toBeInTheDocument();
    });

    it("should render hero features list", () => {
      renderWithRouter(<MarketingHome />);

      const featuresList = screen.getByTestId("hero-features");
      expect(featuresList).toBeInTheDocument();

      expect(screen.getByText("Mobile-first design")).toBeInTheDocument();
      expect(screen.getByText("Real-time sync")).toBeInTheDocument();
      expect(screen.getByText("Offline access")).toBeInTheDocument();
      expect(screen.getByText("Enterprise security")).toBeInTheDocument();
    });

    it("should render hero CTAs", () => {
      renderWithRouter(<MarketingHome />);

      const primaryCTAs = screen.getAllByTestId("hero-primary-cta");
      const secondaryCTAs = screen.getAllByTestId("hero-secondary-cta");
      expect(primaryCTAs.length).toBe(2); // Main hero + CTA hero
      expect(secondaryCTAs.length).toBe(2); // Main hero + CTA hero
      expect(screen.getByText("Get Started Free")).toBeInTheDocument();
      expect(screen.getByText("View Pricing")).toBeInTheDocument();
    });

    it("should render social proof with stats", () => {
      renderWithRouter(<MarketingHome />);

      const socialProofSections = screen.getAllByTestId("hero-social-proof");
      expect(socialProofSections.length).toBe(2); // Main hero + CTA hero
      expect(
        screen.getByText("Trusted by leading service organizations"),
      ).toBeInTheDocument();

      const statsSection = screen.getAllByTestId("hero-stats");
      expect(statsSection.length).toBe(2); // Main hero + CTA hero
      expect(
        screen.getByText("10,000+ Active Technicians"),
      ).toBeInTheDocument();
      expect(screen.getByText("500+ Companies")).toBeInTheDocument();
      expect(screen.getByText("99.9% Uptime")).toBeInTheDocument();
    });
  });

  describe("Features Section", () => {
    it("should render features grid with correct content", () => {
      renderWithRouter(<MarketingHome />);

      const featureGrid = screen.getByTestId("feature-grid");
      expect(featureGrid).toBeInTheDocument();

      expect(
        screen.getByText("Everything you need to manage technical content"),
      ).toBeInTheDocument();
      expect(
        screen.getByText(
          "Streamline your operations with powerful tools designed for service teams",
        ),
      ).toBeInTheDocument();
    });

    it("should render features badge", () => {
      renderWithRouter(<MarketingHome />);

      expect(screen.getByTestId("features-badge")).toBeInTheDocument();
      expect(screen.getByText("Core Features")).toBeInTheDocument();
    });

    it("should render all feature items", () => {
      renderWithRouter(<MarketingHome />);

      expect(screen.getByTestId("feature-user-management")).toBeInTheDocument();
      expect(screen.getByTestId("feature-smart-analytics")).toBeInTheDocument();
      expect(
        screen.getByTestId("feature-enterprise-security"),
      ).toBeInTheDocument();
      expect(screen.getByTestId("feature-mobile-first")).toBeInTheDocument();
      expect(screen.getByTestId("feature-real-time-sync")).toBeInTheDocument();
      expect(screen.getByTestId("feature-global-access")).toBeInTheDocument();
    });

    it("should render feature content correctly", () => {
      renderWithRouter(<MarketingHome />);

      expect(screen.getByText("User Management")).toBeInTheDocument();
      expect(
        screen.getByText(/Complete control over user access/),
      ).toBeInTheDocument();

      expect(screen.getByText("Smart Analytics")).toBeInTheDocument();
      expect(
        screen.getByText(/Deep insights into user engagement/),
      ).toBeInTheDocument();

      expect(screen.getByText("Mobile-First Design")).toBeInTheDocument();
      expect(
        screen.getByText(/Optimized for technicians in the field/),
      ).toBeInTheDocument();
    });
  });

  describe("Testimonials Section", () => {
    it("should render testimonials section with correct content", () => {
      renderWithRouter(<MarketingHome />);

      const testimonialSection = screen.getByTestId("testimonial-section");
      expect(testimonialSection).toBeInTheDocument();

      expect(
        screen.getByText("Trusted by service professionals worldwide"),
      ).toBeInTheDocument();
      expect(
        screen.getByText("See what our customers have to say about Tech Notes"),
      ).toBeInTheDocument();
    });

    it("should render testimonials badge", () => {
      renderWithRouter(<MarketingHome />);

      expect(screen.getByTestId("testimonials-badge")).toBeInTheDocument();
      expect(screen.getByText("Customer Stories")).toBeInTheDocument();
    });

    it("should render all testimonial items", () => {
      renderWithRouter(<MarketingHome />);

      expect(
        screen.getByTestId("testimonial-testimonial-1"),
      ).toBeInTheDocument();
      expect(
        screen.getByTestId("testimonial-testimonial-2"),
      ).toBeInTheDocument();
      expect(
        screen.getByTestId("testimonial-testimonial-3"),
      ).toBeInTheDocument();
    });

    it("should render testimonial content correctly", () => {
      renderWithRouter(<MarketingHome />);

      expect(
        screen.getByText(/Tech Notes has completely transformed/),
      ).toBeInTheDocument();
      expect(
        screen.getByText("Sarah Johnson - Operations Manager"),
      ).toBeInTheDocument();
      expect(screen.getByText("ServicePro Solutions")).toBeInTheDocument();

      expect(
        screen.getByText(/The real-time sync feature ensures/),
      ).toBeInTheDocument();
      expect(
        screen.getByText("Mike Chen - Field Service Director"),
      ).toBeInTheDocument();
      expect(screen.getByText("TechFlow Industries")).toBeInTheDocument();
    });
  });

  describe("Final CTA Section", () => {
    it("should render final CTA hero section", () => {
      renderWithRouter(<MarketingHome />);

      const heroSections = screen.getAllByTestId("marketing-hero");
      const finalCTA = heroSections[1]; // Second hero is the CTA

      expect(finalCTA).toBeInTheDocument();
      expect(
        screen.getByText("Ready to streamline your operations?"),
      ).toBeInTheDocument();
      expect(
        screen.getByText(/Join thousands of teams already using Tech Notes/),
      ).toBeInTheDocument();
    });

    it("should render final CTA buttons", () => {
      renderWithRouter(<MarketingHome />);

      const ctaButtons = screen.getAllByTestId("hero-primary-cta");
      const secondaryButtons = screen.getAllByTestId("hero-secondary-cta");

      expect(ctaButtons.length).toBeGreaterThan(1);
      expect(secondaryButtons.length).toBeGreaterThan(1);

      expect(screen.getByText("Start Free Trial")).toBeInTheDocument();
      expect(screen.getAllByText("See Pricing").length).toBeGreaterThan(0);
    });

    it("should render final CTA social proof", () => {
      renderWithRouter(<MarketingHome />);

      const socialProofSections = screen.getAllByTestId("hero-social-proof");
      expect(socialProofSections.length).toBe(2); // Main hero + CTA hero

      expect(
        screen.getByText("Join 500+ companies already using Tech Notes"),
      ).toBeInTheDocument();
      expect(screen.getByText("14-day Free Trial")).toBeInTheDocument();
      expect(screen.getByText("No Credit Card Required")).toBeInTheDocument();
      expect(screen.getByText("24/7 Support")).toBeInTheDocument();
    });
  });

  describe("Responsive Design", () => {
    it("should have proper section spacing", () => {
      renderWithRouter(<MarketingHome />);

      const sections = document.querySelectorAll("section");
      sections.forEach((section) => {
        expect(section).toHaveClass("py-20", "sm:py-32");
      });
    });

    it("should have proper container constraints", () => {
      renderWithRouter(<MarketingHome />);

      const containers = document.querySelectorAll(".max-w-7xl");
      expect(containers.length).toBeGreaterThan(0);

      containers.forEach((container) => {
        expect(container).toHaveClass("mx-auto", "px-4", "sm:px-6", "lg:px-8");
      });
    });

    it("should have proper background variations", () => {
      renderWithRouter(<MarketingHome />);

      // Testimonials section should have gray background
      const testimonialSection = screen
        .getByTestId("testimonial-section")
        .closest("section");
      expect(testimonialSection).toHaveClass("bg-gray-50");
    });
  });

  describe("Navigation Integration", () => {
    it("should have proper section IDs for navigation", () => {
      renderWithRouter(<MarketingHome />);

      expect(document.querySelector("#features")).toBeInTheDocument();
      expect(document.querySelector("#contact")).toBeInTheDocument();
    });

    it("should maintain semantic HTML structure", () => {
      renderWithRouter(<MarketingHome />);

      const main = document.querySelector("div.bg-white");
      expect(main).toBeInTheDocument();

      const sections = document.querySelectorAll("section");
      expect(sections.length).toBe(3); // Features, Testimonials, CTA
    });
  });
});
