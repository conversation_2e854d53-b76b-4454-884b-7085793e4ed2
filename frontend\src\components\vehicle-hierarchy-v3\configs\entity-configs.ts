/**
 * Entity configuration objects for simple entity management
 * Defines specific behavior for Year and Brand entities
 */

import { Calendar, Building2, TreePine, Car } from "lucide-react";
import type { VehicleYearV3, VehicleBrandV3, VehicleSubBrandV3, VehicleModelV3, TypedApiClient } from "../../../services/api-client";
import type { SimpleEntityConfig } from "../../../hooks/types/entity-crud.types";
import { createValidationConfig } from "../../../hooks/utils/entity-validation.utils";

/**
 * Year entity configuration
 * Defines how Year management behaves in the generic modal
 */
export const createYearConfig = (api: TypedApiClient): SimpleEntityConfig<VehicleYearV3> => ({
  entityName: "Year",
  queryKey: "vehicle-years-v3",
  
  apiMethods: {
    getAll: () => api.vehicleHierarchyV3.getYears(),
    getById: (id: string) => api.vehicleHierarchyV3.getYear(id),
    create: (data: { name: string }) => api.vehicleHierarchyV3.createYear(data),
    update: (id: string, data: { name?: string; isActive?: boolean }) => 
      api.vehicleHierarchyV3.updateYear(id, data),
    delete: (id: string) => api.vehicleHierarchyV3.deleteYear(id),
  },
  
  validation: createValidationConfig("Year", 500),
  
  ui: {
    title: "Manage Vehicle Years",
    icon: Calendar,
    colorScheme: "blue" as const,
    placeholder: "e.g., 2024, 2025",
    emptyStateMessage: "Get started by creating your first year",
  },
});

/**
 * Brand entity configuration
 * Defines how Brand management behaves in the generic modal
 */
export const createBrandConfig = (api: TypedApiClient): SimpleEntityConfig<VehicleBrandV3> => ({
  entityName: "Brand",
  queryKey: "vehicle-brands-v3",
  
  apiMethods: {
    getAll: () => api.vehicleHierarchyV3.getBrands(),
    getById: (id: string) => api.vehicleHierarchyV3.getBrand(id),
    create: (data: { name: string }) => api.vehicleHierarchyV3.createBrand(data),
    update: (id: string, data: { name?: string; isActive?: boolean }) => 
      api.vehicleHierarchyV3.updateBrand(id, data),
    delete: (id: string) => api.vehicleHierarchyV3.deleteBrand(id),
  },
  
  validation: createValidationConfig("Brand", 500),
  
  ui: {
    title: "Manage Vehicle Brands",
    icon: Building2,
    colorScheme: "blue" as const,
    placeholder: "e.g., Toyota, Ford, BMW",
    emptyStateMessage: "Get started by creating your first brand",
  },
});

/**
 * Sub-Brand entity configuration interface
 * Sub-Brands are more complex as they require brand association
 */
export interface SubBrandConfig {
  entityName: string;
  queryKey: string;
  validation: ReturnType<typeof createValidationConfig>;
  ui: {
    title: string;
    icon: React.ComponentType<{ className?: string }>;
    colorScheme: 'blue' | 'green' | 'purple' | 'orange';
    placeholder: string;
    emptyStateMessage: string;
  };
  apiMethods: {
    getAllSubBrands: () => Promise<{ data: VehicleSubBrandV3[]; meta: { count: number; tenantId: string } }>;
    getAllBrands: () => Promise<{ data: VehicleBrandV3[]; meta: { count: number; tenantId: string } }>;
    getSubBrandById: (brandId: string, subBrandId: string) => Promise<{ data: VehicleSubBrandV3 }>;
    createSubBrand: (brandId: string, data: { name: string }) => Promise<{ data: VehicleSubBrandV3 }>;
    updateSubBrand: (brandId: string, subBrandId: string, data: { name?: string; brandId?: string; isActive?: boolean }) => Promise<{ data: VehicleSubBrandV3 }>;
    deleteSubBrand: (brandId: string, subBrandId: string) => Promise<{ data: { success: boolean } }>;
  };
}

/**
 * Sub-Brand entity configuration
 * Defines how Sub-Brand management behaves with brand association
 */
export const createSubBrandConfig = (api: TypedApiClient): SubBrandConfig => ({
  entityName: "Sub-Brand",
  queryKey: "vehicle-sub-brands-v3",

  apiMethods: {
    getAllSubBrands: () => api.vehicleHierarchyV3.getAllSubBrands(),
    getAllBrands: () => api.vehicleHierarchyV3.getBrands(),
    getSubBrandById: (brandId: string, subBrandId: string) => api.vehicleHierarchyV3.getSubBrandById(brandId, subBrandId),
    createSubBrand: (brandId: string, data: { name: string }) => api.vehicleHierarchyV3.createSubBrand(brandId, data),
    updateSubBrand: (brandId: string, subBrandId: string, data: { name?: string; isActive?: boolean }) =>
      api.vehicleHierarchyV3.updateSubBrand(brandId, subBrandId, data),
    deleteSubBrand: (brandId: string, subBrandId: string) => api.vehicleHierarchyV3.deleteSubBrand(brandId, subBrandId),
  },

  validation: createValidationConfig("Sub-Brand", 500),

  ui: {
    title: "Manage Vehicle Sub-Brands",
    icon: TreePine,
    colorScheme: "blue" as const,
    placeholder: "e.g., Premium Line, Sport Series",
    emptyStateMessage: "Get started by creating your first sub-brand",
  },
});

/**
 * Model entity configuration interface
 * Models are complex entities requiring cascading Brand → Sub-Brand selection
 */
export interface ModelConfig {
  entityName: string;
  queryKey: string;
  validation: ReturnType<typeof createValidationConfig>;
  ui: {
    title: string;
    icon: React.ComponentType<{ className?: string }>;
    colorScheme: 'blue' | 'green' | 'purple' | 'orange';
    placeholder: string;
    emptyStateMessage: string;
  };
  apiMethods: {
    getAllModels: () => Promise<{ data: VehicleModelV3[]; meta: { count: number; tenantId: string } }>;
    getAllSubBrandsWithBrands: () => Promise<{ data: VehicleSubBrandV3[]; meta: { count: number; tenantId: string } }>;
    getModelById: (brandId: string, subBrandId: string, modelId: string) => Promise<{ data: VehicleModelV3 }>;
    createModel: (brandId: string, subBrandId: string, data: { name: string }) => Promise<{ data: VehicleModelV3 }>;
    updateModel: (brandId: string, subBrandId: string, modelId: string, data: { name?: string; subBrandId?: string; isActive?: boolean }) => Promise<{ data: VehicleModelV3 }>;
    deleteModel: (brandId: string, subBrandId: string, modelId: string) => Promise<{ data: { success: boolean } }>;
  };
}

/**
 * Model entity configuration
 * Defines how Model management behaves with cascading Brand → Sub-Brand selection
 */
export const createModelConfig = (api: TypedApiClient): ModelConfig => ({
  entityName: "Model",
  queryKey: "vehicle-models-v3",

  apiMethods: {
    getAllModels: () => api.vehicleHierarchyV3.getAllModels(),
    getAllSubBrandsWithBrands: async () => {
      // Fetch all brands first
      const brandsResponse = await api.vehicleHierarchyV3.getBrands();

      // Fetch all sub-brands for each brand
      const allSubBrandsPromises = brandsResponse.data.map(async (brand) => {
        const subBrandsResponse = await api.vehicleHierarchyV3.getSubBrands(brand.id);
        // Add brand information to each sub-brand
        return subBrandsResponse.data.map(subBrand => ({
          ...subBrand,
          brand: brand,
        }));
      });

      const allSubBrandsArrays = await Promise.all(allSubBrandsPromises);
      const allSubBrands = allSubBrandsArrays.flat();

      return {
        data: allSubBrands,
        meta: {
          count: allSubBrands.length,
          tenantId: brandsResponse.meta.tenantId,
        },
      };
    },
    getModelById: (brandId: string, subBrandId: string, modelId: string) => api.vehicleHierarchyV3.getModelById(brandId, subBrandId, modelId),
    createModel: (brandId: string, subBrandId: string, data: { name: string }) => api.vehicleHierarchyV3.createModel(brandId, subBrandId, data),
    updateModel: (brandId: string, subBrandId: string, modelId: string, data: { name?: string; isActive?: boolean }) =>
      api.vehicleHierarchyV3.updateModel(brandId, subBrandId, modelId, data),
    deleteModel: (brandId: string, subBrandId: string, modelId: string) => api.vehicleHierarchyV3.deleteModel(brandId, subBrandId, modelId),
  },

  validation: createValidationConfig("Model", 500),

  ui: {
    title: "Manage Vehicle Models",
    icon: Car,
    colorScheme: "blue" as const,
    placeholder: "e.g., Camry, Accord, F-150",
    emptyStateMessage: "Get started by creating your first model",
  },
});

/**
 * Configuration factory for creating entity configs with API instance
 * Provides a clean way to create configurations with proper API binding
 */
export const createEntityConfigs = (api: TypedApiClient) => ({
  year: createYearConfig(api),
  brand: createBrandConfig(api),
  subBrand: createSubBrandConfig(api),
  model: createModelConfig(api),
});

/**
 * Type-safe configuration keys
 */
export type EntityConfigKey = keyof ReturnType<typeof createEntityConfigs>;

/**
 * Helper function to get configuration by key for simple entities
 */
export const getEntityConfig = (
  api: TypedApiClient,
  entityType: Exclude<EntityConfigKey, 'subBrand' | 'model'>
): SimpleEntityConfig<VehicleYearV3 | VehicleBrandV3> => {
  const configs = createEntityConfigs(api);
  return configs[entityType] as SimpleEntityConfig<VehicleYearV3 | VehicleBrandV3>;
};

/**
 * Helper function to get sub-brand configuration
 */
export const getSubBrandConfig = (api: TypedApiClient): SubBrandConfig => {
  return createSubBrandConfig(api);
};

/**
 * Helper function to get model configuration
 */
export const getModelConfig = (api: TypedApiClient): ModelConfig => {
  return createModelConfig(api);
};
