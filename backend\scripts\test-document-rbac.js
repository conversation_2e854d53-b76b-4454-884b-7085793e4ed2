import { PrismaClient, RoleType } from '@prisma/client';

const prisma = new PrismaClient();

async function testDocumentRBAC() {
  console.log('🧪 Testing Document RBAC Functionality...\n');

  // Get test users with different roles
  const users = await prisma.user.findMany({
    include: {
      userRoles: {
        include: {
          role: {
            include: {
              permissions: {
                include: {
                  permission: true
                }
              }
            }
          }
        }
      }
    },
    take: 5
  });

  console.log('👥 Testing Users and Their Document Permissions:\n');

  for (const user of users) {
    console.log(`🧑‍💼 ${user.firstName} ${user.lastName} (${user.email})`);
    
    if (user.userRoles.length === 0) {
      console.log('  ❌ No roles assigned');
      continue;
    }

    for (const userRole of user.userRoles) {
      const role = userRole.role;
      console.log(`  🎭 Role: ${role.name} (${role.type})`);
      
      const documentPermissions = role.permissions.filter(
        rp => rp.permission.resource === 'DOCUMENT'
      );

      if (documentPermissions.length === 0) {
        console.log('    ❌ No document permissions');
      } else {
        documentPermissions.forEach(rp => {
          const perm = rp.permission;
          let icon = '✅';
          if (perm.action === 'READ') icon = '👁️';
          else if (perm.action === 'WRITE') icon = '✏️';
          else if (perm.action === 'DELETE') icon = '🗑️';
          
          console.log(`    ${icon} ${perm.name} - ${perm.description}`);
        });
      }
    }
    console.log('');
  }

  // Test permission checking logic
  console.log('🔐 Permission Check Simulation:\n');

  const testScenarios = [
    { roleType: 'SYSTEM_ADMIN', action: 'READ', expected: true },
    { roleType: 'SYSTEM_ADMIN', action: 'WRITE', expected: true },
    { roleType: 'SYSTEM_ADMIN', action: 'DELETE', expected: true },
    { roleType: 'COMPANY_ADMIN', action: 'READ', expected: true },
    { roleType: 'COMPANY_ADMIN', action: 'WRITE', expected: true },
    { roleType: 'COMPANY_ADMIN', action: 'DELETE', expected: true },
    { roleType: 'COMPANY_TECH', action: 'READ', expected: true },
    { roleType: 'COMPANY_TECH', action: 'WRITE', expected: false },
    { roleType: 'COMPANY_TECH', action: 'DELETE', expected: false },
  ];

  for (const scenario of testScenarios) {
    const role = await prisma.role.findFirst({
      where: { type: scenario.roleType },
      include: {
        permissions: {
          include: {
            permission: true
          }
        }
      }
    });

    if (!role) {
      console.log(`❌ Role ${scenario.roleType} not found`);
      continue;
    }

    const hasPermission = role.permissions.some(rp => 
      rp.permission.resource === 'DOCUMENT' && 
      rp.permission.action === scenario.action
    );

    const result = hasPermission === scenario.expected ? '✅' : '❌';
    const status = hasPermission ? 'ALLOWED' : 'DENIED';
    
    console.log(`${result} ${role.name} → document:${scenario.action.toLowerCase()} = ${status} (expected: ${scenario.expected ? 'ALLOWED' : 'DENIED'})`);
  }

  await prisma.$disconnect();
}

testDocumentRBAC().catch(console.error);
