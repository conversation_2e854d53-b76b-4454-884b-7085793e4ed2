#!/usr/bin/env tsx

/**
 * Debug script to check if the current user has System Admin role
 * and if the canBypassTenantScope is being set correctly
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '../src/utils/logger';
const prisma = new PrismaClient();

async function debugSystemAdminAccess() {
  try {
    logger.info('🔍 Starting System Admin access debug...');

    // Get all users with their roles
    const users = await prisma.user.findMany({
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        userRoles: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                type: true,
                isSystemRole: true,
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'asc' },
    });

    logger.info(`📊 Found ${users.length} users in database`);

    // Check each user's roles and system admin status
    for (const user of users) {
      logger.info(
        `\n👤 User: ${user.email} (${user.firstName} ${user.lastName})`
      );
      logger.info(`   ID: ${user.id}`);
      logger.info(`   Clerk ID: ${user.clerkId}`);
      logger.info(`   Tenant: ${user.tenant.name} (${user.tenantId})`);
      logger.info(`   Active: ${user.isActive}`);
      logger.info(`   Roles (${user.userRoles.length}):`);

      let hasSystemRole = false;
      for (const userRole of user.userRoles) {
        const isSystemRole = userRole.role.isSystemRole;
        logger.info(
          `     - ${userRole.role.name} (${userRole.role.type}) - System Role: ${isSystemRole}`
        );
        if (isSystemRole) {
          hasSystemRole = true;
        }
      }

      // Simulate the canBypassTenantScope logic from auth middleware
      const canBypassTenantScope = user.userRoles.some(
        (userRole) => userRole.role.isSystemRole
      );

      logger.info(`   Can Bypass Tenant Scope: ${canBypassTenantScope}`);

      if (hasSystemRole) {
        logger.info(`   ✅ This user IS a System Admin`);
      } else {
        logger.info(`   ❌ This user is NOT a System Admin`);
      }
    }

    // Check the System Admin role itself
    logger.info('\n🔧 Checking System Admin role configuration...');
    const systemAdminRole = await prisma.role.findFirst({
      where: {
        type: 'SYSTEM_ADMIN',
      },
      include: {
        userRoles: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
    });

    if (systemAdminRole) {
      logger.info(`✅ System Admin role found: ${systemAdminRole.name}`);
      logger.info(`   ID: ${systemAdminRole.id}`);
      logger.info(`   Type: ${systemAdminRole.type}`);
      logger.info(`   Is System Role: ${systemAdminRole.isSystemRole}`);
      logger.info(`   Assigned to ${systemAdminRole.userRoles.length} users:`);

      for (const userRole of systemAdminRole.userRoles) {
        logger.info(
          `     - ${userRole.user.email} (${userRole.user.firstName} ${userRole.user.lastName})`
        );
      }
    } else {
      logger.error('❌ System Admin role not found in database!');
    }

    // Test the getUsersByTenantWithRoles logic for a system admin
    const systemAdminUsers = users.filter((user) =>
      user.userRoles.some((ur) => ur.role.isSystemRole)
    );

    if (systemAdminUsers.length > 0) {
      const systemAdmin = systemAdminUsers[0];
      logger.info(
        `\n🧪 Testing getUsersByTenantWithRoles for System Admin: ${systemAdmin.email}`
      );

      // Simulate the logic from UserService.getUsersByTenantWithRoles
      const canBypassTenantScope = systemAdmin.userRoles.some(
        (userRole) => userRole.role.isSystemRole
      );

      logger.info(`   Can Bypass Tenant Scope: ${canBypassTenantScope}`);

      // Test the where clause logic
      const whereClause = canBypassTenantScope
        ? {} // No tenant restriction for System Admins
        : { tenantId: systemAdmin.tenantId }; // Tenant-scoped for Company Admins

      logger.info(
        `   Where clause for user query: ${JSON.stringify(whereClause)}`
      );

      // Execute the actual query that would be used
      const usersQuery = await prisma.user.findMany({
        where: whereClause,
        include: {
          tenant: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      logger.info(`   Query returned ${usersQuery.length} users:`);
      for (const user of usersQuery) {
        logger.info(`     - ${user.email} (Tenant: ${user.tenant.name})`);
      }
    } else {
      logger.warn('⚠️  No System Admin users found to test with');
    }
  } catch (error) {
    logger.error('❌ Debug failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the debug
debugSystemAdminAccess().catch(console.error);
