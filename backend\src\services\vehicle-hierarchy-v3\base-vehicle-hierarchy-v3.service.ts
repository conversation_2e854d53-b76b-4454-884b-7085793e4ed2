import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';

import {
  NotFoundError,
  ConflictError,
  ValidationError,
} from '../../types/error.types.js';
import { Logger } from '../../utils/logger.js';
import { BaseTenantService } from '../base-tenant.service.js';
import { PrismaService } from '../prisma.service.js';

/**
 * Base service for Vehicle Hierarchy V3 operations
 * Provides shared patterns and utilities for Brand, Sub-Brand, and Model services
 */
export abstract class BaseVehicleHierarchyV3Service extends BaseTenantService {
  constructor(prismaService: PrismaService, logger: Logger) {
    super(prismaService, logger);
  }

  // ===== SHARED VALIDATION PATTERNS =====

  /**
   * Validate and trim entity name
   */
  protected validateAndTrimName(name: string, entityType: string): string {
    if (!name || !name.trim()) {
      throw new ValidationError(`${entityType} name cannot be empty`);
    }
    return name.trim();
  }

  /**
   * Handle Prisma unique constraint errors
   */
  protected handleUniqueConstraintError(
    error: unknown,
    entityName: string,
    entityType: string
  ): void {
    if (error instanceof PrismaClientKnownRequestError) {
      if (error.code === 'P2002') {
        throw new ConflictError(`${entityType} '${entityName}' already exists`);
      }
    }
  }

  /**
   * Handle not found errors
   */
  protected handleNotFoundError(
    error: unknown,
    entityId: string,
    entityType: string
  ): void {
    if (error instanceof PrismaClientKnownRequestError) {
      if (error.code === 'P2025') {
        throw new NotFoundError(`${entityType} ${entityId} not found`);
      }
    }
  }

  // ===== DISPLAY ORDER MANAGEMENT =====

  /**
   * Get next display order for an entity within a scope
   */
  protected async getNextDisplayOrder(
    model: {
      aggregate: (args: {
        where: Record<string, unknown>;
        _max: { displayOrder: true };
      }) => Promise<{ _max: { displayOrder: number | null } }>;
    },
    whereClause: Record<string, unknown>
  ): Promise<number> {
    const maxDisplayOrder = await model.aggregate({
      where: {
        ...whereClause,
        deletedAt: null,
      },
      _max: {
        displayOrder: true,
      },
    });

    return (maxDisplayOrder._max.displayOrder || 0) + 1;
  }

  /**
   * Execute reorder operations within a transaction
   */
  protected async executeReorder(
    modelName: string,
    orders: { id: string; displayOrder: number }[],
    whereClause: Record<string, unknown>
  ): Promise<void> {
    await this.transaction(async (tx) => {
      for (const { id, displayOrder } of orders) {
        await (tx as any)[modelName].update({
          where: {
            id,
            ...whereClause,
            deletedAt: null,
          },
          data: { displayOrder },
        });
      }
    });
  }

  // ===== SOFT DELETE PATTERNS =====

  /**
   * Execute soft delete for an entity
   */
  protected async executeSoftDelete(
    model: any,
    entityId: string,
    whereClause: Record<string, unknown>
  ): Promise<void> {
    try {
      await model.update({
        where: {
          id: entityId,
          ...whereClause,
          deletedAt: null,
        },
        data: {
          deletedAt: new Date(),
          isActive: false,
        },
      });
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new NotFoundError(`Entity ${entityId} not found`);
        }
      }
      throw error;
    }
  }

  // ===== COMMON QUERY PATTERNS =====

  /**
   * Standard where clause for active entities
   */
  protected getActiveEntityWhere(
    additionalWhere: Record<string, unknown> = {}
  ): Record<string, unknown> {
    return {
      ...additionalWhere,
      deletedAt: null,
    };
  }

  /**
   * Standard order by clause for entities
   */
  protected getStandardOrderBy(): Array<Record<string, 'asc' | 'desc'>> {
    return [{ displayOrder: 'asc' }, { name: 'asc' }];
  }
}
