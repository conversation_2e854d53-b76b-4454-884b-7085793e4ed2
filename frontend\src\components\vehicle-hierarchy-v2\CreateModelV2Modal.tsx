import React, { useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { Modal, Button, Input, FormField, Alert, Badge } from "../index";
import { useTypedApi, type VehicleSubBrandV2 } from "../../services/api-client";
import { Car } from "lucide-react";

interface CreateModelV2ModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedSubBrand?: VehicleSubBrandV2 | null;
}

export const CreateModelV2Modal: React.FC<CreateModelV2ModalProps> = ({
  isOpen,
  onClose,
  selectedSubBrand,
}) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [name, setName] = useState("");
  const [subBrandId, setSubBrandId] = useState(selectedSubBrand?.id || "");
  const [selectedYearIds, setSelectedYearIds] = useState<string[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch sub-brands for selection
  const { data: subBrandsResponse } = useQuery({
    queryKey: ["vehicle-hierarchy-v2", "sub-brands"],
    queryFn: () => api.vehicleHierarchyV2.getSubBrands(),
    enabled: isOpen,
  });

  // Fetch years for optional association
  const { data: yearsResponse } = useQuery({
    queryKey: ["vehicle-years"],
    queryFn: () => api.vehicleHierarchy.getYears(),
    enabled: isOpen,
  });

  const createModelMutation = useMutation({
    mutationFn: async (modelData: { name: string; subBrandId: string }) => {
      const response = await api.vehicleHierarchyV2.createModel(modelData);

      // If years are selected, associate them with the model
      if (selectedYearIds.length > 0) {
        await api.vehicleHierarchyV2.associateModelWithYears(response.data.id, {
          yearIds: selectedYearIds,
        });
      }

      return response;
    },
    onSuccess: () => {
      toast.success("Model created successfully");
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "models"],
      });
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "sub-brands"],
      });
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "full"],
      });
      handleClose();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create model");
    },
  });

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = "Model name is required";
    } else if (name.trim().length < 2) {
      newErrors.name = "Model name must be at least 2 characters";
    } else if (name.trim().length > 100) {
      newErrors.name = "Model name must be less than 100 characters";
    }

    if (!subBrandId) {
      newErrors.subBrandId = "Please select a sub-brand";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    createModelMutation.mutate({
      name: name.trim(),
      subBrandId,
    });
  };

  const handleClose = () => {
    setName("");
    setSubBrandId(selectedSubBrand?.id || "");
    setSelectedYearIds([]);
    setErrors({});
    onClose();
  };

  // Update subBrandId when selectedSubBrand changes
  React.useEffect(() => {
    if (selectedSubBrand?.id) {
      setSubBrandId(selectedSubBrand.id);
    }
  }, [selectedSubBrand]);

  const toggleYear = (yearId: string) => {
    setSelectedYearIds((prev) =>
      prev.includes(yearId)
        ? prev.filter((id) => id !== yearId)
        : [...prev, yearId],
    );
  };

  const selectAllYears = () => {
    if (yearsResponse?.data) {
      setSelectedYearIds(yearsResponse.data.map((year) => year.id));
    }
  };

  const clearAllYears = () => {
    setSelectedYearIds([]);
  };

  const subBrands = subBrandsResponse?.data || [];
  const years = yearsResponse?.data || [];
  const selectedSubBrandInfo = subBrands.find((sb) => sb.id === subBrandId);

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Create New Model"
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex items-center space-x-3 text-gray-600 mb-4">
          <Car className="h-5 w-5" />
          <span>Add a new model to the hierarchy</span>
        </div>

        <FormField label="Sub-Brand" error={errors.subBrandId} required>
          <select
            value={subBrandId}
            onChange={(e) => setSubBrandId(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Select a sub-brand...</option>
            {subBrands.map((subBrand) => (
              <option key={subBrand.id} value={subBrand.id}>
                {subBrand.brand?.name} → {subBrand.name}
              </option>
            ))}
          </select>
          {errors.subBrandId && (
            <p className="mt-1 text-sm text-red-600">{errors.subBrandId}</p>
          )}
        </FormField>

        <FormField label="Model Name" error={errors.name} required>
          <Input
            type="text"
            placeholder="e.g., Regular Cab, LE, G20BHS"
            value={name}
            onChange={(e) => setName(e.target.value)}
            error={errors.name}
            maxLength={100}
          />
        </FormField>

        {selectedSubBrandInfo && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              This model will be added under{" "}
              <strong>
                {selectedSubBrandInfo.brand?.name} → {selectedSubBrandInfo.name}
              </strong>
            </p>
          </div>
        )}

        {/* Year Association Section */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-700">
              Associate with Years (Optional)
            </label>
            <div className="flex space-x-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={selectAllYears}
                disabled={years.length === 0}
              >
                Select All
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={clearAllYears}
                disabled={selectedYearIds.length === 0}
              >
                Clear All
              </Button>
            </div>
          </div>

          <div className="max-h-32 overflow-y-auto border border-gray-200 rounded-md p-3">
            <div className="grid grid-cols-4 gap-2">
              {years.map((year) => (
                <label
                  key={year.id}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={selectedYearIds.includes(year.id)}
                    onChange={() => toggleYear(year.id)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm">{year.year}</span>
                </label>
              ))}
            </div>
          </div>

          {selectedYearIds.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {selectedYearIds.map((yearId) => {
                const year = years.find((y) => y.id === yearId);
                return year ? (
                  <Badge key={yearId} variant="secondary">
                    {year.year}
                  </Badge>
                ) : null;
              })}
            </div>
          )}
        </div>

        {createModelMutation.isError && (
          <Alert variant="error">
            <p>Failed to create model. Please try again.</p>
          </Alert>
        )}

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={createModelMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={
              createModelMutation.isPending || !name.trim() || !subBrandId
            }
          >
            {createModelMutation.isPending ? "Creating..." : "Create Model"}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
