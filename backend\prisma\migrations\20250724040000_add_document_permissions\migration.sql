-- Add Document Permissions Migration
-- This migration adds the missing DOCUMENT permissions and assigns them to appropriate roles
-- according to the 3-tier RBAC system defined in the implementation plan

-- Insert Document Permissions
INSERT INTO "permissions" ("id", "name", "description", "resource", "action", "createdAt", "updatedAt") VALUES
  ('perm_document_read', 'document:read', 'Read and view documents', 'DOCUMENT', 'READ', NOW(), NOW()),
  ('perm_document_write', 'document:write', 'Create and upload documents', 'DOCUMENT', 'WRITE', NOW(), NOW()),
  ('perm_document_delete', 'document:delete', 'Delete documents', 'DOCUMENT', 'DELETE', NOW(), NOW())
ON CONFLICT ("name") DO NOTHING;

-- Assign ALL document permissions to System Admin (gets everything automatically)
INSERT INTO "role_permissions" ("roleId", "permissionId") VALUES
  ('role_system_admin', 'perm_document_read'),
  ('role_system_admin', 'perm_document_write'),
  ('role_system_admin', 'perm_document_delete')
ON CONFLICT ("roleId", "permissionId") DO NOTHING;

-- Assign full document permissions to Company Admin (full access within tenant)
INSERT INTO "role_permissions" ("roleId", "permissionId") VALUES
  ('role_company_admin', 'perm_document_read'),
  ('role_company_admin', 'perm_document_write'),
  ('role_company_admin', 'perm_document_delete')
ON CONFLICT ("roleId", "permissionId") DO NOTHING;

-- Assign read-only document permissions to Company Tech (limited access within tenant)
INSERT INTO "role_permissions" ("roleId", "permissionId") VALUES
  ('role_company_tech', 'perm_document_read')
ON CONFLICT ("roleId", "permissionId") DO NOTHING;
