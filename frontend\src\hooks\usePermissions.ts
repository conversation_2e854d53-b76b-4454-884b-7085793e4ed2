import { useMemo } from "react";
import { useAuth } from "./useAuth";
import type { RoleType } from "../types/auth.types";

export interface PermissionCheck {
  resource: string;
  action: string;
}

/**
 * Hook for checking user permissions and roles
 * Provides convenient methods for permission-based UI logic
 */
export function usePermissions() {
  const { userProfile } = useAuth();

  // Extract permissions array from user profile
  const permissions = useMemo(() => {
    return userProfile?.permissions || [];
  }, [userProfile?.permissions]);

  // Extract role types from user profile
  const roles = useMemo(() => {
    return userProfile?.roles?.map((r: any) => r.role.type) || []; // eslint-disable-line @typescript-eslint/no-explicit-any
  }, [userProfile?.roles]);

  // Check if user has a specific permission
  const hasPermission = useMemo(() => {
    return (resource: string, action: string): boolean => {
      const permissionString = `${resource.toUpperCase()}:${action.toUpperCase()}`;
      return permissions.includes(permissionString);
    };
  }, [permissions]);

  // Check if user has a specific role
  const hasRole = useMemo(() => {
    return (roleType: RoleType): boolean => {
      return roles.includes(roleType);
    };
  }, [roles]);

  // Check if user has any of the specified roles
  const hasAnyRole = useMemo(() => {
    return (roleTypes: RoleType[]): boolean => {
      return roleTypes.some((roleType) => roles.includes(roleType));
    };
  }, [roles]);

  // Check if user has all of the specified roles
  const hasAllRoles = useMemo(() => {
    return (roleTypes: RoleType[]): boolean => {
      return roleTypes.every((roleType) => roles.includes(roleType));
    };
  }, [roles]);

  // Check if user has multiple permissions (AND logic)
  const hasAllPermissions = useMemo(() => {
    return (permissionChecks: PermissionCheck[]): boolean => {
      return permissionChecks.every((check) =>
        hasPermission(check.resource, check.action),
      );
    };
  }, [hasPermission]);

  // Check if user has any of multiple permissions (OR logic)
  const hasAnyPermission = useMemo(() => {
    return (permissionChecks: PermissionCheck[]): boolean => {
      return permissionChecks.some((check) =>
        hasPermission(check.resource, check.action),
      );
    };
  }, [hasPermission]);

  // Convenience role checks
  const isSystemAdmin = useMemo(() => hasRole("SYSTEM_ADMIN"), [hasRole]);
  const isCompanyAdmin = useMemo(() => hasRole("COMPANY_ADMIN"), [hasRole]);
  const isCompanyTech = useMemo(() => hasRole("COMPANY_TECH"), [hasRole]);

  // Convenience permission checks for common operations
  const canManageUsers = useMemo(
    () => hasPermission("USER", "MANAGE") || hasPermission("USER", "WRITE"),
    [hasPermission],
  );

  const canViewUsers = useMemo(
    () => hasPermission("USER", "READ"),
    [hasPermission],
  );

  const canDeleteUsers = useMemo(
    () => hasPermission("USER", "DELETE"),
    [hasPermission],
  );

  const canManageTenant = useMemo(
    () => hasPermission("TENANT", "MANAGE") || hasPermission("TENANT", "WRITE"),
    [hasPermission],
  );

  const canViewTenant = useMemo(
    () => hasPermission("TENANT", "READ"),
    [hasPermission],
  );

  const canManageData = useMemo(
    () => hasPermission("DATA", "WRITE"),
    [hasPermission],
  );

  const canViewData = useMemo(
    () => hasPermission("DATA", "READ"),
    [hasPermission],
  );

  const canManageSystem = useMemo(
    () => hasPermission("SYSTEM", "MANAGE"),
    [hasPermission],
  );

  // Check if user is an admin (System Admin or Company Admin)
  const isAdmin = useMemo(
    () => isSystemAdmin || isCompanyAdmin,
    [isSystemAdmin, isCompanyAdmin],
  );

  // Check if user can bypass tenant scope (System Admin only)
  const canBypassTenantScope = useMemo(
    () => userProfile?.canBypassTenantScope || false,
    [userProfile?.canBypassTenantScope],
  );

  return {
    // Raw data
    permissions,
    roles,

    // Core permission checking functions
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    hasAllPermissions,
    hasAnyPermission,

    // Role convenience checks
    isSystemAdmin,
    isCompanyAdmin,
    isCompanyTech,
    isAdmin,
    canBypassTenantScope,

    // Permission convenience checks
    canManageUsers,
    canViewUsers,
    canDeleteUsers,
    canManageTenant,
    canViewTenant,
    canManageData,
    canViewData,
    canManageSystem,
  };
}

// Export constants from separate file
export { PermissionConstants, RoleConstants } from "../constants/permissions";
