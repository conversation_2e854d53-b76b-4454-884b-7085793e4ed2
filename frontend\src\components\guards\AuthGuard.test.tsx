import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import { <PERSON><PERSON>erRouter } from "react-router-dom";

import { AuthGuard } from "./AuthGuard";
import { useAuth } from "../../hooks/useAuth";
import {
  createMockServices,
  setupMockAuthService,
  testData,
  type MockServices,
} from "../../__tests__/helpers/service-factory.helper";

// Mock the useAuth hook
vi.mock("../../hooks/useAuth");
const mockUseAuth = vi.mocked(useAuth);

// Mock the UserOnboarding component
vi.mock("../organisms/UserOnboarding", () => ({
  UserOnboarding: () => (
    <div data-testid="user-onboarding">User Onboarding</div>
  ),
}));

// Mock the InvitationAcceptance component
vi.mock("../organisms/InvitationAcceptance", () => ({
  InvitationAcceptance: () => (
    <div data-testid="invitation-acceptance">Invitation Acceptance</div>
  ),
}));

// Mock react-router-dom Navigate
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    Navigate: ({ to }: { to: string }) => (
      <div data-testid="navigate" data-to={to}>
        Navigate to {to}
      </div>
    ),
  };
});

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>{children}</BrowserRouter>
);

const TestComponent = () => (
  <div data-testid="protected-content">Protected Content</div>
);

describe("AuthGuard", () => {
  let mockServices: MockServices;

  beforeEach(() => {
    vi.clearAllMocks();
    mockServices = createMockServices();
  });

  describe("Service Unavailable State", () => {
    it("should show service unavailable message when database is down", () => {
      // Arrange
      setupMockAuthService(mockServices.authService, "service-unavailable");

      mockUseAuth.mockReturnValue({
        isLoading: false,
        isAuthenticated: false,
        isOnboarded: false,
        needsOnboarding: false,
        isServiceUnavailable: true,
        clerkUser: null,
        userProfile: null,
        authStatus: {
          serviceUnavailable: true,
          authenticated: false,
          onboarded: false,
          user: null,
        },
        signOut: vi.fn(),
        signIn: vi.fn(),
        refreshAuth: vi.fn(),
        getToken: vi.fn(),
        api: mockServices.authService,
        statusError: null,
        profileError: null,
      });

      // Act
      render(
        <TestWrapper>
          <AuthGuard>
            <TestComponent />
          </AuthGuard>
        </TestWrapper>,
      );

      // Assert
      expect(
        screen.getByText("Service Temporarily Unavailable"),
      ).toBeInTheDocument();
      expect(
        screen.getByText(/authentication service is currently unavailable/i),
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: /retry/i }),
      ).toBeInTheDocument();
      expect(screen.queryByTestId("protected-content")).not.toBeInTheDocument();
    });

    it("should reload page when retry button is clicked", () => {
      // Arrange
      const mockReload = vi.fn();
      Object.defineProperty(window, "location", {
        value: { reload: mockReload },
        writable: true,
      });

      setupMockAuthService(mockServices.authService, "service-unavailable");

      mockUseAuth.mockReturnValue({
        isLoading: false,
        isAuthenticated: false,
        isOnboarded: false,
        needsOnboarding: false,
        isServiceUnavailable: true,
        clerkUser: null,
        userProfile: null,
        authStatus: {
          serviceUnavailable: true,
          authenticated: false,
          onboarded: false,
          user: null,
        },
        signOut: vi.fn(),
        signIn: vi.fn(),
        refreshAuth: vi.fn(),
        getToken: vi.fn(),
        api: mockServices.authService,
        statusError: null,
        profileError: null,
      });

      render(
        <TestWrapper>
          <AuthGuard>
            <TestComponent />
          </AuthGuard>
        </TestWrapper>,
      );

      // Act
      const retryButton = screen.getByRole("button", { name: /retry/i });
      retryButton.click();

      // Assert
      expect(mockReload).toHaveBeenCalled();
    });
  });

  describe("Loading State", () => {
    it("should show loading spinner when authentication is being verified", () => {
      // Arrange
      mockUseAuth.mockReturnValue({
        isLoading: true,
        isAuthenticated: false,
        isOnboarded: false,
        needsOnboarding: false,
        isServiceUnavailable: false,
        clerkUser: null,
        userProfile: null,
        authStatus: undefined,
        signOut: vi.fn(),
        signIn: vi.fn(),
        refreshAuth: vi.fn(),
        getToken: vi.fn(),
        api: mockServices.authService,
        statusError: null,
        profileError: null,
      });

      // Act
      render(
        <TestWrapper>
          <AuthGuard>
            <TestComponent />
          </AuthGuard>
        </TestWrapper>,
      );

      // Assert
      expect(
        screen.getByText("Verifying authentication..."),
      ).toBeInTheDocument();
      expect(screen.queryByTestId("protected-content")).not.toBeInTheDocument();
    });
  });

  describe("Authentication Error State", () => {
    it("should show authentication error when statusError exists and user is not authenticated", () => {
      // Arrange
      mockUseAuth.mockReturnValue({
        isLoading: false,
        isAuthenticated: false,
        isOnboarded: false,
        isServiceUnavailable: false,
        needsOnboarding: false,
        statusError: new Error("Authentication failed"),
        clerkUser: null,
        userProfile: null,
        authStatus: { authenticated: false, onboarded: false, user: null },
        signOut: vi.fn(),
        signIn: vi.fn(),
        refreshAuth: vi.fn(),
        getToken: vi.fn(),
        api: mockServices.authService,
        profileError: null,
      });

      // Act
      render(
        <TestWrapper>
          <AuthGuard>
            <TestComponent />
          </AuthGuard>
        </TestWrapper>,
      );

      // Assert
      expect(screen.getByText("Authentication Error")).toBeInTheDocument();
      expect(
        screen.getByText(/there was an error verifying your authentication/i),
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: /sign out and return home/i }),
      ).toBeInTheDocument();
      expect(screen.queryByTestId("protected-content")).not.toBeInTheDocument();
    });

    it("should call signOut when return to home button is clicked", async () => {
      // Arrange
      const mockSignOut = vi.fn().mockResolvedValue(undefined);

      mockUseAuth.mockReturnValue({
        isLoading: false,
        isAuthenticated: false,
        isOnboarded: false,
        isServiceUnavailable: false,
        needsOnboarding: false,
        statusError: new Error("Authentication failed"),
        clerkUser: null,
        userProfile: null,
        authStatus: { authenticated: false, onboarded: false, user: null },
        signOut: mockSignOut,
        signIn: vi.fn(),
        refreshAuth: vi.fn(),
        getToken: vi.fn(),
        api: mockServices.authService,
        profileError: null,
      });

      render(
        <TestWrapper>
          <AuthGuard>
            <TestComponent />
          </AuthGuard>
        </TestWrapper>,
      );

      // Act
      const homeButton = screen.getByRole("button", {
        name: /sign out and return home/i,
      });
      homeButton.click();

      // Assert
      await waitFor(() => {
        expect(mockSignOut).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe("Unauthenticated State", () => {
    it("should redirect to home when user is not authenticated", () => {
      // Arrange
      mockUseAuth.mockReturnValue({
        isLoading: false,
        isAuthenticated: false,
        isOnboarded: false,
        isServiceUnavailable: false,
        needsOnboarding: false,
        statusError: null,
        clerkUser: null,
        userProfile: null,
        authStatus: { authenticated: false, onboarded: false, user: null },
        signOut: vi.fn(),
        signIn: vi.fn(),
        refreshAuth: vi.fn(),
        getToken: vi.fn(),
        api: mockServices.authService,
        profileError: null,
      });

      // Act
      render(
        <TestWrapper>
          <AuthGuard>
            <TestComponent />
          </AuthGuard>
        </TestWrapper>,
      );

      // Assert
      expect(screen.getByTestId("navigate")).toBeInTheDocument();
      expect(screen.getByTestId("navigate")).toHaveAttribute("data-to", "/");
      expect(screen.queryByTestId("protected-content")).not.toBeInTheDocument();
    });
  });

  describe("Onboarding State", () => {
    it("should show onboarding when user needs onboarding", async () => {
      // Arrange
      mockUseAuth.mockReturnValue({
        isLoading: false,
        isAuthenticated: true,
        isOnboarded: false,
        isServiceUnavailable: false,
        needsOnboarding: true,
        statusError: null,
        clerkUser: testData.clerkUser as any, // eslint-disable-line @typescript-eslint/no-explicit-any
        userProfile: null,
        authStatus: { authenticated: true, onboarded: false, user: null },
        signOut: vi.fn(),
        signIn: vi.fn(),
        refreshAuth: vi.fn(),
        getToken: vi.fn(),
        api: mockServices.authService,
        profileError: null,
      });

      // Act
      render(
        <TestWrapper>
          <AuthGuard>
            <TestComponent />
          </AuthGuard>
        </TestWrapper>,
      );

      // Assert
      await waitFor(() => {
        expect(screen.getByText("Account Setup Required")).toBeInTheDocument();
        expect(
          screen.getByText(/your account exists but setup is incomplete/i),
        ).toBeInTheDocument();
        expect(
          screen.getByRole("button", { name: /sign out and return home/i }),
        ).toBeInTheDocument();
      });
      expect(screen.queryByTestId("protected-content")).not.toBeInTheDocument();
    });
  });

  describe("Authenticated State", () => {
    it("should render protected content when user is fully authenticated and onboarded", () => {
      // Arrange
      setupMockAuthService(mockServices.authService, "authenticated");

      mockUseAuth.mockReturnValue({
        isLoading: false,
        isAuthenticated: true,
        isOnboarded: true,
        isServiceUnavailable: false,
        needsOnboarding: false,
        statusError: null,
        clerkUser: testData.clerkUser as any, // eslint-disable-line @typescript-eslint/no-explicit-any
        userProfile: testData.userProfile,
        authStatus: testData.authStatus,
        signOut: vi.fn(),
        signIn: vi.fn(),
        refreshAuth: vi.fn(),
        getToken: vi.fn(),
        api: mockServices.authService,
        profileError: null,
      });

      // Act
      render(
        <TestWrapper>
          <AuthGuard>
            <TestComponent />
          </AuthGuard>
        </TestWrapper>,
      );

      // Assert
      expect(screen.getByTestId("protected-content")).toBeInTheDocument();
      expect(
        screen.queryByText("Service Temporarily Unavailable"),
      ).not.toBeInTheDocument();
      expect(
        screen.queryByText("Verifying authentication..."),
      ).not.toBeInTheDocument();
    });
  });
});
