#!/usr/bin/env ts-node
/**
 * <PERSON><PERSON><PERSON> to assign System Admin role to a user
 * Usage: npm run assign-system-admin <clerk-user-id>
 *
 * This script should be run after:
 * 1. Database migrations have been applied
 * 2. User has registered through <PERSON> and logged in at least once
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function assignSystemAdmin() {
  const clerkId = process.argv[2];

  if (!clerkId) {
    console.error('❌ Usage: npm run assign-system-admin <clerk-user-id>');
    console.error('   Example: npm run assign-system-admin user_2abc123def456');
    console.error('');
    console.error('   To find the Clerk user ID:');
    console.error('   1. Have the user log in to the application');
    console.error('   2. Check the users table in the database');
    console.error('   3. Use the clerkId from the user record');
    process.exit(1);
  }

  try {
    console.log('🔍 Looking up System Admin role...');

    const systemAdminRole = await prisma.role.findFirst({
      where: { type: 'SYSTEM_ADMIN' },
    });

    if (!systemAdminRole) {
      console.error('❌ System Admin role not found.');
      console.error('   Make sure database migrations have been applied.');
      process.exit(1);
    }

    console.log('👤 Looking up user...');

    const user = await prisma.user.findUnique({
      where: { clerkId },
      include: {
        userRoles: {
          include: { role: true },
        },
      },
    });

    if (!user) {
      console.error(`❌ User with Clerk ID ${clerkId} not found.`);
      console.error('   Make sure the user has logged in at least once.');
      process.exit(1);
    }

    // Check if user already has System Admin role
    const hasSystemAdminRole = user.userRoles.some(
      (ur) => ur.role.type === 'SYSTEM_ADMIN'
    );

    if (hasSystemAdminRole) {
      console.log('✅ User already has System Admin role');
      console.log(
        `   User: ${user.email} (${user.firstName} ${user.lastName})`
      );
      return;
    }

    console.log('🔧 Assigning System Admin role...');

    // Assign System Admin role (tenantId must be null for system roles)
    await prisma.userRole.create({
      data: {
        userId: user.id,
        roleId: systemAdminRole.id,
        tenantId: null, // System roles have no tenant scope
        assignedBy: user.id, // Self-assigned for initial setup
      },
    });

    console.log('✅ System Admin role assigned successfully!');
    console.log(`   User: ${user.email} (${user.firstName} ${user.lastName})`);
    console.log(`   Role: ${systemAdminRole.name}`);
    console.log('   Scope: Global (all tenants)');
    console.log('');
    console.log('🎉 The user now has full system administration privileges.');
  } catch (error) {
    console.error('❌ Failed to assign system admin role:', error);
    process.exit(1);
  }
}

assignSystemAdmin().finally(async () => {
  await prisma.$disconnect();
});
