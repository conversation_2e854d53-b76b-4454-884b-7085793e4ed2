import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  DataTable,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Badge,
  type Column,
} from "../index";
import {
  useTypedApi,
  type UserActivitySummary,
} from "../../services/api-client";
import { usePermissions } from "../../hooks/usePermissions";
import { ChevronLeft, ChevronRight, RefreshCw } from "lucide-react";

interface UserActivityTableProps {
  tenantId?: string; // For System Admins to view specific tenant
  className?: string;
}

export const UserActivityTable: React.FC<UserActivityTableProps> = ({
  tenantId,
  className,
}) => {
  const api = useTypedApi();
  const { isCompanyAdmin, isSystemAdmin } = usePermissions();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy] = useState<"lastLoginAt" | "lastActivityAt" | "email">(
    "lastActivityAt",
  );
  const [sortOrder] = useState<"asc" | "desc">("desc");
  const pageSize = 20;

  // Only Company Admins and System Admins can view user activity
  const canViewActivity = isCompanyAdmin || isSystemAdmin;

  const {
    data: activityResponse,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["user-activity", tenantId, currentPage, sortBy, sortOrder],
    queryFn: () =>
      api.engagement.getUserActivity(
        pageSize,
        (currentPage - 1) * pageSize,
        sortBy,
        sortOrder,
        tenantId,
      ),
    enabled: canViewActivity,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "Never";
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      console.warn("Invalid date string:", dateString);
      return "Invalid Date";
    }
  };

  const getActivityStatus = (user: UserActivitySummary) => {
    if (!user) return { label: "Unknown", variant: "secondary" as const };

    const now = new Date();
    const lastActivity = user?.lastActivityAt
      ? new Date(user.lastActivityAt)
      : null;
    const lastLogin = user?.lastLoginAt ? new Date(user.lastLoginAt) : null;

    if (!lastActivity && !lastLogin) {
      return { label: "Never Active", variant: "secondary" as const };
    }

    // Find the most recent activity (either login or activity)
    const mostRecentActivity =
      lastActivity && lastLogin
        ? new Date(Math.max(lastActivity.getTime(), lastLogin.getTime()))
        : lastActivity || lastLogin;

    if (!mostRecentActivity) {
      return { label: "Never Active", variant: "secondary" as const };
    }

    const daysSinceActivity = Math.floor(
      (now.getTime() - mostRecentActivity.getTime()) / (1000 * 60 * 60 * 24),
    );

    if (daysSinceActivity <= 1) {
      return { label: "Active Today", variant: "success" as const };
    } else if (daysSinceActivity <= 7) {
      return { label: "Active This Week", variant: "primary" as const };
    } else if (daysSinceActivity <= 30) {
      return { label: "Active This Month", variant: "warning" as const };
    } else {
      return { label: "Inactive", variant: "error" as const };
    }
  };

  if (!canViewActivity) {
    return (
      <Alert variant="warning">
        <p>You don't have permission to view user activity data.</p>
      </Alert>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size="lg" />
        <span className="ml-2 text-gray-600">Loading user activity...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="error">
        <p>Failed to load user activity data. Please try again.</p>
        <button
          onClick={() => refetch()}
          className="mt-2 text-sm underline hover:no-underline"
        >
          Retry
        </button>
      </Alert>
    );
  }

  if (!activityResponse?.data) {
    return (
      <Alert variant="info">
        <p>No user activity data available.</p>
      </Alert>
    );
  }

  const { users, totalCount } = activityResponse.data;
  const totalPages = Math.ceil(totalCount / pageSize);

  const columns = [
    {
      key: "email",
      header: "User",
      render: (_value: unknown, user: UserActivitySummary) => (
        <div>
          <div className="font-medium text-gray-900">
            {user?.email || "Unknown"}
          </div>
          {(user?.firstName || user?.lastName) && (
            <div className="text-sm text-gray-500">
              {[user?.firstName, user?.lastName].filter(Boolean).join(" ")}
            </div>
          )}
        </div>
      ),
    },
    {
      key: "status",
      header: "Status",
      render: (_value: unknown, user: UserActivitySummary) => {
        const status = getActivityStatus(user);
        return <Badge variant={status.variant}>{status.label}</Badge>;
      },
    },
    {
      key: "lastLoginAt",
      header: "Last Login",
      render: (_value: unknown, user: UserActivitySummary) => (
        <span className="text-sm text-gray-600">
          {formatDate(user?.lastLoginAt)}
        </span>
      ),
    },
    {
      key: "lastActivityAt",
      header: "Last Activity",
      render: (_value: unknown, user: UserActivitySummary) => (
        <span className="text-sm text-gray-600">
          {formatDate(user?.lastActivityAt)}
        </span>
      ),
    },
    {
      key: "createdAt",
      header: "Joined",
      render: (_value: unknown, user: UserActivitySummary) => (
        <span className="text-sm text-gray-600">
          {user?.createdAt
            ? new Date(user.createdAt).toLocaleDateString()
            : "Unknown"}
        </span>
      ),
    },
  ];

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">User Activity</h2>
          <p className="text-sm text-gray-600 mt-1">
            {totalCount} users • Showing {(currentPage - 1) * pageSize + 1}-
            {Math.min(currentPage * pageSize, totalCount)}
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => refetch()}
          disabled={isLoading}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <DataTable
        data={users as unknown as Record<string, unknown>[]}
        columns={columns as unknown as Column<Record<string, unknown>>[]}
      />

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between mt-6">
          <div className="text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Metadata */}
      <div className="mt-4 text-xs text-gray-500">
        Last updated:{" "}
        {new Date(activityResponse.meta.timestamp).toLocaleString()}
        {tenantId && ` • Tenant: ${activityResponse.meta.tenantId}`}
      </div>
    </div>
  );
};
