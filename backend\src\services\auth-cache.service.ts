import { BackendAuthContext } from '@tech-notes/shared';
import { createClient, RedisClientType } from 'redis';

import { Logger } from '../utils/logger.js';

export interface IAuthCacheService {
  get(key: string): Promise<BackendAuthContext | null>;
  set(key: string, value: BackendAuthContext, ttl: number): Promise<void>;
  invalidate(key: string): Promise<void>;
  disconnect(): Promise<void>;
}

/**
 * Auth caching service with Redis primary + in-memory fallback
 * Provides performance optimization for auth middleware
 */
export class AuthCacheService implements IAuthCacheService {
  private redisClient: RedisClientType | null = null;
  private inMemoryCache = new Map<
    string,
    { data: BackendAuthContext; expires: number }
  >();
  private logger: Logger;
  private readonly DEFAULT_TTL = 300; // 5 minutes in seconds

  constructor(logger: Logger, redisUrl?: string) {
    this.logger = logger;
    this.initializeRedis(redisUrl);
  }

  private async initializeRedis(redisUrl?: string): Promise<void> {
    if (!redisUrl) {
      this.logger.info('Redis URL not provided, using in-memory cache only');
      return;
    }

    try {
      this.redisClient = createClient({ url: redisUrl });

      this.redisClient.on('error', (error) => {
        this.logger.warn(
          'Redis connection error, falling back to in-memory cache',
          {
            error: error.message,
          }
        );
      });

      this.redisClient.on('connect', () => {
        this.logger.info('Redis connected successfully');
      });

      await this.redisClient.connect();
    } catch (error) {
      this.logger.warn(
        'Failed to initialize Redis, using in-memory cache only',
        {
          error: error instanceof Error ? error.message : 'Unknown error',
        }
      );
      this.redisClient = null;
    }
  }

  async get(key: string): Promise<BackendAuthContext | null> {
    // Try Redis first
    if (this.redisClient) {
      try {
        const cached = await this.redisClient.get(key);
        if (cached) {
          this.logger.debug('Auth cache hit (Redis)', { key });
          return JSON.parse(cached) as BackendAuthContext;
        }
      } catch (error) {
        this.logger.warn('Redis get failed, trying in-memory cache', {
          key,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    // Fallback to in-memory cache
    const inMemoryEntry = this.inMemoryCache.get(key);
    if (inMemoryEntry) {
      if (Date.now() < inMemoryEntry.expires) {
        this.logger.debug('Auth cache hit (in-memory)', { key });
        return inMemoryEntry.data;
      } else {
        // Expired entry
        this.inMemoryCache.delete(key);
      }
    }

    this.logger.debug('Auth cache miss', { key });
    return null;
  }

  async set(
    key: string,
    value: BackendAuthContext,
    ttl: number = this.DEFAULT_TTL
  ): Promise<void> {
    const serializedValue = JSON.stringify(value);

    // Try Redis first
    if (this.redisClient) {
      try {
        await this.redisClient.setEx(key, ttl, serializedValue);
        this.logger.debug('Auth cache set (Redis)', { key, ttl });
      } catch (error) {
        this.logger.warn('Redis set failed, using in-memory cache', {
          key,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    // Always set in in-memory cache as fallback
    const expires = Date.now() + ttl * 1000;
    this.inMemoryCache.set(key, { data: value, expires });
    this.logger.debug('Auth cache set (in-memory)', { key, ttl });

    // Clean up expired in-memory entries periodically
    this.cleanupInMemoryCache();
  }

  async invalidate(key: string): Promise<void> {
    // Remove from Redis
    if (this.redisClient) {
      try {
        await this.redisClient.del(key);
        this.logger.debug('Auth cache invalidated (Redis)', { key });
      } catch (error) {
        this.logger.warn('Redis invalidation failed', {
          key,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    // Remove from in-memory cache
    this.inMemoryCache.delete(key);
    this.logger.debug('Auth cache invalidated (in-memory)', { key });
  }

  async disconnect(): Promise<void> {
    if (this.redisClient) {
      try {
        await this.redisClient.disconnect();
        this.logger.info('Redis disconnected');
      } catch (error) {
        this.logger.warn('Error disconnecting Redis', {
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }
    this.inMemoryCache.clear();
  }

  private cleanupInMemoryCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.inMemoryCache.entries()) {
      if (now >= entry.expires) {
        this.inMemoryCache.delete(key);
      }
    }
  }

  /**
   * Generate cache key for user authentication
   */
  static generateUserCacheKey(clerkId: string): string {
    return `auth:user:${clerkId}`;
  }
}
