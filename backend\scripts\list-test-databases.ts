#!/usr/bin/env ts-node

import { Client } from 'pg';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from root directory
dotenv.config({ path: path.resolve(__dirname, '../../.env') });
dotenv.config({
  path: path.resolve(__dirname, '../../.env.local'),
  override: true,
});

async function listTestDatabases() {
  const adminUrl = process.env.TEST_DATABASE_ADMIN_URL;

  if (!adminUrl) {
    console.error(
      '❌ TEST_DATABASE_ADMIN_URL environment variable is required'
    );
    process.exit(1);
  }

  const client = new Client({
    connectionString: adminUrl,
    ssl: {
      rejectUnauthorized: false,
    },
  });

  try {
    console.log('🔗 Connecting to database...');
    await client.connect();

    // Get all databases and their connection info
    const allDbsResult = await client.query(`
      SELECT 
        d.datname,
        COALESCE(s.connections, 0) as active_connections,
        pg_size_pretty(pg_database_size(d.datname)) as size
      FROM pg_database d
      LEFT JOIN (
        SELECT datname, COUNT(*) as connections
        FROM pg_stat_activity
        WHERE datname IS NOT NULL
        AND pid <> pg_backend_pid()
        GROUP BY datname
      ) s ON d.datname = s.datname
      ORDER BY d.datname
    `);

    // Filter for test databases (broader pattern to catch all variations)
    const result = {
      rows: allDbsResult.rows.filter(
        (row) =>
          row.datname.includes('tech_notes_test') ||
          row.datname.startsWith('test_') ||
          row.datname.match(/tech_notes_test_\d+_[a-f0-9]+/)
      ),
    };

    // Also show all databases for debugging
    console.log('\n🔍 All databases on instance:');
    allDbsResult.rows.forEach((row) => {
      const isTestDb =
        row.datname.includes('tech_notes_test') ||
        row.datname.startsWith('test_');
      const marker = isTestDb ? '🧪' : '📁';
      console.log(
        `${marker} ${row.datname} (${row.active_connections} connections, ${row.size})`
      );
    });

    if (result.rows.length === 0) {
      console.log('✨ No test databases found');
      return;
    }

    console.log(`\n📊 Test Database Report:`);
    console.log(
      `${'Database Name'.padEnd(50)} ${'Connections'.padEnd(12)} Size`
    );
    console.log('─'.repeat(75));

    let totalDatabases = 0;
    let cleanableDatabases = 0;
    let totalConnections = 0;

    result.rows.forEach((row) => {
      const cleanable = row.active_connections === 0 ? '✅' : '❌';
      console.log(
        `${row.datname.padEnd(50)} ${String(row.active_connections).padEnd(12)} ${row.size} ${cleanable}`
      );

      totalDatabases++;
      totalConnections += parseInt(row.active_connections);
      if (row.active_connections === 0) {
        cleanableDatabases++;
      }
    });

    console.log('─'.repeat(75));
    console.log(
      `Total: ${totalDatabases} databases, ${totalConnections} active connections`
    );
    console.log(
      `Cleanable: ${cleanableDatabases} databases (✅ = can be dropped, ❌ = has connections)`
    );

    if (cleanableDatabases > 0) {
      console.log(
        `\n💡 You can run 'npm run cleanup:test-dbs' to clean up the databases with 0 connections`
      );
    }

    if (totalConnections > 0) {
      console.log(
        `\n⚠️  ${totalConnections} databases have active connections and cannot be cleaned up automatically`
      );
      console.log(
        `   Consider contacting Render support to clean up orphaned connections`
      );
    }
  } catch (error) {
    console.error('❌ Failed to list databases:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run the listing
listTestDatabases().catch(console.error);
