import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent, Button, Input } from "../../components";
import { QrCode, Camera, Type, Search } from "lucide-react";

type ScanMode = "camera" | "manual";

export const VinScannerPage: React.FC = () => {
  const [scanMode, setScanMode] = useState<ScanMode>("camera");
  const [manualVin, setManualVin] = useState("");
  const [isScanning, setIsScanning] = useState(false);

  const handleCameraScan = () => {
    setIsScanning(true);
    // TODO: Implement camera scanning functionality
    // This would integrate with a QR/barcode scanning library
    setTimeout(() => {
      setIsScanning(false);
      // Simulate scan result
      alert("Camera scanning not yet implemented");
    }, 2000);
  };

  const handleManualSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (manualVin.trim()) {
      // TODO: Process the manually entered VIN
      alert(`Processing VIN: ${manualVin}`);
      setManualVin("");
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">VIN Scanner</h1>
        <p className="text-gray-600">
          Scan or enter vehicle identification numbers
        </p>
      </div>

      {/* Mode Toggle */}
      <div className="flex bg-gray-100 rounded-lg p-1">
        <button
          onClick={() => setScanMode("camera")}
          className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-colors ${
            scanMode === "camera"
              ? "bg-white text-primary-600 shadow-sm"
              : "text-gray-600"
          }`}
        >
          <Camera className="h-5 w-5" />
          <span className="font-medium">Camera</span>
        </button>
        <button
          onClick={() => setScanMode("manual")}
          className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-colors ${
            scanMode === "manual"
              ? "bg-white text-primary-600 shadow-sm"
              : "text-gray-600"
          }`}
        >
          <Type className="h-5 w-5" />
          <span className="font-medium">Manual</span>
        </button>
      </div>

      {/* Camera Scan Mode */}
      {scanMode === "camera" && (
        <Card>
          <CardHeader title="Camera Scanner" />
          <CardContent>
            <div className="space-y-6">
              {/* Camera Preview Area */}
              <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
                {isScanning ? (
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Scanning...</p>
                  </div>
                ) : (
                  <div className="text-center">
                    <QrCode className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-2">
                      Position VIN or QR code in frame
                    </p>
                    <p className="text-sm text-gray-500">
                      Camera will activate when you tap scan
                    </p>
                  </div>
                )}
              </div>

              {/* Scan Button */}
              <Button
                onClick={handleCameraScan}
                disabled={isScanning}
                className="w-full h-14 text-lg"
                variant="primary"
              >
                {isScanning ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Scanning...
                  </>
                ) : (
                  <>
                    <Camera className="h-6 w-6 mr-2" />
                    Start Scan
                  </>
                )}
              </Button>

              {/* Instructions */}
              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="font-medium text-blue-900 mb-2">
                  Scanning Tips:
                </h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Hold device steady</li>
                  <li>• Ensure good lighting</li>
                  <li>• Keep VIN/QR code in center of frame</li>
                  <li>• Clean camera lens if needed</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Manual Entry Mode */}
      {scanMode === "manual" && (
        <Card>
          <CardHeader title="Manual Entry" />
          <CardContent>
            <form onSubmit={handleManualSubmit} className="space-y-6">
              {/* VIN Input */}
              <div>
                <label
                  htmlFor="vin"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Vehicle Identification Number (VIN)
                </label>
                <Input
                  id="vin"
                  type="text"
                  value={manualVin}
                  onChange={(e) => setManualVin(e.target.value.toUpperCase())}
                  placeholder="Enter 17-character VIN"
                  maxLength={17}
                  className="text-lg font-mono"
                  autoComplete="off"
                />
                <p className="text-sm text-gray-500 mt-1">
                  {manualVin.length}/17 characters
                </p>
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                disabled={manualVin.length !== 17}
                className="w-full h-14 text-lg"
                variant="primary"
              >
                <Search className="h-6 w-6 mr-2" />
                Look Up Vehicle
              </Button>

              {/* VIN Format Help */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-2">VIN Format:</h3>
                <p className="text-sm text-gray-600 mb-2">
                  A VIN is exactly 17 characters long and contains:
                </p>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Letters A-Z (except I, O, Q)</li>
                  <li>• Numbers 0-9</li>
                  <li>• No spaces or special characters</li>
                </ul>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
