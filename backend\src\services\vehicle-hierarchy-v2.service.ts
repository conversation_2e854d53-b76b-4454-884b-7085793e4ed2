import {
  <PERSON><PERSON><PERSON>,
  VehicleBrandV2,
  VehicleSubBrandV2,
  VehicleModelV2,
  VehicleModelYearV2,
} from '@prisma/client';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { BackendAuthContext } from '@tech-notes/shared';

import {
  NotFoundError,
  ConflictError,
  ValidationError,
} from '../types/error.types.js';
import { Logger } from '../utils/logger.js';

import { BaseTenantService } from './base-tenant.service.js';
import { PrismaService } from './prisma.service.js';

// Type definitions for service operations
export interface CreateBrandData {
  name: string;
  isActive?: boolean;
}

export interface CreateSubBrandData {
  name: string;
  brandId: string;
  isActive?: boolean;
}

export interface CreateModelV2Data {
  name: string;
  subBrandId: string;
  isActive?: boolean;
}

export interface ModelYearAssociationV2 {
  modelId: string;
  yearIds: string[];
}

export interface HierarchyTreeV2 {
  years: (VehicleYear & {
    brands: (VehicleBrandV2 & {
      subBrands: (VehicleSubBrandV2 & {
        models: (VehicleModelV2 & {
          years: VehicleYear[];
        })[];
      })[];
    })[];
  })[];
}

export interface YearHierarchyV2 {
  year: VehicleYear;
  brands: (VehicleBrandV2 & {
    subBrands: (VehicleSubBrandV2 & {
      models: VehicleModelV2[];
    })[];
  })[];
}

/**
 * Service for managing vehicle hierarchy V2 operations (Year ↔ Model, Brand → Sub-Brand → Model)
 * Extends BaseTenantService for consistent tenant scoping patterns
 */
export class VehicleHierarchyV2Service extends BaseTenantService {
  constructor(prismaService: PrismaService, logger: Logger) {
    super(prismaService, logger);
  }

  // ===== BRAND OPERATIONS =====

  /**
   * Get all brands for a tenant
   */
  async getBrandsByTenant(
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleBrandV2[]> {
    this.logOperation('getBrandsByTenant', { tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleBrandV2.findMany({
          where: { tenantId },
          orderBy: { name: 'asc' },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Create a new brand
   */
  async createBrand(
    brandData: CreateBrandData,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleBrandV2> {
    this.logOperation('createBrand', { name: brandData.name, tenantId });

    if (!brandData.name.trim()) {
      throw new ValidationError('Brand name cannot be empty');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          return await this.prisma.vehicleBrandV2.create({
            data: {
              name: brandData.name.trim(),
              tenantId,
              isActive: brandData.isActive ?? true,
            },
          });
        } catch (error) {
          if (
            error instanceof PrismaClientKnownRequestError &&
            error.code === 'P2002'
          ) {
            throw new ConflictError(
              `Brand '${brandData.name}' already exists for this tenant`
            );
          }
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Update a brand
   */
  async updateBrand(
    brandId: string,
    updateData: { name?: string; isActive?: boolean },
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleBrandV2> {
    this.logOperation('updateBrand', { brandId, tenantId });

    if (updateData.name !== undefined && !updateData.name.trim()) {
      throw new ValidationError('Brand name cannot be empty');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Verify brand exists and belongs to tenant
        const existingBrand = await this.prisma.vehicleBrandV2.findFirst({
          where: { id: brandId, tenantId },
        });

        if (!existingBrand) {
          throw new NotFoundError(
            'Brand not found or does not belong to tenant'
          );
        }

        try {
          return await this.prisma.vehicleBrandV2.update({
            where: { id: brandId },
            data: {
              ...(updateData.name && { name: updateData.name.trim() }),
              ...(updateData.isActive !== undefined && {
                isActive: updateData.isActive,
              }),
            },
          });
        } catch (error) {
          if (
            error instanceof PrismaClientKnownRequestError &&
            error.code === 'P2002'
          ) {
            throw new ConflictError(
              `Brand '${updateData.name}' already exists for this tenant`
            );
          }
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Delete a brand
   */
  async deleteBrand(
    brandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('deleteBrand', { brandId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Check if brand has sub-brands
        const subBrandCount = await this.prisma.vehicleSubBrandV2.count({
          where: { brandId, tenantId },
        });

        if (subBrandCount > 0) {
          throw new ConflictError(
            'Cannot delete brand that has sub-brands. Delete sub-brands first.'
          );
        }

        const deletedCount = await this.prisma.vehicleBrandV2.deleteMany({
          where: { id: brandId, tenantId },
        });

        if (deletedCount.count === 0) {
          throw new NotFoundError(
            'Brand not found or does not belong to tenant'
          );
        }
      },
      BackendAuthContext
    );
  }

  // ===== SUB-BRAND OPERATIONS =====

  /**
   * Get all sub-brands for a tenant, optionally filtered by brand
   */
  async getSubBrandsByTenant(
    tenantId: string,
    brandId?: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleSubBrandV2[]> {
    this.logOperation('getSubBrandsByTenant', { tenantId, brandId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleSubBrandV2.findMany({
          where: {
            tenantId,
            ...(brandId && { brandId }),
          },
          include: {
            brand: true,
          },
          orderBy: [{ brand: { name: 'asc' } }, { name: 'asc' }],
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Get sub-brands by brand ID
   */
  async getSubBrandsByBrand(
    brandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleSubBrandV2[]> {
    this.logOperation('getSubBrandsByBrand', { brandId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Verify brand exists and belongs to tenant
        const brand = await this.prisma.vehicleBrandV2.findFirst({
          where: { id: brandId, tenantId },
        });

        if (!brand) {
          throw new NotFoundError(
            'Brand not found or does not belong to tenant'
          );
        }

        return await this.prisma.vehicleSubBrandV2.findMany({
          where: { brandId, tenantId },
          orderBy: { name: 'asc' },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Create a new sub-brand
   */
  async createSubBrand(
    subBrandData: CreateSubBrandData,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleSubBrandV2> {
    this.logOperation('createSubBrand', {
      name: subBrandData.name,
      brandId: subBrandData.brandId,
      tenantId,
    });

    if (!subBrandData.name.trim()) {
      throw new ValidationError('Sub-brand name cannot be empty');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Verify brand exists and belongs to tenant
        const brand = await this.prisma.vehicleBrandV2.findFirst({
          where: { id: subBrandData.brandId, tenantId },
        });

        if (!brand) {
          throw new NotFoundError(
            'Brand not found or does not belong to tenant'
          );
        }

        try {
          return await this.prisma.vehicleSubBrandV2.create({
            data: {
              name: subBrandData.name.trim(),
              brandId: subBrandData.brandId,
              tenantId,
              isActive: subBrandData.isActive ?? true,
            },
          });
        } catch (error) {
          if (
            error instanceof PrismaClientKnownRequestError &&
            error.code === 'P2002'
          ) {
            throw new ConflictError(
              `Sub-brand '${subBrandData.name}' already exists for this brand`
            );
          }
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Update a sub-brand
   */
  async updateSubBrand(
    subBrandId: string,
    updateData: { name?: string; isActive?: boolean },
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleSubBrandV2> {
    this.logOperation('updateSubBrand', { subBrandId, tenantId });

    if (updateData.name !== undefined && !updateData.name.trim()) {
      throw new ValidationError('Sub-brand name cannot be empty');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Verify sub-brand exists and belongs to tenant
        const existingSubBrand = await this.prisma.vehicleSubBrandV2.findFirst({
          where: { id: subBrandId, tenantId },
        });

        if (!existingSubBrand) {
          throw new NotFoundError(
            'Sub-brand not found or does not belong to tenant'
          );
        }

        try {
          return await this.prisma.vehicleSubBrandV2.update({
            where: { id: subBrandId },
            data: {
              ...(updateData.name && { name: updateData.name.trim() }),
              ...(updateData.isActive !== undefined && {
                isActive: updateData.isActive,
              }),
            },
          });
        } catch (error) {
          if (
            error instanceof PrismaClientKnownRequestError &&
            error.code === 'P2002'
          ) {
            throw new ConflictError(
              `Sub-brand '${updateData.name}' already exists for this brand`
            );
          }
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Delete a sub-brand
   */
  async deleteSubBrand(
    subBrandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('deleteSubBrand', { subBrandId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Check if sub-brand has models
        const modelCount = await this.prisma.vehicleModelV2.count({
          where: { subBrandId, tenantId },
        });

        if (modelCount > 0) {
          throw new ConflictError(
            'Cannot delete sub-brand that has models. Delete models first.'
          );
        }

        const deletedCount = await this.prisma.vehicleSubBrandV2.deleteMany({
          where: { id: subBrandId, tenantId },
        });

        if (deletedCount.count === 0) {
          throw new NotFoundError(
            'Sub-brand not found or does not belong to tenant'
          );
        }
      },
      BackendAuthContext
    );
  }

  // ===== MODEL OPERATIONS =====

  /**
   * Get all models for a tenant, optionally filtered by sub-brand
   */
  async getModelsByTenant(
    tenantId: string,
    subBrandId?: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModelV2[]> {
    this.logOperation('getModelsByTenant', { tenantId, subBrandId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleModelV2.findMany({
          where: {
            tenantId,
            ...(subBrandId && { subBrandId }),
          },
          include: {
            subBrand: {
              include: {
                brand: true,
              },
            },
          },
          orderBy: [
            { subBrand: { brand: { name: 'asc' } } },
            { subBrand: { name: 'asc' } },
            { name: 'asc' },
          ],
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Get models by sub-brand ID
   */
  async getModelsBySubBrand(
    subBrandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModelV2[]> {
    this.logOperation('getModelsBySubBrand', { subBrandId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Verify sub-brand exists and belongs to tenant
        const subBrand = await this.prisma.vehicleSubBrandV2.findFirst({
          where: { id: subBrandId, tenantId },
        });

        if (!subBrand) {
          throw new NotFoundError(
            'Sub-brand not found or does not belong to tenant'
          );
        }

        return await this.prisma.vehicleModelV2.findMany({
          where: { subBrandId, tenantId },
          orderBy: { name: 'asc' },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Create a new model
   */
  async createModel(
    modelData: CreateModelV2Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModelV2> {
    this.logOperation('createModel', {
      name: modelData.name,
      subBrandId: modelData.subBrandId,
      tenantId,
    });

    if (!modelData.name.trim()) {
      throw new ValidationError('Model name cannot be empty');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Verify sub-brand exists and belongs to tenant
        const subBrand = await this.prisma.vehicleSubBrandV2.findFirst({
          where: { id: modelData.subBrandId, tenantId },
        });

        if (!subBrand) {
          throw new NotFoundError(
            'Sub-brand not found or does not belong to tenant'
          );
        }

        try {
          return await this.prisma.vehicleModelV2.create({
            data: {
              name: modelData.name.trim(),
              subBrandId: modelData.subBrandId,
              tenantId,
              isActive: modelData.isActive ?? true,
            },
          });
        } catch (error) {
          if (
            error instanceof PrismaClientKnownRequestError &&
            error.code === 'P2002'
          ) {
            throw new ConflictError(
              `Model '${modelData.name}' already exists for this sub-brand`
            );
          }
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Update a model
   */
  async updateModel(
    modelId: string,
    updateData: { name?: string; isActive?: boolean },
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModelV2> {
    this.logOperation('updateModel', { modelId, tenantId });

    if (updateData.name !== undefined && !updateData.name.trim()) {
      throw new ValidationError('Model name cannot be empty');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Verify model exists and belongs to tenant
        const existingModel = await this.prisma.vehicleModelV2.findFirst({
          where: { id: modelId, tenantId },
        });

        if (!existingModel) {
          throw new NotFoundError(
            'Model not found or does not belong to tenant'
          );
        }

        try {
          return await this.prisma.vehicleModelV2.update({
            where: { id: modelId },
            data: {
              ...(updateData.name && { name: updateData.name.trim() }),
              ...(updateData.isActive !== undefined && {
                isActive: updateData.isActive,
              }),
            },
          });
        } catch (error) {
          if (
            error instanceof PrismaClientKnownRequestError &&
            error.code === 'P2002'
          ) {
            throw new ConflictError(
              `Model '${updateData.name}' already exists for this sub-brand`
            );
          }
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Delete a model
   */
  async deleteModel(
    modelId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('deleteModel', { modelId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Check if model has year associations
        const yearAssociationCount = await this.prisma.vehicleModelYearV2.count(
          {
            where: { modelId, tenantId },
          }
        );

        if (yearAssociationCount > 0) {
          throw new ConflictError(
            'Cannot delete model that has year associations. Remove year associations first.'
          );
        }

        const deletedCount = await this.prisma.vehicleModelV2.deleteMany({
          where: { id: modelId, tenantId },
        });

        if (deletedCount.count === 0) {
          throw new NotFoundError(
            'Model not found or does not belong to tenant'
          );
        }
      },
      BackendAuthContext
    );
  }

  // ===== MODEL-YEAR ASSOCIATION OPERATIONS =====

  /**
   * Get years associated with a model
   */
  async getYearsByModel(
    modelId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleYear[]> {
    this.logOperation('getYearsByModel', { modelId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Verify model exists and belongs to tenant
        const model = await this.prisma.vehicleModelV2.findFirst({
          where: { id: modelId, tenantId },
        });

        if (!model) {
          throw new NotFoundError(
            'Model not found or does not belong to tenant'
          );
        }

        const modelYears = await this.prisma.vehicleModelYearV2.findMany({
          where: { modelId, tenantId },
          include: {
            year: true,
          },
          orderBy: { year: { year: 'desc' } },
        });

        return modelYears.map((my) => my.year);
      },
      BackendAuthContext
    );
  }

  /**
   * Get models associated with a year
   */
  async getModelsByYear(
    yearId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModelV2[]> {
    this.logOperation('getModelsByYear', { yearId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Verify year exists and belongs to tenant
        const year = await this.prisma.vehicleYear.findFirst({
          where: { id: yearId, tenantId },
        });

        if (!year) {
          throw new NotFoundError(
            'Year not found or does not belong to tenant'
          );
        }

        const modelYears = await this.prisma.vehicleModelYearV2.findMany({
          where: { yearId, tenantId },
          include: {
            model: {
              include: {
                subBrand: {
                  include: {
                    brand: true,
                  },
                },
              },
            },
          },
          orderBy: [
            { model: { subBrand: { brand: { name: 'asc' } } } },
            { model: { subBrand: { name: 'asc' } } },
            { model: { name: 'asc' } },
          ],
        });

        return modelYears.map((my) => my.model);
      },
      BackendAuthContext
    );
  }

  /**
   * Associate a model with years
   */
  async associateModelWithYears(
    modelId: string,
    yearIds: string[],
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('associateModelWithYears', {
      modelId,
      yearIds,
      tenantId,
    });

    if (yearIds.length === 0) {
      throw new ValidationError('At least one year ID must be provided');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.transaction(async (tx) => {
          // Verify model exists and belongs to tenant
          const model = await tx.vehicleModelV2.findFirst({
            where: { id: modelId, tenantId },
          });

          if (!model) {
            throw new NotFoundError(
              'Model not found or does not belong to tenant'
            );
          }

          // Verify all years exist and belong to tenant
          const years = await tx.vehicleYear.findMany({
            where: { id: { in: yearIds }, tenantId },
          });

          if (years.length !== yearIds.length) {
            throw new NotFoundError(
              'One or more years not found or do not belong to tenant'
            );
          }

          // Get existing associations to avoid duplicates
          const existingAssociations = await tx.vehicleModelYearV2.findMany({
            where: {
              modelId,
              yearId: { in: yearIds },
              tenantId,
            },
            select: { yearId: true },
          });

          const existingYearIds = new Set(
            existingAssociations.map((a) => a.yearId)
          );
          const newYearIds = yearIds.filter(
            (yearId) => !existingYearIds.has(yearId)
          );

          // Create only new associations
          if (newYearIds.length > 0) {
            await tx.vehicleModelYearV2.createMany({
              data: newYearIds.map((yearId) => ({
                modelId,
                yearId,
                tenantId,
              })),
            });
          }
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Remove year associations from a model
   */
  async removeModelYearAssociations(
    modelId: string,
    yearIds: string[],
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('removeModelYearAssociations', {
      modelId,
      yearIds,
      tenantId,
    });

    if (yearIds.length === 0) {
      throw new ValidationError('At least one year ID must be provided');
    }

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Verify model exists and belongs to tenant
        const model = await this.prisma.vehicleModelV2.findFirst({
          where: { id: modelId, tenantId },
        });

        if (!model) {
          throw new NotFoundError(
            'Model not found or does not belong to tenant'
          );
        }

        await this.prisma.vehicleModelYearV2.deleteMany({
          where: {
            modelId,
            yearId: { in: yearIds },
            tenantId,
          },
        });
      },
      BackendAuthContext
    );
  }

  // ===== HIERARCHY QUERIES =====

  /**
   * Get full hierarchy tree for a tenant
   */
  async getFullHierarchy(
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<HierarchyTreeV2> {
    this.logOperation('getFullHierarchy', { tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Get all years
        const years = await this.prisma.vehicleYear.findMany({
          where: { tenantId },
          orderBy: { year: 'desc' },
        });

        // Get all models with their sub-brands, brands, and associated years
        const models = await this.prisma.vehicleModelV2.findMany({
          where: { tenantId },
          include: {
            subBrand: {
              include: {
                brand: true,
              },
            },
            modelYears: {
              include: {
                year: true,
              },
            },
          },
          orderBy: [
            { subBrand: { brand: { name: 'asc' } } },
            { subBrand: { name: 'asc' } },
            { name: 'asc' },
          ],
        });

        // Transform the data into the desired hierarchy structure
        const hierarchyTree: HierarchyTreeV2 = {
          years: years.map((year) => {
            // Find models available in this year
            const modelsInYear = models.filter((model) =>
              model.modelYears.some((my) => my.yearId === year.id)
            );

            return {
              ...year,
              brands: this.groupModelsByBrandWithYears(modelsInYear),
            };
          }),
        };

        return hierarchyTree;
      },
      BackendAuthContext
    );
  }

  /**
   * Get hierarchy for a specific year
   */
  async getHierarchyByYear(
    year: number,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<YearHierarchyV2 | null> {
    this.logOperation('getHierarchyByYear', { year, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        const vehicleYear = await this.prisma.vehicleYear.findFirst({
          where: { year, tenantId },
          include: {
            modelYearsV2: {
              include: {
                model: {
                  include: {
                    subBrand: {
                      include: {
                        brand: true,
                      },
                    },
                  },
                },
              },
            },
          },
        });

        if (!vehicleYear) {
          return null;
        }

        const brands = this.groupModelsByBrand(
          vehicleYear.modelYearsV2.map((my) => my.model)
        );

        return {
          year: vehicleYear,
          brands,
        };
      },
      BackendAuthContext
    );
  }

  // ===== HELPER METHODS =====

  /**
   * Group models by brand and sub-brand with year information
   */
  private groupModelsByBrandWithYears(
    models: (VehicleModelV2 & {
      subBrand: VehicleSubBrandV2 & {
        brand: VehicleBrandV2;
      };
      modelYears: (VehicleModelYearV2 & {
        year: VehicleYear;
      })[];
    })[]
  ): (VehicleBrandV2 & {
    subBrands: (VehicleSubBrandV2 & {
      models: (VehicleModelV2 & {
        years: VehicleYear[];
      })[];
    })[];
  })[] {
    const brandMap = new Map<
      string,
      VehicleBrandV2 & {
        subBrands: (VehicleSubBrandV2 & {
          models: (VehicleModelV2 & {
            years: VehicleYear[];
          })[];
        })[];
      }
    >();

    models.forEach((model) => {
      const brand = model.subBrand.brand;
      const subBrand = model.subBrand;

      if (!brandMap.has(brand.id)) {
        brandMap.set(brand.id, {
          ...brand,
          subBrands: [],
        });
      }

      const brandEntry = brandMap.get(brand.id)!;
      let subBrandEntry = brandEntry.subBrands.find(
        (sb) => sb.id === subBrand.id
      );

      if (!subBrandEntry) {
        subBrandEntry = {
          ...subBrand,
          models: [],
        };
        brandEntry.subBrands.push(subBrandEntry);
      }

      subBrandEntry.models.push({
        ...model,
        years: model.modelYears.map((my) => my.year),
      });
    });

    return Array.from(brandMap.values()).sort((a, b) =>
      a.name.localeCompare(b.name)
    );
  }

  /**
   * Group models by brand and sub-brand without year information
   */
  private groupModelsByBrand(
    models: (VehicleModelV2 & {
      subBrand: VehicleSubBrandV2 & {
        brand: VehicleBrandV2;
      };
    })[]
  ): (VehicleBrandV2 & {
    subBrands: (VehicleSubBrandV2 & {
      models: VehicleModelV2[];
    })[];
  })[] {
    const brandMap = new Map<
      string,
      VehicleBrandV2 & {
        subBrands: (VehicleSubBrandV2 & {
          models: VehicleModelV2[];
        })[];
      }
    >();

    models.forEach((model) => {
      const brand = model.subBrand.brand;
      const subBrand = model.subBrand;

      if (!brandMap.has(brand.id)) {
        brandMap.set(brand.id, {
          ...brand,
          subBrands: [],
        });
      }

      const brandEntry = brandMap.get(brand.id)!;
      let subBrandEntry = brandEntry.subBrands.find(
        (sb) => sb.id === subBrand.id
      );

      if (!subBrandEntry) {
        subBrandEntry = {
          ...subBrand,
          models: [],
        };
        brandEntry.subBrands.push(subBrandEntry);
      }

      subBrandEntry.models.push(model);
    });

    return Array.from(brandMap.values()).sort((a, b) =>
      a.name.localeCompare(b.name)
    );
  }
}
