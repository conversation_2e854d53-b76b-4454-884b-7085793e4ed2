import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import { MemoryRouter } from "react-router-dom";
import { TechNavigation } from "../../../src/components/navigation/TechNavigation";

const renderTechNavigation = (initialEntries: string[] = ["/tech"]) => {
  return render(
    <MemoryRouter initialEntries={initialEntries}>
      <TechNavigation />
    </MemoryRouter>,
  );
};

describe("TechNavigation", () => {
  it("renders all navigation items", () => {
    renderTechNavigation();

    expect(screen.getByText("Home")).toBeInTheDocument();
    expect(screen.getByText("Scan")).toBeInTheDocument();
    expect(screen.getByText("Orders")).toBeInTheDocument();
    expect(screen.getByText("Actions")).toBeInTheDocument();
  });

  it("has proper navigation structure", () => {
    renderTechNavigation();

    const nav = screen.getByRole("navigation");
    expect(nav).toHaveClass(
      "bg-white/95",
      "backdrop-blur-sm",
      "border-t",
      "border-primary-100/50",
      "sticky",
      "bottom-0",
      "z-50",
      "shadow-lg",
    );

    // Check grid layout for 4 items
    const gridContainer = nav.querySelector(".grid-cols-4");
    expect(gridContainer).toBeInTheDocument();
  });

  it("highlights active link correctly for home route", () => {
    renderTechNavigation(["/tech"]);

    const homeLink = screen.getByRole("link", { name: /home/<USER>
    expect(homeLink).toHaveClass(
      "text-primary-700",
      "bg-gradient-to-t",
      "from-primary-50",
      "to-primary-25",
    );

    const scanLink = screen.getByRole("link", { name: /scan/i });
    expect(scanLink).toHaveClass("text-gray-600");
  });

  it("highlights active link correctly for scan route", () => {
    renderTechNavigation(["/tech/scan"]);

    const scanLink = screen.getByRole("link", { name: /scan/i });
    expect(scanLink).toHaveClass(
      "text-primary-700",
      "bg-gradient-to-t",
      "from-primary-50",
      "to-primary-25",
    );

    const homeLink = screen.getByRole("link", { name: /home/<USER>
    expect(homeLink).toHaveClass("text-gray-600");
  });

  it("highlights active link correctly for work orders route", () => {
    renderTechNavigation(["/tech/work-orders"]);

    const ordersLink = screen.getByRole("link", { name: /orders/i });
    expect(ordersLink).toHaveClass(
      "text-primary-700",
      "bg-gradient-to-t",
      "from-primary-50",
      "to-primary-25",
    );

    const homeLink = screen.getByRole("link", { name: /home/<USER>
    expect(homeLink).toHaveClass("text-gray-600");
  });

  it("highlights active link correctly for quick actions route", () => {
    renderTechNavigation(["/tech/quick-actions"]);

    const actionsLink = screen.getByRole("link", { name: /actions/i });
    expect(actionsLink).toHaveClass(
      "text-primary-700",
      "bg-gradient-to-t",
      "from-primary-50",
      "to-primary-25",
    );

    const homeLink = screen.getByRole("link", { name: /home/<USER>
    expect(homeLink).toHaveClass("text-gray-600");
  });

  it("has correct href attributes for all links", () => {
    renderTechNavigation();

    expect(screen.getByRole("link", { name: /home/<USER>
      "href",
      "/tech",
    );
    expect(screen.getByRole("link", { name: /scan/i })).toHaveAttribute(
      "href",
      "/tech/scan",
    );
    expect(screen.getByRole("link", { name: /orders/i })).toHaveAttribute(
      "href",
      "/tech/work-orders",
    );
    expect(screen.getByRole("link", { name: /actions/i })).toHaveAttribute(
      "href",
      "/tech/quick-actions",
    );
  });

  it("has proper touch target sizes for mobile", () => {
    renderTechNavigation();

    const links = screen.getAllByRole("link");
    links.forEach((link) => {
      expect(link).toHaveClass("min-h-[56px]"); // Enhanced 56px minimum touch target for better mobile UX
    });
  });

  it("has proper icon and label structure", () => {
    renderTechNavigation();

    const homeLink = screen.getByRole("link", { name: /home/<USER>

    // Check for icon (svg element)
    const icon = homeLink.querySelector("svg");
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveClass("h-6", "w-6");

    // Check for label with enhanced styling
    const label = screen.getByText("Home");
    expect(label).toHaveClass("text-xs", "font-semibold", "leading-none");
  });

  it("has hover states for inactive links", () => {
    renderTechNavigation(["/tech/scan"]); // Make scan active, others inactive

    const homeLink = screen.getByRole("link", { name: /home/<USER>
    expect(homeLink).toHaveClass(
      "hover:text-primary-600",
      "hover:bg-primary-50/50",
    );
  });

  it("handles exact matching for home route", () => {
    // Test that /tech/something doesn't highlight home
    renderTechNavigation(["/tech/scan"]);

    const homeLink = screen.getByRole("link", { name: /home/<USER>
    expect(homeLink).not.toHaveClass("text-primary-700", "bg-gradient-to-t");
    expect(homeLink).toHaveClass("text-gray-600");
  });

  it("handles sub-routes correctly", () => {
    // Test that /tech/work-orders/123 still highlights work orders
    renderTechNavigation(["/tech/work-orders/123"]);

    const ordersLink = screen.getByRole("link", { name: /orders/i });
    expect(ordersLink).toHaveClass(
      "text-primary-700",
      "bg-gradient-to-t",
      "from-primary-50",
      "to-primary-25",
    );
  });

  it("has proper accessibility attributes", () => {
    renderTechNavigation();

    const nav = screen.getByRole("navigation");
    expect(nav).toBeInTheDocument();

    // All navigation items should be links
    const links = screen.getAllByRole("link");
    expect(links).toHaveLength(4);

    // Each link should have accessible text
    expect(screen.getByRole("link", { name: /home/<USER>
    expect(screen.getByRole("link", { name: /scan/i })).toBeInTheDocument();
    expect(screen.getByRole("link", { name: /orders/i })).toBeInTheDocument();
    expect(screen.getByRole("link", { name: /actions/i })).toBeInTheDocument();
  });
});
