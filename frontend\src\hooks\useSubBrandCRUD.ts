/**
 * Sub-Brand specific CRUD hook that extends useBaseCRUD with brand selection logic
 * Handles the complexity of brand association while reusing shared validation and error handling
 */

import React, { useState, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import type { VehicleSubBrandV3, VehicleBrandV3 } from "../services/api-client";
import type { SubBrandConfig } from "../components/vehicle-hierarchy-v3/configs/entity-configs";
import { validateEntityName, sanitizeEntityName } from "./utils/entity-validation.utils";

export interface SubBrandCRUDReturn {
  // Data state
  subBrands: VehicleSubBrandV3[];
  brands: VehicleBrandV3[];
  sortedSubBrands: VehicleSubBrandV3[];
  isLoading: boolean;
  error: string | null;
  
  // Form state
  newSubBrandName: string;
  selectedBrandId: string;
  editingSubBrand: VehicleSubBrandV3 | null;
  editSubBrandName: string;
  editSubBrandActive: boolean;
  editSelectedBrandId: string;
  deleteConfirmSubBrand: VehicleSubBrandV3 | null;
  
  // Operation state
  isPending: boolean;
  currentOperation: 'create' | 'update' | 'delete' | null;
  
  // State setters
  setNewSubBrandName: (name: string) => void;
  setSelectedBrandId: (brandId: string) => void;
  setEditingSubBrand: (subBrand: VehicleSubBrandV3 | null) => void;
  setEditSubBrandName: (name: string) => void;
  setEditSubBrandActive: (active: boolean) => void;
  setEditSelectedBrandId: (brandId: string) => void;
  setDeleteConfirmSubBrand: (subBrand: VehicleSubBrandV3 | null) => void;
  
  // Action handlers
  handleCreate: () => void;
  handleEdit: (subBrand: VehicleSubBrandV3) => void;
  handleUpdate: () => void;
  handleDelete: (subBrand: VehicleSubBrandV3) => void;
  confirmDelete: () => void;
  handleClose: () => void;
  
  // Validation
  validateName: (name: string) => string | null;
}

/**
 * Sub-Brand CRUD hook that extends the base patterns with brand selection
 */
export const useSubBrandCRUD = (
  config: SubBrandConfig,
  isOpen: boolean
): SubBrandCRUDReturn => {
  const queryClient = useQueryClient();

  // ===== STATE MANAGEMENT =====
  const [newSubBrandName, setNewSubBrandName] = useState("");
  const [selectedBrandId, setSelectedBrandId] = useState("");
  const [editingSubBrand, setEditingSubBrand] = useState<VehicleSubBrandV3 | null>(null);
  const [editSubBrandName, setEditSubBrandName] = useState("");
  const [editSubBrandActive, setEditSubBrandActive] = useState(true);
  const [editSelectedBrandId, setEditSelectedBrandId] = useState("");
  const [deleteConfirmSubBrand, setDeleteConfirmSubBrand] = useState<VehicleSubBrandV3 | null>(null);
  const [currentOperation, setCurrentOperation] = useState<'create' | 'update' | 'delete' | null>(null);
  const [error, setError] = useState<string | null>(null);

  // ===== DATA FETCHING =====
  // Fetch all sub-brands with brand information
  const {
    data: subBrandsResponse,
    isLoading: isLoadingSubBrands,
    error: subBrandsError,
  } = useQuery({
    queryKey: [config.queryKey],
    queryFn: config.apiMethods.getAllSubBrands,
    enabled: isOpen,
  });

  // Fetch all brands for dropdown
  const {
    data: brandsResponse,
    isLoading: isLoadingBrands,
    error: brandsError,
  } = useQuery({
    queryKey: ["vehicle-brands-v3"],
    queryFn: config.apiMethods.getAllBrands,
    enabled: isOpen,
  });

  const subBrands = useMemo(() => subBrandsResponse?.data || [], [subBrandsResponse?.data]);
  const brands = useMemo(() => brandsResponse?.data || [], [brandsResponse?.data]);
  const isLoading = isLoadingSubBrands || isLoadingBrands;
  const queryError = subBrandsError || brandsError;

  // Handle query errors
  React.useEffect(() => {
    if (queryError) {
      const errorMessage = queryError instanceof Error ? queryError.message : 'Failed to fetch data';
      setError(errorMessage);
      handleError(queryError instanceof Error ? queryError : new Error(errorMessage), 'fetch');
    }
  }, [queryError]);

  // Sort sub-brands by brand name, then by sub-brand name
  const sortedSubBrands = useMemo(() => {
    return [...subBrands].sort((a, b) => {
      const brandA = a.brand?.name || "";
      const brandB = b.brand?.name || "";
      if (brandA !== brandB) {
        return brandA.localeCompare(brandB);
      }
      return a.name.localeCompare(b.name);
    });
  }, [subBrands]);

  // ===== MUTATIONS =====
  const createMutation = useMutation({
    mutationFn: (data: { brandId: string; name: string }) => {
      setCurrentOperation('create');
      return config.apiMethods.createSubBrand(data.brandId, { name: sanitizeEntityName(data.name) });
    },
    onSuccess: () => {
      handleSuccess(`${config.entityName} created successfully`);
      queryClient.invalidateQueries({ queryKey: [config.queryKey] });
      setNewSubBrandName("");
      setSelectedBrandId("");
      setCurrentOperation(null);
    },
    onError: (error: Error) => {
      handleError(error, 'create');
      setCurrentOperation(null);
    },
  });

  const updateMutation = useMutation({
    mutationFn: (data: { brandId: string; subBrandId: string; name: string; isActive: boolean; newBrandId?: string }) => {
      setCurrentOperation('update');
      return config.apiMethods.updateSubBrand(data.brandId, data.subBrandId, {
        name: sanitizeEntityName(data.name),
        brandId: data.newBrandId, // Send the new brand ID if it's being changed
        isActive: data.isActive,
      });
    },
    onSuccess: () => {
      handleSuccess(`${config.entityName} updated successfully`);
      queryClient.invalidateQueries({ queryKey: [config.queryKey] });
      setEditingSubBrand(null);
      setEditSubBrandName("");
      setEditSubBrandActive(true);
      setEditSelectedBrandId("");
      setCurrentOperation(null);
    },
    onError: (error: Error) => {
      handleError(error, 'update');
      setCurrentOperation(null);
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (data: { brandId: string; subBrandId: string }) => {
      setCurrentOperation('delete');
      return config.apiMethods.deleteSubBrand(data.brandId, data.subBrandId);
    },
    onSuccess: () => {
      handleSuccess(`${config.entityName} deleted successfully`);
      queryClient.invalidateQueries({ queryKey: [config.queryKey] });
      setDeleteConfirmSubBrand(null);
      setCurrentOperation(null);
    },
    onError: (error: Error) => {
      handleError(error, 'delete');
      setCurrentOperation(null);
    },
  });

  // ===== COMPUTED STATE =====
  const isPending = createMutation.isPending || updateMutation.isPending || deleteMutation.isPending;

  // ===== UTILITY FUNCTIONS =====
  const validateName = (name: string): string | null => {
    return validateEntityName(name, config.validation);
  };

  const handleSuccess = (message: string) => {
    toast.success(message);
    setError(null);
  };

  const handleError = (error: Error, operation: string) => {
    const message = error.message || `Failed to ${operation} ${config.entityName.toLowerCase()}`;
    toast.error(message);
    setError(message);
  };

  // ===== ACTION HANDLERS =====
  const handleCreate = () => {
    const nameError = validateName(newSubBrandName);
    if (nameError) {
      toast.error(nameError);
      return;
    }
    if (!selectedBrandId) {
      toast.error("Please select a brand");
      return;
    }
    createMutation.mutate({ brandId: selectedBrandId, name: newSubBrandName.trim() });
  };

  const handleEdit = (subBrand: VehicleSubBrandV3) => {
    setEditingSubBrand(subBrand);
    setEditSubBrandName(subBrand.name);
    setEditSubBrandActive(subBrand.isActive);
    setEditSelectedBrandId(subBrand.brandId);
  };

  const handleUpdate = () => {
    if (!editingSubBrand) return;

    const nameError = validateName(editSubBrandName);
    if (nameError) {
      toast.error(nameError);
      return;
    }
    if (!editSelectedBrandId) {
      toast.error("Please select a brand");
      return;
    }

    updateMutation.mutate({
      brandId: editingSubBrand.brandId, // Original brand ID for the API path
      subBrandId: editingSubBrand.id,
      name: editSubBrandName.trim(),
      isActive: editSubBrandActive,
      newBrandId: editSelectedBrandId !== editingSubBrand.brandId ? editSelectedBrandId : undefined, // Only send if changed
    });
  };

  const handleDelete = (subBrand: VehicleSubBrandV3) => {
    setDeleteConfirmSubBrand(subBrand);
  };

  const confirmDelete = () => {
    if (!deleteConfirmSubBrand) return;
    deleteMutation.mutate({
      brandId: deleteConfirmSubBrand.brandId,
      subBrandId: deleteConfirmSubBrand.id,
    });
  };

  const handleClose = () => {
    if (isPending) return;
    setNewSubBrandName("");
    setSelectedBrandId("");
    setEditingSubBrand(null);
    setEditSubBrandName("");
    setEditSubBrandActive(true);
    setEditSelectedBrandId("");
    setDeleteConfirmSubBrand(null);
    setError(null);
    setCurrentOperation(null);
  };

  // ===== RETURN INTERFACE =====
  return {
    // Data state
    subBrands,
    brands,
    sortedSubBrands,
    isLoading,
    error,
    
    // Form state
    newSubBrandName,
    selectedBrandId,
    editingSubBrand,
    editSubBrandName,
    editSubBrandActive,
    editSelectedBrandId,
    deleteConfirmSubBrand,
    
    // Operation state
    isPending,
    currentOperation,
    
    // State setters
    setNewSubBrandName,
    setSelectedBrandId,
    setEditingSubBrand,
    setEditSubBrandName,
    setEditSubBrandActive,
    setEditSelectedBrandId,
    setDeleteConfirmSubBrand,
    
    // Action handlers
    handleCreate,
    handleEdit,
    handleUpdate,
    handleDelete,
    confirmDelete,
    handleClose,
    
    // Validation
    validateName,
  };
};
