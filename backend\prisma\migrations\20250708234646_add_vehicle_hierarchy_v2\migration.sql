-- CreateTable
CREATE TABLE "vehicle_brands_v2" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vehicle_brands_v2_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "vehicle_sub_brands_v2" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "brandId" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vehicle_sub_brands_v2_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "vehicle_models_v2" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "subBrandId" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vehicle_models_v2_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "vehicle_model_years_v2" (
    "modelId" TEXT NOT NULL,
    "yearId" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vehicle_model_years_v2_pkey" PRIMARY KEY ("modelId","yearId")
);

-- CreateIndex
CREATE INDEX "vehicle_brands_v2_tenantId_idx" ON "vehicle_brands_v2"("tenantId");

-- CreateIndex
CREATE INDEX "vehicle_brands_v2_name_idx" ON "vehicle_brands_v2"("name");

-- CreateIndex
CREATE INDEX "vehicle_brands_v2_isActive_idx" ON "vehicle_brands_v2"("isActive");

-- CreateIndex
CREATE UNIQUE INDEX "vehicle_brands_v2_name_tenantId_key" ON "vehicle_brands_v2"("name", "tenantId");

-- CreateIndex
CREATE INDEX "vehicle_sub_brands_v2_tenantId_idx" ON "vehicle_sub_brands_v2"("tenantId");

-- CreateIndex
CREATE INDEX "vehicle_sub_brands_v2_brandId_idx" ON "vehicle_sub_brands_v2"("brandId");

-- CreateIndex
CREATE INDEX "vehicle_sub_brands_v2_name_idx" ON "vehicle_sub_brands_v2"("name");

-- CreateIndex
CREATE INDEX "vehicle_sub_brands_v2_isActive_idx" ON "vehicle_sub_brands_v2"("isActive");

-- CreateIndex
CREATE UNIQUE INDEX "vehicle_sub_brands_v2_name_brandId_tenantId_key" ON "vehicle_sub_brands_v2"("name", "brandId", "tenantId");

-- CreateIndex
CREATE INDEX "vehicle_models_v2_tenantId_idx" ON "vehicle_models_v2"("tenantId");

-- CreateIndex
CREATE INDEX "vehicle_models_v2_subBrandId_idx" ON "vehicle_models_v2"("subBrandId");

-- CreateIndex
CREATE INDEX "vehicle_models_v2_name_idx" ON "vehicle_models_v2"("name");

-- CreateIndex
CREATE INDEX "vehicle_models_v2_isActive_idx" ON "vehicle_models_v2"("isActive");

-- CreateIndex
CREATE UNIQUE INDEX "vehicle_models_v2_name_subBrandId_tenantId_key" ON "vehicle_models_v2"("name", "subBrandId", "tenantId");

-- CreateIndex
CREATE INDEX "vehicle_model_years_v2_tenantId_idx" ON "vehicle_model_years_v2"("tenantId");

-- CreateIndex
CREATE INDEX "vehicle_model_years_v2_modelId_idx" ON "vehicle_model_years_v2"("modelId");

-- CreateIndex
CREATE INDEX "vehicle_model_years_v2_yearId_idx" ON "vehicle_model_years_v2"("yearId");

-- AddForeignKey
ALTER TABLE "vehicle_brands_v2" ADD CONSTRAINT "vehicle_brands_v2_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_sub_brands_v2" ADD CONSTRAINT "vehicle_sub_brands_v2_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_sub_brands_v2" ADD CONSTRAINT "vehicle_sub_brands_v2_brandId_fkey" FOREIGN KEY ("brandId") REFERENCES "vehicle_brands_v2"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_models_v2" ADD CONSTRAINT "vehicle_models_v2_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_models_v2" ADD CONSTRAINT "vehicle_models_v2_subBrandId_fkey" FOREIGN KEY ("subBrandId") REFERENCES "vehicle_sub_brands_v2"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_model_years_v2" ADD CONSTRAINT "vehicle_model_years_v2_modelId_fkey" FOREIGN KEY ("modelId") REFERENCES "vehicle_models_v2"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_model_years_v2" ADD CONSTRAINT "vehicle_model_years_v2_yearId_fkey" FOREIGN KEY ("yearId") REFERENCES "vehicle_years"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_model_years_v2" ADD CONSTRAINT "vehicle_model_years_v2_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;
