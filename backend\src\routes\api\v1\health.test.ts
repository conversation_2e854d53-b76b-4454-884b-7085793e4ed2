import express from 'express';
import request from 'supertest';

import { createHealthRouter } from './health.js';

describe('Health Route', () => {
  // Helper function to create a test app with specific mocks
  function createTestAppWithMocks(healthCheckResult: boolean = true) {
    const mockPrismaService = {
      healthCheck: jest.fn().mockResolvedValue(healthCheckResult),
    };

    const mockLogger = {
      error: jest.fn(),
      warn: jest.fn(),
      info: jest.fn(),
      debug: jest.fn(),
    };

    const mockMiddlewareFactory = {
      createActivityTracking: jest
        .fn()
        .mockReturnValue((req: any, res: any, next: any) => next()),
    };

    const healthRouter = createHealthRouter({
      prismaService: mockPrismaService as any,
      middlewareFactory: mockMiddlewareFactory as any,
      logger: mockLogger as any,
    });

    const app = express();
    app.use(express.json());
    app.use('/api/v1/health', healthRouter);

    return { app, mockPrismaService, mockLogger, mockMiddlewareFactory };
  }

  describe('GET /api/v1/health', () => {
    it('should return health status when database is connected', async () => {
      // Arrange
      const { app } = createTestAppWithMocks(true);

      // Act
      const response = await request(app).get('/api/v1/health').expect(200);

      // Assert
      expect(response.body).toMatchObject({
        data: {
          status: 'ok',
          environment: 'test',
          database: 'connected',
        },
      });
      expect(response.body.timestamp).toBeDefined();
    });

    it('should return disconnected status when database health check fails', async () => {
      // Arrange
      const { app } = createTestAppWithMocks(false);

      // Act
      const response = await request(app).get('/api/v1/health').expect(200);

      // Assert
      expect(response.body).toMatchObject({
        data: {
          status: 'ok',
          environment: 'test',
          database: 'disconnected',
        },
      });
      expect(response.body.timestamp).toBeDefined();
    });

    it('should return error status when database health check throws exception', async () => {
      // Arrange
      const mockPrismaService = {
        healthCheck: jest
          .fn()
          .mockRejectedValue(new Error('Database connection failed')),
      };

      const mockLogger = {
        error: jest.fn(),
        warn: jest.fn(),
        info: jest.fn(),
        debug: jest.fn(),
      };

      const mockMiddlewareFactory = {
        createActivityTracking: jest
          .fn()
          .mockReturnValue((req: any, res: any, next: any) => next()),
      };

      const healthRouter = createHealthRouter({
        prismaService: mockPrismaService as any,
        middlewareFactory: mockMiddlewareFactory as any,
        logger: mockLogger as any,
      });

      const app = express();
      app.use(express.json());
      app.use('/api/v1/health', healthRouter);

      // Act
      const response = await request(app).get('/api/v1/health').expect(200);

      // Assert
      expect(response.body).toMatchObject({
        data: {
          status: 'ok',
          environment: 'test',
          database: 'error',
        },
      });
      expect(response.body.timestamp).toBeDefined();
      expect(mockLogger.error).toHaveBeenCalledWith('Health check failed', {
        error: expect.any(Error),
      });
    });
  });
});
