import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import { Tooltip } from "../../../src/components/atoms/Tooltip";

describe("Tooltip", () => {
  it("renders children correctly", () => {
    render(
      <Tooltip content="Test tooltip">
        <button>Test Button</button>
      </Tooltip>,
    );

    expect(screen.getByText("Test Button")).toBeInTheDocument();
  });

  it("renders with different positions", () => {
    render(
      <Tooltip content="Test tooltip" position="bottom">
        <span>Test Content</span>
      </Tooltip>,
    );

    expect(screen.getByText("Test Content")).toBeInTheDocument();
  });

  it("applies custom className", () => {
    render(
      <Tooltip content="Test tooltip" className="custom-class">
        <span>Test Content</span>
      </Tooltip>,
    );

    const tooltipContainer = screen.getByText("Test Content").parentElement;
    expect(tooltipContainer).toHaveClass("custom-class");
  });
});
