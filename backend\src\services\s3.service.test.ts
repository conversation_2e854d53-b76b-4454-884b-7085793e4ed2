import { ValidationError } from '../types/error.types.js';
import { Logger } from '../utils/logger.js';

import { S3Service } from './s3.service.js';

// Mock the AWS SDK
jest.mock('@aws-sdk/client-s3');
jest.mock('@aws-sdk/s3-request-presigner');

describe('S3Service', () => {
  let s3Service: S3Service;
  let mockLogger: Logger;

  beforeEach(() => {
    // Create mock logger
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    } as any;

    s3Service = new S3Service(mockLogger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('generatePresignedUploadUrl', () => {
    const validRequest = {
      fileName: 'test-document.pdf',
      fileSize: 1024 * 1024, // 1MB
      mimeType: 'application/pdf',
      tenantId: 'tenant-123',
      documentId: 'doc-456',
    };

    it('should validate file name', async () => {
      const invalidRequest = { ...validRequest, fileName: '' };

      await expect(
        s3Service.generatePresignedUploadUrl(invalidRequest)
      ).rejects.toThrow(ValidationError);
    });

    it('should validate MIME type', async () => {
      const invalidRequest = {
        ...validRequest,
        mimeType: 'application/x-malware',
      };

      await expect(
        s3Service.generatePresignedUploadUrl(invalidRequest)
      ).rejects.toThrow(ValidationError);
    });

    it('should validate file size for PDF', async () => {
      const oversizedRequest = {
        ...validRequest,
        fileSize: 60 * 1024 * 1024, // 60MB (exceeds 50MB limit for PDF)
      };

      await expect(
        s3Service.generatePresignedUploadUrl(oversizedRequest)
      ).rejects.toThrow(ValidationError);
    });

    it('should validate file size for images', async () => {
      const oversizedRequest = {
        ...validRequest,
        mimeType: 'image/jpeg',
        fileSize: 15 * 1024 * 1024, // 15MB (exceeds 10MB limit for images)
      };

      await expect(
        s3Service.generatePresignedUploadUrl(oversizedRequest)
      ).rejects.toThrow(ValidationError);
    });

    it('should reject zero-sized files', async () => {
      const zeroSizeRequest = { ...validRequest, fileSize: 0 };

      await expect(
        s3Service.generatePresignedUploadUrl(zeroSizeRequest)
      ).rejects.toThrow(ValidationError);
    });

    it('should accept valid file types', async () => {
      const validTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/html',
        'text/csv',
        'image/jpeg',
        'image/png',
      ];

      for (const mimeType of validTypes) {
        const request = { ...validRequest, mimeType };
        // This will fail due to mocked AWS SDK, but should pass validation
        try {
          await s3Service.generatePresignedUploadUrl(request);
        } catch (error) {
          // Expected to fail due to mocked AWS SDK, but not due to validation
          expect(error).not.toBeInstanceOf(ValidationError);
        }
      }
    });
  });

  describe('generatePresignedDownloadUrl', () => {
    it('should validate S3 key format', async () => {
      const invalidRequest = {
        s3Key: 'invalid-key-format',
        fileName: 'test.pdf',
      };

      await expect(
        s3Service.generatePresignedDownloadUrl(invalidRequest)
      ).rejects.toThrow(ValidationError);
    });

    it('should accept valid S3 key format', async () => {
      const validRequest = {
        s3Key: 'tenant-123/doc456-test_document.pdf',
        fileName: 'test.pdf',
      };

      // This will fail due to mocked AWS SDK, but should pass validation
      try {
        await s3Service.generatePresignedDownloadUrl(validRequest);
      } catch (error) {
        // Expected to fail due to mocked AWS SDK, but not due to validation
        expect(error).not.toBeInstanceOf(ValidationError);
      }
    });
  });

  describe('S3 key generation', () => {
    it('should generate proper S3 key with tenant isolation', () => {
      // Access private method through type assertion for testing
      const s3ServiceAny = s3Service as any;
      const s3Key = s3ServiceAny.generateS3Key(
        '123',
        'doc-456',
        'test document.pdf'
      );

      expect(s3Key).toBe('tenant-123/doc-456-test_document.pdf');
      expect(s3Key).toMatch(/^tenant-[^/]+\/[^/]+-[^/]+$/);
    });

    it('should sanitize file names', () => {
      const s3ServiceAny = s3Service as any;
      const sanitized = s3ServiceAny.sanitizeFileName(
        'test@#$%^&*()document!.pdf'
      );

      expect(sanitized).toBe('test_document_.pdf');
      expect(sanitized).not.toMatch(/[^a-zA-Z0-9._-]/);
    });
  });

  describe('file validation', () => {
    it('should validate file size limits correctly', () => {
      const s3ServiceAny = s3Service as any;

      // Test PDF limit (50MB)
      expect(() =>
        s3ServiceAny.validateFile(
          'test.pdf',
          50 * 1024 * 1024,
          'application/pdf'
        )
      ).not.toThrow();
      expect(() =>
        s3ServiceAny.validateFile(
          'test.pdf',
          51 * 1024 * 1024,
          'application/pdf'
        )
      ).toThrow(ValidationError);

      // Test image limit (10MB)
      expect(() =>
        s3ServiceAny.validateFile('test.jpg', 10 * 1024 * 1024, 'image/jpeg')
      ).not.toThrow();
      expect(() =>
        s3ServiceAny.validateFile('test.jpg', 11 * 1024 * 1024, 'image/jpeg')
      ).toThrow(ValidationError);

      // Test Office document limit (25MB)
      expect(() =>
        s3ServiceAny.validateFile(
          'test.docx',
          25 * 1024 * 1024,
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
      ).not.toThrow();
      expect(() =>
        s3ServiceAny.validateFile(
          'test.docx',
          26 * 1024 * 1024,
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
      ).toThrow(ValidationError);
    });
  });

  describe('logging', () => {
    it('should log operations', () => {
      const s3ServiceAny = s3Service as any;
      s3ServiceAny.logOperation('test-operation', { key: 'value' });

      expect(mockLogger.info).toHaveBeenCalledWith(
        'S3Service: test-operation',
        { key: 'value' }
      );
    });

    it('should log errors', () => {
      const s3ServiceAny = s3Service as any;
      const error = new Error('Test error');
      s3ServiceAny.logError('test-operation', error, { key: 'value' });

      expect(mockLogger.error).toHaveBeenCalledWith(
        'S3Service: test-operation failed',
        {
          error: 'Test error',
          stack: error.stack,
          key: 'value',
        }
      );
    });
  });
});
