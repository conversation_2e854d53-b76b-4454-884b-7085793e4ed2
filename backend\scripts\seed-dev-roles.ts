#!/usr/bin/env ts-node

/**
 * Development Role Seeding Script
 *
 * This script assigns roles to existing users in the development database:
 * - First user becomes SystemAdmin
 * - Second user becomes CompanyAdmin for their tenant
 *
 * Usage: npm run seed:dev:roles
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Simple logger for this script
const logger = {
  info: (message: string, ...args: any[]) =>
    console.log(`[INFO] ${message}`, ...args),
  warn: (message: string, ...args: any[]) =>
    console.warn(`[WARN] ${message}`, ...args),
  error: (message: string, ...args: any[]) =>
    console.error(`[ERROR] ${message}`, ...args),
};

interface UserWithTenant {
  id: string;
  clerkId: string;
  email: string;
  tenantId: string;
  tenant: {
    id: string;
    name: string;
    slug: string;
  };
}

async function seedDevRoles() {
  try {
    logger.info('🌱 Starting development role seeding...');

    // Get all users with their tenant information
    const users = (await prisma.user.findMany({
      include: {
        tenant: true,
      },
      orderBy: {
        createdAt: 'asc', // First created user will be SystemAdmin
      },
    })) as UserWithTenant[];

    if (users.length === 0) {
      logger.warn('⚠️  No users found in database. Please create users first.');
      return;
    }

    logger.info(`📊 Found ${users.length} users in database`);

    // Get or create roles
    const systemAdminRole = await prisma.role.upsert({
      where: { name: 'System Admin' },
      update: {},
      create: {
        name: 'System Admin',
        description: 'System administrator with global access',
        type: 'SYSTEM_ADMIN',
        isSystemRole: true,
      },
    });

    const companyAdminRole = await prisma.role.upsert({
      where: { name: 'Company Admin' },
      update: {},
      create: {
        name: 'Company Admin',
        description: 'Company administrator with tenant-scoped access',
        type: 'COMPANY_ADMIN',
        isSystemRole: false,
      },
    });

    logger.info('✅ Roles ensured in database');

    // Assign SystemAdmin to first user
    const firstUser = users[0];

    // Check if user already has SystemAdmin role
    const existingSystemAdminAssignment = await prisma.userRole.findFirst({
      where: {
        userId: firstUser.id,
        roleId: systemAdminRole.id,
      },
    });

    if (!existingSystemAdminAssignment) {
      await prisma.userRole.create({
        data: {
          userId: firstUser.id,
          roleId: systemAdminRole.id,
          tenantId: null, // SystemAdmin is global
        },
      });
      logger.info(
        `🔑 Assigned SystemAdmin role to user: ${firstUser.email} (${firstUser.clerkId})`
      );
    } else {
      logger.info(`✅ User ${firstUser.email} already has SystemAdmin role`);
    }

    // Assign CompanyAdmin to second user (if exists)
    if (users.length > 1) {
      const secondUser = users[1];

      // Check if user already has CompanyAdmin role for their tenant
      const existingCompanyAdminAssignment = await prisma.userRole.findFirst({
        where: {
          userId: secondUser.id,
          roleId: companyAdminRole.id,
          tenantId: secondUser.tenantId,
        },
      });

      if (!existingCompanyAdminAssignment) {
        await prisma.userRole.create({
          data: {
            userId: secondUser.id,
            roleId: companyAdminRole.id,
            tenantId: secondUser.tenantId,
          },
        });
        logger.info(
          `🏢 Assigned CompanyAdmin role to user: ${secondUser.email} (${secondUser.clerkId}) for tenant: ${secondUser.tenant.name}`
        );
      } else {
        logger.info(
          `✅ User ${secondUser.email} already has CompanyAdmin role for their tenant`
        );
      }
    }

    // Display final role assignments
    logger.info('\n📋 Final Role Assignments:');

    for (const user of users) {
      const userRoles = await prisma.userRole.findMany({
        where: { userId: user.id },
        include: {
          role: true,
          tenant: true,
        },
      });

      logger.info(`\n👤 User: ${user.email} (${user.clerkId})`);
      logger.info(`   Tenant: ${user.tenant.name} (${user.tenant.slug})`);

      if (userRoles.length === 0) {
        logger.info('   Roles: None assigned');
      } else {
        userRoles.forEach((userRole) => {
          const scope = userRole.tenantId
            ? `tenant: ${userRole.tenant?.name}`
            : 'global';
          logger.info(`   Role: ${userRole.role.name} (${scope})`);
        });
      }
    }

    logger.info('\n🎉 Development role seeding completed successfully!');
  } catch (error) {
    logger.error('❌ Error during development role seeding:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedDevRoles()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}

export { seedDevRoles };
