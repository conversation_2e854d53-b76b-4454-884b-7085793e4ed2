import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  SystemAdminOnly,
} from "../../components";
import { useAuth } from "../../hooks/useAuth";
import { usePermissions } from "../../hooks/usePermissions";
import { Shield, Users, Plus } from "lucide-react";

export const RolesPage: React.FC = () => {
  const { userProfile, isLoading: authLoading } = useAuth();
  const { isSystemAdmin, isCompanyAdmin } = usePermissions();

  if (authLoading) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-gray-900">Role Management</h1>
        <div className="animate-pulse">
          <div className="h-32 bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Role Management</h1>
          <p className="text-gray-600 mt-1">
            Manage roles and permissions across the system
          </p>
        </div>
        <SystemAdminOnly>
          <Button
            onClick={() => alert("Role management functionality coming soon!")}
            className="flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Create Role</span>
          </Button>
        </SystemAdminOnly>
      </div>

      <Alert variant="info">
        <div className="space-y-2">
          <h3 className="font-medium">Role Management Interface</h3>
          <p className="text-sm">
            This comprehensive role management system will provide:
          </p>
          <ul className="text-sm list-disc list-inside space-y-1 ml-4">
            <li>Create, edit, and delete custom roles</li>
            <li>Assign and remove roles from users</li>
            <li>Manage role permissions and access levels</li>
            <li>System role protection and validation</li>
            <li>Cross-tenant role management for System Admins</li>
          </ul>
        </div>
      </Alert>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader title="Role Creation" />
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-primary-50 rounded-full">
                <Shield className="h-5 w-5 text-primary-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Custom Roles</h3>
                <p className="text-sm text-gray-500">
                  Create tailored roles for your organization
                </p>
              </div>
            </div>
            <ul className="text-sm text-gray-600 list-disc list-inside space-y-1">
              <li>Define role names and descriptions</li>
              <li>Set role types and permissions</li>
              <li>Tenant-specific or system-wide roles</li>
            </ul>
            <div className="pt-2">
              <Badge variant="warning">Coming Soon</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader title="User Assignment" />
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-primary-50 rounded-full">
                <Users className="h-5 w-5 text-primary-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Role Assignment</h3>
                <p className="text-sm text-gray-500">
                  Assign roles to users efficiently
                </p>
              </div>
            </div>
            <ul className="text-sm text-gray-600 list-disc list-inside space-y-1">
              <li>Assign multiple roles to users</li>
              <li>Remove roles with validation</li>
              <li>Bulk role assignment operations</li>
            </ul>
            <div className="pt-2">
              <Badge variant="warning">Coming Soon</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader title="Permission Management" />
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-primary-50 rounded-full">
                <Shield className="h-5 w-5 text-primary-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Access Control</h3>
                <p className="text-sm text-gray-500">
                  Fine-grained permission management
                </p>
              </div>
            </div>
            <ul className="text-sm text-gray-600 list-disc list-inside space-y-1">
              <li>Resource-based permissions</li>
              <li>Action-level access control</li>
              <li>Permission inheritance and validation</li>
            </ul>
            <div className="pt-2">
              <Badge variant="warning">Coming Soon</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {userProfile && (
        <Card>
          <CardHeader title="Your Current Roles" />
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Roles currently assigned to your account:
              </p>
              <div className="flex flex-wrap gap-2">
                {userProfile.roles && userProfile.roles.length > 0 ? (
                  userProfile.roles.map((role: any, index: number) => ( // eslint-disable-line @typescript-eslint/no-explicit-any
                    <Badge key={index} variant="primary">
                      {role.role.name}
                    </Badge>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">No roles assigned</p>
                )}
              </div>
              {isSystemAdmin && (
                <div className="pt-4 border-t border-gray-200">
                  <Badge variant="admin">System Administrator</Badge>
                  <p className="text-sm text-gray-600 mt-2">
                    You have full system access and can manage all roles and
                    permissions.
                  </p>
                </div>
              )}
              {isCompanyAdmin && !isSystemAdmin && (
                <div className="pt-4 border-t border-gray-200">
                  <Badge variant="primary">Company Administrator</Badge>
                  <p className="text-sm text-gray-600 mt-2">
                    You can manage roles and users within your tenant.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
