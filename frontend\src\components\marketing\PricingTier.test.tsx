import { render, screen, waitFor } from "../../__tests__/helpers/test-utils";
import { describe, it, expect, vi } from "vitest";
import { PricingTier } from "./PricingTier";
import type { PricingTier as PricingTierType } from "../../types/pricing.types";

// Mock the pricing data service
vi.mock("../../services/pricing-data.service", () => ({
  getFeaturesForTier: vi.fn().mockResolvedValue([
    {
      name: "Admin Managed",
      description: "All user management handled by company admin",
      basic: true,
      plus: true,
      pro: true,
      enterprise: true,
      includeOnCard: true,
    },
    {
      name: "Invite Codes",
      description: "Generate 1-time sign-up codes for new users",
      basic: false,
      plus: true,
      pro: true,
      enterprise: true,
      includeOnCard: true,
    },
  ]),
}));

const mockTier: PricingTierType = {
  name: "Basic",
  price: "$5",
  period: "month",
  description: "Perfect for small teams getting started",
  icon: "Users",
  popular: false,
  buttonText: "Get Started",
  buttonVariant: "primary",
  order: 1,
};

const mockPopularTier: PricingTierType = {
  name: "Pro",
  price: "$20",
  period: "month",
  description: "Everything in Plus plus automation",
  icon: "Star",
  popular: true,
  buttonText: "Get Started",
  buttonVariant: "primary",
  order: 3,
};

describe("PricingTier", () => {
  it("renders tier information correctly", async () => {
    render(<PricingTier tier={mockTier} />);

    expect(screen.getByText("Basic")).toBeInTheDocument();
    expect(screen.getByText("$5")).toBeInTheDocument();
    expect(screen.getByText("/month")).toBeInTheDocument();
    expect(
      screen.getByText("Perfect for small teams getting started"),
    ).toBeInTheDocument();
    expect(screen.getByText("Get Started")).toBeInTheDocument();

    // Wait for async state updates to complete
    await waitFor(() => {
      expect(screen.getByText("Basic")).toBeInTheDocument();
    });
  });

  it("shows popular badge for popular tiers", async () => {
    render(<PricingTier tier={mockPopularTier} />);

    expect(screen.getByText("Best Value")).toBeInTheDocument();
    expect(screen.getByText("Pro")).toBeInTheDocument();

    // Wait for async state updates to complete
    await waitFor(() => {
      expect(screen.getByText("Pro")).toBeInTheDocument();
    });
  });

  it("does not show popular badge for non-popular tiers", async () => {
    render(<PricingTier tier={mockTier} />);

    expect(screen.queryByText("Most Popular")).not.toBeInTheDocument();

    // Wait for async state updates to complete
    await waitFor(() => {
      expect(screen.getByText("Basic")).toBeInTheDocument();
    });
  });

  it("handles custom pricing correctly", async () => {
    const customTier: PricingTierType = {
      ...mockTier,
      price: "Custom",
      buttonText: "Contact Sales",
      buttonVariant: "secondary",
    };

    render(<PricingTier tier={customTier} />);

    expect(screen.getByText("Custom")).toBeInTheDocument();
    expect(screen.queryByText("/month")).not.toBeInTheDocument();
    expect(screen.getByText("Contact Sales")).toBeInTheDocument();

    // Wait for async state updates to complete
    await waitFor(() => {
      expect(screen.getByText("Custom")).toBeInTheDocument();
    });
  });

  // TODO: Add tests for CSV data integration once component is fully implemented
  // it('loads and displays features from CSV data', async () => {
  //   render(<PricingTier tier={mockTier} />);
  //   await screen.findByText('Admin Managed');
  //   expect(screen.getByText('All user management handled by company admin')).toBeInTheDocument();
  // });

  // it('shows incremental features for higher tiers', async () => {
  //   render(<PricingTier tier={mockPopularTier} />);
  //   await screen.findByText(/Includes everything in/);
  //   expect(screen.getByText(/plus:/)).toBeInTheDocument();
  // });
});
