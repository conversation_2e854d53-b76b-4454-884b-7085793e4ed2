/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { BrowserRouter } from "react-router-dom";
import { PricingPage } from "./PricingPage";

// Mock the organisms
vi.mock("../../components", () => ({
  MarketingHero: ({
    title,
    description,
    primaryCta,
    secondaryCta,
    announcement,
    socialProof,
  }: any) => (
    <div data-testid="marketing-hero">
      {announcement && (
        <div data-testid="hero-announcement">{announcement.text}</div>
      )}
      <h1>{title}</h1>
      <p>{description}</p>
      {primaryCta && (
        <button data-testid="hero-primary-cta">{primaryCta.text}</button>
      )}
      {secondaryCta && (
        <button data-testid="hero-secondary-cta">{secondaryCta.text}</button>
      )}
      {socialProof && (
        <div data-testid="hero-social-proof">
          <span>{socialProof.text}</span>
          {socialProof.stats && (
            <div data-testid="hero-stats">
              {socialProof.stats.map((stat: any, index: number) => (
                <div key={index}>
                  {stat.value} {stat.label}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  ),
  PricingCards: ({ title, subtitle, badge, tiers, billingPeriod }: any) => (
    <div data-testid="pricing-cards">
      {badge && <div data-testid="pricing-badge">{badge.text}</div>}
      <h2>{title}</h2>
      <p>{subtitle}</p>
      {billingPeriod && (
        <div data-testid="billing-toggle">
          <button onClick={() => billingPeriod.onToggle("monthly")}>
            Monthly
          </button>
          <button onClick={() => billingPeriod.onToggle("yearly")}>
            Yearly
          </button>
          <span>Current: {billingPeriod.current}</span>
          {billingPeriod.discount && <span>{billingPeriod.discount}</span>}
        </div>
      )}
      <div data-testid="pricing-tiers">
        {tiers.map((tier: any) => (
          <div key={tier.id} data-testid={`tier-${tier.id}`}>
            <h3>{tier.name}</h3>
            <div>
              {tier.price}
              {tier.period && `/${tier.period}`}
            </div>
            <p>{tier.description}</p>
            <button>{tier.buttonText}</button>
            {tier.popular && (
              <div data-testid={`popular-${tier.id}`}>Popular</div>
            )}
          </div>
        ))}
      </div>
    </div>
  ),
  FeatureGrid: ({ title, subtitle, badge, features }: any) => (
    <div data-testid="feature-grid">
      {badge && <div data-testid="features-badge">{badge.text}</div>}
      <h2>{title}</h2>
      <p>{subtitle}</p>
      <div data-testid="features-list">
        {features.map((feature: any) => (
          <div key={feature.id} data-testid={`feature-${feature.id}`}>
            <h3>{feature.title}</h3>
            <p>{feature.description}</p>
          </div>
        ))}
      </div>
    </div>
  ),
  TestimonialSection: ({ title, subtitle, badge, testimonials }: any) => (
    <div data-testid="testimonial-section">
      {badge && <div data-testid="testimonials-badge">{badge.text}</div>}
      <h2>{title}</h2>
      <p>{subtitle}</p>
      <div data-testid="testimonials-list">
        {testimonials.map((testimonial: any) => (
          <div
            key={testimonial.id}
            data-testid={`testimonial-${testimonial.id}`}
          >
            <blockquote>{testimonial.quote}</blockquote>
            <div>
              {testimonial.author.name} - {testimonial.author.title}
            </div>
            <div>{testimonial.author.company}</div>
          </div>
        ))}
      </div>
    </div>
  ),
}));

const renderWithRouter = (component: React.ReactElement) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe("PricingPage", () => {
  describe("Page Structure", () => {
    it("should render all main sections", () => {
      renderWithRouter(<PricingPage />);

      const heroSections = screen.getAllByTestId("marketing-hero");
      expect(heroSections).toHaveLength(2); // Main hero + CTA hero
      expect(screen.getByTestId("pricing-cards")).toBeInTheDocument();
      expect(screen.getByTestId("feature-grid")).toBeInTheDocument();
      expect(screen.getByTestId("testimonial-section")).toBeInTheDocument();
    });

    it("should have proper semantic structure", () => {
      renderWithRouter(<PricingPage />);

      const sections = document.querySelectorAll("section");
      expect(sections.length).toBe(5); // Hero, Pricing, Features, Testimonials, CTA

      // Check for section IDs for navigation
      expect(document.querySelector("#pricing")).toBeInTheDocument();
      expect(document.querySelector("#contact")).toBeInTheDocument();
    });

    it("should have proper background styling", () => {
      renderWithRouter(<PricingPage />);

      // Check the main container has bg-white class
      const mainContainer = document.querySelector(".bg-white");
      expect(mainContainer).toBeInTheDocument();
      expect(mainContainer).toHaveClass("bg-white");
    });
  });

  describe("Hero Section", () => {
    it("should render main hero with correct content", () => {
      renderWithRouter(<PricingPage />);

      expect(
        screen.getByText("Simple, transparent pricing"),
      ).toBeInTheDocument();
      expect(
        screen.getByText(/Choose the perfect plan for your team/),
      ).toBeInTheDocument();
    });

    it("should render hero announcement", () => {
      renderWithRouter(<PricingPage />);

      expect(screen.getByTestId("hero-announcement")).toBeInTheDocument();
      expect(
        screen.getByText("🎉 Save 20% with annual billing"),
      ).toBeInTheDocument();
    });

    it("should render hero CTAs", () => {
      renderWithRouter(<PricingPage />);

      const primaryCTAs = screen.getAllByTestId("hero-primary-cta");
      const secondaryCTAs = screen.getAllByTestId("hero-secondary-cta");
      expect(primaryCTAs.length).toBe(2); // Main hero + CTA hero
      expect(secondaryCTAs.length).toBe(2); // Main hero + CTA hero
      expect(screen.getAllByText("Start Free Trial").length).toBeGreaterThan(1); // Hero + pricing tiers
      expect(screen.getAllByText("Contact Sales").length).toBeGreaterThan(1); // Hero + pricing tiers
    });

    it("should render social proof with stats", () => {
      renderWithRouter(<PricingPage />);

      const socialProofSections = screen.getAllByTestId("hero-social-proof");
      expect(socialProofSections.length).toBe(2); // Main hero + CTA hero
      expect(
        screen.getByText("Join 500+ companies already using Tech Notes"),
      ).toBeInTheDocument();
      expect(screen.getByText("14-day Free Trial")).toBeInTheDocument();
      expect(screen.getByText("No Setup Fees")).toBeInTheDocument();
      expect(screen.getByText("Cancel Anytime")).toBeInTheDocument();
    });
  });

  describe("Pricing Cards Section", () => {
    it("should render pricing cards with correct content", () => {
      renderWithRouter(<PricingPage />);

      const pricingCards = screen.getByTestId("pricing-cards");
      expect(pricingCards).toBeInTheDocument();

      expect(screen.getByText("Choose your plan")).toBeInTheDocument();
      expect(
        screen.getByText(/All plans include a 14-day free trial/),
      ).toBeInTheDocument();
    });

    it("should render pricing badge", () => {
      renderWithRouter(<PricingPage />);

      expect(screen.getByTestId("pricing-badge")).toBeInTheDocument();
      expect(screen.getByText("Most Popular")).toBeInTheDocument();
    });

    it("should render all pricing tiers", () => {
      renderWithRouter(<PricingPage />);

      expect(screen.getByTestId("tier-basic")).toBeInTheDocument();
      expect(screen.getByTestId("tier-plus")).toBeInTheDocument();
      expect(screen.getByTestId("tier-pro")).toBeInTheDocument();
      expect(screen.getByTestId("tier-enterprise")).toBeInTheDocument();
    });

    it("should render tier content correctly", () => {
      renderWithRouter(<PricingPage />);

      expect(screen.getByText("Basic")).toBeInTheDocument();
      expect(screen.getByText("$7/month")).toBeInTheDocument();
      expect(
        screen.getByText(/Perfect for small teams getting started/),
      ).toBeInTheDocument();

      expect(screen.getByText("Plus")).toBeInTheDocument();
      expect(screen.getByText("$15/month")).toBeInTheDocument();
      expect(
        screen.getByText(/Everything in Basic plus advanced features/),
      ).toBeInTheDocument();

      expect(screen.getByText("Pro")).toBeInTheDocument();
      expect(screen.getByText("$30/month")).toBeInTheDocument();
      expect(
        screen.getByText(/Everything in Plus plus automation/),
      ).toBeInTheDocument();

      expect(screen.getByText("Enterprise")).toBeInTheDocument();
      expect(screen.getByText("Custom")).toBeInTheDocument();
      expect(
        screen.getByText(/Advanced features for large organizations/),
      ).toBeInTheDocument();
    });

    it("should highlight popular tier", () => {
      renderWithRouter(<PricingPage />);

      expect(screen.getByTestId("popular-pro")).toBeInTheDocument();
    });

    it("should render billing period toggle", () => {
      renderWithRouter(<PricingPage />);

      const billingToggle = screen.getByTestId("billing-toggle");
      expect(billingToggle).toBeInTheDocument();
      expect(screen.getByText("Current: monthly")).toBeInTheDocument();
    });

    it("should handle billing period changes", () => {
      renderWithRouter(<PricingPage />);

      const yearlyButton = screen.getByText("Yearly");
      fireEvent.click(yearlyButton);

      expect(screen.getByText("Current: yearly")).toBeInTheDocument();
      expect(screen.getByText("Save 20%")).toBeInTheDocument();

      // Prices should change to yearly
      expect(screen.getByText("$70/year")).toBeInTheDocument();
      expect(screen.getByText("$150/year")).toBeInTheDocument();
      expect(screen.getByText("$300/year")).toBeInTheDocument();
    });
  });

  describe("Features Section", () => {
    it("should render features grid with correct content", () => {
      renderWithRouter(<PricingPage />);

      const featureGrid = screen.getByTestId("feature-grid");
      expect(featureGrid).toBeInTheDocument();

      expect(screen.getByText("Why choose Tech Notes?")).toBeInTheDocument();
      expect(
        screen.getByText(
          /Everything you need to manage technical documentation/,
        ),
      ).toBeInTheDocument();
    });

    it("should render features badge", () => {
      renderWithRouter(<PricingPage />);

      expect(screen.getByTestId("features-badge")).toBeInTheDocument();
      expect(screen.getByText("Platform Benefits")).toBeInTheDocument();
    });

    it("should render all feature items", () => {
      renderWithRouter(<PricingPage />);

      expect(screen.getByTestId("feature-mobile-first")).toBeInTheDocument();
      expect(
        screen.getByTestId("feature-enterprise-security"),
      ).toBeInTheDocument();
      expect(
        screen.getByTestId("feature-advanced-analytics"),
      ).toBeInTheDocument();
      expect(screen.getByTestId("feature-real-time-sync")).toBeInTheDocument();
      expect(screen.getByTestId("feature-global-access")).toBeInTheDocument();
      expect(screen.getByTestId("feature-premium-support")).toBeInTheDocument();
    });

    it("should render feature content correctly", () => {
      renderWithRouter(<PricingPage />);

      expect(screen.getByText("Mobile-First Design")).toBeInTheDocument();
      expect(
        screen.getByText(/Built for technicians in the field/),
      ).toBeInTheDocument();

      expect(screen.getByText("Enterprise Security")).toBeInTheDocument();
      expect(
        screen.getByText(/Bank-level security with role-based access/),
      ).toBeInTheDocument();

      expect(screen.getByText("Premium Support")).toBeInTheDocument();
      expect(
        screen.getByText(/24\/7 support with dedicated account managers/),
      ).toBeInTheDocument();
    });
  });

  describe("Testimonials Section", () => {
    it("should render testimonials section with correct content", () => {
      renderWithRouter(<PricingPage />);

      const testimonialSection = screen.getByTestId("testimonial-section");
      expect(testimonialSection).toBeInTheDocument();

      expect(
        screen.getByText("Trusted by service professionals worldwide"),
      ).toBeInTheDocument();
      expect(
        screen.getByText(
          /See what our customers have to say about Tech Notes pricing/,
        ),
      ).toBeInTheDocument();
    });

    it("should render testimonials badge", () => {
      renderWithRouter(<PricingPage />);

      expect(screen.getByTestId("testimonials-badge")).toBeInTheDocument();
      expect(screen.getByText("Customer Success")).toBeInTheDocument();
    });

    it("should render all testimonial items", () => {
      renderWithRouter(<PricingPage />);

      expect(
        screen.getByTestId("testimonial-testimonial-1"),
      ).toBeInTheDocument();
      expect(
        screen.getByTestId("testimonial-testimonial-2"),
      ).toBeInTheDocument();
      expect(
        screen.getByTestId("testimonial-testimonial-3"),
      ).toBeInTheDocument();
    });

    it("should render testimonial content correctly", () => {
      renderWithRouter(<PricingPage />);

      expect(
        screen.getByText(/The pricing is transparent and fair/),
      ).toBeInTheDocument();
      expect(
        screen.getByText("Sarah Johnson - Operations Manager"),
      ).toBeInTheDocument();
      expect(screen.getByText("ServicePro Solutions")).toBeInTheDocument();

      expect(
        screen.getByText(/Enterprise plan gives us everything we need/),
      ).toBeInTheDocument();
      expect(
        screen.getByText("Mike Chen - Field Service Director"),
      ).toBeInTheDocument();
      expect(screen.getByText("TechFlow Industries")).toBeInTheDocument();
    });
  });

  describe("Final CTA Section", () => {
    it("should render final CTA hero section", () => {
      renderWithRouter(<PricingPage />);

      const heroSections = screen.getAllByTestId("marketing-hero");
      const finalCTA = heroSections[1]; // Second hero is the CTA

      expect(finalCTA).toBeInTheDocument();
      expect(screen.getByText("Ready to get started?")).toBeInTheDocument();
      expect(
        screen.getByText(/Join thousands of service professionals/),
      ).toBeInTheDocument();
    });

    it("should render final CTA social proof", () => {
      renderWithRouter(<PricingPage />);

      const socialProofSections = screen.getAllByTestId("hero-social-proof");
      expect(socialProofSections.length).toBe(2); // Main hero + CTA hero

      expect(
        screen.getByText(/No setup fees • Cancel anytime • 24\/7 support/),
      ).toBeInTheDocument();
      expect(screen.getByText("500+ Happy Customers")).toBeInTheDocument();
      expect(screen.getByText("99.9% Uptime")).toBeInTheDocument();
      expect(screen.getAllByText("24/7 Support").length).toBeGreaterThan(0);
    });
  });

  describe("Responsive Design", () => {
    it("should have proper section spacing", () => {
      renderWithRouter(<PricingPage />);

      const sections = document.querySelectorAll("section");
      sections.forEach((section) => {
        expect(section).toHaveClass("py-20", "sm:py-32");
      });
    });

    it("should have proper container constraints", () => {
      renderWithRouter(<PricingPage />);

      const containers = document.querySelectorAll(".max-w-7xl");
      expect(containers.length).toBeGreaterThan(0);

      containers.forEach((container) => {
        expect(container).toHaveClass("mx-auto", "px-4", "sm:px-6", "lg:px-8");
      });
    });

    it("should have proper background variations", () => {
      renderWithRouter(<PricingPage />);

      // Pricing and testimonials sections should have gray background
      const grayBackgroundSections = document.querySelectorAll(".bg-gray-50");
      expect(grayBackgroundSections.length).toBe(2);
    });
  });

  describe("Navigation Integration", () => {
    it("should have proper section IDs for navigation", () => {
      renderWithRouter(<PricingPage />);

      expect(document.querySelector("#pricing")).toBeInTheDocument();
      expect(document.querySelector("#contact")).toBeInTheDocument();
    });

    it("should maintain semantic HTML structure", () => {
      renderWithRouter(<PricingPage />);

      const main = document.querySelector("div.bg-white");
      expect(main).toBeInTheDocument();

      const sections = document.querySelectorAll("section");
      expect(sections.length).toBe(5); // Hero, Pricing, Features, Testimonials, CTA
    });
  });
});
