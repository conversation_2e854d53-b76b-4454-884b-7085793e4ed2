/**
 * Test Type Definitions
 *
 * High-value types for core business entities used in tests.
 * These provide type safety and better IDE support while keeping
 * framework mocks as 'any' for simplicity.
 */

import {
  RoleType,
  PermissionResource,
  PermissionAction,
  InvitationStatus,
} from '@prisma/client';

// ============================================================================
// CORE BUSINESS ENTITIES
// ============================================================================

/**
 * Test representation of User entity
 * Includes only fields commonly used in tests
 */
export interface TestUser {
  id: string;
  clerkId: string;
  tenantId: string;
  email: string;
  firstName?: string | null;
  lastName?: string | null;
  imageUrl?: string | null;
  isActive: boolean;
  lastLoginAt?: Date | null;
  lastActivityAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Test representation of Tenant entity
 */
export interface TestTenant {
  id: string;
  name: string;
  slug: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Test representation of Role entity
 */
export interface TestRole {
  id: string;
  name: string;
  type: RoleType;
  description: string;
  isSystemRole: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Test representation of Permission entity
 */
export interface TestPermission {
  id: string;
  name: string;
  description: string;
  resource: PermissionResource;
  action: PermissionAction;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Test representation of UserRole entity
 */
export interface TestUserRole {
  id: string;
  userId: string;
  roleId: string;
  tenantId?: string | null;
  assignedBy?: string | null;
  createdAt: Date;
  updatedAt: Date;
  role: {
    id: string;
    name: string;
    type: RoleType;
    isSystemRole: boolean;
  };
}

/**
 * Test representation of Invitation entity
 */
export interface TestInvitation {
  id: string;
  clerkInvitationId: string;
  email: string;
  tenantId: string;
  roleId: string;
  createdBy: string;
  usedBy?: string | null;
  usedAt?: Date | null;
  expiresAt: Date;
  status: InvitationStatus;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// API REQUEST/RESPONSE TYPES
// ============================================================================

/**
 * Common API request patterns
 */
export interface TestApiRequest {
  body?: Record<string, any>;
  params?: Record<string, string>;
  query?: Record<string, string>;
  headers?: Record<string, string>;
  user?: TestAuthContext;
}

/**
 * Common API response patterns
 */
export interface TestApiResponse {
  status: number;
  data?: any;
  error?: string;
  message?: string;
}

// ============================================================================
// AUTH & CONTEXT TYPES
// ============================================================================

/**
 * Test authentication context - compatible with existing BackendAuthContext
 */
export interface TestAuthContext {
  id: string; // Maps to userId in new context
  userId: string;
  tenantId: string; // Required to match BackendAuthContext
  clerkId: string;
  email: string;
  firstName?: string | null;
  lastName?: string | null;
  imageUrl?: string | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  canBypassTenantScope: boolean;
  roles?: TestUserRole[]; // Optional to match BackendAuthContext
  permissions?: string[]; // Optional to match BackendAuthContext
}

/**
 * Test JWT payload structure
 */
export interface TestJwtPayload {
  sub: string; // clerkId
  email: string;
  tenantId?: string;
  iat?: number;
  exp?: number;
}

// ============================================================================
// FACTORY TYPES
// ============================================================================

/**
 * Factory options for creating test entities
 */
export interface TestUserFactory {
  id?: string;
  clerkId?: string;
  tenantId?: string;
  email?: string;
  firstName?: string | null;
  lastName?: string | null;
  isActive?: boolean;
  roleType?: RoleType;
}

export interface TestTenantFactory {
  id?: string;
  name?: string;
  slug?: string;
  isActive?: boolean;
}

export interface TestRoleFactory {
  id?: string;
  name?: string;
  type?: RoleType;
  description?: string;
  isSystemRole?: boolean;
}

// ============================================================================
// MOCK SERVICE TYPES
// ============================================================================

/**
 * Enhanced mock interfaces for core business services
 * Keep framework mocks (Express, Prisma internals) as 'any'
 */
export interface MockUserService {
  findById: jest.MockedFunction<(id: string) => Promise<TestUser | null>>;
  findByClerkId: jest.MockedFunction<
    (clerkId: string) => Promise<TestUser | null>
  >;
  create: jest.MockedFunction<(data: Partial<TestUser>) => Promise<TestUser>>;
  update: jest.MockedFunction<
    (id: string, data: Partial<TestUser>) => Promise<TestUser>
  >;
  delete: jest.MockedFunction<(id: string) => Promise<void>>;
  findByTenant: jest.MockedFunction<(tenantId: string) => Promise<TestUser[]>>;
}

export interface MockTenantService {
  findById: jest.MockedFunction<(id: string) => Promise<TestTenant | null>>;
  findBySlug: jest.MockedFunction<(slug: string) => Promise<TestTenant | null>>;
  create: jest.MockedFunction<
    (data: Partial<TestTenant>) => Promise<TestTenant>
  >;
  update: jest.MockedFunction<
    (id: string, data: Partial<TestTenant>) => Promise<TestTenant>
  >;
  delete: jest.MockedFunction<(id: string) => Promise<void>>;
}

export interface MockRoleService {
  findById: jest.MockedFunction<(id: string) => Promise<TestRole | null>>;
  findByType: jest.MockedFunction<(type: RoleType) => Promise<TestRole[]>>;
  create: jest.MockedFunction<(data: Partial<TestRole>) => Promise<TestRole>>;
  assignToUser: jest.MockedFunction<
    (userId: string, roleId: string, tenantId?: string) => Promise<TestUserRole>
  >;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Common test scenarios
 */
export type TestScenario =
  | 'system-admin'
  | 'company-admin'
  | 'company-tech'
  | 'unauthorized'
  | 'invalid-tenant'
  | 'inactive-user';

/**
 * Test database state
 */
export interface TestDatabaseState {
  users: TestUser[];
  tenants: TestTenant[];
  roles: TestRole[];
  userRoles: TestUserRole[];
  permissions: TestPermission[];
}

/**
 * Test assertion helpers
 */
export interface TestAssertions {
  expectUser: (actual: any) => jest.Matchers<TestUser>;
  expectTenant: (actual: any) => jest.Matchers<TestTenant>;
  expectRole: (actual: any) => jest.Matchers<TestRole>;
  expectApiResponse: (actual: any) => jest.Matchers<TestApiResponse>;
}
