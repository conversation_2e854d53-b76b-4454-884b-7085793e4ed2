import React, { useState, useEffect } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { <PERSON><PERSON>, <PERSON><PERSON>, Alert, Badge } from "../index";
import { useTypedApi, type VehicleModelV2 } from "../../services/api-client";
import { Calendar, Check } from "lucide-react";

interface ModelYearAssociationV2ModalProps {
  isOpen: boolean;
  onClose: () => void;
  model: VehicleModelV2 | null;
}

export const ModelYearAssociationV2Modal: React.FC<
  ModelYearAssociationV2ModalProps
> = ({ isOpen, onClose, model }) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [selectedYearIds, setSelectedYearIds] = useState<string[]>([]);
  const [currentYearIds, setCurrentYearIds] = useState<string[]>([]);

  // Fetch all available years
  const { data: yearsResponse } = useQuery({
    queryKey: ["vehicle-years"],
    queryFn: () => api.vehicleHierarchy.getYears(),
    enabled: isOpen,
  });

  // Fetch current year associations for the model
  const { data: modelYearsResponse } = useQuery({
    queryKey: ["vehicle-hierarchy-v2", "models", model?.id, "years"],
    queryFn: async () => {
      if (!model) return { data: [], meta: { total: 0 } };
      return api.vehicleHierarchyV2.getYearsByModel(model.id);
    },
    enabled: isOpen && !!model,
  });

  // Update state when model or associations change
  useEffect(() => {
    if (modelYearsResponse?.data) {
      const yearIds = modelYearsResponse.data.map(
        (year: { id: string }) => year.id,
      );
      setCurrentYearIds(yearIds);
      setSelectedYearIds(yearIds);
    }
  }, [modelYearsResponse]);

  const associateYearsMutation = useMutation({
    mutationFn: (yearIds: string[]) => {
      if (!model) throw new Error("No model selected");
      return api.vehicleHierarchyV2.associateModelWithYears(model.id, {
        yearIds,
      });
    },
    onSuccess: () => {
      toast.success("Year associations updated successfully");
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "models", model?.id, "years"],
      });
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "full"],
      });
      handleClose();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update year associations");
    },
  });

  const removeYearAssociationsMutation = useMutation({
    mutationFn: (yearIds: string[]) => {
      if (!model) throw new Error("No model selected");
      return api.vehicleHierarchyV2.removeModelYearAssociations(model.id, {
        yearIds,
      });
    },
    onSuccess: () => {
      toast.success("Year associations removed successfully");
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "models", model?.id, "years"],
      });
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "full"],
      });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to remove year associations");
    },
  });

  const handleSubmit = async () => {
    if (!model) return;

    const yearsToAdd = selectedYearIds.filter(
      (id) => !currentYearIds.includes(id),
    );
    const yearsToRemove = currentYearIds.filter(
      (id) => !selectedYearIds.includes(id),
    );

    try {
      // Remove associations first
      if (yearsToRemove.length > 0) {
        await removeYearAssociationsMutation.mutateAsync(yearsToRemove);
      }

      // Add new associations
      if (yearsToAdd.length > 0) {
        await associateYearsMutation.mutateAsync(yearsToAdd);
      }

      // If no changes, just close
      if (yearsToAdd.length === 0 && yearsToRemove.length === 0) {
        handleClose();
      }
    } catch {
      // Error handling is done in the mutation onError callbacks
    }
  };

  const handleClose = () => {
    setSelectedYearIds([]);
    setCurrentYearIds([]);
    onClose();
  };

  const toggleYear = (yearId: string) => {
    setSelectedYearIds((prev) =>
      prev.includes(yearId)
        ? prev.filter((id) => id !== yearId)
        : [...prev, yearId],
    );
  };

  const selectAllYears = () => {
    if (yearsResponse?.data) {
      setSelectedYearIds(yearsResponse.data.map((year) => year.id));
    }
  };

  const clearAllYears = () => {
    setSelectedYearIds([]);
  };

  if (!model) return null;

  const years = yearsResponse?.data || [];
  const hasChanges =
    JSON.stringify(selectedYearIds.sort()) !==
    JSON.stringify(currentYearIds.sort());
  const isPending =
    associateYearsMutation.isPending ||
    removeYearAssociationsMutation.isPending;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Manage Model Years"
      size="lg"
    >
      <div className="space-y-6">
        <div className="flex items-center space-x-3 text-gray-600 mb-4">
          <Calendar className="h-5 w-5" />
          <span>Associate years with this model</span>
        </div>

        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Model:</strong> {model.subBrand?.brand?.name} →{" "}
            {model.subBrand?.name} → {model.name}
          </p>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-700">
              Available Years
            </label>
            <div className="flex space-x-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={selectAllYears}
                disabled={years.length === 0 || isPending}
              >
                Select All
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={clearAllYears}
                disabled={selectedYearIds.length === 0 || isPending}
              >
                Clear All
              </Button>
            </div>
          </div>

          <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-md p-4">
            <div className="grid grid-cols-4 gap-3">
              {years.map((year) => {
                const isSelected = selectedYearIds.includes(year.id);
                const wasOriginallySelected = currentYearIds.includes(year.id);

                return (
                  <label
                    key={year.id}
                    className={`flex items-center justify-between p-2 border rounded cursor-pointer transition-colors ${
                      isSelected
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-300 hover:border-gray-400"
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => toggleYear(year.id)}
                        disabled={isPending}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm font-medium">{year.year}</span>
                    </div>
                    {wasOriginallySelected && (
                      <Check className="h-4 w-4 text-green-600" />
                    )}
                  </label>
                );
              })}
            </div>
          </div>

          {selectedYearIds.length > 0 && (
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-700">
                Selected Years ({selectedYearIds.length}):
              </p>
              <div className="flex flex-wrap gap-1">
                {selectedYearIds.map((yearId) => {
                  const year = years.find((y) => y.id === yearId);
                  const wasOriginallySelected = currentYearIds.includes(yearId);

                  return year ? (
                    <Badge
                      key={yearId}
                      variant={wasOriginallySelected ? "default" : "secondary"}
                      className="flex items-center space-x-1"
                    >
                      <span>{year.year}</span>
                      {!wasOriginallySelected && (
                        <span className="text-xs">(new)</span>
                      )}
                    </Badge>
                  ) : null;
                })}
              </div>
            </div>
          )}
        </div>

        {(associateYearsMutation.isError ||
          removeYearAssociationsMutation.isError) && (
          <Alert variant="error">
            <p>Failed to update year associations. Please try again.</p>
          </Alert>
        )}

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isPending}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={!hasChanges || isPending}
          >
            {isPending ? "Updating..." : "Update Associations"}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
