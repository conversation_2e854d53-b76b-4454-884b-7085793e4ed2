/**
 * Simple Test Mocks - AI-Optimized
 *
 * Minimal, focused mock utilities that are easy for AI agents to understand and use.
 * No complex factories, no centralized state, just simple, direct mocking.
 */

import type { RoleType } from '@prisma/client';

import {
  TestUser,
  TestTenant,
  TestRole,
  TestAuthContext,
  TestUserFactory,
  TestTenantFactory,
  TestRoleFactory,
} from './types/test.types.js';

// Removed unused imports - PrismaClient and Logger are not used in this file

// Simple type definitions for mocks (no 'any' types)
export interface MockPrismaService {
  prisma: {
    user: {
      findMany: jest.MockedFunction<any>;
      findUnique: jest.MockedFunction<any>;
      findFirst: jest.MockedFunction<any>;
      create: jest.MockedFunction<any>;
      update: jest.MockedFunction<any>;
      delete: jest.MockedFunction<any>;
    };
    tenant: {
      findMany: jest.MockedFunction<any>;
      findUnique: jest.MockedFunction<any>;
      create: jest.MockedFunction<any>;
      update: jest.MockedFunction<any>;
      delete: jest.MockedFunction<any>;
    };
    role: {
      findMany: jest.MockedFunction<any>;
      findUnique: jest.MockedFunction<any>;
      create: jest.MockedFunction<any>;
      update: jest.MockedFunction<any>;
      delete: jest.MockedFunction<any>;
    };
    $transaction: jest.MockedFunction<any>;
    permission: {
      findMany: jest.MockedFunction<any>;
      findUnique: jest.MockedFunction<any>;
      create: jest.MockedFunction<any>;
      update: jest.MockedFunction<any>;
      delete: jest.MockedFunction<any>;
    };
    userRole: {
      findMany: jest.MockedFunction<any>;
      findUnique: jest.MockedFunction<any>;
      create: jest.MockedFunction<any>;
      update: jest.MockedFunction<any>;
      delete: jest.MockedFunction<any>;
    };
    document: {
      findMany: jest.MockedFunction<any>;
      findUnique: jest.MockedFunction<any>;
      findFirst: jest.MockedFunction<any>;
      create: jest.MockedFunction<any>;
      update: jest.MockedFunction<any>;
      delete: jest.MockedFunction<any>;
      count: jest.MockedFunction<any>;
    };
  };
  authHealthCheck: jest.MockedFunction<() => Promise<{ healthy: boolean }>>;
}

export interface MockLogger {
  error: jest.MockedFunction<(message: string, ...args: any[]) => void>;
  warn: jest.MockedFunction<(message: string, ...args: any[]) => void>;
  info: jest.MockedFunction<(message: string, ...args: any[]) => void>;
  debug: jest.MockedFunction<(message: string, ...args: any[]) => void>;
}

export interface MockPermissionService {
  hasPermission: jest.MockedFunction<
    (context: TestAuthContext, permission: string) => Promise<boolean>
  >;
  getUserPermissions: jest.MockedFunction<
    (userId: string, tenantId?: string) => Promise<string[]>
  >;
}

export interface MockS3Service {
  generatePresignedUploadUrl: jest.MockedFunction<any>;
  generatePresignedDownloadUrl: jest.MockedFunction<any>;
  testConnectivity: jest.MockedFunction<any>;
}

// Simple mock creators
export function createMockPrismaService(): MockPrismaService {
  const mockPrisma = {
    prisma: {
      user: {
        findMany: jest.fn(),
        findUnique: jest.fn(),
        findFirst: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      },
      tenant: {
        findMany: jest.fn(),
        findUnique: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      },
      role: {
        findMany: jest.fn(),
        findUnique: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      },
      $transaction: jest.fn().mockImplementation((fn) => fn(mockPrisma.prisma)),
      permission: {
        findMany: jest.fn(),
        findUnique: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      },
      userRole: {
        findMany: jest.fn(),
        findUnique: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      },
      document: {
        findMany: jest.fn(),
        findUnique: jest.fn(),
        findFirst: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
        count: jest.fn(),
      },
    },
    authHealthCheck: jest.fn().mockResolvedValue({ healthy: true }),
  };

  return mockPrisma;
}

export function createMockLogger(): MockLogger {
  return {
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
    debug: jest.fn(),
  };
}

export function createMockPermissionService(): MockPermissionService {
  return {
    hasPermission: jest.fn(),
    getUserPermissions: jest.fn(),
  };
}

export function createMockS3Service(): MockS3Service {
  return {
    generatePresignedUploadUrl: jest.fn(),
    generatePresignedDownloadUrl: jest.fn(),
    testConnectivity: jest.fn(),
  };
}

// Enhanced mock service interfaces for business logic
export interface MockUserServiceEnhanced {
  findById: jest.MockedFunction<(id: string) => Promise<TestUser | null>>;
  findByClerkId: jest.MockedFunction<
    (clerkId: string) => Promise<TestUser | null>
  >;
  create: jest.MockedFunction<(data: Partial<TestUser>) => Promise<TestUser>>;
  update: jest.MockedFunction<
    (id: string, data: Partial<TestUser>) => Promise<TestUser>
  >;
  findByTenant: jest.MockedFunction<(tenantId: string) => Promise<TestUser[]>>;
  // Keep other methods as 'any' for framework compatibility
  [key: string]: any;
}

export interface MockTenantServiceEnhanced {
  findById: jest.MockedFunction<(id: string) => Promise<TestTenant | null>>;
  findBySlug: jest.MockedFunction<(slug: string) => Promise<TestTenant | null>>;
  create: jest.MockedFunction<
    (data: Partial<TestTenant>) => Promise<TestTenant>
  >;
  update: jest.MockedFunction<
    (id: string, data: Partial<TestTenant>) => Promise<TestTenant>
  >;
  // Keep other methods as 'any' for framework compatibility
  [key: string]: any;
}

export interface MockRoleServiceEnhanced {
  findById: jest.MockedFunction<(id: string) => Promise<TestRole | null>>;
  findByType: jest.MockedFunction<(type: RoleType) => Promise<TestRole[]>>;
  create: jest.MockedFunction<(data: Partial<TestRole>) => Promise<TestRole>>;
  // Keep other methods as 'any' for framework compatibility
  [key: string]: any;
}

// Factory functions for enhanced mock services
export function createMockUserService(): MockUserServiceEnhanced {
  return {
    findById: jest.fn(),
    findByClerkId: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    findByTenant: jest.fn(),
  };
}

export function createMockTenantService(): MockTenantServiceEnhanced {
  return {
    findById: jest.fn(),
    findBySlug: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  };
}

export function createMockRoleService(): MockRoleServiceEnhanced {
  return {
    findById: jest.fn(),
    findByType: jest.fn(),
    create: jest.fn(),
  };
}

// Simple test data with proper types
export const testData = {
  user: {
    id: 'test-user-id',
    clerkId: 'test-clerk-id',
    tenantId: 'test-tenant-id',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    imageUrl: 'https://example.com/test.jpg',
    isActive: true,
    lastLoginAt: null,
    lastActivityAt: null,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  } as TestUser,
  tenant: {
    id: 'test-tenant-id',
    name: 'Test Tenant',
    slug: 'test-tenant',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  } as TestTenant,
  role: {
    id: 'test-role-id',
    name: 'Company Admin',
    type: 'COMPANY_ADMIN' as RoleType,
    description: 'Test company admin role',
    isSystemRole: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  } as TestRole,
  createUserData: {
    clerkId: 'test-clerk-id',
    tenantId: 'test-tenant-id',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    imageUrl: 'https://example.com/test.jpg',
    isActive: true,
  },
  BackendAuthContext: {
    id: 'test-user-id',
    userId: 'test-user-id',
    clerkId: 'test-clerk-id',
    tenantId: 'test-tenant-id',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    imageUrl: 'https://example.com/test.jpg',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    canBypassTenantScope: false,
    roles: [],
    permissions: ['USER_READ', 'USER_WRITE'],
  } as TestAuthContext,
  document: {
    id: 'test-document-id',
    tenantId: 'test-tenant-id',
    fileName: 'test-document.pdf',
    originalName: 'Test Document.pdf',
    fileSize: 1024000, // 1MB
    mimeType: 'application/pdf',
    s3Key: 'tenant-test-tenant-id/test-document-id-test-document.pdf',
    title: 'Test Document',
    description: 'A test document for unit testing',
    createdBy: 'test-user-id',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    deletedAt: null,
  },
  createDocumentData: {
    fileName: 'test-document.pdf',
    originalName: 'Test Document.pdf',
    fileSize: 1024000,
    mimeType: 'application/pdf',
    title: 'Test Document',
    description: 'A test document for unit testing',
  },
  s3UploadResponse: {
    uploadUrl: 'https://test-bucket.s3.amazonaws.com/presigned-upload-url',
    s3Key: 'tenant-test-tenant-id/test-document-id-test-document.pdf',
    expiresIn: 900, // 15 minutes
  },
};

// Simple mock behaviors with proper types
export function mockSuccessfulUserFind(
  mockPrisma: MockPrismaService,
  users: TestUser[] = [testData.user]
): void {
  mockPrisma.prisma.user.findMany.mockResolvedValue(users);
}

export function mockSuccessfulUserCreate(
  mockPrisma: MockPrismaService,
  user: TestUser = testData.user
): void {
  mockPrisma.prisma.user.create.mockResolvedValue(user);
}

export function mockSuccessfulTenantFind(
  mockPrisma: MockPrismaService,
  tenant: TestTenant = testData.tenant
): void {
  mockPrisma.prisma.tenant.findUnique.mockResolvedValue(tenant);
}

// Factory functions for creating test data
export function createTestUser(overrides: TestUserFactory = {}): TestUser {
  return {
    ...testData.user,
    ...overrides,
  };
}

export function createTestTenant(
  overrides: TestTenantFactory = {}
): TestTenant {
  return {
    ...testData.tenant,
    ...overrides,
  };
}

export function createTestRole(overrides: TestRoleFactory = {}): TestRole {
  return {
    ...testData.role,
    ...overrides,
  };
}
