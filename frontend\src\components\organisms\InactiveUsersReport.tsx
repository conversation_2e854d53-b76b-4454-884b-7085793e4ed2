import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  DataT<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ge,
  type Column,
} from "../index";
import {
  useTypedApi,
  type UserActivitySummary,
} from "../../services/api-client";
import { usePermissions } from "../../hooks/usePermissions";
import {
  ChevronLeft,
  ChevronRight,
  RefreshCw,
  AlertTriangle,
} from "lucide-react";

interface InactiveUsersReportProps {
  tenantId?: string; // For System Admins to view specific tenant
  className?: string;
}

export const InactiveUsersReport: React.FC<InactiveUsersReportProps> = ({
  tenantId,
  className,
}) => {
  const api = useTypedApi();
  const { isCompanyAdmin, isSystemAdmin } = usePermissions();

  // State for threshold and pagination
  const [thresholdDays, setThresholdDays] = useState(7); // Default to 1 week
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 20;

  // Only Company Admins and System Admins can view inactive users
  const canViewInactiveUsers = isCompanyAdmin || isSystemAdmin;

  const {
    data: inactiveResponse,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["inactive-users", tenantId, thresholdDays, currentPage],
    queryFn: () =>
      api.engagement.getInactiveUsers(
        thresholdDays,
        pageSize,
        (currentPage - 1) * pageSize,
        tenantId,
      ),
    enabled: canViewInactiveUsers,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "Never";
    return new Date(dateString).toLocaleString();
  };

  const getDaysSinceActivity = (lastActivityAt: string | null | undefined) => {
    if (!lastActivityAt) return "Never";
    const days = Math.floor(
      (Date.now() - new Date(lastActivityAt).getTime()) / (1000 * 60 * 60 * 24),
    );
    return `${days} days ago`;
  };

  const getDaysSinceLogin = (lastLoginAt: string | null | undefined) => {
    if (!lastLoginAt) return "Never";
    const days = Math.floor(
      (Date.now() - new Date(lastLoginAt).getTime()) / (1000 * 60 * 60 * 24),
    );
    return `${days} days ago`;
  };

  const handleThresholdChange = (days: number) => {
    setThresholdDays(days);
    setCurrentPage(1); // Reset to first page when threshold changes
  };

  if (!canViewInactiveUsers) {
    return (
      <Alert variant="warning">
        <p>You don't have permission to view inactive users report.</p>
      </Alert>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size="lg" />
        <span className="ml-2 text-gray-600">Loading inactive users...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="error">
        <p>Failed to load inactive users report. Please try again.</p>
        <button
          onClick={() => refetch()}
          className="mt-2 text-sm underline hover:no-underline"
        >
          Retry
        </button>
      </Alert>
    );
  }

  if (!inactiveResponse?.data) {
    return (
      <Alert variant="info">
        <p>No inactive users data available.</p>
      </Alert>
    );
  }

  const { users, totalCount } = inactiveResponse.data;
  const totalPages = Math.ceil(totalCount / pageSize);

  const columns = [
    {
      key: "email",
      header: "User",
      render: (_value: unknown, user: UserActivitySummary) => (
        <div>
          <div className="font-medium text-gray-900">
            {user?.email || "Unknown"}
          </div>
          {(user?.firstName || user?.lastName) && (
            <div className="text-sm text-gray-500">
              {[user?.firstName, user?.lastName].filter(Boolean).join(" ")}
            </div>
          )}
        </div>
      ),
    },
    {
      key: "lastLoginAt",
      header: "Last Login",
      render: (_value: unknown, user: UserActivitySummary) => (
        <div>
          <div className="text-sm text-gray-600">
            {formatDate(user?.lastLoginAt)}
          </div>
          <div className="text-xs text-gray-500">
            {getDaysSinceLogin(user?.lastLoginAt)}
          </div>
        </div>
      ),
    },
    {
      key: "lastActivityAt",
      header: "Last Activity",
      render: (_value: unknown, user: UserActivitySummary) => (
        <div>
          <div className="text-sm text-gray-600">
            {formatDate(user?.lastActivityAt)}
          </div>
          <div className="text-xs text-gray-500">
            {getDaysSinceActivity(user?.lastActivityAt)}
          </div>
        </div>
      ),
    },
    {
      key: "createdAt",
      header: "Joined",
      render: (_value: unknown, user: UserActivitySummary) => (
        <span className="text-sm text-gray-600">
          {user?.createdAt
            ? new Date(user.createdAt).toLocaleDateString()
            : "Unknown"}
        </span>
      ),
    },
    {
      key: "status",
      header: "Status",
      render: (_value: unknown, user: UserActivitySummary) => {
        const daysSinceActivity = user?.lastActivityAt
          ? Math.floor(
              (Date.now() - new Date(user.lastActivityAt).getTime()) /
                (1000 * 60 * 60 * 24),
            )
          : Infinity;

        if (daysSinceActivity === Infinity) {
          return <Badge variant="error">Never Active</Badge>;
        } else if (daysSinceActivity >= 30) {
          return <Badge variant="error">Highly Inactive</Badge>;
        } else if (daysSinceActivity >= 14) {
          return <Badge variant="warning">Very Inactive</Badge>;
        } else {
          return <Badge variant="secondary">Inactive</Badge>;
        }
      },
    },
  ];

  return (
    <div className={className}>
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
              Inactive Users Report
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Users who haven't been active for {thresholdDays} days or more
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>

        {/* Threshold Selection */}
        <div className="flex items-center space-x-4 mb-4">
          <span className="text-sm font-medium text-gray-700">
            Inactive threshold:
          </span>
          <div className="flex space-x-2">
            {[7, 14, 30, 60].map((days) => (
              <Button
                key={days}
                variant={thresholdDays === days ? "primary" : "outline"}
                size="sm"
                onClick={() => handleThresholdChange(days)}
              >
                {days} days
              </Button>
            ))}
          </div>
        </div>

        {totalCount > 0 && (
          <Alert variant="warning">
            <p>
              Found {totalCount} users who haven't been active for{" "}
              {thresholdDays} days or more. Consider reaching out to re-engage
              these users.
            </p>
          </Alert>
        )}
      </div>

      {totalCount === 0 ? (
        <Alert variant="success">
          <p>
            Great! No users have been inactive for {thresholdDays} days or more.
          </p>
        </Alert>
      ) : (
        <>
          <div className="mb-4 text-sm text-gray-600">
            Showing {(currentPage - 1) * pageSize + 1}-
            {Math.min(currentPage * pageSize, totalCount)} of {totalCount}{" "}
            inactive users
          </div>

          <DataTable
            data={users as unknown as Record<string, unknown>[]}
            columns={columns as unknown as Column<Record<string, unknown>>[]}
          />

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </>
      )}

      {/* Metadata */}
      <div className="mt-4 text-xs text-gray-500">
        Last updated:{" "}
        {new Date(inactiveResponse.meta.timestamp).toLocaleString()}
        {tenantId && ` • Tenant: ${inactiveResponse.meta.tenantId}`}
      </div>
    </div>
  );
};
