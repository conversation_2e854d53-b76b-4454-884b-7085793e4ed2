import { BackendAuthContext } from '@tech-notes/shared';

import { logger } from '../utils/logger.js';

import { AuthCacheService } from './auth-cache.service.js';

describe('AuthCacheService', () => {
  let authCacheService: AuthCacheService;
  const mockAuthContext: BackendAuthContext = {
    id: 'user-123',
    clerkId: 'clerk-123',
    tenantId: 'tenant-123',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    imageUrl: 'https://example.com/avatar.jpg',
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  };

  beforeEach(() => {
    // Initialize without Redis URL to test in-memory fallback
    authCacheService = new AuthCacheService(logger);
  });

  afterEach(async () => {
    await authCacheService.disconnect();
  });

  describe('in-memory cache', () => {
    it('should store and retrieve auth context', async () => {
      const key = 'auth:user:clerk-123';

      // Set value
      await authCacheService.set(key, mockAuthContext, 300);

      // Get value
      const retrieved = await authCacheService.get(key);

      expect(retrieved).toEqual(mockAuthContext);
    });

    it('should return null for non-existent key', async () => {
      const result = await authCacheService.get('non-existent-key');
      expect(result).toBeNull();
    });

    it('should invalidate cached values', async () => {
      const key = 'auth:user:clerk-123';

      // Set value
      await authCacheService.set(key, mockAuthContext, 300);

      // Verify it exists
      let retrieved = await authCacheService.get(key);
      expect(retrieved).toEqual(mockAuthContext);

      // Invalidate
      await authCacheService.invalidate(key);

      // Verify it's gone
      retrieved = await authCacheService.get(key);
      expect(retrieved).toBeNull();
    });

    it('should handle expired entries', async () => {
      const key = 'auth:user:clerk-123';

      // Set value with very short TTL
      await authCacheService.set(key, mockAuthContext, 0.001); // 1ms

      // Wait for expiration
      await new Promise((resolve) => setTimeout(resolve, 10));

      // Should return null for expired entry
      const retrieved = await authCacheService.get(key);
      expect(retrieved).toBeNull();
    });
  });

  describe('cache key generation', () => {
    it('should generate correct cache key format', () => {
      const clerkId = 'clerk-123';
      const key = AuthCacheService.generateUserCacheKey(clerkId);
      expect(key).toBe('auth:user:clerk-123');
    });
  });

  describe('error handling', () => {
    it('should handle cache operations gracefully', async () => {
      // These should not throw errors
      await expect(authCacheService.get('test-key')).resolves.toBeNull();
      await expect(
        authCacheService.set('test-key', mockAuthContext, 300)
      ).resolves.toBeUndefined();
      await expect(
        authCacheService.invalidate('test-key')
      ).resolves.toBeUndefined();
    });
  });
});
