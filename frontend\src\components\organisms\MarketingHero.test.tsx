import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { ArrowRight, Star } from "lucide-react";
import { MarketingHero } from "./MarketingHero";

const defaultProps = {
  title: "Transform Your Business",
  description:
    "Powerful tools to help you grow and succeed in the modern marketplace.",
};

describe("MarketingHero", () => {
  describe("Basic Rendering", () => {
    it("should render title and description", () => {
      render(<MarketingHero {...defaultProps} />);

      expect(screen.getByRole("heading", { level: 1 })).toHaveTextContent(
        "Transform Your Business",
      );
      expect(
        screen.getByText(
          "Powerful tools to help you grow and succeed in the modern marketplace.",
        ),
      ).toBeInTheDocument();
    });

    it("should apply default centered layout", () => {
      render(<MarketingHero {...defaultProps} />);

      const container = screen
        .getByRole("heading", { level: 1 })
        .closest("div");
      expect(container).toHaveClass("text-center", "max-w-4xl", "mx-auto");
    });

    it("should apply Salient gradient background", () => {
      render(<MarketingHero {...defaultProps} />);

      const heroContainer = screen
        .getByRole("heading", { level: 1 })
        .closest('div[class*="bg-gradient-to-b"]');
      expect(heroContainer).toHaveClass(
        "bg-gradient-to-b",
        "from-gray-50",
        "to-white",
      );
    });
  });

  describe("Layout Variants", () => {
    it("should render centered layout", () => {
      render(<MarketingHero {...defaultProps} layout="centered" />);

      const container = screen
        .getByRole("heading", { level: 1 })
        .closest("div");
      expect(container).toHaveClass("text-center", "max-w-4xl", "mx-auto");
    });

    it("should render split layout", () => {
      render(<MarketingHero {...defaultProps} layout="split" />);

      // Find the container with the grid layout classes
      const gridContainer = screen
        .getByRole("heading", { level: 1 })
        .closest('div[class*="grid"]');
      expect(gridContainer).toHaveClass(
        "grid",
        "lg:grid-cols-2",
        "gap-12",
        "lg:gap-16",
        "items-center",
      );
    });

    it("should render minimal layout", () => {
      render(<MarketingHero {...defaultProps} layout="minimal" />);

      const container = screen
        .getByRole("heading", { level: 1 })
        .closest("div");
      expect(container).toHaveClass("text-center", "max-w-3xl", "mx-auto");
    });

    it("should render feature-rich layout", () => {
      render(<MarketingHero {...defaultProps} layout="feature-rich" />);

      const container = screen
        .getByRole("heading", { level: 1 })
        .closest("div");
      expect(container).toHaveClass("text-center", "max-w-5xl", "mx-auto");
    });
  });

  describe("Announcement Badge", () => {
    it("should render announcement badge", () => {
      const announcement = {
        text: "New Feature Available",
        variant: "primary" as const,
        icon: <Star data-testid="star-icon" />,
      };

      render(<MarketingHero {...defaultProps} announcement={announcement} />);

      expect(screen.getByText("New Feature Available")).toBeInTheDocument();
      expect(screen.getByTestId("star-icon")).toBeInTheDocument();
    });

    it("should not render announcement when not provided", () => {
      render(<MarketingHero {...defaultProps} />);

      expect(
        screen.queryByRole("generic", { name: /announcement/i }),
      ).not.toBeInTheDocument();
    });
  });

  describe("Feature Highlights", () => {
    it("should render feature list with checkmarks", () => {
      const features = ["Easy Setup", "Secure & Reliable", "24/7 Support"];

      render(<MarketingHero {...defaultProps} features={features} />);

      expect(screen.getByText("Easy Setup")).toBeInTheDocument();
      expect(screen.getByText("Secure & Reliable")).toBeInTheDocument();
      expect(screen.getByText("24/7 Support")).toBeInTheDocument();

      // Check for checkmark SVG icons
      const svgElements = document.querySelectorAll("svg");
      expect(svgElements).toHaveLength(3);
    });

    it("should not render features when empty array", () => {
      render(<MarketingHero {...defaultProps} features={[]} />);

      expect(screen.queryByRole("list")).not.toBeInTheDocument();
    });
  });

  describe("Call-to-Action Buttons", () => {
    it("should render primary CTA button", () => {
      const primaryCta = {
        text: "Get Started",
        onClick: vi.fn(),
        icon: <ArrowRight data-testid="arrow-icon" />,
      };

      render(<MarketingHero {...defaultProps} primaryCta={primaryCta} />);

      const button = screen.getByRole("button", { name: /get started/i });
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass(
        "bg-gradient-to-r",
        "from-primary-600",
        "to-primary-700",
      );
      expect(screen.getByTestId("arrow-icon")).toBeInTheDocument();
    });

    it("should render primary CTA as link", () => {
      const primaryCta = {
        text: "Get Started",
        href: "/signup",
      };

      render(<MarketingHero {...defaultProps} primaryCta={primaryCta} />);

      const link = screen.getByRole("link", { name: /get started/i });
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute("href", "/signup");
    });

    it("should render secondary CTA button", () => {
      const secondaryCta = {
        text: "Learn More",
        onClick: vi.fn(),
        variant: "outline" as const,
      };

      render(<MarketingHero {...defaultProps} secondaryCta={secondaryCta} />);

      const button = screen.getByRole("button", { name: /learn more/i });
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass("border-2", "border-gray-300", "bg-white");
    });

    it("should handle CTA button clicks", () => {
      const handlePrimaryClick = vi.fn();
      const handleSecondaryClick = vi.fn();

      const primaryCta = { text: "Get Started", onClick: handlePrimaryClick };
      const secondaryCta = {
        text: "Learn More",
        onClick: handleSecondaryClick,
      };

      render(
        <MarketingHero
          {...defaultProps}
          primaryCta={primaryCta}
          secondaryCta={secondaryCta}
        />,
      );

      fireEvent.click(screen.getByRole("button", { name: /get started/i }));
      fireEvent.click(screen.getByRole("button", { name: /learn more/i }));

      expect(handlePrimaryClick).toHaveBeenCalledTimes(1);
      expect(handleSecondaryClick).toHaveBeenCalledTimes(1);
    });
  });

  describe("Social Proof", () => {
    it("should render social proof with stats", () => {
      const socialProof = {
        text: "Trusted by thousands of companies",
        stats: [
          { value: "10,000+", label: "Active Users" },
          { value: "99.9%", label: "Uptime" },
        ],
      };

      render(<MarketingHero {...defaultProps} socialProof={socialProof} />);

      expect(
        screen.getByText("Trusted by thousands of companies"),
      ).toBeInTheDocument();
      expect(screen.getByText("10,000+")).toBeInTheDocument();
      expect(screen.getByText("Active Users")).toBeInTheDocument();
      expect(screen.getByText("99.9%")).toBeInTheDocument();
      expect(screen.getByText("Uptime")).toBeInTheDocument();
    });

    it("should render social proof with logos", () => {
      const socialProof = {
        text: "Trusted by industry leaders",
        logos: [
          { src: "/logo1.png", alt: "Company 1" },
          { src: "/logo2.png", alt: "Company 2" },
        ],
      };

      render(<MarketingHero {...defaultProps} socialProof={socialProof} />);

      expect(
        screen.getByText("Trusted by industry leaders"),
      ).toBeInTheDocument();
      expect(screen.getByAltText("Company 1")).toBeInTheDocument();
      expect(screen.getByAltText("Company 2")).toBeInTheDocument();
    });
  });

  describe("Hero Visual", () => {
    it("should render hero visual in split layout", () => {
      const heroVisual = {
        src: "/hero-image.jpg",
        alt: "Product Screenshot",
      };

      render(
        <MarketingHero
          {...defaultProps}
          layout="split"
          heroVisual={heroVisual}
        />,
      );

      const image = screen.getByAltText("Product Screenshot");
      expect(image).toBeInTheDocument();
      expect(image).toHaveClass(
        "w-full",
        "h-auto",
        "rounded-2xl",
        "shadow-2xl",
      );
    });

    it("should not render hero visual when not provided", () => {
      render(<MarketingHero {...defaultProps} layout="split" />);

      expect(screen.queryByRole("img")).not.toBeInTheDocument();
    });
  });

  describe("Custom Content", () => {
    it("should render custom content", () => {
      const customContent = (
        <div data-testid="custom-content">Custom marketing content</div>
      );

      render(<MarketingHero {...defaultProps} customContent={customContent} />);

      expect(screen.getByTestId("custom-content")).toBeInTheDocument();
      expect(screen.getByText("Custom marketing content")).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("should have proper heading hierarchy", () => {
      render(<MarketingHero {...defaultProps} />);

      const heading = screen.getByRole("heading", { level: 1 });
      expect(heading).toBeInTheDocument();
      expect(heading).toHaveClass("text-4xl", "font-bold", "tracking-tight");
    });

    it("should have proper focus states on buttons", () => {
      const primaryCta = { text: "Get Started", onClick: vi.fn() };

      render(<MarketingHero {...defaultProps} primaryCta={primaryCta} />);

      const button = screen.getByRole("button", { name: /get started/i });
      expect(button).toHaveClass(
        "focus:outline-none",
        "focus:ring-2",
        "focus:ring-offset-2",
      );
    });
  });

  describe("Responsive Design", () => {
    it("should have responsive typography classes", () => {
      render(<MarketingHero {...defaultProps} />);

      const heading = screen.getByRole("heading", { level: 1 });
      expect(heading).toHaveClass("text-4xl", "sm:text-6xl");
    });

    it("should have responsive padding", () => {
      render(<MarketingHero {...defaultProps} />);

      const container = screen
        .getByRole("heading", { level: 1 })
        .closest('div[class*="py-20"]');
      expect(container).toHaveClass("py-20", "sm:py-32");
    });
  });
});
