import { RoleType, PermissionResource, PermissionAction } from '@prisma/client';

export interface CreateRoleData {
  name: string;
  type: RoleType;
  description: string;
  isSystemRole?: boolean;
  permissionIds?: string[];
}

export interface CreatePermissionData {
  name: string;
  description: string;
  resource: PermissionResource;
  action: PermissionAction;
}

export interface AssignRoleData {
  userId: string;
  roleId: string;
  tenantId?: string; // MUST be null for system roles
  assignedBy: string;
}

export interface RoleContext {
  tenantId?: string;
}

export interface PermissionCheck {
  resource: PermissionResource;
  action: PermissionAction;
  context?: RoleContext;
}

// Re-export UserRoleWithContext from auth.types for convenience
export type { UserRoleWithContext } from '@tech-notes/shared';
