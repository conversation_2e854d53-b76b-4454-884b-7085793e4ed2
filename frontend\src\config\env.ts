/**
 * Centralized environment variable validation for frontend
 * Ensures all required environment variables are present and valid
 */

function validateEnv(key: string, defaultValue?: string): string {
  const value = import.meta.env[key] || defaultValue;

  if (!value) {
    throw new Error(`Missing required environment variable: ${key}`);
  }

  return value;
}

export const env = {
  clerkPublishableKey: validateEnv("VITE_CLERK_PUBLISHABLE_KEY"),
  apiUrl: validateEnv("VITE_API_URL", "http://localhost:8081"),
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,
  mode: import.meta.env.MODE,
} as const;

// Log environment configuration in development (disabled during tests)
if (env.isDevelopment && env.mode !== "test") {
  console.log("Frontend Environment Configuration:", {
    clerkPublishableKey: env.clerkPublishableKey ? "✓ Set" : "✗ Missing",
    apiUrl: env.apiUrl,
    mode: env.mode,
    isDevelopment: env.isDevelopment,
    isProduction: env.isProduction,
  });
}
