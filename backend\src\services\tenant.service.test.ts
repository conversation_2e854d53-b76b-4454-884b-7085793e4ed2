import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { BackendAuthContext } from '@tech-notes/shared';

import {
  createMockPrismaService,
  createMockLogger,
  testData,
  MockPrismaService,
  MockLogger,
} from '../__tests__/simple-mocks.js';
import {
  ConflictError,
  NotFoundError,
  ValidationError,
} from '../types/error.types.js';

import { TenantService } from './tenant.service.js';

describe('TenantService', () => {
  let tenantService: TenantService;
  let mockPrisma: MockPrismaService;
  let mockLogger: MockLogger;
  let mockAuthContext: BackendAuthContext;

  beforeEach(() => {
    // Create simple, direct mocks
    mockPrisma = createMockPrismaService();
    mockLogger = createMockLogger();

    tenantService = new TenantService(mockPrisma as any, mockLogger as any);

    // Mock auth context for tests
    mockAuthContext = {
      id: 'test-user-id',
      clerkId: 'test-clerk-id',
      tenantId: 'test-tenant-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      imageUrl: null,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      canBypassTenantScope: true, // System admin for tests
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createTenant', () => {
    it('should create a new tenant', async () => {
      // Arrange
      const tenantData = {
        name: 'Test Organization',
        slug: 'test-org',
      };
      const expectedTenant = {
        id: 'test-tenant-id',
        name: 'Test Organization',
        slug: 'test-org',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.prisma.tenant.create.mockResolvedValue(expectedTenant);

      // Act
      const tenant = await tenantService.createTenant(
        tenantData,
        mockAuthContext
      );

      // Assert
      expect(tenant).toEqual(expectedTenant);
      expect(mockPrisma.prisma.tenant.create).toHaveBeenCalledWith({
        data: tenantData,
      });
    });

    it('should throw ConflictError for duplicate slug', async () => {
      // Arrange
      const tenantData = {
        name: 'Test Organization',
        slug: 'test-org',
      };

      // Create a proper PrismaClientKnownRequestError instance
      const error = new PrismaClientKnownRequestError(
        'Unique constraint violation',
        {
          code: 'P2002',
          clientVersion: '5.0.0',
        }
      );

      mockPrisma.prisma.tenant.create.mockRejectedValue(error);

      // Act & Assert
      await expect(
        tenantService.createTenant(tenantData, mockAuthContext)
      ).rejects.toThrow(ConflictError);
      expect(mockPrisma.prisma.tenant.create).toHaveBeenCalledWith({
        data: tenantData,
      });
    });

    it('should throw ValidationError for invalid slug format', async () => {
      // Arrange
      const tenantData = {
        name: 'Test Organization',
        slug: 'Test Org!', // Invalid characters
      };

      // Act & Assert
      await expect(
        tenantService.createTenant(tenantData, mockAuthContext)
      ).rejects.toThrow(ValidationError);
      expect(mockPrisma.prisma.tenant.findUnique).not.toHaveBeenCalled();
      expect(mockPrisma.prisma.tenant.create).not.toHaveBeenCalled();
    });
  });

  describe('getTenantById', () => {
    it('should return tenant by ID', async () => {
      // Arrange
      const tenantId = testData.tenant.id;
      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(testData.tenant);

      // Act
      const retrieved = await tenantService.getTenantById(
        tenantId,
        mockAuthContext
      );

      // Assert
      expect(retrieved).toEqual(testData.tenant);
      expect(mockPrisma.prisma.tenant.findUnique).toHaveBeenCalledWith({
        where: { id: tenantId },
      });
    });

    it('should return null for non-existent tenant', async () => {
      // Arrange
      const tenantId = 'non-existent-id';
      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(null);

      // Act
      const result = await tenantService.getTenantById(
        tenantId,
        mockAuthContext
      );

      // Assert
      expect(result).toBeNull();
      expect(mockPrisma.prisma.tenant.findUnique).toHaveBeenCalledWith({
        where: { id: tenantId },
      });
    });
  });

  describe('getTenantBySlug', () => {
    it('should return tenant by slug', async () => {
      // Arrange
      const slug = testData.tenant.slug;
      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(testData.tenant);

      // Act
      const retrieved = await tenantService.getTenantBySlug(slug);

      // Assert
      expect(retrieved).toEqual(testData.tenant);
      expect(mockPrisma.prisma.tenant.findUnique).toHaveBeenCalledWith({
        where: { slug },
      });
    });

    it('should return null for non-existent slug', async () => {
      // Arrange
      const slug = 'non-existent-slug';
      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(null);

      // Act
      const result = await tenantService.getTenantBySlug(slug);

      // Assert
      expect(result).toBeNull();
      expect(mockPrisma.prisma.tenant.findUnique).toHaveBeenCalledWith({
        where: { slug },
      });
    });
  });

  describe('updateTenant', () => {
    it('should update tenant data', async () => {
      // Arrange
      const tenantId = testData.tenant.id;
      const updateData = { name: 'Updated Organization' };
      const updatedTenant = {
        ...testData.tenant,
        name: 'Updated Organization',
      };

      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(testData.tenant);
      mockPrisma.prisma.tenant.update.mockResolvedValue(updatedTenant);

      // Act
      const updated = await tenantService.updateTenant(
        tenantId,
        updateData,
        mockAuthContext
      );

      // Assert
      expect(updated.name).toBe('Updated Organization');
      expect(updated.slug).toBe(testData.tenant.slug); // Should remain unchanged
      expect(mockPrisma.prisma.tenant.findUnique).toHaveBeenCalledWith({
        where: { id: tenantId },
      });
      expect(mockPrisma.prisma.tenant.update).toHaveBeenCalledWith({
        where: { id: tenantId },
        data: updateData,
      });
    });

    it('should throw NotFoundError for non-existent tenant', async () => {
      // Arrange
      const tenantId = 'non-existent-id';
      const updateData = { name: 'Updated' };
      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(
        tenantService.updateTenant(tenantId, updateData, mockAuthContext)
      ).rejects.toThrow(NotFoundError);
      expect(mockPrisma.prisma.tenant.update).not.toHaveBeenCalled();
    });

    it('should throw ValidationError for invalid slug format', async () => {
      // Arrange
      const tenantId = testData.tenant.id;
      const updateData = { slug: 'Invalid Slug!' };

      // Act & Assert
      await expect(
        tenantService.updateTenant(tenantId, updateData, mockAuthContext)
      ).rejects.toThrow(ValidationError);
      expect(mockPrisma.prisma.tenant.findUnique).not.toHaveBeenCalled();
      expect(mockPrisma.prisma.tenant.update).not.toHaveBeenCalled();
    });
  });

  describe('isSlugAvailable', () => {
    it('should return true for available slug', async () => {
      // Arrange
      const slug = 'available-slug';
      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(null);

      // Act
      const available = await tenantService.isSlugAvailable(slug);

      // Assert
      expect(available).toBe(true);
      expect(mockPrisma.prisma.tenant.findUnique).toHaveBeenCalledWith({
        where: { slug },
        select: { id: true },
      });
    });

    it('should return false for taken slug', async () => {
      // Arrange
      const slug = 'taken-slug';
      const existingTenant = { id: 'existing-tenant-id' };
      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(
        existingTenant as any
      );

      // Act
      const available = await tenantService.isSlugAvailable(slug);

      // Assert
      expect(available).toBe(false);
      expect(mockPrisma.prisma.tenant.findUnique).toHaveBeenCalledWith({
        where: { slug },
        select: { id: true },
      });
    });

    it('should return true when excluding the same tenant', async () => {
      // Arrange
      const slug = 'test-slug';
      const tenantId = testData.tenant.id;
      const existingTenant = { id: tenantId };
      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(
        existingTenant as any
      );

      // Act
      const available = await tenantService.isSlugAvailable(slug, tenantId);

      // Assert
      expect(available).toBe(true);
      expect(mockPrisma.prisma.tenant.findUnique).toHaveBeenCalledWith({
        where: { slug },
        select: { id: true },
      });
    });
  });

  describe('deleteTenant', () => {
    it('should delete tenant', async () => {
      // Arrange
      const tenantId = testData.tenant.id;
      const tenantWithCount = {
        ...testData.tenant,
        _count: { users: 0 },
      };
      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(tenantWithCount);
      mockPrisma.prisma.tenant.delete.mockResolvedValue(testData.tenant);

      // Act
      await tenantService.deleteTenant(tenantId, mockAuthContext);

      // Assert
      expect(mockPrisma.prisma.tenant.findUnique).toHaveBeenCalledWith({
        where: { id: tenantId },
        include: { _count: { select: { users: true } } },
      });
      expect(mockPrisma.prisma.tenant.delete).toHaveBeenCalledWith({
        where: { id: tenantId },
      });
    });

    it('should throw NotFoundError for non-existent tenant', async () => {
      // Arrange
      const tenantId = 'non-existent-id';
      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(
        tenantService.deleteTenant(tenantId, mockAuthContext)
      ).rejects.toThrow(NotFoundError);
      expect(mockPrisma.prisma.tenant.delete).not.toHaveBeenCalled();
    });
  });
});
