import React, { useState, useEffect } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  LoadingSpinner,
} from "../../../components";
import { useTypedApi } from "../../../services/api-client";
import type { VehicleModel } from "../../../services/api-client";
import { Settings, Calendar, Plus, X } from "lucide-react";

interface ModelYearAssociationModalProps {
  isOpen: boolean;
  onClose: () => void;
  model: VehicleModel | null;
}

export const ModelYearAssociationModal: React.FC<
  ModelYearAssociationModalProps
> = ({ isOpen, onClose, model }) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [selectedYearIds, setSelectedYearIds] = useState<string[]>([]);

  // Fetch all years
  const { data: yearsResponse, isLoading: isLoadingYears } = useQuery({
    queryKey: ["vehicle-years"],
    queryFn: () => api.vehicleHierarchy.getYears(),
    enabled: isOpen,
  });

  // Fetch current model-year associations
  const { data: modelYearsResponse, isLoading: isLoadingModelYears } = useQuery(
    {
      queryKey: ["model-years", model?.id],
      queryFn: async () => {
        if (!model) {
          return { data: [], meta: { count: 0, tenantId: "" } };
        }
        return api.vehicleHierarchy.getYearsByModel(model.id);
      },
      enabled: isOpen && !!model,
    },
  );

  // Associate model with years mutation
  const associateYearsMutation = useMutation({
    mutationFn: (yearIds: string[]) => {
      if (!model) throw new Error("No model selected");
      return api.vehicleHierarchy.associateModelWithYears(model.id, {
        yearIds,
      });
    },
    onSuccess: () => {
      toast.success("Model-year associations updated successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      queryClient.invalidateQueries({ queryKey: ["model-years", model?.id] });
      handleClose();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update associations");
    },
  });

  // Remove association mutation
  const removeAssociationMutation = useMutation({
    mutationFn: (yearId: string) => {
      if (!model) throw new Error("No model selected");
      return api.vehicleHierarchy.removeModelYearAssociation(model.id, yearId);
    },
    onSuccess: () => {
      toast.success("Association removed successfully");
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-full"] });
      queryClient.invalidateQueries({ queryKey: ["model-years", model?.id] });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to remove association");
    },
  });

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen && modelYearsResponse?.data) {
      setSelectedYearIds(modelYearsResponse.data.map((year) => year.id));
    } else {
      setSelectedYearIds([]);
    }
  }, [isOpen, modelYearsResponse]);

  const handleClose = () => {
    setSelectedYearIds([]);
    onClose();
  };

  const toggleYear = (yearId: string) => {
    setSelectedYearIds((prev) =>
      prev.includes(yearId)
        ? prev.filter((id) => id !== yearId)
        : [...prev, yearId],
    );
  };

  const handleSave = () => {
    if (!model) return;

    const currentYearIds =
      modelYearsResponse?.data.map((year) => year.id) || [];
    const newYearIds = selectedYearIds.filter(
      (id) => !currentYearIds.includes(id),
    );

    if (newYearIds.length > 0) {
      associateYearsMutation.mutate(newYearIds);
    } else {
      handleClose();
    }
  };

  const handleRemoveAssociation = (yearId: string) => {
    removeAssociationMutation.mutate(yearId);
  };

  const currentAssociations = modelYearsResponse?.data || [];
  const availableYears =
    yearsResponse?.data.filter(
      (year) => !currentAssociations.some((assoc) => assoc.id === year.id),
    ) || [];

  const isLoading = isLoadingYears || isLoadingModelYears;
  const isPending =
    associateYearsMutation.isPending || removeAssociationMutation.isPending;

  if (!model) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Manage Model-Year Associations"
      size="lg"
      zIndex={60}
    >
      <div className="space-y-6">
        <div className="flex items-center space-x-3 text-gray-600">
          <Settings className="h-5 w-5" />
          <span>
            Managing associations for: <strong>{model.name}</strong>
          </span>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner size="md" />
          </div>
        ) : (
          <div className="space-y-6">
            {/* Current Associations */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                Current Associations
              </h3>
              {currentAssociations.length > 0 ? (
                <div className="space-y-2">
                  {currentAssociations
                    .sort((a, b) => b.year - a.year)
                    .map((year) => (
                      <div
                        key={year.id}
                        className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          <Calendar className="h-4 w-4 text-green-600" />
                          <span className="font-medium text-green-900">
                            {year.year}
                          </span>
                          <Badge variant="success">Associated</Badge>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveAssociation(year.id)}
                          disabled={isPending}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                </div>
              ) : (
                <div className="text-center py-6 text-gray-500">
                  <Calendar className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p>No year associations found</p>
                </div>
              )}
            </div>

            {/* Available Years */}
            {availableYears.length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  Add New Associations
                </h3>
                <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-lg p-3 space-y-2">
                  {availableYears
                    .sort((a, b) => b.year - a.year)
                    .map((year) => (
                      <label
                        key={year.id}
                        className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded"
                      >
                        <input
                          type="checkbox"
                          checked={selectedYearIds.includes(year.id)}
                          onChange={() => toggleYear(year.id)}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-sm font-medium">{year.year}</span>
                      </label>
                    ))}
                </div>
                <div className="mt-3">
                  <Badge variant="secondary">
                    {selectedYearIds.length} year
                    {selectedYearIds.length !== 1 ? "s" : ""} selected
                  </Badge>
                </div>
              </div>
            )}
          </div>
        )}

        {(associateYearsMutation.error || removeAssociationMutation.error) && (
          <Alert variant="error">
            {associateYearsMutation.error?.message ||
              removeAssociationMutation.error?.message ||
              "Operation failed"}
          </Alert>
        )}

        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isPending}
          >
            Cancel
          </Button>
          {selectedYearIds.length > 0 && (
            <Button
              onClick={handleSave}
              disabled={isPending}
              className="flex items-center space-x-2"
            >
              {isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4" />
                  <span>Add Associations</span>
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};
