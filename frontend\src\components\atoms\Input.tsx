import React from "react";
import { clsx } from "clsx";

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  icon,
  className,
  id,
  ...props
}) => {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className="w-full">
      {label && (
        <label
          htmlFor={inputId}
          className="block text-sm font-semibold text-gray-800 mb-2 tracking-tight"
        >
          {label}
        </label>
      )}
      <div className="relative">
        {icon && (
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none text-gray-500">
            {icon}
          </div>
        )}
        <input
          id={inputId}
          className={clsx(
            "w-full px-4 py-3 border border-gray-300 rounded-xl bg-white shadow-xs focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 hover:border-gray-400 transition-all duration-300 placeholder-gray-500 text-gray-900 font-medium",
            icon && "pl-11",
            error &&
              "border-error-300 focus:border-error-500 focus:ring-error-500/20 hover:border-error-400",
            className,
          )}
          {...props}
        />
      </div>
      {error && (
        <p className="mt-2 text-sm text-error-600 font-medium">{error}</p>
      )}
    </div>
  );
};
