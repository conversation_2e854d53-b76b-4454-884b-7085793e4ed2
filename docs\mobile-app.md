# Mobile App - Tech Notes

## Overview

React Native mobile app built with Expo SDK 53, using standalone architecture (not part of yarn workspace).

## Architecture Decision

**Standalone Approach**: Mobile app is intentionally **not** part of the shared workspace to avoid:
- React version conflicts (mobile uses React 19, web uses React 18.3.1)
- Complex Metro configuration requirements
- Dependency resolution issues in monorepo

## Tech Stack

- **Expo SDK 53** with React Native 0.79.5
- **React 19.0.0** (latest stable)
- **Expo Router** for file-based navigation
- **TypeScript** support
- **npm** for dependency management (separate from monorepo yarn)

## Development

```bash
# Start mobile development
cd mobile && npm start

# Test on device
# Scan QR code with Expo Go app

# Web preview
cd mobile && npm run web
```

## Code Sharing Strategy

**Current**: Manual copy of essential types/constants from shared workspace (implemented in `src/types/` and `src/constants/`)
**Future**: Evaluate minimal shared workspace only if significant duplication emerges

**Priority**: Stable, working mobile app over theoretical code sharing benefits

## Project Structure

```
mobile/
├── app/                 # Expo Router screens
├── src/                 # Source code
│   ├── design-system/   # Mobile design system (tokens + components)
│   ├── types/           # Copied types from shared workspace
│   └── constants/       # Copied constants from shared workspace
├── components/          # Expo-generated components
├── constants/           # App constants
├── hooks/              # Custom hooks
├── assets/             # Images, fonts
└── package.json        # npm dependencies (not workspace)
```

## Authentication

Uses existing Clerk setup with mobile-specific configuration:
- `@clerk/clerk-expo` for React Native
- `expo-secure-store` for token persistence
- Same Clerk project as web app

## API Integration

Connects to existing backend API:
- Authenticated requests using Clerk tokens
- React Query for data fetching
- Same endpoints as web app (`/api/v1/*`)

## Key Files

- `app.json` - Expo configuration
- `app.config.js` - Environment variable configuration
- `app/_layout.tsx` - Root layout with providers
- `app/(tabs)/` - Main app screens (Dashboard, Profile, Settings)
- `src/design-system/` - Mobile design system tokens and components
- `src/types/auth.types.ts` - Authentication types (copied from shared)
- `src/constants/api.constants.ts` - API endpoints (copied from shared)
