import React, { useEffect, useState } from "react";
import { Card } from "../atoms/Card";
import { Button } from "../atoms/Button";
import { Badge } from "../atoms/Badge";
import { SetupRequiredBadge } from "../atoms/SetupRequiredBadge";
import type {
  PricingTier as PricingTierType,
  Feature,
} from "../../types/pricing.types";
import { Users, Zap, Star, Shield, Check, X } from "lucide-react";
import { clsx } from "clsx";
import { getFeaturesForTier } from "../../services/pricing-data.service";

interface PricingTierProps {
  tier: PricingTierType;
}

const iconMap = {
  Users,
  Zap,
  Star,
  Shield,
  Check,
  X,
};

const tierHierarchy = ["basic", "plus", "pro", "enterprise"];

export const PricingTier: React.FC<PricingTierProps> = ({ tier }) => {
  const [features, setFeatures] = useState<Feature[]>([]);
  const [loading, setLoading] = useState(true);
  const IconComponent = iconMap[tier.icon as keyof typeof iconMap] || Users;

  useEffect(() => {
    const loadFeatures = async () => {
      try {
        const tierFeatures = await getFeaturesForTier(tier.name);
        setFeatures(tierFeatures);
      } catch (error) {
        console.error("Error loading tier features:", error);
      } finally {
        setLoading(false);
      }
    };

    loadFeatures();
  }, [tier.name]);

  const getPreviousTier = (currentTier: string): string | null => {
    const currentIndex = tierHierarchy.indexOf(currentTier.toLowerCase());
    return currentIndex > 0 ? tierHierarchy[currentIndex - 1] : null;
  };

  const getNewFeaturesForTier = (): Feature[] => {
    const currentTierName = tier.name.toLowerCase();
    const previousTier = getPreviousTier(currentTierName);

    if (!previousTier) {
      // For Basic tier, show all features
      return features;
    }

    // For other tiers, show only features that are new in this tier
    return features.filter((feature) => {
      const currentTierKey = currentTierName as
        | "basic"
        | "plus"
        | "pro"
        | "enterprise";
      const previousTierKey = previousTier as
        | "basic"
        | "plus"
        | "pro"
        | "enterprise";

      const currentTierValue = feature[currentTierKey];
      const previousTierValue = feature[previousTierKey];

      // Check if feature is available in current tier
      const isAvailableInCurrent =
        currentTierValue === true ||
        (typeof currentTierValue === "string" && currentTierValue.length > 0);
      // Check if feature was NOT available in previous tier
      const wasNotAvailableInPrevious =
        previousTierValue === false || !previousTierValue;

      // Feature is new if it's available in current tier but not in previous tier
      return isAvailableInCurrent && wasNotAvailableInPrevious;
    });
  };

  const newFeatures = getNewFeaturesForTier();

  return (
    <Card
      variant={tier.popular ? "elevated" : "default"}
      className={clsx(
        "relative p-8 h-full flex flex-col",
        tier.popular && "ring-2 ring-primary-600 shadow-lg",
      )}
    >
      {tier.popular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <Badge variant="primary" size="md">
            Best Value
          </Badge>
        </div>
      )}

      <div className="flex-1">
        {/* Icon */}
        <div className="flex justify-center mb-6">
          <div className="h-16 w-16 bg-primary-100 rounded-full flex items-center justify-center">
            <IconComponent className="h-8 w-8 text-primary-600" />
          </div>
        </div>

        {/* Plan Name */}
        <h3 className="text-2xl font-bold text-gray-900 text-center mb-2">
          {tier.name}
        </h3>

        {/* Description */}
        <p className="text-gray-600 text-center mb-6">{tier.description}</p>

        {/* Price */}
        <div className="text-center mb-8">
          <div className="flex items-baseline justify-center">
            <span className="text-4xl font-bold text-gray-900">
              {tier.price}
            </span>
            {tier.price !== "Custom" && (
              <span className="text-gray-600 ml-1">/{tier.period}</span>
            )}
          </div>
        </div>

        {/* Features */}
        {!loading && newFeatures.length > 0 && (
          <div className="mb-8 flex-1">
            {/* Add "Includes:" for Basic tier, or "Includes everything in [Previous], plus:" for others */}
            {tier.name.toLowerCase() === "basic" ? (
              <p className="text-sm text-gray-600 mb-3">Includes:</p>
            ) : (
              getPreviousTier(tier.name.toLowerCase()) && (
                <p className="text-sm text-gray-600 mb-3">
                  Includes everything in{" "}
                  {(() => {
                    const prevTier = getPreviousTier(tier.name.toLowerCase());
                    return prevTier
                      ? prevTier.charAt(0).toUpperCase() + prevTier.slice(1)
                      : "";
                  })()}
                  , plus:
                </p>
              )
            )}
            <ul className="space-y-2">
              {newFeatures.map((feature, index) => (
                <li key={index} className="flex items-start text-sm">
                  <Check className="h-4 w-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="text-gray-700">{feature.name}</span>
                      {feature.setupFee && <SetupRequiredBadge />}
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* CTA Button */}
      <div className="mt-auto">
        <Button
          variant={tier.buttonVariant}
          className="w-full"
          size="lg"
          asChild
        >
          <a
            href={`mailto:<EMAIL>?subject=${encodeURIComponent(tier.buttonText)}`}
            className="inline-flex items-center justify-center"
          >
            {tier.buttonText}
          </a>
        </Button>
      </div>
    </Card>
  );
};
