#!/usr/bin/env ts-node

/**
 * Debug script to understand why the migration didn't create user_roles records
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function debugMigration() {
  try {
    console.log('🔍 Debugging migration issue...\n');

    const targetClerkId = 'user_2yqMjrYXAekqQVzPeusn3YFLB8X';

    // 1. Check if user exists
    console.log('1. Checking if user exists...');
    const user = await prisma.user.findUnique({
      where: { clerkId: targetClerkId },
    });

    if (!user) {
      console.log('❌ User not found!');
      return;
    }

    console.log('✅ User found:');
    console.log(`   Database ID: ${user.id}`);
    console.log(`   Clerk ID: ${user.clerkId}`);
    console.log(`   Email: ${user.email}\n`);

    // 2. Check if System Admin role exists
    console.log('2. Checking if System Admin role exists...');
    const systemAdminRole = await prisma.role.findUnique({
      where: { id: 'role_system_admin' },
    });

    if (!systemAdminRole) {
      console.log('❌ System Admin role not found!');
      return;
    }

    console.log('✅ System Admin role found:');
    console.log(`   Role ID: ${systemAdminRole.id}`);
    console.log(`   Role Name: ${systemAdminRole.name}`);
    console.log(`   Role Type: ${systemAdminRole.type}\n`);

    // 3. Check existing user roles
    console.log('3. Checking existing user roles...');
    const existingRoles = await prisma.userRole.findMany({
      where: { userId: user.id },
      include: { role: true },
    });

    console.log(`Found ${existingRoles.length} existing roles for this user:`);
    existingRoles.forEach((ur) => {
      console.log(
        `   - ${ur.role.name} (${ur.role.type}) - Tenant: ${ur.tenantId || 'Global'}`
      );
    });

    // 4. Check if user already has System Admin role
    console.log('\n4. Checking for existing System Admin role...');
    const hasSystemAdmin = existingRoles.some(
      (ur) => ur.role.type === 'SYSTEM_ADMIN'
    );
    console.log(`Has System Admin role: ${hasSystemAdmin ? 'YES' : 'NO'}`);

    // 5. Test the migration query manually
    console.log('\n5. Testing migration query logic...');
    const migrationResult = await prisma.$queryRaw`
      SELECT 
        u.id as user_id,
        u."clerkId",
        u.email,
        CASE 
          WHEN EXISTS (
            SELECT 1 FROM "user_roles" ur
            JOIN "roles" r ON ur."roleId" = r.id
            WHERE ur."userId" = u.id 
              AND r."type" = 'SYSTEM_ADMIN'
          ) THEN 'HAS_SYSTEM_ADMIN'
          ELSE 'NO_SYSTEM_ADMIN'
        END as admin_status
      FROM "users" u
      WHERE u."clerkId" = ${targetClerkId}
    `;

    console.log('Migration query result:', migrationResult);

    // 6. Show all user_roles in the system
    console.log('\n6. All user_roles in the system:');
    const allUserRoles = await prisma.userRole.findMany({
      include: {
        user: { select: { email: true, clerkId: true } },
        role: { select: { name: true, type: true } },
      },
    });

    console.log(`Total user_roles records: ${allUserRoles.length}`);
    allUserRoles.forEach((ur) => {
      console.log(
        `   - ${ur.user.email} has ${ur.role.name} (${ur.role.type})`
      );
    });
  } catch (error) {
    console.error('❌ Error during debug:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the debug
debugMigration().catch(console.error);
