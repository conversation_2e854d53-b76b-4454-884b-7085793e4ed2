import React from "react";
import { SignIn } from "@clerk/clerk-react";
import { useSearchParams } from "react-router-dom";
import { Button } from "../../components/atoms/Button";

export const SignInPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const invitationToken = searchParams.get("invitation");

  // If there's an invitation token, redirect back to accept-invitation after sign-in
  // Otherwise, redirect to /dashboard which will handle role-based routing
  const redirectUrl = invitationToken
    ? `/accept-invitation?invitation=${encodeURIComponent(invitationToken)}`
    : "/dashboard";
  return (
    <div className="min-h-screen bg-white flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-extrabold text-gray-900">
            {invitationToken
              ? "Accept Your Invitation"
              : "Sign in to Tech Notes"}
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {invitationToken
              ? "Sign in or create an account to accept your invitation"
              : "Access your account to manage your technical documentation"}
          </p>
        </div>
      </div>

      <div className="sm:mx-auto sm:w-full sm:max-w-md flex justify-center">
        <SignIn
          routing="hash"
          signUpUrl={undefined} // Remove signup link
          fallbackRedirectUrl={redirectUrl} // Redirect based on invitation context
          appearance={{
            elements: {
              footer: "hidden", // Hide the footer that contains signup link
              headerTitle: "hidden", // Hide the default title
              headerSubtitle: "hidden", // Hide the default subtitle
              card: "bg-white border border-gray-200 rounded-lg shadow-sm w-full max-w-md mx-auto", // Clean card styling with centering
              cardBox: "shadow-none", // Remove extra shadows
              rootBox: "w-full flex justify-center", // Center the root container
              socialButtonsBlockButton:
                "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 shadow-sm rounded-lg font-medium transition-colors duration-200",
              formButtonPrimary:
                "bg-primary-600 hover:bg-primary-700 text-white font-medium py-2.5 px-4 rounded-lg shadow-sm transition-colors duration-200 w-full",
              formFieldInput:
                "border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200",
              formFieldLabel: "text-sm font-medium text-gray-700 mb-1",
              identityPreviewText: "text-gray-900",
              identityPreviewEditButton:
                "text-primary-600 hover:text-primary-500",
              dividerLine: "bg-gray-200",
              dividerText: "text-gray-500 text-sm",
            },
            layout: {
              socialButtonsPlacement: "top",
            },
          }}
        />
      </div>

      {/* Contact Sales Section */}
      <div className="sm:mx-auto sm:w-full sm:max-w-md mt-8">
        <div className="text-center p-6 bg-gradient-to-br from-primary-50 to-primary-100 border border-primary-200 rounded-lg">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-primary-900 mb-2">
              Need Access?
            </h3>
            <p className="text-sm text-primary-700">
              Tech Notes is invite-only. Contact our sales team to get started
              with your technical documentation platform.
            </p>
          </div>
          <Button
            onClick={() => {
              window.location.href =
                "mailto:<EMAIL>?subject=Tech Notes Access Request&body=Hi, I would like to request access to Tech Notes. Please contact me to discuss getting started.";
            }}
            variant="primary"
            size="sm"
            className="font-medium"
          >
            Contact Sales Team
          </Button>
        </div>
      </div>

      {/* Additional Information */}
      <div className="sm:mx-auto sm:w-full sm:max-w-md mt-4">
        <div className="text-center">
          <p className="text-xs text-gray-500">
            Existing customers can sign in above. New customers should contact
            sales for access.
          </p>
        </div>
      </div>
    </div>
  );
};
