#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to assign System Admin role to a specific user
 * This will allow them to see users from all tenants
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '../src/utils/logger';

const prisma = new PrismaClient();

async function assignSystemAdminToUser() {
  try {
    logger.info('🔧 Starting System Admin role assignment...');

    // The user email you want to make a System Admin
    const targetEmail = '<EMAIL>'; // Change this to your email if different

    logger.info(`🎯 Target user: ${targetEmail}`);

    // Find the user
    const user = await prisma.user.findFirst({
      where: { email: targetEmail },
      include: {
        userRoles: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                type: true,
                isSystemRole: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      logger.error(`❌ User not found: ${targetEmail}`);
      return;
    }

    logger.info(
      `👤 Found user: ${user.firstName} ${user.lastName} (${user.email})`
    );
    logger.info(
      `   Current roles: ${user.userRoles.map((ur) => ur.role.name).join(', ')}`
    );

    // Check if user already has System Admin role
    const hasSystemAdminRole = user.userRoles.some(
      (ur) => ur.role.type === 'SYSTEM_ADMIN'
    );

    if (hasSystemAdminRole) {
      logger.info('✅ User already has System Admin role');
      return;
    }

    // Find the System Admin role
    const systemAdminRole = await prisma.role.findFirst({
      where: { type: 'SYSTEM_ADMIN' },
    });

    if (!systemAdminRole) {
      logger.error('❌ System Admin role not found in database');
      return;
    }

    logger.info(`🔧 Assigning System Admin role...`);

    // Assign System Admin role (tenantId must be null for system roles)
    await prisma.userRole.create({
      data: {
        userId: user.id,
        roleId: systemAdminRole.id,
        tenantId: null, // System roles have no tenant scope
        assignedBy: user.id, // Self-assigned for this script
      },
    });

    logger.info('✅ System Admin role assigned successfully!');
    logger.info('');
    logger.info('🎉 Next steps:');
    logger.info('1. Refresh your browser or log out and log back in');
    logger.info('2. Go to /app/users page');
    logger.info('3. You should now see users from all tenants');
    logger.info('');
    logger.info('📊 Expected behavior:');
    logger.info('- You should see all 3 users from different tenants');
    logger.info('- The page should show tenant information for each user');
    logger.info('- You should have access to system-wide admin features');

    // Verify the assignment
    const updatedUser = await prisma.user.findFirst({
      where: { email: targetEmail },
      include: {
        userRoles: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                type: true,
                isSystemRole: true,
              },
            },
          },
        },
      },
    });

    logger.info('');
    logger.info('🔍 Verification:');
    logger.info(
      `   Updated roles: ${updatedUser?.userRoles.map((ur) => ur.role.name).join(', ')}`
    );

    const canBypassTenantScope =
      updatedUser?.userRoles.some((ur) => ur.role.isSystemRole) || false;
    logger.info(`   Can Bypass Tenant Scope: ${canBypassTenantScope}`);
  } catch (error) {
    logger.error('❌ Failed to assign System Admin role:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the assignment
assignSystemAdminToUser().catch(console.error);
