/**
 * Base CRUD hook providing shared logic for entity management
 * Handles common patterns: React Query, state management, validation, error handling
 */

import React, { useState, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import type {
  BaseEntity,
  BaseCRUDConfig,
  CRUDHookReturn,
  CRUDOperation,
} from "./types/entity-crud.types";
import {
  validateEntityName,
  validateEntityChanges,
  sanitizeEntityName,
} from "./utils/entity-validation.utils";

/**
 * Base CRUD hook that provides common functionality for all entity types
 */
export const useBaseCRUD = <T extends BaseEntity>(
  config: BaseCRUDConfig<T>,
  isOpen: boolean
): CRUDHookReturn<T> => {
  const queryClient = useQueryClient();

  // ===== STATE MANAGEMENT =====
  const [newEntityName, setNewEntityName] = useState("");
  const [editingEntity, setEditingEntity] = useState<T | null>(null);
  const [editEntityName, setEditEntityName] = useState("");
  const [editEntityActive, setEditEntityActive] = useState(true);
  const [deleteConfirmEntity, setDeleteConfirmEntity] = useState<T | null>(null);
  const [currentOperation, setCurrentOperation] = useState<CRUDOperation | null>(null);
  const [error, setError] = useState<string | null>(null);

  // ===== DATA FETCHING =====
  const { data: entitiesResponse, isLoading, error: queryError } = useQuery({
    queryKey: [config.queryKey],
    queryFn: config.apiMethods.getAll,
    enabled: isOpen,
  });

  // Handle query errors
  React.useEffect(() => {
    if (queryError) {
      const errorMessage = queryError instanceof Error ? queryError.message : 'Failed to fetch data';
      setError(errorMessage);
      handleError(queryError instanceof Error ? queryError : new Error(errorMessage), 'fetch');
    }
  }, [queryError]);

  const entities = useMemo(() => entitiesResponse?.data || [], [entitiesResponse?.data]);

  // ===== MUTATIONS =====
  const createMutation = useMutation({
    mutationFn: (name: string) => {
      setCurrentOperation('create');
      return config.apiMethods.create({ name: sanitizeEntityName(name) });
    },
    onSuccess: () => {
      handleSuccess(`${config.entityName} created successfully`);
      queryClient.invalidateQueries({ queryKey: [config.queryKey] });
      setNewEntityName("");
      setCurrentOperation(null);
    },
    onError: (error: Error) => {
      handleError(error, 'create');
      setCurrentOperation(null);
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ entityId, data }: { entityId: string; data: Partial<T> }) => {
      setCurrentOperation('update');
      return config.apiMethods.update(entityId, data);
    },
    onSuccess: () => {
      handleSuccess(`${config.entityName} updated successfully`);
      queryClient.invalidateQueries({ queryKey: [config.queryKey] });
      setEditingEntity(null);
      setEditEntityName("");
      setEditEntityActive(true);
      setCurrentOperation(null);
    },
    onError: (error: Error) => {
      handleError(error, 'update');
      setCurrentOperation(null);
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (entityId: string) => {
      setCurrentOperation('delete');
      return config.apiMethods.delete(entityId);
    },
    onSuccess: () => {
      handleSuccess(`${config.entityName} deleted successfully`);
      queryClient.invalidateQueries({ queryKey: [config.queryKey] });
      setDeleteConfirmEntity(null);
      setCurrentOperation(null);
    },
    onError: (error: Error) => {
      handleError(error, 'delete');
      setCurrentOperation(null);
    },
  });

  // ===== COMPUTED STATE =====
  const isPending = createMutation.isPending || updateMutation.isPending || deleteMutation.isPending;

  const sortedEntities = useMemo(() => {
    return [...entities].sort((a, b) => {
      if (a.displayOrder !== b.displayOrder) {
        return a.displayOrder - b.displayOrder;
      }
      return a.name.localeCompare(b.name);
    });
  }, [entities]);

  // ===== UTILITY FUNCTIONS =====
  const validateName = (name: string): string | null => {
    return validateEntityName(name, config.validation);
  };

  const handleSuccess = (message: string) => {
    toast.success(message);
    setError(null);
  };

  const handleError = (error: Error, operation: CRUDOperation) => {
    const message = error.message || `Failed to ${operation} ${config.entityName.toLowerCase()}`;
    toast.error(message);
    setError(message);
  };

  // ===== ACTION HANDLERS =====
  const handleCreate = () => {
    const nameError = validateName(newEntityName);
    if (nameError) {
      toast.error(nameError);
      return;
    }
    createMutation.mutate(newEntityName.trim());
  };

  const handleEdit = (entity: T) => {
    setEditingEntity(entity);
    setEditEntityName(entity.name);
    setEditEntityActive(entity.isActive);
  };

  const handleUpdate = () => {
    if (!editingEntity) return;
    
    const nameError = validateName(editEntityName);
    if (nameError) {
      toast.error(nameError);
      return;
    }

    const { hasChanges, changes } = validateEntityChanges(
      editingEntity,
      editEntityName,
      editEntityActive
    );

    if (!hasChanges) {
      setEditingEntity(null);
      return;
    }

    updateMutation.mutate({ entityId: editingEntity.id, data: changes });
  };

  const handleDelete = (entity: T) => {
    setDeleteConfirmEntity(entity);
  };

  const confirmDelete = () => {
    if (!deleteConfirmEntity) return;
    deleteMutation.mutate(deleteConfirmEntity.id);
  };

  const handleClose = () => {
    if (isPending) return;
    setEditingEntity(null);
    setDeleteConfirmEntity(null);
    setNewEntityName("");
    setEditEntityName("");
    setEditEntityActive(true);
    setError(null);
    setCurrentOperation(null);
  };

  // ===== RETURN INTERFACE =====
  return {
    // Data state
    entities,
    sortedEntities,
    isLoading,
    error,
    
    // Form state
    newEntityName,
    editingEntity,
    editEntityName,
    editEntityActive,
    deleteConfirmEntity,
    
    // Operation state
    isPending,
    currentOperation,
    
    // State setters
    setNewEntityName,
    setEditingEntity,
    setEditEntityName,
    setEditEntityActive,
    setDeleteConfirmEntity,
    
    // Action handlers
    handleCreate,
    handleEdit,
    handleUpdate,
    handleDelete,
    confirmDelete,
    handleClose,
    
    // Validation
    validateName,
  };
};
