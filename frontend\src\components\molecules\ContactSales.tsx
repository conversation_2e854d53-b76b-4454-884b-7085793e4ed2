import React from "react";
import { Button } from "../atoms/Button";

interface ContactSalesProps {
  title?: string;
  description?: string;
  buttonText?: string;
  variant?: "default" | "compact" | "inline";
  className?: string;
}

export const ContactSales: React.FC<ContactSalesProps> = ({
  title = "Need Access?",
  description = "Contact our sales team to get started with Tech Notes.",
  buttonText = "Contact Sales",
  variant = "default",
  className = "",
}) => {
  const handleContactSales = () => {
    window.location.href =
      "mailto:<EMAIL>?subject=Tech Notes Access Request&body=Hi, I would like to request access to Tech Notes. Please contact me to discuss getting started.";
  };

  if (variant === "inline") {
    return (
      <div className={`flex items-center gap-4 ${className}`}>
        <Button onClick={handleContactSales} variant="secondary" size="sm">
          {buttonText}
        </Button>
      </div>
    );
  }

  if (variant === "compact") {
    return (
      <div className={`text-center p-4 bg-gray-50 rounded-lg ${className}`}>
        <p className="text-sm text-gray-600 mb-3">{description}</p>
        <Button onClick={handleContactSales} variant="primary" size="sm">
          {buttonText}
        </Button>
      </div>
    );
  }

  // Default variant
  return (
    <div
      className={`text-center p-8 bg-gradient-to-r from-primary-50 to-primary-100 rounded-lg ${className}`}
    >
      <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600 mb-6 max-w-md mx-auto">{description}</p>
      <Button onClick={handleContactSales} variant="primary" size="lg">
        {buttonText}
      </Button>
    </div>
  );
};
