#!/usr/bin/env tsx

/**
 * Verification script for Document table and sample data
 * Verifies that Phase 2 implementation is working correctly
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifyDocuments() {
  try {
    console.log('🔍 Verifying Document table and sample data...\n');

    // Check if documents table exists and has data
    const documentCount = await prisma.document.count();
    console.log(`📊 Total documents in database: ${documentCount}`);

    if (documentCount === 0) {
      console.log('⚠️  No documents found. Run seed script to populate sample data.');
      return;
    }

    // Get all documents with tenant and creator info
    const documents = await prisma.document.findMany({
      include: {
        tenant: {
          select: { name: true, slug: true }
        },
        creator: {
          select: { email: true, firstName: true, lastName: true }
        }
      },
      orderBy: { createdAt: 'asc' }
    });

    console.log('\n📄 Document Details:');
    console.log('─'.repeat(80));

    documents.forEach((doc, index) => {
      console.log(`${index + 1}. ${doc.title || doc.fileName}`);
      console.log(`   📁 File: ${doc.originalName} (${doc.fileName})`);
      console.log(`   📏 Size: ${(doc.fileSize / 1024).toFixed(1)} KB`);
      console.log(`   🏢 Tenant: ${doc.tenant.name} (${doc.tenant.slug})`);
      console.log(`   👤 Creator: ${doc.creator.firstName} ${doc.creator.lastName} (${doc.creator.email})`);
      console.log(`   🔗 S3 Key: ${doc.s3Key}`);
      console.log(`   📅 Created: ${doc.createdAt.toISOString()}`);
      console.log('');
    });

    // Verify tenant isolation
    const tenantGroups = documents.reduce((acc, doc) => {
      const tenantSlug = doc.tenant.slug;
      if (!acc[tenantSlug]) acc[tenantSlug] = [];
      acc[tenantSlug].push(doc);
      return acc;
    }, {} as Record<string, typeof documents>);

    console.log('🏢 Documents by Tenant:');
    console.log('─'.repeat(40));
    Object.entries(tenantGroups).forEach(([slug, docs]) => {
      console.log(`${slug}: ${docs.length} documents`);
    });

    // Verify S3 key format
    console.log('\n🔗 S3 Key Format Verification:');
    console.log('─'.repeat(40));
    const validS3Keys = documents.every(doc => {
      const expectedPrefix = `tenant-${doc.tenantId}/`;
      const isValid = doc.s3Key.startsWith(expectedPrefix);
      if (!isValid) {
        console.log(`❌ Invalid S3 key: ${doc.s3Key}`);
      }
      return isValid;
    });

    if (validS3Keys) {
      console.log('✅ All S3 keys follow correct tenant isolation format');
    }

    console.log('\n✅ Document verification completed successfully!');

  } catch (error) {
    console.error('❌ Error during verification:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run verification
verifyDocuments().catch(console.error);
