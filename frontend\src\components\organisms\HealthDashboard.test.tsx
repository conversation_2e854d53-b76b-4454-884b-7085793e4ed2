import { describe, it, expect, beforeEach } from "vitest";
import {
  render,
  screen,
  fireEvent,
  waitFor,
} from "../../__tests__/helpers/test-utils";
import HealthDashboard from "./HealthDashboard";
import {
  createMockHealthService,
  setupMockHealthService,
  type MockHealthService,
} from "../../__tests__/helpers/service-factory.helper";

describe("HealthDashboard", () => {
  let mockHealthService: MockHealthService;

  beforeEach(() => {
    mockHealthService = createMockHealthService();
  });

  describe("Successful Health Check", () => {
    it("should render health dashboard with successful data", async () => {
      // Arrange - Setup successful health check
      setupMockHealthService(mockHealthService, "success");

      // Act
      render(<HealthDashboard healthService={mockHealthService} />);

      // Assert - Verify loading state initially
      expect(screen.getByText("Loading...")).toBeInTheDocument();

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText("Healthy")).toBeInTheDocument();
      });

      // Verify health data is displayed
      expect(screen.getByText("System Health")).toBeInTheDocument();
      expect(screen.getByText("test")).toBeInTheDocument(); // environment
      expect(screen.getByText("connected")).toBeInTheDocument(); // database
      expect(mockHealthService.checkHealth).toHaveBeenCalledTimes(1);
    });

    it("should display formatted timestamp", async () => {
      // Arrange
      setupMockHealthService(mockHealthService, "success");

      // Act
      render(<HealthDashboard healthService={mockHealthService} />);

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText("Healthy")).toBeInTheDocument();
      });

      // Assert - Verify timestamp is formatted and displayed
      expect(screen.getByText("Last Updated:")).toBeInTheDocument();
      // The exact formatted date will depend on locale, so just check it exists
      const timestampElement =
        screen.getByText("Last Updated:").nextElementSibling;
      expect(timestampElement).toBeInTheDocument();
      expect(timestampElement?.textContent).not.toBe("Unknown");
    });

    it("should handle refresh functionality", async () => {
      // Arrange
      setupMockHealthService(mockHealthService, "success");

      // Act
      render(<HealthDashboard healthService={mockHealthService} />);

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText("Healthy")).toBeInTheDocument();
      });

      // Clear previous calls
      mockHealthService.checkHealth.mockClear();

      // Click refresh button
      const refreshButton = screen.getByRole("button", { name: /refresh/i });
      fireEvent.click(refreshButton);

      // Assert - Verify service was called again
      await waitFor(() => {
        expect(mockHealthService.checkHealth).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe("Error Handling", () => {
    it("should display error state when health check fails", async () => {
      // Arrange - Setup service to fail
      setupMockHealthService(mockHealthService, "error");

      // Act
      render(<HealthDashboard healthService={mockHealthService} />);

      // Wait for error state (with longer timeout for retries)
      await waitFor(
        () => {
          expect(screen.getByText("Connection Error")).toBeInTheDocument();
        },
        { timeout: 5000 },
      );

      // Assert - Verify error message and UI
      expect(
        screen.getByText(
          "Unable to connect to backend. Please ensure the backend server is running.",
        ),
      ).toBeInTheDocument();
      expect(screen.getByText("Error")).toBeInTheDocument();
      expect(mockHealthService.checkHealth).toHaveBeenCalled();
    });

    it("should show error status indicator when health check fails", async () => {
      // Arrange
      setupMockHealthService(mockHealthService, "error");

      // Act
      render(<HealthDashboard healthService={mockHealthService} />);

      // Wait for error state
      await waitFor(
        () => {
          expect(screen.getByText("Error")).toBeInTheDocument();
        },
        { timeout: 5000 },
      );

      // Assert - Verify error status indicator
      const statusIndicator = screen.getByRole("status");
      expect(statusIndicator).toHaveAttribute("aria-label", "Status: error");
      expect(statusIndicator).toHaveClass("bg-red-500");
    });

    it("should allow retry after error", async () => {
      // Arrange - Setup service to fail initially
      setupMockHealthService(mockHealthService, "error");

      // Act
      render(<HealthDashboard healthService={mockHealthService} />);

      // Wait for error state
      await waitFor(
        () => {
          expect(screen.getByText("Connection Error")).toBeInTheDocument();
        },
        { timeout: 5000 },
      );

      // Change mock to succeed on retry
      mockHealthService.checkHealth.mockClear();
      setupMockHealthService(mockHealthService, "success");

      // Click refresh to retry
      const refreshButton = screen.getByRole("button", { name: /refresh/i });
      fireEvent.click(refreshButton);

      // Assert - Verify successful retry
      await waitFor(
        () => {
          expect(screen.getByText("Healthy")).toBeInTheDocument();
        },
        { timeout: 3000 },
      );
      expect(screen.queryByText("Connection Error")).not.toBeInTheDocument();
    });
  });

  describe("Loading States", () => {
    it("should show loading state during initial fetch", () => {
      // Arrange - Setup service that never resolves
      setupMockHealthService(mockHealthService, "loading");

      // Act
      render(<HealthDashboard healthService={mockHealthService} />);

      // Assert - Verify loading state
      expect(screen.getByText("Loading...")).toBeInTheDocument();
      const statusIndicator = screen.getByRole("status");
      expect(statusIndicator).toHaveAttribute("aria-label", "Status: loading");
      expect(statusIndicator).toHaveClass("bg-yellow-500", "animate-pulse");
    });
  });

  describe("Custom Configuration", () => {
    it("should accept custom refresh interval", async () => {
      // Arrange
      setupMockHealthService(mockHealthService, "success");
      const customInterval = 30000;

      // Act
      render(
        <HealthDashboard
          healthService={mockHealthService}
          refreshInterval={customInterval}
        />,
      );

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText("Healthy")).toBeInTheDocument();
      });

      // Assert - Component renders successfully with custom interval
      expect(mockHealthService.checkHealth).toHaveBeenCalledTimes(1);
    });

    it("should use default refresh interval when not specified", async () => {
      // Arrange
      setupMockHealthService(mockHealthService, "success");

      // Act
      render(<HealthDashboard healthService={mockHealthService} />);

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText("Healthy")).toBeInTheDocument();
      });

      // Assert - Component works with default interval
      expect(mockHealthService.checkHealth).toHaveBeenCalledTimes(1);
    });
  });

  describe("Service Dependency Injection", () => {
    it("should use the injected health service", async () => {
      // Arrange
      setupMockHealthService(mockHealthService, "success");

      // Act
      render(<HealthDashboard healthService={mockHealthService} />);

      // Wait for service call
      await waitFor(() => {
        expect(mockHealthService.checkHealth).toHaveBeenCalledTimes(1);
      });

      // Assert - Verify correct service was used
      expect(mockHealthService.checkHealth).toHaveBeenCalledWith();
    });

    it("should work with different service implementations", async () => {
      // Arrange - Create alternative service
      const alternativeService = createMockHealthService();
      const alternativeResponse = {
        data: {
          status: "ok",
          environment: "production",
          database: "connected",
        },
        timestamp: "2024-01-02T00:00:00.000Z",
      };
      alternativeService.checkHealth.mockResolvedValue(alternativeResponse);

      // Act
      render(<HealthDashboard healthService={alternativeService} />);

      // Wait for data load
      await waitFor(() => {
        expect(screen.getByText("production")).toBeInTheDocument();
      });

      // Assert - Verify alternative service was used
      expect(alternativeService.checkHealth).toHaveBeenCalledTimes(1);
      expect(mockHealthService.checkHealth).not.toHaveBeenCalled();
    });
  });

  describe("Error Boundary Integration", () => {
    it("should be wrapped in error boundary", () => {
      // Arrange
      setupMockHealthService(mockHealthService, "success");

      // Act
      render(<HealthDashboard healthService={mockHealthService} />);

      // Assert - Component renders without throwing
      expect(screen.getByText("System Health")).toBeInTheDocument();
    });
  });
});
