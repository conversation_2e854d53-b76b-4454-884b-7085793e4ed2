-- CreateTable
CREATE TABLE "vehicle_sub_brands_v3" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(500) NOT NULL,
    "brandId" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "displayOrder" INTEGER NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vehicle_sub_brands_v3_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "vehicle_sub_brands_v3_tenantId_idx" ON "vehicle_sub_brands_v3"("tenantId");

-- CreateIndex
CREATE INDEX "vehicle_sub_brands_v3_brandId_idx" ON "vehicle_sub_brands_v3"("brandId");

-- CreateIndex
CREATE INDEX "vehicle_sub_brands_v3_name_idx" ON "vehicle_sub_brands_v3"("name");

-- CreateIndex
CREATE INDEX "vehicle_sub_brands_v3_isActive_idx" ON "vehicle_sub_brands_v3"("isActive");

-- CreateIndex
CREATE INDEX "vehicle_sub_brands_v3_deletedAt_idx" ON "vehicle_sub_brands_v3"("deletedAt");

-- CreateIndex
CREATE INDEX "vehicle_sub_brands_v3_brandId_displayOrder_idx" ON "vehicle_sub_brands_v3"("brandId", "displayOrder");

-- CreateIndex
CREATE INDEX "vehicle_sub_brands_v3_tenantId_brandId_idx" ON "vehicle_sub_brands_v3"("tenantId", "brandId");

-- CreateIndex (partial unique index for soft deletes)
CREATE UNIQUE INDEX "vehicle_sub_brands_v3_name_brandId_tenantId_active_key" ON "vehicle_sub_brands_v3"("name", "brandId", "tenantId") WHERE "deletedAt" IS NULL;

-- AddForeignKey
ALTER TABLE "vehicle_sub_brands_v3" ADD CONSTRAINT "vehicle_sub_brands_v3_brandId_fkey" FOREIGN KEY ("brandId") REFERENCES "vehicle_brands_v3"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_sub_brands_v3" ADD CONSTRAINT "vehicle_sub_brands_v3_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;
