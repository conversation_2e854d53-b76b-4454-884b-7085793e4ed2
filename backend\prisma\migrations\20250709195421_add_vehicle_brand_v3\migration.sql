-- CreateTable
CREATE TABLE "vehicle_brands_v3" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(500) NOT NULL,
    "tenantId" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "displayOrder" INTEGER NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vehicle_brands_v3_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "vehicle_brands_v3_tenantId_idx" ON "vehicle_brands_v3"("tenantId");

-- CreateIndex
CREATE INDEX "vehicle_brands_v3_name_idx" ON "vehicle_brands_v3"("name");

-- CreateIndex
CREATE INDEX "vehicle_brands_v3_isActive_idx" ON "vehicle_brands_v3"("isActive");

-- CreateIndex
CREATE INDEX "vehicle_brands_v3_deletedAt_idx" ON "vehicle_brands_v3"("deletedAt");

-- CreateIndex
CREATE INDEX "vehicle_brands_v3_tenantId_displayOrder_idx" ON "vehicle_brands_v3"("tenantId", "displayOrder");

-- CreateIndex
CREATE UNIQUE INDEX "vehicle_brands_v3_name_tenantId_key" ON "vehicle_brands_v3"("name", "tenantId");

-- AddForeignKey
ALTER TABLE "vehicle_brands_v3" ADD CONSTRAINT "vehicle_brands_v3_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;
