import { afterEach } from '@jest/globals';

// Simple test environment setup - no Docker, no complex infrastructure
// Removed console.log to reduce test output noise

// Set test environment
process.env.NODE_ENV = 'test';

// Set minimal environment variables for unit tests
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_mock';
process.env.CLERK_SECRET_KEY = 'sk_test_mock_key_for_tests';
process.env.CLERK_PUBLISHABLE_KEY = 'pk_test_mock_key_for_tests';
process.env.LOG_LEVEL = 'error'; // Reduce noise in test output
process.env.CORS_ORIGIN = 'http://localhost:5173';
process.env.FRONTEND_URL = 'http://localhost:5173';
// AWS S3 Configuration (test values)
process.env.AWS_REGION = 'us-east-1';
process.env.AWS_S3_BUCKET_NAME = 'tech-notes-documents-test';
process.env.AWS_ACCESS_KEY_ID = 'test_access_key_id';
process.env.AWS_SECRET_ACCESS_KEY = 'test_secret_access_key';

// Clean up mocks between tests
afterEach(() => {
  jest.clearAllMocks();
});
