/*
  Warnings:

  - A unique constraint covering the columns `[name,brandId,tenantId]` on the table `vehicle_sub_brands_v3` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateTable
CREATE TABLE "vehicle_models_v3" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(500) NOT NULL,
    "subBrandId" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "displayOrder" INTEGER NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vehicle_models_v3_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "vehicle_models_v3_tenantId_idx" ON "vehicle_models_v3"("tenantId");

-- CreateIndex
CREATE INDEX "vehicle_models_v3_subBrandId_idx" ON "vehicle_models_v3"("subBrandId");

-- CreateIndex
CREATE INDEX "vehicle_models_v3_name_idx" ON "vehicle_models_v3"("name");

-- CreateIndex
CREATE INDEX "vehicle_models_v3_isActive_idx" ON "vehicle_models_v3"("isActive");

-- CreateIndex
CREATE INDEX "vehicle_models_v3_deletedAt_idx" ON "vehicle_models_v3"("deletedAt");

-- CreateIndex
CREATE INDEX "vehicle_models_v3_subBrandId_displayOrder_idx" ON "vehicle_models_v3"("subBrandId", "displayOrder");

-- CreateIndex
CREATE INDEX "vehicle_models_v3_tenantId_subBrandId_idx" ON "vehicle_models_v3"("tenantId", "subBrandId");

-- CreateIndex
CREATE UNIQUE INDEX "vehicle_models_v3_name_subBrandId_tenantId_key" ON "vehicle_models_v3"("name", "subBrandId", "tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "vehicle_sub_brands_v3_name_brandId_tenantId_key" ON "vehicle_sub_brands_v3"("name", "brandId", "tenantId");

-- AddForeignKey
ALTER TABLE "vehicle_models_v3" ADD CONSTRAINT "vehicle_models_v3_subBrandId_fkey" FOREIGN KEY ("subBrandId") REFERENCES "vehicle_sub_brands_v3"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_models_v3" ADD CONSTRAINT "vehicle_models_v3_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;
