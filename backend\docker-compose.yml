services:
  postgres:
    image: postgres:15-alpine
    container_name: tech-notes-postgres
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: tech_notes_dev
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U user -d tech_notes_dev']
      interval: 10s
      timeout: 5s
      retries: 5

  postgres-test:
    image: postgres:15-alpine
    container_name: tech-notes-postgres-test
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: tech_notes_test
    ports:
      - '5433:5432'
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U user -d tech_notes_test']
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
  postgres_test_data:
