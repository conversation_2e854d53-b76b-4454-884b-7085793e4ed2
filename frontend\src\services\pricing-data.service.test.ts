import { describe, it, expect, vi } from "vitest";
import {
  parsePricingTiers,
  parseFeatureGroups,
  parsePricingConfig,
  getFeaturesForTier,
} from "../../src/services/pricing-data.service";

// Mock the generated data
vi.mock("../../src/data/pricing-data", () => ({
  PRICING_TIERS: [
    {
      name: "Basic",
      price: "$5",
      period: "month",
      description: "Perfect for small teams getting started",
      icon: "Users",
      popular: false,
      buttonText: "Get Started",
      buttonVariant: "primary",
      order: 1,
    },
    {
      name: "Pro",
      price: "$20",
      period: "month",
      description: "Everything in Plus plus automation",
      icon: "Star",
      popular: true,
      buttonText: "Get Started",
      buttonVariant: "primary",
      order: 3,
    },
  ],
  FEATURE_GROUPS: [
    {
      groupName: "User Management",
      features: [
        {
          name: "Admin Managed",
          description: "All user management handled by company admin",
          basic: true,
          plus: true,
          pro: true,
          enterprise: true,
          includeOnCard: true,
        },
        {
          name: "Invite Codes",
          description: "Generate 1-time sign-up codes for new users",
          basic: false,
          plus: true,
          pro: true,
          enterprise: true,
          includeOnCard: true,
        },
        {
          name: "Advanced User Roles",
          description: "Custom permission levels and role management",
          basic: false,
          plus: false,
          pro: true,
          enterprise: true,
          includeOnCard: false,
        },
      ],
    },
  ],
  PRICING_CONFIG: {
    companyName: "Tech Notes",
    tagline: "Test tagline",
    pricingNote: "All prices are per month per location",
    ctaHeadline: "Ready to Get Started?",
    ctaDescription: "Test description",
    trialButtonText: "Start Free Trial",
    demoButtonText: "Schedule Demo",
  },
}));

describe("Pricing Data Service", () => {
  describe("parsePricingTiers", () => {
    it("should return pricing tiers from generated data", async () => {
      const tiers = await parsePricingTiers();

      expect(tiers).toHaveLength(2);
      expect(tiers[0]).toMatchObject({
        name: "Basic",
        price: "$5",
        period: "month",
        popular: false,
      });
      expect(tiers[1]).toMatchObject({
        name: "Pro",
        price: "$20",
        popular: true,
      });
    });

    it("should return consistent data on multiple calls", async () => {
      const tiers1 = await parsePricingTiers();
      const tiers2 = await parsePricingTiers();

      expect(tiers1).toEqual(tiers2);
    });
  });

  describe("parseFeatureGroups", () => {
    it("should return feature groups from generated data", async () => {
      const groups = await parseFeatureGroups();

      expect(groups).toHaveLength(1);
      expect(groups[0]).toMatchObject({
        groupName: "User Management",
        features: expect.arrayContaining([
          expect.objectContaining({
            name: "Admin Managed",
            basic: true,
            includeOnCard: true,
          }),
        ]),
      });
    });

    it("should include all feature properties", async () => {
      const groups = await parseFeatureGroups();
      const feature = groups[0].features[0];

      expect(feature).toHaveProperty("name");
      expect(feature).toHaveProperty("description");
      expect(feature).toHaveProperty("basic");
      expect(feature).toHaveProperty("plus");
      expect(feature).toHaveProperty("pro");
      expect(feature).toHaveProperty("enterprise");
      expect(feature).toHaveProperty("includeOnCard");
    });
  });

  describe("parsePricingConfig", () => {
    it("should return pricing config from generated data", async () => {
      const config = await parsePricingConfig();

      expect(config).toMatchObject({
        companyName: "Tech Notes",
        tagline: "Test tagline",
        pricingNote: "All prices are per month per location",
        ctaHeadline: "Ready to Get Started?",
        trialButtonText: "Start Free Trial",
        demoButtonText: "Schedule Demo",
      });
    });
  });

  describe("getFeaturesForTier", () => {
    it("should return features available for basic tier", async () => {
      const features = await getFeaturesForTier("basic");

      expect(features).toHaveLength(1); // Only Admin Managed is includeOnCard=true for basic
      expect(features[0]).toMatchObject({
        name: "Admin Managed",
        basic: true,
        includeOnCard: true,
      });
    });

    it("should return features available for pro tier", async () => {
      const features = await getFeaturesForTier("pro");

      expect(features).toHaveLength(2); // Admin Managed + Invite Codes (both includeOnCard=true)
      expect(features.map((f) => f.name)).toContain("Admin Managed");
      expect(features.map((f) => f.name)).toContain("Invite Codes");
    });

    it("should only return features with includeOnCard=true", async () => {
      const features = await getFeaturesForTier("pro");

      // Advanced User Roles has includeOnCard=false, so shouldn't be included
      expect(features.map((f) => f.name)).not.toContain("Advanced User Roles");
      expect(features.every((f) => f.includeOnCard)).toBe(true);
    });

    it("should handle case-insensitive tier names", async () => {
      const featuresLower = await getFeaturesForTier("basic");
      const featuresUpper = await getFeaturesForTier("BASIC");
      const featuresMixed = await getFeaturesForTier("Basic");

      expect(featuresLower).toEqual(featuresUpper);
      expect(featuresLower).toEqual(featuresMixed);
    });

    it("should return empty array for invalid tier", async () => {
      const features = await getFeaturesForTier("invalid");

      expect(features).toEqual([]);
    });

    it("should handle string values as available features", async () => {
      // This would test custom tier values like "Enhanced", "Premium", etc.
      // For now, we're using boolean values, but the logic supports strings
      const features = await getFeaturesForTier("enterprise");

      expect(Array.isArray(features)).toBe(true);
    });
  });
});
