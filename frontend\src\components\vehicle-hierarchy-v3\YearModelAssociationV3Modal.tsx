/**
 * Year-Model Association V3 Modal Component
 * Allows managing model associations for a specific year with checkboxes and filtering
 */

import React, { useState, useEffect, useMemo } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { Modal, Button, Alert, Badge, LoadingSpinner, CompanyAdminOnly, Input } from "../index";
import { useTypedApi, type VehicleYearV3, type VehicleModelYearV3 } from "../../services/api-client";
import { Calendar, Check, Search, Filter } from "lucide-react";

interface YearModelAssociationV3ModalProps {
  isOpen: boolean;
  onClose: () => void;
  year: VehicleYearV3 | null;
}

export const YearModelAssociationV3Modal: React.FC<YearModelAssociationV3ModalProps> = ({
  isOpen,
  onClose,
  year,
}) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [selectedModelIds, setSelectedModelIds] = useState<string[]>([]);
  const [currentModelIds, setCurrentModelIds] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedBrandFilter, setSelectedBrandFilter] = useState<string>("");
  const [selectedSubBrandFilter, setSelectedSubBrandFilter] = useState<string>("");

  // Fetch all available models with brand and sub-brand information
  const { data: modelsResponse, isLoading: isLoadingModels } = useQuery({
    queryKey: ["vehicle-models-v3-all"],
    queryFn: () => api.vehicleHierarchyV3.getAllModels(),
    enabled: isOpen,
  });

  // Fetch current year-model associations
  const { data: yearModelsResponse, isLoading: isLoadingYearModels } = useQuery({
    queryKey: ["vehicle-year-models-v3", year?.id],
    queryFn: () => {
      if (!year) throw new Error("No year selected");
      return api.vehicleHierarchyV3.getModelsByYear(year.id);
    },
    enabled: isOpen && !!year,
  });

  // Update selected models when data loads
  useEffect(() => {
    if (yearModelsResponse?.data) {
      const modelIds = yearModelsResponse.data.map((yearModel: VehicleModelYearV3) => yearModel.model?.id).filter(Boolean) as string[];
      setSelectedModelIds(modelIds);
      setCurrentModelIds(modelIds);
    }
  }, [yearModelsResponse]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedModelIds([]);
      setCurrentModelIds([]);
      setSearchQuery("");
      setSelectedBrandFilter("");
      setSelectedSubBrandFilter("");
    }
  }, [isOpen]);

  // Create year-model association mutation
  const createAssociationMutation = useMutation({
    mutationFn: (modelId: string) => {
      if (!year) throw new Error("No year selected");
      return api.vehicleHierarchyV3.createModelYear(modelId, {
        modelId,
        yearId: year.id,
      });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create association");
    },
  });

  // Delete year-model association mutation
  const deleteAssociationMutation = useMutation({
    mutationFn: (modelId: string) => {
      if (!year) throw new Error("No year selected");
      return api.vehicleHierarchyV3.deleteModelYear(modelId, year.id);
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to remove association");
    },
  });

  const handleModelToggle = (modelId: string) => {
    setSelectedModelIds(prev => 
      prev.includes(modelId) 
        ? prev.filter(id => id !== modelId)
        : [...prev, modelId]
    );
  };

  const handleSaveChanges = async () => {
    if (!year) return;

    const modelsToAdd = selectedModelIds.filter(id => !currentModelIds.includes(id));
    const modelsToRemove = currentModelIds.filter(id => !selectedModelIds.includes(id));

    try {
      // Remove associations
      for (const modelId of modelsToRemove) {
        await deleteAssociationMutation.mutateAsync(modelId);
      }

      // Add new associations
      for (const modelId of modelsToAdd) {
        await createAssociationMutation.mutateAsync(modelId);
      }

      toast.success("Model associations updated successfully");

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["vehicle-year-models-v3", year.id] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-models-v3"] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-hierarchy-v3"] });

      handleClose();
    } catch {
      // Error handling is done in the mutations
    }
  };

  const handleClose = () => {
    setSelectedModelIds([]);
    setCurrentModelIds([]);
    setSearchQuery("");
    setSelectedBrandFilter("");
    setSelectedSubBrandFilter("");
    onClose();
  };

  // Filter and search logic
  const filteredModels = useMemo(() => {
    if (!modelsResponse?.data) return [];

    return modelsResponse.data.filter((model) => {
      // Search filter
      if (searchQuery && !model.name.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }

      // Brand filter
      if (selectedBrandFilter && model.subBrand?.brand?.id !== selectedBrandFilter) {
        return false;
      }

      // Sub-brand filter
      if (selectedSubBrandFilter && model.subBrand?.id !== selectedSubBrandFilter) {
        return false;
      }

      return true;
    });
  }, [modelsResponse?.data, searchQuery, selectedBrandFilter, selectedSubBrandFilter]);

  // Get unique brands and sub-brands for filter dropdowns
  const availableBrands = useMemo(() => {
    if (!modelsResponse?.data) return [];
    const brands = modelsResponse.data
      .map(model => model.subBrand?.brand)
      .filter(Boolean)
      .filter((brand, index, self) => self.findIndex(b => b?.id === brand?.id) === index);
    return brands.sort((a, b) => a!.name.localeCompare(b!.name));
  }, [modelsResponse?.data]);

  const availableSubBrands = useMemo(() => {
    if (!modelsResponse?.data) return [];
    let subBrands = modelsResponse.data
      .map(model => model.subBrand)
      .filter(Boolean);

    // Filter by selected brand if any
    if (selectedBrandFilter) {
      subBrands = subBrands.filter(subBrand => subBrand?.brand?.id === selectedBrandFilter);
    }

    // Remove duplicates
    subBrands = subBrands.filter((subBrand, index, self) => 
      self.findIndex(sb => sb?.id === subBrand?.id) === index
    );

    return subBrands.sort((a, b) => a!.name.localeCompare(b!.name));
  }, [modelsResponse?.data, selectedBrandFilter]);

  const models = filteredModels || [];
  const isLoading = isLoadingModels || isLoadingYearModels;
  const isPending = createAssociationMutation.isPending || deleteAssociationMutation.isPending;
  const hasChanges = JSON.stringify(selectedModelIds.sort()) !== JSON.stringify(currentModelIds.sort());

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Add Models to Year" size="xl">
      <div className="space-y-6">
        {/* Year Info */}
        {year && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-blue-600" />
              <div>
                <h3 className="font-medium text-blue-900">
                  Adding Models to Year: {year.name}
                </h3>
                <p className="text-sm text-blue-700">
                  Select which models should be available for this year
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search models..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Brand Filter */}
          <div>
            <select
              value={selectedBrandFilter}
              onChange={(e) => {
                setSelectedBrandFilter(e.target.value);
                setSelectedSubBrandFilter(""); // Reset sub-brand filter
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Brands</option>
              {availableBrands.map((brand) => (
                <option key={brand!.id} value={brand!.id}>
                  {brand!.name}
                </option>
              ))}
            </select>
          </div>

          {/* Sub-Brand Filter */}
          <div>
            <select
              value={selectedSubBrandFilter}
              onChange={(e) => setSelectedSubBrandFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={!selectedBrandFilter}
            >
              <option value="">All Sub-Brands</option>
              {availableSubBrands.map((subBrand) => (
                <option key={subBrand!.id} value={subBrand!.id}>
                  {subBrand!.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        )}

        {/* Error State */}
        {!isLoading && models.length === 0 && modelsResponse?.data && modelsResponse.data.length > 0 && (
          <Alert variant="info">
            <Filter className="h-4 w-4" />
            No models match your current filters. Try adjusting your search or filter criteria.
          </Alert>
        )}

        {!isLoading && (!modelsResponse?.data || modelsResponse.data.length === 0) && (
          <Alert variant="warning">
            No models available. Please create some models first.
          </Alert>
        )}

        {/* Models Selection */}
        {!isLoading && models.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-gray-900">Available Models</h4>
              <div className="text-sm text-gray-600">
                {selectedModelIds.length} of {models.length} selected
              </div>
            </div>

            <div className="max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
              <div className="divide-y divide-gray-200">
                {models.map((model) => {
                  const isSelected = selectedModelIds.includes(model.id);
                  const wasOriginallySelected = currentModelIds.includes(model.id);

                  return (
                    <label
                      key={model.id}
                      className={`
                        flex items-center justify-between p-4 cursor-pointer transition-all duration-200 hover:bg-gray-50
                        ${isSelected ? 'bg-blue-50' : 'bg-white'}
                        ${!model.isActive ? 'opacity-50' : ''}
                      `}
                    >
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => handleModelToggle(model.id)}
                          disabled={!model.isActive || isPending}
                          className="sr-only"
                        />
                        <div className={`
                          w-5 h-5 border rounded flex items-center justify-center
                          ${isSelected
                            ? 'bg-blue-600 border-blue-600'
                            : 'border-gray-300'
                          }
                        `}>
                          {isSelected && <Check className="w-3 h-3 text-white" />}
                        </div>

                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-gray-900">
                              {model.name}
                            </span>
                            {!model.isActive && (
                              <Badge variant="warning" size="sm">
                                Inactive
                              </Badge>
                            )}
                          </div>
                          <div className="text-sm text-gray-600">
                            {model.subBrand?.brand?.name} → {model.subBrand?.name}
                          </div>
                        </div>
                      </div>

                      {/* Change indicator */}
                      {isSelected !== wasOriginallySelected && (
                        <div className={`
                          w-3 h-3 rounded-full
                          ${isSelected ? 'bg-green-500' : 'bg-red-500'}
                        `} />
                      )}
                    </label>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <CompanyAdminOnly>
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="text-sm text-gray-600">
              {selectedModelIds.length} model(s) selected
              {hasChanges && (
                <Badge variant="secondary" className="ml-2">
                  Changes pending
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveChanges}
                disabled={!hasChanges || isPending}
                className="flex items-center space-x-2"
              >
                {isPending && <LoadingSpinner size="sm" />}
                <span>Save Changes</span>
              </Button>
            </div>
          </div>
        </CompanyAdminOnly>
      </div>
    </Modal>
  );
};
