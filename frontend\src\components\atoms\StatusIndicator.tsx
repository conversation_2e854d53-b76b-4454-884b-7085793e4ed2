import React from "react";
import { clsx } from "clsx";
import type { StatusIndicatorProps } from "../../types/api.types";

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  size = "md",
  className,
}) => {
  const sizeClasses = {
    sm: "h-2 w-2",
    md: "h-3 w-3",
    lg: "h-4 w-4",
  };

  const statusClasses = {
    ok: "bg-green-500",
    error: "bg-red-500",
    loading: "bg-yellow-500 animate-pulse",
  };

  return (
    <div
      className={clsx(
        "rounded-full",
        sizeClasses[size],
        statusClasses[status],
        className,
      )}
      role="status"
      aria-label={`Status: ${status}`}
    />
  );
};

export default StatusIndicator;
