I # TypeScript Express Compatibility Fix

## Problem
Backend has 109+ TypeScript compilation errors preventing deployment. Root cause: `AuthenticatedRequest` interface incompatible with Express v5 types.

## Error <PERSON>tern
```
Property 'param' is missing in type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>' but required in type 'AuthenticatedRequest'.
```

## Solution Overview
1. Fix shared workspace `AuthenticatedRequest` interface
2. Create backend-specific type utilities  
3. Update all route handlers to use compatible pattern
4. Verify build succeeds

## Task 1: Fix Shared Workspace Interface (2 hours)

**File**: `shared/src/types/auth.types.ts`

**Current (broken)**:
```typescript
export interface AuthenticatedRequest extends Request {
  user?: BackendAuthContext;
}
```

**Fix**: Create separate interfaces for different contexts:
```typescript
// Base interface without Express dependency
export interface AuthenticatedRequestBase {
  user?: BackendAuthContext;
}

// Backend-specific interface (extends Express Request)
export interface AuthenticatedRequest extends Request {
  user?: BackendAuthContext;
}
```

**Test**: Run `cd shared && npm run build` - must succeed.

## Task 2: Create Backend Type Utilities (1 hour)

**File**: `backend/src/utils/request-types.ts`

```typescript
import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequestBase } from '@tech-notes/shared';

export type AuthenticatedRequest = Request & AuthenticatedRequestBase;

export function getRequestUser(req: Request) {
  return (req as AuthenticatedRequest).user;
}

export type AuthHandler = (req: Request, res: Response, next: NextFunction) => Promise<void> | void;
```

## Task 3: Update Route Handler Pattern (4-8 hours)

**Pattern to find and replace across all route files**:

**Before (causes errors)**:
```typescript
async (req: AuthenticatedRequest, res, next: NextFunction) => {
  const { tenantId } = req.user!;
}
```

**After (works)**:
```typescript
async (req, res, next: NextFunction) => {
  const { tenantId } = getRequestUser(req)!;
}
```

**Files to update**:
- `src/routes/api/v1/*.ts` (all route files)
- `src/index.ts` (error handler)
- Any middleware files using `AuthenticatedRequest`

**Import to add**:
```typescript
import { getRequestUser } from '../../../utils/request-types.js';
```

## Task 4: Fix Middleware Factory (30 minutes)

**File**: `backend/src/middleware/middleware-factory.ts`

Ensure all methods return `RequestHandler` with proper type assertions:
```typescript
createAuth(): RequestHandler {
  return authMiddleware({ required: true }) as unknown as RequestHandler;
}
```

## Task 5: Verify Build (30 minutes)

**Commands to run**:
```bash
cd shared && npm run build
cd backend && yarn run build
```

**Success criteria**: Zero TypeScript compilation errors.

## Automation Approach

**Option 1**: Manual updates (safer, 6-8 hours)
**Option 2**: Find/replace script (faster, 2-4 hours, higher risk)

For Option 2, create script to:
1. Find: `async \(req: AuthenticatedRequest,`
2. Replace: `async (req,`
3. Find: `req\.user`
4. Replace: `getRequestUser(req)`

## Validation

After each file update:
```bash
npx tsc --noEmit --skipLibCheck [filename]
```

Final validation:
```bash
cd backend && yarn run build
```

Must complete with exit code 0.
