import {
  createMockPrismaService,
  createMockLogger,
  testData,
  MockPrismaService,
  MockLogger,
} from '../__tests__/simple-mocks.js';
import { NotFoundError, ValidationError } from '../types/error.types.js';

import { UserService } from './user.service.js';

// Mock the dependencies
jest.mock('./prisma.service');
jest.mock('../utils/logger');

describe('UserService', () => {
  let userService: UserService;
  let mockPrisma: MockPrismaService;
  let mockLogger: MockLogger;

  beforeEach(() => {
    // Create simple, direct mocks
    mockPrisma = createMockPrismaService();
    mockLogger = createMockLogger();

    // Create UserService instance with mocked dependencies
    userService = new UserService(mockPrisma as any, mockLogger as any);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getUsersByTenant', () => {
    it('should return users for specified tenant', async () => {
      // Arrange
      const tenantId = testData.tenant.id;
      const expectedUsers = [testData.user];

      // Setup simple mocks
      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(testData.tenant);
      mockPrisma.prisma.user.findMany.mockResolvedValue(expectedUsers);

      // Act
      const result = await userService.getUsersByTenant(tenantId);

      // Assert
      expect(result).toEqual(expectedUsers);
      expect(mockPrisma.prisma.tenant.findUnique).toHaveBeenCalledWith({
        where: { id: tenantId },
        select: { id: true },
      });
      expect(mockPrisma.prisma.user.findMany).toHaveBeenCalledWith({
        where: { tenantId },
        orderBy: { createdAt: 'desc' },
      });
    });

    it('should throw NotFoundError when tenant does not exist', async () => {
      // Arrange
      const tenantId = 'non-existent-tenant';
      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(userService.getUsersByTenant(tenantId)).rejects.toThrow(
        NotFoundError
      );
      expect(mockPrisma.prisma.user.findMany).not.toHaveBeenCalled();
    });
  });

  describe('getUserById', () => {
    it('should return user when found in correct tenant', async () => {
      // Arrange
      const userId = testData.user.id;
      const tenantId = testData.user.tenantId;

      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(testData.tenant);
      mockPrisma.prisma.user.findFirst.mockResolvedValue(testData.user);

      // Act
      const result = await userService.getUserById(userId, tenantId);

      // Assert
      expect(result).toEqual(testData.user);
      expect(mockPrisma.prisma.user.findFirst).toHaveBeenCalledWith({
        where: {
          id: userId,
          tenantId,
        },
      });
    });

    it('should return null when user not found', async () => {
      // Arrange
      const userId = 'non-existent-user';
      const tenantId = testData.tenant.id;

      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(testData.tenant);
      mockPrisma.prisma.user.findFirst.mockResolvedValue(null);

      // Act
      const result = await userService.getUserById(userId, tenantId);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('getUserByClerkId', () => {
    it('should return user with tenant data', async () => {
      // Arrange
      const clerkId = testData.user.clerkId;
      const userWithTenant = { ...testData.user, tenant: testData.tenant };

      mockPrisma.prisma.user.findUnique.mockResolvedValue(userWithTenant);

      // Act
      const result = await userService.getUserByClerkId(clerkId);

      // Assert
      expect(result).toEqual(userWithTenant);
      expect(mockPrisma.prisma.user.findUnique).toHaveBeenCalledWith({
        where: { clerkId },
        include: { tenant: true },
      });
    });

    it('should return null when user not found', async () => {
      // Arrange
      const clerkId = 'non-existent-clerk-id';
      mockPrisma.prisma.user.findUnique.mockResolvedValue(null);

      // Act
      const result = await userService.getUserByClerkId(clerkId);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('createUser', () => {
    it('should create user successfully', async () => {
      // Arrange
      const userData = testData.createUserData;
      const createdUser = testData.user;

      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(testData.tenant);
      mockPrisma.prisma.user.create.mockResolvedValue(createdUser);

      // Act
      const result = await userService.createUser(userData);

      // Assert
      expect(result).toEqual(createdUser);
      expect(mockPrisma.prisma.user.create).toHaveBeenCalledWith({
        data: userData,
      });
    });

    it('should validate tenant access before creating user', async () => {
      // Arrange
      const userData = testData.createUserData;
      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(userService.createUser(userData)).rejects.toThrow(
        NotFoundError
      );
      expect(mockPrisma.prisma.user.create).not.toHaveBeenCalled();
    });
  });

  describe('syncUserFromClerk', () => {
    it('should create new user when user does not exist', async () => {
      // Arrange
      const clerkUser = {
        id: 'new-clerk-id',
        emailAddresses: [{ id: '1', emailAddress: '<EMAIL>' }],
        firstName: 'New',
        lastName: 'User',
        imageUrl: 'https://example.com/new.jpg',
      };
      const tenantId = testData.tenant.id;

      mockPrisma.prisma.user.findUnique.mockResolvedValue(null);
      mockPrisma.prisma.tenant.findUnique.mockResolvedValue(testData.tenant);
      mockPrisma.prisma.user.create.mockResolvedValue({
        ...testData.user,
        clerkId: clerkUser.id,
        email: clerkUser.emailAddresses[0].emailAddress,
      });

      // Act
      await userService.syncUserFromClerk(clerkUser, tenantId);

      // Assert
      expect(mockPrisma.prisma.user.create).toHaveBeenCalledWith({
        data: {
          clerkId: clerkUser.id,
          email: clerkUser.emailAddresses[0].emailAddress,
          firstName: clerkUser.firstName,
          lastName: clerkUser.lastName,
          imageUrl: clerkUser.imageUrl,
          tenantId,
        },
      });
    });

    it('should throw ValidationError when user has no email', async () => {
      // Arrange
      const clerkUser = {
        id: 'clerk-id',
        emailAddresses: [],
        firstName: 'Test',
        lastName: 'User',
      };

      // Act & Assert
      await expect(userService.syncUserFromClerk(clerkUser)).rejects.toThrow(
        ValidationError
      );
    });
  });
});
