import { ClerkAuthOptions } from '@tech-notes/shared';
import { Request, Response, NextFunction } from 'express';

import { UserService } from '../services/user.service.js';
import { Logger } from '../utils/logger.js';

export interface ClerkAuthMiddleware {
  (
    options?: ClerkAuthOptions
  ): (req: Request, res: Response, next: NextFunction) => Promise<void>;
}

export interface ClerkMiddlewareDependencies {
  userService: UserService;
  logger: Logger;
}
