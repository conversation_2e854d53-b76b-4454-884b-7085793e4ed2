# Expo Mobile App Implementation Plan - Tech Notes

## Executive Summary

**UPDATED APPROACH**: 4-phase implementation plan for adding React Native mobile app using Expo with a **standalone approach** that avoids shared workspace complexity.

**Key Decision**: After testing, shared workspace integration causes fundamental React version conflicts (React 19 vs 18.3.1) and complex Metro configuration issues. The standalone approach provides a stable, maintainable solution.

**Technical Approach:**
- **Expo SDK 53** with **React 19.0.0** and **React Native 0.79.5**
- **Standalone mobile app** (not yarn workspace) to avoid version conflicts
- **Manual code sharing** for essential types/constants (copy approach initially)
- **Standard npm** for mobile dependencies (separate from monorepo yarn)
- iPhone testing via Expo Go

**Environment Setup:**
```bash
# Uses existing .env.local variables
CLERK_PUBLISHABLE_KEY="pk_test_..."
VITE_API_URL="http://localhost:8080"
```

## Phase 1: Clean Slate Setup (~20 minutes) ✅ COMPLETE

**Status**: Successfully implemented and tested.

1. **Remove Any Existing Broken Mobile Setup**
   ```bash
   # Remove mobile from root package.json workspaces if present
   # Delete existing mobile/ directory if it exists
   yarn install  # Clean up workspace references
   ```

2. **Create Fresh Expo App**
   ```bash
   # Create standalone Expo app (NOT as workspace)
   npx create-expo-app@latest mobile
   cd mobile
   npm install  # Use npm for mobile to avoid workspace conflicts
   ```

3. **Verify Clean Setup**
   ```bash
   cd mobile
   npm run web  # Should start successfully on http://localhost:8081
   ```

**Success**: ✅ Expo app runs cleanly with no dependency conflicts, uses latest stable SDK

## Phase 2: Basic Mobile Structure (~20 minutes)

**Goal**: Set up navigation and basic screens following standard Expo Router patterns.

1. **Customize App Structure**
   - Update app.json with proper app name and branding
   - Customize the default Expo Router tab structure
   - Create basic screens: Dashboard, Profile, Settings

2. **Copy Essential Shared Types**
   ```bash
   # Manually copy essential types from shared workspace
   # Create mobile/src/types/ directory
   # Copy auth types, API types, and constants as needed
   ```

3. **Setup Mobile-Specific Configuration**
   - Configure environment variables for mobile
   - Set up proper TypeScript configuration
   - Add mobile-specific scripts and linting

4. **Test on Device**
   ```bash
   cd mobile && npm run ios  # or npm run android
   # Or use Expo Go: npm start and scan QR code
   ```

**Success**: App displays properly with navigation, can test on device via Expo Go

## Phase 3: Authentication Integration (~25 minutes)

**Goal**: Implement Clerk authentication using standard Expo patterns.

1. **Install Clerk for Expo**
   ```bash
   cd mobile
   npm install @clerk/clerk-expo expo-secure-store expo-crypto
   npm install expo-web-browser expo-constants
   ```

2. **Configure Environment Variables**
   - Create mobile/.env with CLERK_PUBLISHABLE_KEY
   - Update app.config.js to expose environment variables
   - Copy API base URL configuration

3. **Setup Authentication Flow**
   - Wrap app in ClerkProvider with SecureStore token cache
   - Create authentication screens (Login, SignUp)
   - Implement auth-gated navigation (show login or main app)

4. **Copy Auth Types and Utilities**
   - Copy essential auth types from shared workspace
   - Create mobile-specific auth utilities
   - Implement login/logout functionality

**Success**: Login flow works, authentication persists, can access protected screens

## Phase 4: API Integration & Core Features (~25 minutes) ✅ COMPLETE

**Status**: Successfully implemented and tested.

**Goal**: Connect mobile app to existing backend API with authenticated requests.

1. **Install API Dependencies** ✅
   ```bash
   cd mobile
   npm install @tanstack/react-query axios
   ```

2. **Create API Service Layer** ✅
   - Created mobile-specific API client with Clerk token integration
   - Implemented comprehensive error handling and retry logic
   - Added TypeScript types for API responses
   - Followed main project patterns with mobile optimizations

3. **Implement Core Screens** ✅
   - **Dashboard**: Real health check data, user statistics, system status
   - **Profile**: Backend user information, tenant details, authentication status
   - Added loading states, error handling, and refresh functionality
   - Integrated React Query for optimized data fetching

4. **Setup State Management** ✅
   - Wrapped app with QueryClientProvider
   - Configured mobile-optimized React Query defaults
   - Added offline support and network error handling
   - Implemented query key management and invalidation patterns

**Success**: ✅ App fetches real data from backend, displays user profiles with backend data, handles authentication seamlessly, includes comprehensive error handling and loading states

## Validation & Success Criteria

**After Each Phase:**
- Verify mobile app builds and runs: `cd mobile && npm run web`
- Test app launches on device via Expo Go: `cd mobile && npm start`
- Ensure core development workflows still work: `yarn check` and `yarn dev` from root
- Validate no conflicts with existing web/backend functionality

**Final Success Criteria:**
- ✅ Mobile app launches successfully on iOS/Android via Expo Go
- ✅ Authentication works with existing Clerk setup
- ✅ API integration connects to existing backend
- ✅ Core navigation and user flows function properly
- ✅ No conflicts with existing web app or backend
- ✅ Clean dependency management (no version conflicts)
- ✅ Proper TypeScript support and error handling

**Development Workflow:**
```bash
# Start mobile development
cd mobile && npm start          # Start Expo development server
# Scan QR code with iPhone camera or use simulator

# Continue web/backend development normally
yarn dev                        # Starts backend and frontend as usual
```

## Architecture Benefits

**Why Standalone Approach:**
- ✅ **No version conflicts**: Mobile uses React 19, web uses React 18.3.1
- ✅ **Simple dependency management**: Standard npm for mobile, yarn for monorepo
- ✅ **Clean separation**: Mobile development doesn't affect web/backend
- ✅ **Standard Expo patterns**: No complex Metro configuration required
- ✅ **Easy maintenance**: Each platform can evolve independently

**Code Sharing Strategy:**
- **Phase 1-4**: Copy essential types and constants manually
- **Future**: Evaluate minimal shared workspace if significant duplication emerges
- **Focus**: Get working mobile app first, optimize sharing later

This plan prioritizes a **stable, working mobile app** over theoretical code sharing benefits that were causing practical development issues.
