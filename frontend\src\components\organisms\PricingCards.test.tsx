import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { Users, Zap, Star, Crown } from "lucide-react";
import { PricingCards } from "./PricingCards";

const mockTiers = [
  {
    id: "1",
    name: "Basic",
    price: "$9",
    period: "month",
    description: "Perfect for small teams",
    icon: <Users data-testid="users-icon" />,
    features: [
      { name: "Up to 5 users", included: true },
      { name: "Basic support", included: true },
      { name: "Advanced analytics", included: false },
      { name: "Priority support", included: false },
    ],
    buttonText: "Get Started",
    buttonVariant: "outline" as const,
  },
  {
    id: "2",
    name: "Pro",
    price: "$29",
    period: "month",
    description: "Best for growing teams",
    icon: <Zap data-testid="zap-icon" />,
    popular: true,
    features: [
      { name: "Up to 25 users", included: true },
      { name: "Priority support", included: true },
      { name: "Advanced analytics", included: true, highlight: true },
      { name: "Custom integrations", included: false },
    ],
    buttonText: "Start Free Trial",
    buttonVariant: "primary" as const,
  },
  {
    id: "3",
    name: "Enterprise",
    price: "Custom",
    description: "For large organizations",
    icon: <Crown data-testid="crown-icon" />,
    features: [
      { name: "Unlimited users", included: true },
      { name: "Dedicated support", included: true },
      { name: "Custom integrations", included: true },
      { name: "SLA guarantee", included: true },
    ],
    buttonText: "Contact Sales",
    buttonVariant: "secondary" as const,
  },
];

const defaultProps = {
  tiers: mockTiers,
};

describe("PricingCards", () => {
  describe("Basic Rendering", () => {
    it("should render all pricing tiers", () => {
      render(<PricingCards {...defaultProps} />);

      expect(screen.getByText("Basic")).toBeInTheDocument();
      expect(screen.getByText("Pro")).toBeInTheDocument();
      expect(screen.getByText("Enterprise")).toBeInTheDocument();
    });

    it("should render pricing information", () => {
      render(<PricingCards {...defaultProps} />);

      expect(screen.getByText("$9")).toBeInTheDocument();
      expect(screen.getByText("$29")).toBeInTheDocument();
      expect(screen.getByText("Custom")).toBeInTheDocument();
      expect(screen.getAllByText("/month")).toHaveLength(2);
    });

    it("should render descriptions", () => {
      render(<PricingCards {...defaultProps} />);

      expect(screen.getByText("Perfect for small teams")).toBeInTheDocument();
      expect(screen.getByText("Best for growing teams")).toBeInTheDocument();
      expect(screen.getByText("For large organizations")).toBeInTheDocument();
    });

    it("should render icons", () => {
      render(<PricingCards {...defaultProps} />);

      expect(screen.getByTestId("users-icon")).toBeInTheDocument();
      expect(screen.getByTestId("zap-icon")).toBeInTheDocument();
      expect(screen.getByTestId("crown-icon")).toBeInTheDocument();
    });
  });

  describe("Layout Variants", () => {
    it("should render grid layout by default", () => {
      render(<PricingCards {...defaultProps} />);

      const container = screen.getByText("Basic").closest('div[class*="grid"]');
      expect(container).toHaveClass(
        "grid",
        "gap-8",
        "grid-cols-1",
        "md:grid-cols-2",
        "lg:grid-cols-3",
      );
    });

    it("should render comparison layout", () => {
      render(<PricingCards {...defaultProps} layout="comparison" />);

      const container = screen.getByText("Basic").closest('div[class*="grid"]');
      expect(container).toHaveClass("grid", "gap-8", "relative");
    });

    it("should render stacked layout", () => {
      render(<PricingCards {...defaultProps} layout="stacked" />);

      const container = screen
        .getByText("Basic")
        .closest('div[class*="space-y"]');
      expect(container).toHaveClass("space-y-6");
    });

    it("should render with different column counts", () => {
      render(<PricingCards {...defaultProps} columns={4} />);

      const container = screen.getByText("Basic").closest('div[class*="grid"]');
      expect(container).toHaveClass(
        "grid-cols-1",
        "md:grid-cols-2",
        "lg:grid-cols-4",
      );
    });
  });

  describe("Spacing Options", () => {
    it("should apply tight spacing", () => {
      render(<PricingCards {...defaultProps} spacing="tight" />);

      const container = screen
        .getByText("Basic")
        .closest('div[class*="gap-4"]');
      expect(container).toHaveClass("gap-4");
    });

    it("should apply normal spacing by default", () => {
      render(<PricingCards {...defaultProps} />);

      const container = screen
        .getByText("Basic")
        .closest('div[class*="gap-8"]');
      expect(container).toHaveClass("gap-8");
    });

    it("should apply loose spacing", () => {
      render(<PricingCards {...defaultProps} spacing="loose" />);

      const container = screen
        .getByText("Basic")
        .closest('div[class*="gap-12"]');
      expect(container).toHaveClass("gap-12");
    });
  });

  describe("Header Section", () => {
    it("should render title and subtitle", () => {
      render(
        <PricingCards
          {...defaultProps}
          title="Choose Your Plan"
          subtitle="Select the perfect plan for your team's needs"
        />,
      );

      expect(screen.getByRole("heading", { level: 2 })).toHaveTextContent(
        "Choose Your Plan",
      );
      expect(
        screen.getByText("Select the perfect plan for your team's needs"),
      ).toBeInTheDocument();
    });

    it("should render badge", () => {
      const badge = {
        text: "Limited Time",
        variant: "warning" as const,
        icon: <Star data-testid="star-icon" />,
      };

      render(<PricingCards {...defaultProps} badge={badge} />);

      expect(screen.getByText("Limited Time")).toBeInTheDocument();
      expect(screen.getByTestId("star-icon")).toBeInTheDocument();
    });

    it("should not render header when no title, subtitle, badge, or billing period", () => {
      render(<PricingCards {...defaultProps} />);

      expect(
        screen.queryByRole("heading", { level: 2 }),
      ).not.toBeInTheDocument();
    });
  });

  describe("Popular Tier Highlighting", () => {
    it("should highlight popular tier by default", () => {
      render(<PricingCards {...defaultProps} />);

      const popularCard = screen
        .getByText("Pro")
        .closest('div[class*="ring-2"]');
      expect(popularCard).toHaveClass(
        "ring-2",
        "ring-primary-600",
        "shadow-lg",
        "scale-105",
      );

      expect(screen.getByText("Most Popular")).toBeInTheDocument();
    });

    it("should not highlight popular tier when disabled", () => {
      render(<PricingCards {...defaultProps} highlightPopular={false} />);

      expect(screen.queryByText("Most Popular")).not.toBeInTheDocument();

      const proCard = screen.getByText("Pro").closest('div[class*="ring-2"]');
      expect(proCard).not.toBeInTheDocument();
    });
  });

  describe("Feature Lists", () => {
    it("should render included features with check icons", () => {
      render(<PricingCards {...defaultProps} />);

      expect(screen.getByText("Up to 5 users")).toBeInTheDocument();
      expect(screen.getByText("Basic support")).toBeInTheDocument();

      // Check for check icons (SVG elements)
      const checkIcons = document.querySelectorAll(
        'svg[class*="text-green-600"]',
      );
      expect(checkIcons.length).toBeGreaterThan(0);
    });

    it("should render excluded features with X icons", () => {
      render(<PricingCards {...defaultProps} />);

      // Use getAllByText to handle multiple instances
      const analyticsFeatures = screen.getAllByText("Advanced analytics");
      expect(analyticsFeatures.length).toBeGreaterThan(0);

      const integrationFeatures = screen.getAllByText("Custom integrations");
      expect(integrationFeatures.length).toBeGreaterThan(0);

      // Check for X icons (SVG elements)
      const xIcons = document.querySelectorAll('svg[class*="text-gray-400"]');
      expect(xIcons.length).toBeGreaterThan(0);
    });

    it("should highlight special features", () => {
      render(<PricingCards {...defaultProps} />);

      // Find the highlighted version specifically
      const highlightedFeature = screen
        .getAllByText("Advanced analytics")
        .find((el) => el.classList.contains("font-semibold"));
      expect(highlightedFeature).toHaveClass(
        "font-semibold",
        "text-primary-700",
      );
    });
  });

  describe("CTA Buttons", () => {
    it("should render buttons with correct variants", () => {
      render(<PricingCards {...defaultProps} />);

      const basicButton = screen.getByRole("button", { name: "Get Started" });
      expect(basicButton).toHaveClass("border-2", "border-gray-300"); // outline variant

      const proButton = screen.getByRole("button", {
        name: "Start Free Trial",
      });
      expect(proButton).toHaveClass("bg-gradient-to-r", "from-primary-600"); // primary variant

      const enterpriseButton = screen.getByRole("button", {
        name: "Contact Sales",
      });
      expect(enterpriseButton).toHaveClass("bg-gray-100"); // secondary variant
    });

    it("should handle button clicks", () => {
      const handleClick = vi.fn();
      const tiersWithActions = mockTiers.map((tier) => ({
        ...tier,
        buttonAction: handleClick,
      }));

      render(<PricingCards tiers={tiersWithActions} />);

      fireEvent.click(screen.getByRole("button", { name: "Get Started" }));
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it("should render buttons as links when href provided", () => {
      const tiersWithHrefs = mockTiers.map((tier) => ({
        ...tier,
        buttonHref: `/signup/${tier.id}`,
      }));

      render(<PricingCards tiers={tiersWithHrefs} />);

      const basicLink = screen.getByRole("link", { name: "Get Started" });
      expect(basicLink).toHaveAttribute("href", "/signup/1");
    });
  });

  describe("Billing Period Toggle", () => {
    it("should render billing period toggle", () => {
      const billingPeriod = {
        current: "monthly" as const,
        onToggle: vi.fn(),
        discount: "Save 20%",
      };

      render(<PricingCards {...defaultProps} billingPeriod={billingPeriod} />);

      expect(
        screen.getByRole("button", { name: "Monthly" }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: /Yearly/ }),
      ).toBeInTheDocument();
      expect(screen.getByText("Save 20%")).toBeInTheDocument();
    });

    it("should handle billing period toggle", () => {
      const handleToggle = vi.fn();
      const billingPeriod = {
        current: "monthly" as const,
        onToggle: handleToggle,
      };

      render(<PricingCards {...defaultProps} billingPeriod={billingPeriod} />);

      fireEvent.click(screen.getByRole("button", { name: /Yearly/ }));
      expect(handleToggle).toHaveBeenCalledWith("yearly");
    });

    it("should highlight active billing period", () => {
      const billingPeriod = {
        current: "yearly" as const,
        onToggle: vi.fn(),
      };

      render(<PricingCards {...defaultProps} billingPeriod={billingPeriod} />);

      const yearlyButton = screen.getByRole("button", { name: /Yearly/ });
      expect(yearlyButton).toHaveClass(
        "bg-white",
        "text-gray-900",
        "shadow-sm",
      );

      const monthlyButton = screen.getByRole("button", { name: "Monthly" });
      expect(monthlyButton).toHaveClass("text-gray-600");
    });
  });

  describe("Custom Render Function", () => {
    it("should use custom render function when provided", () => {
      const customRender = vi.fn((tier, index) => (
        <div key={tier.id} data-testid={`custom-tier-${index}`}>
          Custom: {tier.name}
        </div>
      ));

      render(<PricingCards {...defaultProps} renderTier={customRender} />);

      expect(screen.getByTestId("custom-tier-0")).toBeInTheDocument();
      expect(screen.getByText("Custom: Basic")).toBeInTheDocument();
      expect(customRender).toHaveBeenCalledTimes(3);
    });
  });

  describe("Custom Content", () => {
    it("should render custom content in tiers", () => {
      const tiersWithCustomContent = [
        {
          ...mockTiers[0],
          customContent: <div data-testid="custom-content">Special offer!</div>,
        },
        ...mockTiers.slice(1),
      ];

      render(<PricingCards tiers={tiersWithCustomContent} />);

      expect(screen.getByTestId("custom-content")).toBeInTheDocument();
      expect(screen.getByText("Special offer!")).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("should have proper heading hierarchy", () => {
      render(<PricingCards {...defaultProps} title="Pricing Plans" />);

      const sectionHeading = screen.getByRole("heading", { level: 2 });
      expect(sectionHeading).toBeInTheDocument();

      const tierHeadings = screen.getAllByRole("heading", { level: 3 });
      expect(tierHeadings).toHaveLength(3);
    });

    it("should have proper button accessibility", () => {
      render(<PricingCards {...defaultProps} />);

      const buttons = screen.getAllByRole("button");
      buttons.forEach((button) => {
        expect(button).toHaveClass(
          "focus:outline-none",
          "focus:ring-2",
          "focus:ring-offset-2",
        );
      });
    });
  });

  describe("Responsive Design", () => {
    it("should have responsive grid classes", () => {
      render(<PricingCards {...defaultProps} columns={4} />);

      const container = screen.getByText("Basic").closest('div[class*="grid"]');
      expect(container).toHaveClass(
        "grid-cols-1",
        "md:grid-cols-2",
        "lg:grid-cols-4",
      );
    });

    it("should have responsive typography in header", () => {
      render(<PricingCards {...defaultProps} title="Pricing Plans" />);

      const heading = screen.getByRole("heading", { level: 2 });
      expect(heading).toHaveClass("text-3xl", "sm:text-4xl");
    });
  });

  describe("Stacked Layout Specific", () => {
    it("should center cards in stacked layout", () => {
      render(<PricingCards {...defaultProps} layout="stacked" />);

      const cardContainers = screen
        .getAllByText(/Basic|Pro|Enterprise/)
        .map((text) => text.closest('div[class*="max-w-md"]'));

      cardContainers.forEach((container) => {
        expect(container).toHaveClass("max-w-md", "mx-auto");
      });
    });
  });
});
