import React from "react";
import { Button } from "../atoms/Button";

export const PricingCTA: React.FC = () => {
  return (
    <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl px-8 py-16 text-center">
      <h2 className="text-3xl font-bold text-white sm:text-4xl mb-4">
        Ready to Get Started?
      </h2>
      <p className="text-xl text-primary-100 max-w-2xl mx-auto mb-8">
        See for yourself how Tech Notes can transform your relationship with
        technicians in the field
      </p>
      <div className="flex justify-center">
        <Button
          variant="secondary"
          size="xl"
          className="bg-white text-primary-600 hover:bg-gray-50"
          asChild
        >
          <a
            href="mailto:<EMAIL>?subject=Schedule Demo"
            className="inline-flex items-center justify-center"
          >
            Schedule Demo
          </a>
        </Button>
      </div>
    </div>
  );
};
