import React, { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>nt,
  <PERSON><PERSON>,
  Al<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal,
  SystemAdminOnly,
  CompanyAdminOnly,
  Input,
  FormField,
} from "../../components";
import { useTypedApi } from "../../services/api-client";
import { usePermissions } from "../../hooks/usePermissions";

interface Invitation {
  id: string;
  email: string;
  status: "PENDING" | "ACCEPTED" | "EXPIRED" | "REVOKED";
  expiresAt: string;
  createdAt: string;
  role: {
    name: string;
    type: string;
  };
  creator: {
    email: string;
    firstName?: string | null;
    lastName?: string | null;
  };
  user?: {
    email: string;
    firstName?: string | null;
    lastName?: string | null;
  } | null;
}

interface InvitationsResponse {
  data: Invitation[];
  meta: {
    count: number;
    tenantId: string;
  };
}

interface CreateTenantWithAdminData {
  tenantName: string;
  tenantSlug: string;
  adminEmail: string;
  adminFirstName?: string;
  adminLastName?: string;
}

interface InviteUserData {
  email: string;
  roleType: "COMPANY_ADMIN" | "COMPANY_TECH";
  firstName?: string;
  lastName?: string;
}

export const InvitationsPage: React.FC = () => {
  const api = useTypedApi();
  const { canManageUsers } = usePermissions();

  const [showCreateTenantModal, setShowCreateTenantModal] = useState(false);
  const [showInviteUserModal, setShowInviteUserModal] = useState(false);
  const [createTenantData, setCreateTenantData] =
    useState<CreateTenantWithAdminData>({
      tenantName: "",
      tenantSlug: "",
      adminEmail: "",
      adminFirstName: "",
      adminLastName: "",
    });
  const [inviteUserData, setInviteUserData] = useState<InviteUserData>({
    email: "",
    roleType: "COMPANY_TECH",
    firstName: "",
    lastName: "",
  });

  // Fetch invitations
  const {
    data: invitationsResponse,
    isLoading: isLoadingInvitations,
    error: invitationsError,
    refetch: refetchInvitations,
  } = useQuery<InvitationsResponse>({
    queryKey: ["invitations"],
    queryFn: async () => {
      return api.makeRequest<InvitationsResponse>("/api/v1/invitations");
    },
    enabled: canManageUsers,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Create tenant with admin mutation
  const createTenantMutation = useMutation({
    mutationFn: async (data: CreateTenantWithAdminData) => {
      return api.makeRequest("/api/v1/invitations/tenant-with-admin", {
        method: "POST",
        body: JSON.stringify(data),
      });
    },
    onSuccess: () => {
      toast.success("Tenant created and admin invitation sent successfully");
      setShowCreateTenantModal(false);
      setCreateTenantData({
        tenantName: "",
        tenantSlug: "",
        adminEmail: "",
        adminFirstName: "",
        adminLastName: "",
      });
      refetchInvitations();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create tenant");
    },
  });

  // Invite user mutation
  const inviteUserMutation = useMutation({
    mutationFn: async (data: InviteUserData) => {
      return api.makeRequest("/api/v1/invitations/invite-user", {
        method: "POST",
        body: JSON.stringify(data),
      });
    },
    onSuccess: () => {
      toast.success("User invitation sent successfully");
      setShowInviteUserModal(false);
      setInviteUserData({
        email: "",
        roleType: "COMPANY_TECH",
        firstName: "",
        lastName: "",
      });
      refetchInvitations();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to invite user");
    },
  });

  // Revoke invitation mutation
  const revokeInvitationMutation = useMutation({
    mutationFn: async (invitationId: string) => {
      return api.makeRequest(`/api/v1/invitations/${invitationId}`, {
        method: "DELETE",
      });
    },
    onSuccess: () => {
      toast.success("Invitation revoked successfully");
      refetchInvitations();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to revoke invitation");
    },
  });

  const handleCreateTenant = (e: React.FormEvent) => {
    e.preventDefault();

    if (!createTenantData.tenantName.trim()) {
      toast.error("Tenant name is required");
      return;
    }

    if (!createTenantData.tenantSlug.trim()) {
      toast.error("Tenant slug is required");
      return;
    }

    if (!/^[a-z0-9-]+$/.test(createTenantData.tenantSlug)) {
      toast.error(
        "Tenant slug must contain only lowercase letters, numbers, and hyphens",
      );
      return;
    }

    if (!createTenantData.adminEmail.trim()) {
      toast.error("Admin email is required");
      return;
    }

    createTenantMutation.mutate(createTenantData);
  };

  const handleInviteUser = (e: React.FormEvent) => {
    e.preventDefault();

    if (!inviteUserData.email.trim()) {
      toast.error("Email is required");
      return;
    }

    inviteUserMutation.mutate(inviteUserData);
  };

  const handleRevokeInvitation = (invitationId: string) => {
    if (window.confirm("Are you sure you want to revoke this invitation?")) {
      revokeInvitationMutation.mutate(invitationId);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "PENDING":
        return "warning";
      case "ACCEPTED":
        return "success";
      case "EXPIRED":
        return "error";
      case "REVOKED":
        return "secondary";
      default:
        return "secondary";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (!canManageUsers) {
    return (
      <div className="space-y-6">
        <Alert variant="error">
          You don't have permission to manage invitations.
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Invitations</h1>
          <p className="text-gray-600 mt-1">
            Manage user invitations and tenant creation
          </p>
        </div>

        <div className="flex space-x-3">
          {/* System Admin: Create Tenant */}
          <SystemAdminOnly>
            <Button
              onClick={() => setShowCreateTenantModal(true)}
              variant="secondary"
            >
              Create Tenant
            </Button>
          </SystemAdminOnly>

          {/* Company Admin: Invite User */}
          <CompanyAdminOnly>
            <Button onClick={() => setShowInviteUserModal(true)}>
              Invite User
            </Button>
          </CompanyAdminOnly>
        </div>
      </div>

      {/* Invitations List */}
      <Card>
        <CardHeader title="Invitations" />
        <CardContent>
          {isLoadingInvitations ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : invitationsError ? (
            <Alert variant="error">
              Failed to load invitations: {invitationsError.message}
            </Alert>
          ) : !invitationsResponse?.data.length ? (
            <div className="text-center py-8 text-gray-500">
              No invitations found
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Email
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Role
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Expires
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {invitationsResponse.data.map((invitation) => (
                    <tr key={invitation.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {invitation.email}
                          </div>
                          {invitation.user && (
                            <div className="text-sm text-gray-500">
                              Accepted by {invitation.user.firstName}{" "}
                              {invitation.user.lastName}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {invitation.role.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {invitation.role.type}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge
                          variant={getStatusBadgeVariant(invitation.status)}
                        >
                          {invitation.status}
                        </Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(invitation.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(invitation.expiresAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {invitation.status === "PENDING" && (
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() =>
                              handleRevokeInvitation(invitation.id)
                            }
                            disabled={revokeInvitationMutation.isPending}
                          >
                            Revoke
                          </Button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Tenant Modal */}
      <Modal
        isOpen={showCreateTenantModal}
        onClose={() => setShowCreateTenantModal(false)}
        title="Create New Tenant"
      >
        <form onSubmit={handleCreateTenant} className="space-y-4">
          <FormField label="Tenant Name" required>
            <Input
              value={createTenantData.tenantName}
              onChange={(e) =>
                setCreateTenantData((prev) => ({
                  ...prev,
                  tenantName: e.target.value,
                }))
              }
              placeholder="Organization Name"
              required
            />
          </FormField>

          <FormField label="Tenant Slug" required>
            <Input
              value={createTenantData.tenantSlug}
              onChange={(e) =>
                setCreateTenantData((prev) => ({
                  ...prev,
                  tenantSlug: e.target.value
                    .toLowerCase()
                    .replace(/[^a-z0-9-]/g, ""),
                }))
              }
              placeholder="organization-slug"
              pattern="^[a-z0-9-]+$"
              required
            />
            <p className="text-sm text-gray-500 mt-1">
              Only lowercase letters, numbers, and hyphens allowed
            </p>
          </FormField>

          <FormField label="Admin Email" required>
            <Input
              type="email"
              value={createTenantData.adminEmail}
              onChange={(e) =>
                setCreateTenantData((prev) => ({
                  ...prev,
                  adminEmail: e.target.value,
                }))
              }
              placeholder="<EMAIL>"
              required
            />
          </FormField>

          <FormField label="Admin First Name">
            <Input
              value={createTenantData.adminFirstName}
              onChange={(e) =>
                setCreateTenantData((prev) => ({
                  ...prev,
                  adminFirstName: e.target.value,
                }))
              }
              placeholder="First Name"
            />
          </FormField>

          <FormField label="Admin Last Name">
            <Input
              value={createTenantData.adminLastName}
              onChange={(e) =>
                setCreateTenantData((prev) => ({
                  ...prev,
                  adminLastName: e.target.value,
                }))
              }
              placeholder="Last Name"
            />
          </FormField>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setShowCreateTenantModal(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={createTenantMutation.isPending}>
              {createTenantMutation.isPending ? "Creating..." : "Create Tenant"}
            </Button>
          </div>
        </form>
      </Modal>

      {/* Invite User Modal */}
      <Modal
        isOpen={showInviteUserModal}
        onClose={() => setShowInviteUserModal(false)}
        title="Invite User"
      >
        <form onSubmit={handleInviteUser} className="space-y-4">
          <FormField label="Email" required>
            <Input
              type="email"
              value={inviteUserData.email}
              onChange={(e) =>
                setInviteUserData((prev) => ({
                  ...prev,
                  email: e.target.value,
                }))
              }
              placeholder="<EMAIL>"
              required
            />
          </FormField>

          <FormField label="Role" required>
            <select
              value={inviteUserData.roleType}
              onChange={(e) =>
                setInviteUserData((prev) => ({
                  ...prev,
                  roleType: e.target.value as "COMPANY_ADMIN" | "COMPANY_TECH",
                }))
              }
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              required
            >
              <option value="COMPANY_TECH">Company Tech</option>
              <option value="COMPANY_ADMIN">Company Admin</option>
            </select>
          </FormField>

          <FormField label="First Name">
            <Input
              value={inviteUserData.firstName}
              onChange={(e) =>
                setInviteUserData((prev) => ({
                  ...prev,
                  firstName: e.target.value,
                }))
              }
              placeholder="First Name"
            />
          </FormField>

          <FormField label="Last Name">
            <Input
              value={inviteUserData.lastName}
              onChange={(e) =>
                setInviteUserData((prev) => ({
                  ...prev,
                  lastName: e.target.value,
                }))
              }
              placeholder="Last Name"
            />
          </FormField>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setShowInviteUserModal(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={inviteUserMutation.isPending}>
              {inviteUserMutation.isPending ? "Sending..." : "Send Invitation"}
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
};
