-- CreateTable
CREATE TABLE "vehicle_years_v3" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(500) NOT NULL,
    "tenantId" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "displayOrder" INTEGER NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vehicle_years_v3_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "vehicle_model_years_v3" (
    "id" TEXT NOT NULL,
    "modelId" TEXT NOT NULL,
    "yearId" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vehicle_model_years_v3_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "vehicle_years_v3_tenantId_idx" ON "vehicle_years_v3"("tenantId");

-- CreateIndex
CREATE INDEX "vehicle_years_v3_name_idx" ON "vehicle_years_v3"("name");

-- CreateIndex
CREATE INDEX "vehicle_years_v3_isActive_idx" ON "vehicle_years_v3"("isActive");

-- CreateIndex
CREATE INDEX "vehicle_years_v3_deletedAt_idx" ON "vehicle_years_v3"("deletedAt");

-- CreateIndex
CREATE INDEX "vehicle_years_v3_tenantId_displayOrder_idx" ON "vehicle_years_v3"("tenantId", "displayOrder");

-- CreateIndex
CREATE UNIQUE INDEX "vehicle_years_v3_name_tenantId_key" ON "vehicle_years_v3"("name", "tenantId");

-- CreateIndex
CREATE INDEX "vehicle_model_years_v3_tenantId_idx" ON "vehicle_model_years_v3"("tenantId");

-- CreateIndex
CREATE INDEX "vehicle_model_years_v3_modelId_idx" ON "vehicle_model_years_v3"("modelId");

-- CreateIndex
CREATE INDEX "vehicle_model_years_v3_yearId_idx" ON "vehicle_model_years_v3"("yearId");

-- CreateIndex
CREATE INDEX "vehicle_model_years_v3_tenantId_modelId_idx" ON "vehicle_model_years_v3"("tenantId", "modelId");

-- CreateIndex
CREATE INDEX "vehicle_model_years_v3_tenantId_yearId_idx" ON "vehicle_model_years_v3"("tenantId", "yearId");

-- CreateIndex
CREATE UNIQUE INDEX "vehicle_model_years_v3_modelId_yearId_tenantId_key" ON "vehicle_model_years_v3"("modelId", "yearId", "tenantId");

-- AddForeignKey
ALTER TABLE "vehicle_years_v3" ADD CONSTRAINT "vehicle_years_v3_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_model_years_v3" ADD CONSTRAINT "vehicle_model_years_v3_modelId_fkey" FOREIGN KEY ("modelId") REFERENCES "vehicle_models_v3"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_model_years_v3" ADD CONSTRAINT "vehicle_model_years_v3_yearId_fkey" FOREIGN KEY ("yearId") REFERENCES "vehicle_years_v3"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vehicle_model_years_v3" ADD CONSTRAINT "vehicle_model_years_v3_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;
