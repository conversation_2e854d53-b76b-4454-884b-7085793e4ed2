import { VehicleModelV3 } from '@prisma/client';
import { BackendAuthContext } from '@tech-notes/shared';

import { NotFoundError } from '../../types/error.types.js';
import { Logger } from '../../utils/logger.js';
import { PrismaService } from '../prisma.service.js';

import { BaseVehicleHierarchyV3Service } from './base-vehicle-hierarchy-v3.service.js';
import { VehicleSubBrandV3Service } from './vehicle-sub-brand-v3.service.js';

export interface CreateModelV3Data {
  name: string;
  subBrandId: string;
}

export interface UpdateModelV3Data {
  name?: string;
  subBrandId?: string;
  isActive?: boolean;
  displayOrder?: number;
}

/**
 * Service for managing Vehicle Model V3 operations
 * Handles CRUD operations for models with sub-brand relationship validation
 */
export class VehicleModelV3Service extends BaseVehicleHierarchyV3Service {
  constructor(
    prismaService: PrismaService,
    logger: Logger,
    private subBrandService: VehicleSubBrandV3Service
  ) {
    super(prismaService, logger);
  }

  /**
   * Get all models for a specific sub-brand
   */
  async getModelsBySubBrand(
    subBrandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModelV3[]> {
    this.logOperation('getModelsBySubBrand', { subBrandId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Validate sub-brand ownership first
        await this.subBrandService.validateSubBrandOwnership(
          subBrandId,
          tenantId
        );

        return await this.prisma.vehicleModelV3.findMany({
          where: this.getActiveEntityWhere({
            subBrandId,
            tenantId,
          }),
          include: {
            modelYears: {
              include: {
                year: true,
              },
            },
          },
          orderBy: { displayOrder: 'asc' },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Get a specific model by ID
   */
  async getModelById(
    modelId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModelV3 | null> {
    this.logOperation('getModelById', { modelId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleModelV3.findFirst({
          where: this.getActiveEntityWhere({
            id: modelId,
            tenantId,
          }),
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Create a new model
   */
  async createModel(
    modelData: CreateModelV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModelV3> {
    this.logOperation('createModel', {
      name: modelData.name,
      subBrandId: modelData.subBrandId,
      tenantId,
    });

    const trimmedName = this.validateAndTrimName(modelData.name, 'Model');

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Validate sub-brand ownership first
          await this.subBrandService.validateSubBrandOwnership(
            modelData.subBrandId,
            tenantId
          );

          // Get next display order for this sub-brand
          const nextDisplayOrder = await this.getNextDisplayOrder(
            this.prisma.vehicleModelV3,
            {
              subBrandId: modelData.subBrandId,
              tenantId,
            }
          );

          return await this.prisma.vehicleModelV3.create({
            data: {
              name: trimmedName,
              subBrandId: modelData.subBrandId,
              tenantId,
              displayOrder: nextDisplayOrder,
            },
          });
        } catch (error) {
          this.handleUniqueConstraintError(error, trimmedName, 'Model');
          this.logError('createModel', error as Error, {
            modelData,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Update a model
   */
  async updateModel(
    modelId: string,
    updateData: UpdateModelV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleModelV3> {
    this.logOperation('updateModel', { modelId, updateData, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Validate sub-brand ownership if subBrandId is being changed
          if (updateData.subBrandId !== undefined) {
            await this.subBrandService.validateSubBrandOwnership(
              updateData.subBrandId,
              tenantId
            );
          }

          // Prepare update data
          const dataToUpdate: Record<string, unknown> = {};

          if (updateData.name !== undefined) {
            dataToUpdate.name = this.validateAndTrimName(
              updateData.name,
              'Model'
            );
          }

          if (updateData.subBrandId !== undefined) {
            dataToUpdate.subBrandId = updateData.subBrandId;
          }

          if (updateData.isActive !== undefined) {
            dataToUpdate.isActive = updateData.isActive;
          }

          if (updateData.displayOrder !== undefined) {
            dataToUpdate.displayOrder = updateData.displayOrder;
          }

          return await this.prisma.vehicleModelV3.update({
            where: {
              id: modelId,
              tenantId,
              deletedAt: null,
            },
            data: dataToUpdate,
          });
        } catch (error) {
          this.handleNotFoundError(error, modelId, 'Model');
          this.handleUniqueConstraintError(
            error,
            updateData.name || '',
            'Model'
          );
          this.logError('updateModel', error as Error, {
            modelId,
            updateData,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Soft delete a model
   */
  async deleteModel(
    modelId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('deleteModel', { modelId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          await this.executeSoftDelete(this.prisma.vehicleModelV3, modelId, {
            tenantId,
          });
        } catch (error) {
          this.handleNotFoundError(error, modelId, 'Model');
          this.logError('deleteModel', error as Error, {
            modelId,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Reorder models within a sub-brand
   */
  async reorderModels(
    subBrandId: string,
    modelOrders: { id: string; displayOrder: number }[],
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('reorderModels', {
      subBrandId,
      modelOrders,
      tenantId,
    });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Validate sub-brand ownership first
          await this.subBrandService.validateSubBrandOwnership(
            subBrandId,
            tenantId
          );

          await this.executeReorder('vehicleModelV3', modelOrders, {
            subBrandId,
            tenantId,
          });
        } catch (error) {
          this.logError('reorderModels', error as Error, {
            subBrandId,
            modelOrders,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Validate that a model exists and belongs to the tenant
   * Used by other services for relationship validation
   */
  async validateModelOwnership(
    modelId: string,
    tenantId: string
  ): Promise<void> {
    const model = await this.prisma.vehicleModelV3.findFirst({
      where: this.getActiveEntityWhere({
        id: modelId,
        tenantId,
      }),
      select: { id: true },
    });

    if (!model) {
      throw new NotFoundError(`Model ${modelId} not found or not accessible`);
    }
  }
}
