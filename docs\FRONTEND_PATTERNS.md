# Frontend Patterns - Tech Notes

Essential React patterns for AI agents working on the Tech Notes project.

---

## 🏗️ Architecture Overview

**Stack**: React 18 + TypeScript + Vite + TailwindCSS + React Query + Clerk
**Structure**: Atomic Design (atoms/molecules/organisms) + Service Layer + DI

```
src/
├── components/
│   ├── atoms/          # Button, Card, Input, Badge, Alert, LoadingSpinner
│   ├── molecules/      # FormField, StatCard, DataTable
│   ├── organisms/      # HealthDashboard, MarketingHero, FeatureGrid, TestimonialSection, PricingCards
│   └── index.ts        # Centralized exports
├── hooks/              # useHealth, useAuthenticatedApi, useToast
├── services/           # API integration with interfaces
├── types/              # TypeScript definitions
└── layouts/            # AppLayout, PublicLayout
```

**Design System**: Use `import { Button, Card, Input, MarketingHero, FeatureGrid, PricingCards } from '@/components'` for consistent UI.

---

## 🧱 Component Patterns

### Atom Template

```typescript
import React from 'react';
import { clsx } from 'clsx';

interface ComponentProps {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  children: React.ReactNode;
}

const Component: React.FC<ComponentProps> = ({
  variant = 'primary',
  size = 'md',
  className,
  children,
  ...props
}) => {
  return (
    <div
      className={clsx(
        'base-classes',
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

export default Component;
export type { ComponentProps };
```

### Page Component Pattern

```typescript
import { Button, Card, Alert, LoadingSpinner } from '@/components';

const Dashboard: React.FC = () => {
  const { data, isLoading, error, refetch } = useHealth();

  if (isLoading) return <LoadingSpinner />;
  if (error) return (
    <Alert variant="error">
      <p>Error: {error.message}</p>
      <Button onClick={() => refetch()}>Retry</Button>
    </Alert>
  );

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Dashboard</h1>
      {data && <HealthCard data={data} />}
    </div>
  );
};
```

---

## 🎣 Hook Patterns

### API Hook with React Query

```typescript
export const useHealth = (
  healthService: IHealthService,
  refetchInterval = 60000,
) => {
  return useQuery({
    queryKey: ["health"],
    queryFn: () => healthService.checkHealth(),
    refetchInterval,
    staleTime: 30000,
    retry: 3,
  });
};
```

### Utility Hook

```typescript
export const useToast = () => {
  const showSuccess = useCallback(
    (message: string) => toast.success(message),
    [],
  );
  const showError = useCallback((message: string) => toast.error(message), []);
  return { showSuccess, showError };
};
```

---

## 💉 Service Layer & DI

### Service Interface Pattern

```typescript
export interface IHealthService {
  checkHealth(): Promise<HealthResponse>;
}

export class HealthService implements IHealthService {
  async checkHealth(): Promise<HealthResponse> {
    const response = await fetch("/api/v1/health");
    if (!response.ok)
      throw new Error(`Health check failed: ${response.status}`);
    return response.json();
  }
}
```

### Component with DI

```typescript
interface ComponentProps {
  service: IService;
}

const Component: React.FC<ComponentProps> = ({ service }) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ["data"],
    queryFn: () => service.getData(),
  });
  // Component logic...
};
```

---

## 🎨 Layout Patterns

### Dual Layout Strategy

```typescript
function AppRouter() {
  const location = useLocation();
  const isPublicRoute = ['/', '/pricing'].includes(location.pathname);
  if (isPublicRoute) return <PublicSite />;
  return <AuthenticatedApp />;
}
```

### Layout Components

```typescript
// PublicLayout.tsx - Marketing layout
const PublicLayout: React.FC = () => (
  <div className="min-h-screen bg-white">
    <header className="bg-white border-b border-gray-100">
      <nav className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <Link to="/" className="text-2xl font-bold text-primary-600">Tech Notes</Link>
          <div className="flex items-center space-x-8">
            <Link to="/pricing">Pricing</Link>
            <Link to="/app">Sign In</Link>
          </div>
        </div>
      </nav>
    </header>
    <main><Outlet /></main>
  </div>
);

// AppLayout.tsx - Authenticated layout
const AppLayout: React.FC = () => (
  <div className="min-h-screen bg-gray-50">
    <header className="bg-white shadow-sm border-b border-gray-200">
      <nav className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <Link to="/app" className="text-xl font-bold text-gray-900">Tech Notes</Link>
          <div className="flex items-center space-x-4">
            <Link to="/app/health">Health</Link>
            <Link to="/app/showcase">Showcase</Link>
            <UserButton afterSignOutUrl="/" />
          </div>
        </div>
      </nav>
    </header>
    <main className="py-8">
      <div className="max-w-7xl mx-auto px-4">
        <ErrorBoundary><Outlet /></ErrorBoundary>
      </div>
    </main>
  </div>
);
```

---

## 🔧 Configuration

### Environment & Setup

```typescript
// config/env.schema.ts
const config = z.object({
  VITE_CLERK_PUBLISHABLE_KEY: z.string().min(1),
  VITE_API_URL: z.string().url().default('http://localhost:8080'),
}).parse(import.meta.env);

// main.tsx - Basic setup
const queryClient = new QueryClient({
  defaultOptions: { queries: { staleTime: 30000, retry: 3 } }
});

<ClerkProvider publishableKey={config.VITE_CLERK_PUBLISHABLE_KEY}>
  <QueryClientProvider client={queryClient}>
    <App />
  </QueryClientProvider>
</ClerkProvider>
```

---

## 📝 TypeScript Patterns

### Essential Types

```typescript
// types/ui.types.ts
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "danger" | "ghost";
  size?: "sm" | "md" | "lg";
  loading?: boolean;
  children: React.ReactNode;
}

// types/api.types.ts
export interface HealthResponse {
  data: { status: string; environment: string; database: string };
  timestamp: string;
}

export interface ApiError {
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
}
```

---

## 🧪 Testing Patterns

### Component Test Template

```typescript
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import Button from '@/components/atoms/Button';

describe('Button', () => {
  it('should handle click events', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click me</Button>);

    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should show loading state', () => {
    render(<Button loading>Loading</Button>);

    expect(screen.getByRole('button')).toBeDisabled();
    expect(screen.getByRole('status')).toBeInTheDocument();
  });
});
```

### Test Utilities

```typescript
export const renderWithProviders = (ui: React.ReactElement) => {
  const testQueryClient = new QueryClient({ defaultOptions: { queries: { retry: false } } });
  return render(
    <QueryClientProvider client={testQueryClient}>
      <MemoryRouter>{ui}</MemoryRouter>
    </QueryClientProvider>
  );
};
```

---

## 🚨 Critical Rules

### Must Do

- **Always use TypeScript strict mode**
- **Wrap components in ErrorBoundary**
- **Handle loading/error states in all API calls**
- **Use service interfaces for dependency injection**
- **Follow atomic design principles**
- **Include accessibility attributes (ARIA)**

### Must Avoid

- **Don't use React Query for local UI state** (use useState)
- **Don't access import.meta.env directly** (use config module)
- **Don't create new objects/functions in render** (use useCallback/useMemo)
- **Don't test implementation details** (test user behavior)
- **Don't skip error boundaries**

---

## 📚 Quick Reference

### Essential Imports

```typescript
// React & Router
import React, { useState, useEffect, useCallback } from "react";
import { Link, Navigate, Outlet } from "react-router-dom";

// Clerk Auth
import { useUser, SignedIn, SignedOut } from "@clerk/clerk-react";

// React Query
import { useQuery, useMutation } from "@tanstack/react-query";

// Design System
import { Button, Card, Input, Alert, LoadingSpinner } from "@/components";

// Styling
import { clsx } from "clsx";

// Custom
import { useAuthenticatedApi } from "@/hooks/auth";
import { useToast } from "@/hooks/ui";
```

### Routing Patterns

```typescript
// Public route detection
const isPublicRoute = ['/', '/pricing'].includes(location.pathname);

// Auth guards in routing
<SignedIn>
  <Routes>
    <Route path="/app" element={<AppLayout />}>
      <Route index element={<Dashboard />} />
    </Route>
  </Routes>
</SignedIn>
<SignedOut><Navigate to="/" replace /></SignedOut>
```

### Status Patterns

```typescript
// Loading state
if (isLoading) return <LoadingSpinner />;

// Error state
if (error) return (
  <Alert variant="error">
    <p>{error.message}</p>
    <Button onClick={() => refetch()}>Retry</Button>
  </Alert>
);

// Success state
return <ComponentContent data={data} />;
```

---

**Remember**: Keep components small, use dependency injection, handle all states (loading/error/success), and follow the established atomic design structure.
