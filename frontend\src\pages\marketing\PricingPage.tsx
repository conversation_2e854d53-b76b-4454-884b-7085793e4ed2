import React, { useState } from "react";
import {
  MarketingHero,
  PricingCards,
  TestimonialSection,
  FeatureGrid,
} from "../../components";
import {
  ArrowRight,
  Users,
  Zap,
  Star,
  Crown,
  CheckCircle,
  Shield,
  Smartphone,
  BarChart3,
  Clock,
  Globe,
  Headphones,
} from "lucide-react";

export const PricingPage: React.FC = () => {
  const [billingPeriod, setBillingPeriod] = useState<"monthly" | "yearly">(
    "monthly",
  );

  // Hero section data
  const heroData = {
    layout: "minimal" as const,
    title: "Simple, transparent pricing",
    description:
      "Choose the perfect plan for your team. Start free, upgrade when you're ready.",
    announcement: {
      text: "🎉 Save 20% with annual billing",
      variant: "success" as const,
      icon: <Star className="h-4 w-4" />,
    },
    primaryCta: {
      text: "Start Free Trial",
      href: "/sign-in",
      icon: <ArrowRight className="h-5 w-5" />,
    },
    secondaryCta: {
      text: "Contact Sales",
      href: "#contact",
      variant: "outline" as const,
    },
    socialProof: {
      text: "Join 500+ companies already using Tech Notes",
      stats: [
        { value: "14-day", label: "Free Trial" },
        { value: "No", label: "Setup Fees" },
        { value: "Cancel", label: "Anytime" },
      ],
    },
  };

  // Pricing tiers data
  const getPricingTiers = () => {
    const monthlyTiers = [
      {
        id: "basic",
        name: "Basic",
        price: "$7",
        period: "month",
        description: "Perfect for small teams getting started",
        icon: <Users className="h-6 w-6 text-primary-600" />,
        popular: false,
        features: [
          { name: "Admin managed users", included: true },
          { name: "Login reports", included: true },
          { name: "Custom vehicle groupings", included: true },
          { name: "Create custom documents", included: true },
          { name: "Mobile optimized design", included: true },
          { name: "QR code scanning", included: true },
          { name: "Email support", included: true },
          { name: "Invite codes & self sign-up", included: false },
          { name: "Usage reports", included: false },
          { name: "VIN uploads", included: false },
          { name: "White-label branding", included: false },
        ],
        buttonText: "Start Free Trial",
        buttonVariant: "outline" as const,
        buttonHref: "/sign-in",
      },
      {
        id: "plus",
        name: "Plus",
        price: "$15",
        period: "month",
        description: "Everything in Basic plus advanced features",
        icon: <Zap className="h-6 w-6 text-primary-600" />,
        popular: false,
        features: [
          { name: "Everything in Basic", included: true, highlight: true },
          { name: "Invite codes & self sign-up", included: true },
          { name: "Usage reports", included: true },
          { name: "VIN uploads", included: true },
          { name: "White-label branding", included: true },
          { name: "VIN scanning", included: true },
          { name: "Priority support", included: true },
          { name: "Advanced analytics", included: false },
          { name: "API access", included: false },
          { name: "Custom integrations", included: false },
          { name: "Dedicated account manager", included: false },
        ],
        buttonText: "Start Free Trial",
        buttonVariant: "primary" as const,
        buttonHref: "/sign-in",
      },
      {
        id: "pro",
        name: "Pro",
        price: "$30",
        period: "month",
        description: "Everything in Plus plus automation and analytics",
        icon: <Star className="h-6 w-6 text-primary-600" />,
        popular: true,
        features: [
          { name: "Everything in Plus", included: true, highlight: true },
          { name: "Advanced analytics", included: true },
          { name: "API access", included: true },
          { name: "Custom integrations", included: true },
          { name: "Workflow automation", included: true },
          { name: "Advanced reporting", included: true },
          { name: "Phone support", included: true },
          { name: "SSO integration", included: false },
          { name: "Dedicated account manager", included: false },
          { name: "Custom training", included: false },
          { name: "SLA guarantee", included: false },
        ],
        buttonText: "Start Free Trial",
        buttonVariant: "primary" as const,
        buttonHref: "/sign-in",
      },
      {
        id: "enterprise",
        name: "Enterprise",
        price: "Custom",
        period: "",
        description: "Advanced features for large organizations",
        icon: <Crown className="h-6 w-6 text-primary-600" />,
        popular: false,
        features: [
          { name: "Everything in Pro", included: true, highlight: true },
          { name: "SSO integration", included: true },
          { name: "Dedicated account manager", included: true },
          { name: "Custom training", included: true },
          { name: "SLA guarantee", included: true },
          { name: "Custom development", included: true },
          { name: "On-premise deployment", included: true },
          { name: "24/7 phone support", included: true },
          { name: "Unlimited users", included: true },
          { name: "Advanced security", included: true },
          { name: "Compliance reporting", included: true },
        ],
        buttonText: "Contact Sales",
        buttonVariant: "outline" as const,
        buttonHref: "#contact",
      },
    ];

    const yearlyTiers = monthlyTiers.map((tier) => ({
      ...tier,
      price:
        tier.id === "enterprise"
          ? "Custom"
          : tier.id === "basic"
            ? "$70"
            : tier.id === "plus"
              ? "$150"
              : "$300",
      period: tier.id === "enterprise" ? "" : "year",
    }));

    return billingPeriod === "monthly" ? monthlyTiers : yearlyTiers;
  };

  // Features section data
  const featuresData = {
    title: "Why choose Tech Notes?",
    subtitle:
      "Everything you need to manage technical documentation and work orders",
    badge: {
      text: "Platform Benefits",
      variant: "primary" as const,
      icon: <CheckCircle className="h-4 w-4" />,
    },
    features: [
      {
        id: "mobile-first",
        title: "Mobile-First Design",
        description:
          "Built for technicians in the field with offline access and real-time synchronization across all devices.",
        icon: <Smartphone className="h-6 w-6" />,
        variant: "elevated" as const,
        interactive: true,
      },
      {
        id: "enterprise-security",
        title: "Enterprise Security",
        description:
          "Bank-level security with role-based access control, audit logs, and compliance-ready data protection.",
        icon: <Shield className="h-6 w-6" />,
        variant: "elevated" as const,
        interactive: true,
      },
      {
        id: "advanced-analytics",
        title: "Advanced Analytics",
        description:
          "Deep insights into user engagement, login patterns, and content usage to optimize your operations.",
        icon: <BarChart3 className="h-6 w-6" />,
        variant: "elevated" as const,
        interactive: true,
      },
      {
        id: "real-time-sync",
        title: "Real-Time Sync",
        description:
          "Instant updates across all devices ensure your team always has the latest information.",
        icon: <Clock className="h-6 w-6" />,
        variant: "elevated" as const,
        interactive: true,
      },
      {
        id: "global-access",
        title: "Global Access",
        description:
          "Access your technical documentation from anywhere in the world with 99.9% uptime guarantee.",
        icon: <Globe className="h-6 w-6" />,
        variant: "elevated" as const,
        interactive: true,
      },
      {
        id: "premium-support",
        title: "Premium Support",
        description:
          "24/7 support with dedicated account managers for Enterprise customers and priority support for all plans.",
        icon: <Headphones className="h-6 w-6" />,
        variant: "elevated" as const,
        interactive: true,
      },
    ],
  };

  // Testimonials section data
  const testimonialsData = {
    title: "Trusted by service professionals worldwide",
    subtitle:
      "See what our customers have to say about Tech Notes pricing and value",
    badge: {
      text: "Customer Success",
      variant: "success" as const,
      icon: <Star className="h-4 w-4" />,
    },
    testimonials: [
      {
        id: "testimonial-1",
        quote:
          "The pricing is transparent and fair. We started with Basic and upgraded to Pro as we grew. The ROI has been incredible.",
        author: {
          name: "Sarah Johnson",
          title: "Operations Manager",
          company: "ServicePro Solutions",
        },
        rating: 5,
        variant: "elevated" as const,
      },
      {
        id: "testimonial-2",
        quote:
          "Enterprise plan gives us everything we need for our 500+ technicians. The dedicated support is worth every penny.",
        author: {
          name: "Mike Chen",
          title: "Field Service Director",
          company: "TechFlow Industries",
        },
        rating: 5,
        variant: "elevated" as const,
      },
      {
        id: "testimonial-3",
        quote:
          "Started with the free trial and immediately saw the value. Plus plan is perfect for our mid-size operation.",
        author: {
          name: "Emily Rodriguez",
          title: "IT Manager",
          company: "Global Maintenance Corp",
        },
        rating: 5,
        variant: "elevated" as const,
      },
    ],
  };

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="py-20 sm:py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <MarketingHero {...heroData} />
        </div>
      </section>

      {/* Pricing Cards Section */}
      <section className="py-20 sm:py-32 bg-gray-50" id="pricing">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <PricingCards
            tiers={getPricingTiers()}
            title="Choose your plan"
            subtitle="All plans include a 14-day free trial. No credit card required."
            badge={{
              text: "Most Popular",
              variant: "primary",
              icon: <Star className="h-4 w-4" />,
            }}
            layout="grid"
            columns={4}
            spacing="normal"
            highlightPopular={true}
            showComparison={false}
            billingPeriod={{
              current: billingPeriod,
              onToggle: setBillingPeriod,
              discount: billingPeriod === "yearly" ? "Save 20%" : undefined,
            }}
          />
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 sm:py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FeatureGrid {...featuresData} />
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 sm:py-32 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <TestimonialSection {...testimonialsData} />
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 sm:py-32" id="contact">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <MarketingHero
            layout="minimal"
            title="Ready to get started?"
            description="Join thousands of service professionals who trust Tech Notes for their technical documentation needs."
            primaryCta={{
              text: "Start Free Trial",
              href: "/sign-in",
              icon: <ArrowRight className="h-5 w-5" />,
            }}
            secondaryCta={{
              text: "Contact Sales",
              href: "#contact",
              variant: "outline",
            }}
            socialProof={{
              text: "No setup fees • Cancel anytime • 24/7 support",
              stats: [
                { value: "500+", label: "Happy Customers" },
                { value: "99.9%", label: "Uptime" },
                { value: "24/7", label: "Support" },
              ],
            }}
          />
        </div>
      </section>
    </div>
  );
};
