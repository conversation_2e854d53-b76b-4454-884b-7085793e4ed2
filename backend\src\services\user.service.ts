import { clerkClient } from '@clerk/clerk-sdk-node';
import { User, Prisma, RoleType } from '@prisma/client';
import { BackendAuthContext, UserRoleWithContext } from '@tech-notes/shared';

import {
  NotFoundError,
  ValidationError,
  ConflictError,
} from '../types/error.types.js';

import { BaseTenantService } from './base-tenant.service.js';

export interface CreateUserData {
  clerkId: string;
  email: string;
  firstName?: string;
  lastName?: string;
  imageUrl?: string;
  isActive?: boolean;
  tenantId: string;
}

export interface UpdateUserData {
  email?: string;
  firstName?: string;
  lastName?: string;
  imageUrl?: string;
  isActive?: boolean;
}

export interface ClerkUserData {
  id: string;
  emailAddresses: Array<{ id: string; emailAddress: string }>;
  firstName?: string | null;
  lastName?: string | null;
  imageUrl?: string | null;
}

export class UserService extends BaseTenantService {
  /**
   * Get all users for a specific tenant
   */
  async getUsersByTenant(tenantId: string): Promise<User[]> {
    this.logOperation('getUsersByTenant', { tenantId });

    return await this.withTenantScope(tenantId, async () => {
      return await this.prisma.user.findMany({
        where: { tenantId },
        orderBy: { createdAt: 'desc' },
      });
    });
  }

  /**
   * Get user by ID with tenant validation
   */
  async getUserById(userId: string, tenantId: string): Promise<User | null> {
    this.logOperation('getUserById', { userId, tenantId });

    return await this.withTenantScope(tenantId, async () => {
      return await this.prisma.user.findFirst({
        where: {
          id: userId,
          tenantId, // Double validation: scope + where clause
        },
      });
    });
  }

  /**
   * Get user by Clerk ID (global lookup for auth)
   */
  async getUserByClerkId(clerkId: string): Promise<User | null> {
    this.logOperation('getUserByClerkId', { clerkId });

    try {
      return await this.prisma.user.findUnique({
        where: { clerkId },
        include: { tenant: true },
      });
    } catch (error) {
      this.logError('getUserByClerkId', error as Error, { clerkId });
      throw error;
    }
  }

  /**
   * Get Clerk user information
   */
  async getClerkUser(clerkId: string): Promise<ClerkUserData> {
    this.logOperation('getClerkUser', { clerkId });

    try {
      const clerkUser = await clerkClient.users.getUser(clerkId);

      return {
        id: clerkUser.id,
        emailAddresses: clerkUser.emailAddresses.map((email) => ({
          id: email.id,
          emailAddress: email.emailAddress,
        })),
        firstName: clerkUser.firstName,
        lastName: clerkUser.lastName,
        imageUrl: clerkUser.imageUrl,
      };
    } catch (error) {
      this.logError('getClerkUser', error as Error, { clerkId });
      throw error;
    }
  }

  /**
   * Get user by email (global lookup)
   */
  async getUserByEmail(email: string): Promise<User | null> {
    this.logOperation('getUserByEmail', { email });

    try {
      return await this.prisma.user.findFirst({
        where: { email },
      });
    } catch (error) {
      this.logError('getUserByEmail', error as Error, { email });
      throw error;
    }
  }

  /**
   * Create a new user
   */
  async createUser(userData: CreateUserData): Promise<User> {
    this.logOperation('createUser', {
      email: userData.email,
      tenantId: userData.tenantId,
    });

    return await this.withTenantScope(userData.tenantId, async () => {
      try {
        return await this.prisma.user.create({
          data: userData,
        });
      } catch (error) {
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === 'P2002') {
            throw new ConflictError('User with this Clerk ID already exists');
          }
        }
        this.logError(
          'createUser',
          error as Error,
          userData as unknown as Record<string, unknown>
        );
        throw error;
      }
    });
  }

  /**
   * Update user data
   */
  async updateUser(
    userId: string,
    userData: UpdateUserData,
    tenantId: string
  ): Promise<User> {
    this.logOperation('updateUser', { userId, tenantId });

    return await this.withTenantScope(tenantId, async () => {
      try {
        const user = await this.prisma.user.findFirst({
          where: { id: userId, tenantId },
        });

        if (!user) {
          throw new NotFoundError('User not found');
        }

        return await this.prisma.user.update({
          where: { id: userId },
          data: userData,
        });
      } catch (error) {
        this.logError('updateUser', error as Error, { userId, tenantId });
        throw error;
      }
    });
  }

  /**
   * Delete user
   */
  async deleteUser(userId: string, tenantId: string): Promise<void> {
    this.logOperation('deleteUser', { userId, tenantId });

    return await this.withTenantScope(tenantId, async () => {
      try {
        const user = await this.prisma.user.findFirst({
          where: { id: userId, tenantId },
        });

        if (!user) {
          throw new NotFoundError('User not found');
        }

        await this.prisma.user.delete({
          where: { id: userId },
        });
      } catch (error) {
        this.logError('deleteUser', error as Error, { userId, tenantId });
        throw error;
      }
    });
  }

  /**
   * Deactivate a user (set isActive to false)
   */
  async deactivateUser(userId: string, tenantId: string): Promise<User> {
    this.logOperation('deactivateUser', { userId, tenantId });

    return await this.withTenantScope(tenantId, async () => {
      const user = await this.prisma.user.findFirst({
        where: {
          id: userId,
          tenantId,
        },
      });

      if (!user) {
        throw new NotFoundError(`User ${userId} not found`);
      }

      try {
        return await this.prisma.user.update({
          where: { id: userId },
          data: { isActive: false },
        });
      } catch (error) {
        this.logError('deactivateUser', error as Error, { userId, tenantId });
        throw error;
      }
    });
  }

  /**
   * Activate a user (set isActive to true)
   */
  async activateUser(userId: string, tenantId: string): Promise<User> {
    this.logOperation('activateUser', { userId, tenantId });

    return await this.withTenantScope(tenantId, async () => {
      const user = await this.prisma.user.findFirst({
        where: {
          id: userId,
          tenantId,
        },
      });

      if (!user) {
        throw new NotFoundError(`User ${userId} not found`);
      }

      try {
        return await this.prisma.user.update({
          where: { id: userId },
          data: { isActive: true },
        });
      } catch (error) {
        this.logError('activateUser', error as Error, { userId, tenantId });
        throw error;
      }
    });
  }

  /**
   * Sync user data from Clerk
   */
  async syncUserFromClerk(
    clerkUser: ClerkUserData,
    tenantId?: string
  ): Promise<User> {
    this.logOperation('syncUserFromClerk', {
      clerkId: clerkUser.id,
      tenantId,
    });

    const primaryEmail = clerkUser.emailAddresses.find(
      (email) => email.id === clerkUser.emailAddresses[0]?.id
    );

    if (!primaryEmail) {
      throw new ValidationError('User must have a valid email address');
    }

    const existingUser = await this.getUserByClerkId(clerkUser.id);

    if (existingUser) {
      return await this.updateUser(
        existingUser.id,
        {
          email: primaryEmail.emailAddress,
          firstName: clerkUser.firstName || undefined,
          lastName: clerkUser.lastName || undefined,
          imageUrl: clerkUser.imageUrl || undefined,
        },
        existingUser.tenantId
      );
    } else {
      if (!tenantId) {
        throw new ValidationError('tenantId is required for new user creation');
      }

      return await this.createUser({
        clerkId: clerkUser.id,
        email: primaryEmail.emailAddress,
        firstName: clerkUser.firstName || undefined,
        lastName: clerkUser.lastName || undefined,
        imageUrl: clerkUser.imageUrl || undefined,
        tenantId,
      });
    }
  }

  /**
   * Example transaction: Create user with additional profile data
   */
  async createUserWithProfile(userData: CreateUserData): Promise<User> {
    this.logOperation('createUserWithProfile', {
      email: userData.email,
      tenantId: userData.tenantId,
    });

    return await this.withTenantScope(userData.tenantId, async () => {
      return await this.transaction(async (tx) => {
        const user = await tx.user.create({
          data: userData,
        });

        // Example: Could create additional profile data here
        // await tx.userProfile.create({
        //   data: { userId: user.id, tenantId: userData.tenantId }
        // });

        return user;
      });
    });
  }

  // ===== RBAC METHODS =====

  /**
   * Get user by Clerk ID with roles (for auth middleware)
   */
  async getUserByClerkIdWithRoles(
    clerkId: string
  ): Promise<(User & { userRoles: UserRoleWithContext[] }) | null> {
    this.logOperation('getUserByClerkIdWithRoles', { clerkId });

    try {
      const user = await this.prisma.user.findUnique({
        where: { clerkId },
        include: {
          tenant: true,
          userRoles: {
            include: {
              role: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                  isSystemRole: true,
                },
              },
            },
          },
        },
      });

      if (!user) {
        return null;
      }

      // Transform userRoles to match UserRoleWithContext interface
      const userRoles: UserRoleWithContext[] = user.userRoles.map(
        (userRole) => ({
          id: userRole.id,
          role: userRole.role,
          tenantId: userRole.tenantId,
        })
      );

      return {
        ...user,
        userRoles,
      };
    } catch (error) {
      this.logError('getUserByClerkIdWithRoles', error as Error, { clerkId });
      throw error;
    }
  }

  /**
   * Get users by tenant with their roles (for admin interfaces)
   */
  async getUsersByTenantWithRoles(
    tenantId: string,
    BackendAuthContext: BackendAuthContext
  ): Promise<Array<User & { userRoles: UserRoleWithContext[] }>> {
    this.logOperation('getUsersByTenantWithRoles', {
      tenantId,
      requestedBy: BackendAuthContext.id,
      canBypassTenantScope: BackendAuthContext.canBypassTenantScope,
    });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // System Admins can see all users across all tenants
        // Company Admins and below only see users in their tenant
        const whereClause = BackendAuthContext.canBypassTenantScope
          ? {} // No tenant restriction for System Admins
          : { tenantId }; // Tenant-scoped for Company Admins

        const users = await this.prisma.user.findMany({
          where: whereClause,
          include: {
            userRoles: {
              include: {
                role: {
                  select: {
                    id: true,
                    name: true,
                    type: true,
                    isSystemRole: true,
                  },
                },
              },
              // For System Admins: show all roles
              // For Company Admins: only show roles relevant to this tenant or system roles
              where: BackendAuthContext.canBypassTenantScope
                ? {} // No role restriction for System Admins
                : {
                    OR: [
                      { tenantId: tenantId },
                      { tenantId: null }, // System roles
                    ],
                  },
            },
            // Include tenant information for System Admins to see which tenant each user belongs to
            tenant: BackendAuthContext.canBypassTenantScope
              ? {
                  select: {
                    id: true,
                    name: true,
                    slug: true,
                  },
                }
              : undefined,
          },
          orderBy: { createdAt: 'desc' },
        });

        return users.map((user) => ({
          ...user,
          userRoles: user.userRoles.map((userRole) => ({
            id: userRole.id,
            role: userRole.role,
            tenantId: userRole.tenantId,
          })),
        }));
      },
      BackendAuthContext
    );
  }

  /**
   * Get user by ID with roles
   */
  async getUserByIdWithRoles(
    userId: string,
    tenantId: string,
    BackendAuthContext: BackendAuthContext
  ): Promise<(User & { userRoles: UserRoleWithContext[] }) | null> {
    this.logOperation('getUserByIdWithRoles', {
      userId,
      tenantId,
      requestedBy: BackendAuthContext.id,
    });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        const user = await this.prisma.user.findFirst({
          where: {
            id: userId,
            tenantId,
          },
          include: {
            userRoles: {
              include: {
                role: {
                  select: {
                    id: true,
                    name: true,
                    type: true,
                    isSystemRole: true,
                  },
                },
              },
            },
          },
        });

        if (!user) {
          return null;
        }

        return {
          ...user,
          userRoles: user.userRoles.map((userRole) => ({
            id: userRole.id,
            role: userRole.role,
            tenantId: userRole.tenantId,
          })),
        };
      },
      BackendAuthContext
    );
  }

  /**
   * Check if user has a specific role type
   */
  async userHasRoleType(
    userId: string,
    roleType: RoleType,
    BackendAuthContext: BackendAuthContext
  ): Promise<boolean> {
    this.logOperation('userHasRoleType', {
      userId,
      roleType,
      requestedBy: BackendAuthContext.id,
    });

    try {
      // System Admins can check any user
      if (!BackendAuthContext.canBypassTenantScope) {
        // Validate the target user is in the same tenant
        const targetUser = await this.prisma.user.findUnique({
          where: { id: userId },
          select: { tenantId: true },
        });

        if (
          !targetUser ||
          targetUser.tenantId !== BackendAuthContext.tenantId
        ) {
          throw new NotFoundError('User not found or access denied');
        }
      }

      const userRole = await this.prisma.userRole.findFirst({
        where: {
          userId,
          role: {
            type: roleType,
          },
        },
        include: {
          role: {
            select: { type: true },
          },
        },
      });

      return !!userRole;
    } catch (error) {
      this.logError('userHasRoleType', error as Error, { userId, roleType });
      return false;
    }
  }

  /**
   * Get all users with System Admin role (System Admin only)
   */
  async getSystemAdmins(
    BackendAuthContext: BackendAuthContext
  ): Promise<User[]> {
    this.logOperation('getSystemAdmins', {
      requestedBy: BackendAuthContext.id,
    });

    if (!BackendAuthContext.canBypassTenantScope) {
      throw new NotFoundError(
        'Only System Admins can view system administrators'
      );
    }

    const systemAdminUsers = await this.prisma.user.findMany({
      where: {
        userRoles: {
          some: {
            role: {
              isSystemRole: true,
            },
          },
        },
      },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return systemAdminUsers;
  }
}
