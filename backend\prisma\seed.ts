/**
 * Database Seed Script
 *
 * This script seeds the database with initial data for development and testing.
 *
 * For tests: Seeds both RBAC data and test data
 * For production: RBAC data comes from migration, this script not used
 *
 * IMPORTANT: Before running this script, update the Clerk IDs below:
 * 1. Go to your Clerk Dashboard > Users
 * 2. Click on each user (testadmin, dev-companyadmin, devtech)
 * 3. Copy their User ID (starts with "user_")
 * 4. Replace the placeholder Clerk IDs in this file (search for "dev-system-admin-clerk-id", etc.)
 *
 * Usage: npm run db:seed
 */

import {
  PrismaClient,
  RoleType,
  PermissionResource,
  PermissionAction,
} from '@prisma/client';

const prisma = new PrismaClient();

async function seedRBACForTests() {
  console.log('🔐 Seeding RBAC system for tests...');

  // Create permissions
  const permissions = await Promise.all([
    // User management permissions
    prisma.permission.upsert({
      where: { name: 'user:read' },
      update: {},
      create: {
        name: 'user:read',
        description: 'Read user information',
        resource: PermissionResource.USER,
        action: PermissionAction.READ,
      },
    }),
    prisma.permission.upsert({
      where: { name: 'user:write' },
      update: {},
      create: {
        name: 'user:write',
        description: 'Create and update users',
        resource: PermissionResource.USER,
        action: PermissionAction.WRITE,
      },
    }),
    prisma.permission.upsert({
      where: { name: 'user:delete' },
      update: {},
      create: {
        name: 'user:delete',
        description: 'Delete users',
        resource: PermissionResource.USER,
        action: PermissionAction.DELETE,
      },
    }),
    prisma.permission.upsert({
      where: { name: 'user:manage' },
      update: {},
      create: {
        name: 'user:manage',
        description: 'Full user management',
        resource: PermissionResource.USER,
        action: PermissionAction.MANAGE,
      },
    }),
    // Tenant management permissions
    prisma.permission.upsert({
      where: { name: 'tenant:read' },
      update: {},
      create: {
        name: 'tenant:read',
        description: 'Read tenant information',
        resource: PermissionResource.TENANT,
        action: PermissionAction.READ,
      },
    }),
    prisma.permission.upsert({
      where: { name: 'tenant:write' },
      update: {},
      create: {
        name: 'tenant:write',
        description: 'Update tenant settings',
        resource: PermissionResource.TENANT,
        action: PermissionAction.WRITE,
      },
    }),
    prisma.permission.upsert({
      where: { name: 'tenant:manage' },
      update: {},
      create: {
        name: 'tenant:manage',
        description: 'Full tenant management',
        resource: PermissionResource.TENANT,
        action: PermissionAction.MANAGE,
      },
    }),
    // System permissions
    prisma.permission.upsert({
      where: { name: 'system:manage' },
      update: {},
      create: {
        name: 'system:manage',
        description: 'System administration',
        resource: PermissionResource.SYSTEM,
        action: PermissionAction.MANAGE,
      },
    }),
    // Data permissions
    prisma.permission.upsert({
      where: { name: 'data:read' },
      update: {},
      create: {
        name: 'data:read',
        description: 'Read application data',
        resource: PermissionResource.DATA,
        action: PermissionAction.READ,
      },
    }),
    prisma.permission.upsert({
      where: { name: 'data:write' },
      update: {},
      create: {
        name: 'data:write',
        description: 'Create and update application data',
        resource: PermissionResource.DATA,
        action: PermissionAction.WRITE,
      },
    }),
    // Document permissions
    prisma.permission.upsert({
      where: { name: 'document:read' },
      update: {},
      create: {
        name: 'document:read',
        description: 'Read and view documents',
        resource: PermissionResource.DOCUMENT,
        action: PermissionAction.READ,
      },
    }),
    prisma.permission.upsert({
      where: { name: 'document:write' },
      update: {},
      create: {
        name: 'document:write',
        description: 'Create and upload documents',
        resource: PermissionResource.DOCUMENT,
        action: PermissionAction.WRITE,
      },
    }),
    prisma.permission.upsert({
      where: { name: 'document:delete' },
      update: {},
      create: {
        name: 'document:delete',
        description: 'Delete documents',
        resource: PermissionResource.DOCUMENT,
        action: PermissionAction.DELETE,
      },
    }),
  ]);

  // Create roles
  const systemAdminRole = await prisma.role.upsert({
    where: { name: 'System Admin' },
    update: {},
    create: {
      name: 'System Admin',
      type: RoleType.SYSTEM_ADMIN,
      description: 'Global system administrator with access to all tenants',
      isSystemRole: true,
    },
  });

  const companyAdminRole = await prisma.role.upsert({
    where: { name: 'Company Admin' },
    update: {},
    create: {
      name: 'Company Admin',
      type: RoleType.COMPANY_ADMIN,
      description: 'Full administrative access within a specific tenant',
      isSystemRole: false,
    },
  });

  const companyTechRole = await prisma.role.upsert({
    where: { name: 'Company Tech' },
    update: {},
    create: {
      name: 'Company Tech',
      type: RoleType.COMPANY_TECH,
      description:
        'Technical user with limited administrative access within a tenant',
      isSystemRole: false,
    },
  });

  // Assign permissions to roles
  // System Admin gets all permissions
  await prisma.rolePermission.createMany({
    data: permissions.map((permission) => ({
      roleId: systemAdminRole.id,
      permissionId: permission.id,
    })),
    skipDuplicates: true,
  });

  // Company Admin gets tenant and user management permissions
  const companyAdminPermissions = permissions.filter(
    (p) => p.resource !== PermissionResource.SYSTEM
  );
  await prisma.rolePermission.createMany({
    data: companyAdminPermissions.map((permission) => ({
      roleId: companyAdminRole.id,
      permissionId: permission.id,
    })),
    skipDuplicates: true,
  });

  // Company Tech gets read permissions and data write
  const companyTechPermissions = permissions.filter(
    (p) =>
      p.action === PermissionAction.READ ||
      (p.resource === PermissionResource.DATA &&
        p.action === PermissionAction.WRITE)
  );
  await prisma.rolePermission.createMany({
    data: companyTechPermissions.map((permission) => ({
      roleId: companyTechRole.id,
      permissionId: permission.id,
    })),
    skipDuplicates: true,
  });

  console.log('✅ RBAC system seeded successfully');
  console.log(`Created ${permissions.length} permissions and 3 roles`);
}

async function main() {
  console.log('🌱 Starting database seed...');
  console.log('ℹ️  For tests: Seeds both RBAC data and test data');
  console.log(
    'ℹ️  For production: RBAC data comes from migration, this script not used'
  );

  // Seed RBAC system (needed for tests since they don't run migrations)
  await seedRBACForTests();

  // Create test tenant
  const testTenant = await prisma.tenant.upsert({
    where: { slug: 'test-tenant' },
    update: {},
    create: {
      name: 'Test Tenant',
      slug: 'test-tenant',
      isActive: true,
    },
  });

  console.log('✅ Created test tenant:', testTenant.name);

  // Create System Admin user
  // TODO: Replace with actual Clerk ID from your Clerk dashboard
  // Go to Clerk Dashboard > Users > click on testadmin user > copy the User ID
  const systemAdminUser = await prisma.user.upsert({
    where: { clerkId: 'user_2zhzALLWopsDoUbDU0f25VaQF6D' }, // Replace with actual Clerk ID for testadmin
    update: {},
    create: {
      clerkId: 'user_2zhzALLWopsDoUbDU0f25VaQF6D',
      email: '<EMAIL>',
      firstName: 'System',
      lastName: 'Admin',
      tenantId: testTenant.id,
    },
  });

  console.log('✅ Created System Admin user:', systemAdminUser.email);

  // Create Company Admin user with their own tenant
  const companyTenant = await prisma.tenant.upsert({
    where: { slug: 'company-tenant' },
    update: {},
    create: {
      name: 'Company Tenant',
      slug: 'company-tenant',
      isActive: true,
    },
  });

  const companyAdminUser = await prisma.user.upsert({
    where: { clerkId: 'user_2z65BXYUnvs6lVS8eQ4eYIzn8rM' }, // Replace with actual Clerk ID for dev-companyadmin
    update: {},
    create: {
      clerkId: 'user_2z65BXYUnvs6lVS8eQ4eYIzn8rM',
      email: '<EMAIL>',
      firstName: 'Company',
      lastName: 'Admin',
      tenantId: companyTenant.id,
    },
  });

  console.log('✅ Created Company Admin user:', companyAdminUser.email);

  // Create Company Tech user (same tenant as Company Admin)
  const companyTechUser = await prisma.user.upsert({
    where: { clerkId: 'user_2z65MOxfftZkeRmwfhhY1296Shj' }, // Replace with actual Clerk ID for devtech
    update: {},
    create: {
      clerkId: 'user_2z65MOxfftZkeRmwfhhY1296Shj',
      email: '<EMAIL>',
      firstName: 'Company',
      lastName: 'Tech',
      tenantId: companyTenant.id,
    },
  });

  console.log('✅ Created Company Tech user:', companyTechUser.email);

  // Get roles for assignment
  const systemAdminRole = await prisma.role.findUnique({
    where: { name: 'System Admin' },
  });
  const companyAdminRole = await prisma.role.findUnique({
    where: { name: 'Company Admin' },
  });
  const companyTechRole = await prisma.role.findUnique({
    where: { name: 'Company Tech' },
  });

  if (!systemAdminRole || !companyAdminRole || !companyTechRole) {
    throw new Error(
      'Required roles not found. Make sure RBAC seeding completed successfully.'
    );
  }

  // Assign System Admin role (global, no tenant restriction)
  // Check if role assignment already exists
  const existingSystemAdminRole = await prisma.userRole.findFirst({
    where: {
      userId: systemAdminUser.id,
      roleId: systemAdminRole.id,
      tenantId: null,
    },
  });

  if (!existingSystemAdminRole) {
    await prisma.userRole.create({
      data: {
        userId: systemAdminUser.id,
        roleId: systemAdminRole.id,
        tenantId: null, // System Admin is global
      },
    });
    console.log('✅ Assigned System Admin role to:', systemAdminUser.email);
  } else {
    console.log(
      '✅ System Admin role already assigned to:',
      systemAdminUser.email
    );
  }

  // Assign Company Admin role (tenant-scoped)
  await prisma.userRole.upsert({
    where: {
      userId_roleId_tenantId: {
        userId: companyAdminUser.id,
        roleId: companyAdminRole.id,
        tenantId: companyTenant.id,
      },
    },
    update: {},
    create: {
      userId: companyAdminUser.id,
      roleId: companyAdminRole.id,
      tenantId: companyTenant.id,
    },
  });

  console.log('✅ Assigned Company Admin role to:', companyAdminUser.email);

  // Assign Company Tech role (tenant-scoped)
  await prisma.userRole.upsert({
    where: {
      userId_roleId_tenantId: {
        userId: companyTechUser.id,
        roleId: companyTechRole.id,
        tenantId: companyTenant.id,
      },
    },
    update: {},
    create: {
      userId: companyTechUser.id,
      roleId: companyTechRole.id,
      tenantId: companyTenant.id,
    },
  });

  console.log('✅ Assigned Company Tech role to:', companyTechUser.email);

  // Create sample documents for testing
  const sampleDocuments = [
    {
      fileName: 'user_manual.pdf',
      originalName: 'User Manual.pdf',
      fileSize: 2048576, // 2MB
      mimeType: 'application/pdf',
      title: 'User Manual',
      description: 'Complete user manual for the system',
      tenantId: testTenant.id,
      createdBy: systemAdminUser.id,
    },
    {
      fileName: 'technical_specs.docx',
      originalName: 'Technical Specifications.docx',
      fileSize: 1024000, // 1MB
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      title: 'Technical Specifications',
      description: 'Detailed technical specifications document',
      tenantId: companyTenant.id,
      createdBy: companyAdminUser.id,
    },
    {
      fileName: 'data_export.xlsx',
      originalName: 'Data Export.xlsx',
      fileSize: 512000, // 512KB
      mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      title: 'Data Export',
      description: 'Monthly data export spreadsheet',
      tenantId: companyTenant.id,
      createdBy: companyTechUser.id,
    },
    {
      fileName: 'test_data.csv',
      originalName: 'test.csv',
      fileSize: 1024, // 1KB
      mimeType: 'text/csv',
      title: 'Test Data',
      description: 'Sample CSV data for testing',
      tenantId: testTenant.id,
      createdBy: systemAdminUser.id,
    },
    {
      fileName: 'product_image.jpg',
      originalName: 'Product Image.jpg',
      fileSize: 2048000, // 2MB
      mimeType: 'image/jpeg',
      title: 'Product Image',
      description: 'High-resolution product photograph',
      tenantId: companyTenant.id,
      createdBy: companyAdminUser.id,
    },
  ];

  for (const docData of sampleDocuments) {
    // Generate unique S3 key for each document
    const documentId = `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const s3Key = `tenant-${docData.tenantId}/${documentId}-${docData.fileName}`;

    await prisma.document.upsert({
      where: { s3Key },
      update: {},
      create: {
        ...docData,
        s3Key,
      },
    });

    console.log(`✅ Created sample document: ${docData.title}`);
  }

  console.log('🎉 Database seed completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Database seed failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
