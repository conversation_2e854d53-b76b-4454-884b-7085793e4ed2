{"extends": "./tsconfig.json", "compilerOptions": {"strict": false, "noEmitOnError": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "noImplicitReturns": false, "noImplicitThis": false, "skipLibCheck": true, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false}, "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}