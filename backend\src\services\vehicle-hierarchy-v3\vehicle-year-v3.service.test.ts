import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { BackendAuthContext } from '@tech-notes/shared';

import {
  ConflictError,
  NotFoundError,
  ValidationError,
} from '../../types/error.types.js';

import { VehicleYearV3Service } from './vehicle-year-v3.service.js';

describe('VehicleYearV3Service', () => {
  let vehicleYearV3Service: VehicleYearV3Service;
  let mockServices: any;
  let mockAuthContext: BackendAuthContext;

  beforeEach(() => {
    // Create mocks directly in test following project patterns
    mockServices = {
      prismaService: {
        prisma: {
          tenant: {
            findUnique: jest.fn().mockResolvedValue({ id: 'test-tenant-id' }),
          },
          user: {
            findFirst: jest.fn().mockResolvedValue({ id: 'test-user-id' }),
          },
          vehicleYearV3: {
            findMany: jest.fn(),
            findFirst: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            aggregate: jest.fn(),
          },
          $transaction: jest.fn(),
        },
        healthCheck: jest.fn().mockResolvedValue(true),
      },
      logger: {
        error: jest.fn(),
        warn: jest.fn(),
        info: jest.fn(),
        debug: jest.fn(),
        verbose: jest.fn(),
        silly: jest.fn(),
        log: jest.fn(),
        query: jest.fn(),
      },
    };

    mockAuthContext = {
      id: 'test-user-id',
      clerkId: 'test-clerk-id',
      tenantId: 'test-tenant-id',
      email: '<EMAIL>',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    vehicleYearV3Service = new VehicleYearV3Service(
      mockServices.prismaService,
      mockServices.logger
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getYearsByTenant', () => {
    it('should return years for tenant', async () => {
      const mockYears = [
        {
          id: 'year-1',
          name: '2020',
          tenantId: 'test-tenant-id',
          isActive: true,
          displayOrder: 1,
          deletedAt: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'year-2',
          name: '2021',
          tenantId: 'test-tenant-id',
          isActive: true,
          displayOrder: 2,
          deletedAt: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockServices.prismaService.prisma.vehicleYearV3.findMany.mockResolvedValue(
        mockYears
      );

      const result = await vehicleYearV3Service.getYearsByTenant(
        'test-tenant-id',
        mockAuthContext
      );

      expect(result).toEqual(mockYears);
      expect(
        mockServices.prismaService.prisma.vehicleYearV3.findMany
      ).toHaveBeenCalledWith({
        where: {
          tenantId: 'test-tenant-id',
          deletedAt: null,
        },
        orderBy: [{ displayOrder: 'asc' }, { name: 'asc' }],
      });
    });
  });

  describe('getYearById', () => {
    it('should return year by ID', async () => {
      const mockYear = {
        id: 'year-1',
        name: '2020',
        tenantId: 'test-tenant-id',
        isActive: true,
        displayOrder: 1,
        deletedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockServices.prismaService.prisma.vehicleYearV3.findFirst.mockResolvedValue(
        mockYear
      );

      const result = await vehicleYearV3Service.getYearById(
        'year-1',
        'test-tenant-id',
        mockAuthContext
      );

      expect(result).toEqual(mockYear);
      expect(
        mockServices.prismaService.prisma.vehicleYearV3.findFirst
      ).toHaveBeenCalledWith({
        where: {
          id: 'year-1',
          tenantId: 'test-tenant-id',
          deletedAt: null,
        },
      });
    });

    it('should return null if year not found', async () => {
      mockServices.prismaService.prisma.vehicleYearV3.findFirst.mockResolvedValue(
        null
      );

      const result = await vehicleYearV3Service.getYearById(
        'non-existent-year',
        'test-tenant-id',
        mockAuthContext
      );

      expect(result).toBeNull();
    });
  });

  describe('createYear', () => {
    it('should create a new year', async () => {
      const yearData = { name: '2022' };
      const mockCreatedYear = {
        id: 'year-3',
        name: '2022',
        tenantId: 'test-tenant-id',
        isActive: true,
        displayOrder: 3,
        deletedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockServices.prismaService.prisma.vehicleYearV3.aggregate.mockResolvedValue(
        {
          _max: { displayOrder: 2 },
        }
      );
      mockServices.prismaService.prisma.vehicleYearV3.create.mockResolvedValue(
        mockCreatedYear
      );

      const result = await vehicleYearV3Service.createYear(
        yearData,
        'test-tenant-id',
        mockAuthContext
      );

      expect(result).toEqual(mockCreatedYear);
      expect(
        mockServices.prismaService.prisma.vehicleYearV3.create
      ).toHaveBeenCalledWith({
        data: {
          name: '2022',
          tenantId: 'test-tenant-id',
          displayOrder: 3,
        },
      });
    });

    it('should throw ValidationError for empty name', async () => {
      const yearData = { name: '' };

      await expect(
        vehicleYearV3Service.createYear(
          yearData,
          'test-tenant-id',
          mockAuthContext
        )
      ).rejects.toThrow(ValidationError);
    });

    it('should throw ConflictError for duplicate year name', async () => {
      const yearData = { name: '2020' };
      const duplicateError = new PrismaClientKnownRequestError(
        'Unique constraint failed',
        {
          code: 'P2002',
          clientVersion: '5.0.0',
        }
      );

      mockServices.prismaService.prisma.vehicleYearV3.aggregate.mockResolvedValue(
        {
          _max: { displayOrder: 2 },
        }
      );
      mockServices.prismaService.prisma.vehicleYearV3.create.mockRejectedValue(
        duplicateError
      );

      await expect(
        vehicleYearV3Service.createYear(
          yearData,
          'test-tenant-id',
          mockAuthContext
        )
      ).rejects.toThrow(ConflictError);
    });
  });

  describe('updateYear', () => {
    it('should update a year', async () => {
      const updateData = { name: '2020 Updated' };
      const mockUpdatedYear = {
        id: 'year-1',
        name: '2020 Updated',
        tenantId: 'test-tenant-id',
        isActive: true,
        displayOrder: 1,
        deletedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockServices.prismaService.prisma.vehicleYearV3.update.mockResolvedValue(
        mockUpdatedYear
      );

      const result = await vehicleYearV3Service.updateYear(
        'year-1',
        updateData,
        'test-tenant-id',
        mockAuthContext
      );

      expect(result).toEqual(mockUpdatedYear);
      expect(
        mockServices.prismaService.prisma.vehicleYearV3.update
      ).toHaveBeenCalledWith({
        where: {
          id: 'year-1',
          tenantId: 'test-tenant-id',
          deletedAt: null,
        },
        data: { name: '2020 Updated' },
      });
    });

    it('should throw NotFoundError for non-existent year', async () => {
      const updateData = { name: 'Updated Name' };
      const notFoundError = new PrismaClientKnownRequestError(
        'Record not found',
        {
          code: 'P2025',
          clientVersion: '5.0.0',
        }
      );

      mockServices.prismaService.prisma.vehicleYearV3.update.mockRejectedValue(
        notFoundError
      );

      await expect(
        vehicleYearV3Service.updateYear(
          'non-existent-year',
          updateData,
          'test-tenant-id',
          mockAuthContext
        )
      ).rejects.toThrow(NotFoundError);
    });
  });

  describe('deleteYear', () => {
    it('should soft delete a year', async () => {
      const mockUpdatedYear = {
        id: 'year-1',
        name: '2020',
        tenantId: 'test-tenant-id',
        isActive: false,
        displayOrder: 1,
        deletedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockServices.prismaService.prisma.vehicleYearV3.update.mockResolvedValue(
        mockUpdatedYear
      );

      await vehicleYearV3Service.deleteYear(
        'year-1',
        'test-tenant-id',
        mockAuthContext
      );

      expect(
        mockServices.prismaService.prisma.vehicleYearV3.update
      ).toHaveBeenCalledWith({
        where: {
          id: 'year-1',
          tenantId: 'test-tenant-id',
          deletedAt: null,
        },
        data: {
          isActive: false,
          deletedAt: expect.any(Date),
        },
      });
    });
  });

  describe('validateYearOwnership', () => {
    it('should validate year ownership successfully', async () => {
      const mockYear = { id: 'year-1' };
      mockServices.prismaService.prisma.vehicleYearV3.findFirst.mockResolvedValue(
        mockYear
      );

      await expect(
        vehicleYearV3Service.validateYearOwnership('year-1', 'test-tenant-id')
      ).resolves.not.toThrow();
    });

    it('should throw NotFoundError for non-existent year', async () => {
      mockServices.prismaService.prisma.vehicleYearV3.findFirst.mockResolvedValue(
        null
      );

      await expect(
        vehicleYearV3Service.validateYearOwnership(
          'non-existent-year',
          'test-tenant-id'
        )
      ).rejects.toThrow(NotFoundError);
    });
  });
});
