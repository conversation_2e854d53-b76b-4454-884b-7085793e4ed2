import React, { useState, useEffect } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { Modal, Button, Input, FormField, Alert } from "../index";
import { useTypedApi, type VehicleSubBrandV2 } from "../../services/api-client";
import { Tag } from "lucide-react";

interface EditSubBrandV2ModalProps {
  isOpen: boolean;
  onClose: () => void;
  subBrand: VehicleSubBrandV2 | null;
}

export const EditSubBrandV2Modal: React.FC<EditSubBrandV2ModalProps> = ({
  isOpen,
  onClose,
  subBrand,
}) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [name, setName] = useState("");
  const [isActive, setIsActive] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch brands for reference (read-only in edit mode)
  const { data: brandsResponse } = useQuery({
    queryKey: ["vehicle-hierarchy-v2", "brands"],
    queryFn: () => api.vehicleHierarchyV2.getBrands(),
    enabled: isOpen && !!subBrand,
  });

  // Populate form when subBrand changes
  useEffect(() => {
    if (subBrand) {
      setName(subBrand.name);
      setIsActive(subBrand.isActive);
    }
  }, [subBrand]);

  const updateSubBrandMutation = useMutation({
    mutationFn: (subBrandData: { name?: string; isActive?: boolean }) => {
      if (!subBrand) throw new Error("No sub-brand selected");
      return api.vehicleHierarchyV2.updateSubBrand(subBrand.id, subBrandData);
    },
    onSuccess: () => {
      toast.success("Sub-brand updated successfully");
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "sub-brands"],
      });
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "brands"],
      });
      queryClient.invalidateQueries({
        queryKey: ["vehicle-hierarchy-v2", "full"],
      });
      handleClose();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update sub-brand");
    },
  });

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = "Sub-brand name is required";
    } else if (name.trim().length < 2) {
      newErrors.name = "Sub-brand name must be at least 2 characters";
    } else if (name.trim().length > 100) {
      newErrors.name = "Sub-brand name must be less than 100 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Only send changed fields
    const updateData: { name?: string; isActive?: boolean } = {};

    if (name.trim() !== subBrand?.name) {
      updateData.name = name.trim();
    }

    if (isActive !== subBrand?.isActive) {
      updateData.isActive = isActive;
    }

    // If nothing changed, just close
    if (Object.keys(updateData).length === 0) {
      handleClose();
      return;
    }

    updateSubBrandMutation.mutate(updateData);
  };

  const handleClose = () => {
    setName("");
    setIsActive(true);
    setErrors({});
    onClose();
  };

  if (!subBrand) return null;

  const brands = brandsResponse?.data || [];
  const parentBrand =
    brands.find((b) => b.id === subBrand.brandId) || subBrand.brand;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Edit Sub-Brand"
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex items-center space-x-3 text-gray-600 mb-4">
          <Tag className="h-5 w-5" />
          <span>Edit sub-brand details</span>
        </div>

        {parentBrand && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Parent Brand:</strong> {parentBrand.name}
            </p>
            <p className="text-xs text-blue-600 mt-1">
              Note: To change the parent brand, you'll need to create a new
              sub-brand.
            </p>
          </div>
        )}

        <FormField label="Sub-Brand Name" error={errors.name} required>
          <Input
            type="text"
            placeholder="e.g., F-150, Camry, GeoPro"
            value={name}
            onChange={(e) => setName(e.target.value)}
            error={errors.name}
            maxLength={100}
          />
        </FormField>

        <FormField label="Status">
          <div className="flex items-center space-x-3">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="isActive"
                checked={isActive}
                onChange={() => setIsActive(true)}
                className="text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm">Active</span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="isActive"
                checked={!isActive}
                onChange={() => setIsActive(false)}
                className="text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm">Inactive</span>
            </label>
          </div>
        </FormField>

        {updateSubBrandMutation.isError && (
          <Alert variant="error">
            <p>Failed to update sub-brand. Please try again.</p>
          </Alert>
        )}

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={updateSubBrandMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={updateSubBrandMutation.isPending || !name.trim()}
          >
            {updateSubBrandMutation.isPending
              ? "Updating..."
              : "Update Sub-Brand"}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
