import { InvitationStatus, RoleType } from '@prisma/client';

export interface CreateInvitationData {
  email: string;
  tenantId: string;
  roleId: string;
  createdBy: string;
  expiresAt?: Date; // Optional, defaults to 30 days from now
}

export interface InvitationWithDetails {
  id: string;
  clerkInvitationId: string;
  email: string;
  tenantId: string;
  roleId: string;
  createdBy: string;
  usedBy?: string | null;
  usedAt?: Date | null;
  expiresAt: Date;
  status: InvitationStatus;
  createdAt: Date;
  updatedAt: Date;

  // Relations
  tenant: {
    id: string;
    name: string;
    slug: string;
  };
  role: {
    id: string;
    name: string;
    type: RoleType;
  };
  creator: {
    id: string;
    email: string;
    firstName?: string | null;
    lastName?: string | null;
  };
  user?: {
    id: string;
    email: string;
    firstName?: string | null;
    lastName?: string | null;
  } | null;
}

export interface InvitationListItem {
  id: string;
  email: string;
  status: InvitationStatus;
  expiresAt: Date;
  createdAt: Date;
  role: {
    name: string;
    type: RoleType;
  };
  creator: {
    email: string;
    firstName?: string | null;
    lastName?: string | null;
  };
  user?: {
    email: string;
    firstName?: string | null;
    lastName?: string | null;
  } | null;
}

export interface CreateTenantWithAdminData {
  tenantName: string;
  tenantSlug: string;
  adminEmail: string;
  adminFirstName?: string;
  adminLastName?: string;
}

export interface InviteUserData {
  email: string;
  roleType: RoleType;
  firstName?: string;
  lastName?: string;
}

export interface ClerkInvitationResponse {
  id: string;
  email_address: string;
  public_metadata: Record<string, unknown>;
  created_at: number;
  updated_at: number;
  status: 'pending' | 'accepted' | 'revoked';
  url?: string;
}

export interface AcceptInvitationData {
  invitationToken: string;
  firstName?: string;
  lastName?: string;
  password?: string;
}
