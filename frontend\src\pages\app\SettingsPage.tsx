import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import {
  Card,
  CardHeader,
  CardContent,
  Alert,
  Badge,
  <PERSON>ton,
  Modal,
  LoadingSpinner,
  SystemAdminOnly,
  CompanyAdminOnly,
} from "../../components";
import { useAuth } from "../../hooks/useAuth";
import { usePermissions } from "../../hooks/usePermissions";
import { useTypedApi } from "../../services/api-client";
import type {
  Tenant,
  CreateTenantData,
  UpdateTenantData,
  ApiError,
} from "../../services/api-client";
import { Building2, Edit, Trash2, Plus, Save } from "lucide-react";

// Tenant Form Component
interface TenantFormProps {
  tenant?: Tenant;
  onSubmit: (data: CreateTenantData) => void;
  onCancel: () => void;
  isLoading?: boolean;
  isEdit?: boolean;
}

const TenantForm: React.FC<TenantFormProps> = ({
  tenant,
  onSubmit,
  onCancel,
  isLoading = false,
  isEdit = false,
}) => {
  const [formData, setFormData] = useState({
    name: tenant?.name || "",
    slug: tenant?.slug || "",
    isActive: tenant?.isActive ?? true,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim() || !formData.slug.trim()) {
      toast.error("Name and slug are required");
      return;
    }
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Tenant Name *
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          placeholder="Enter tenant name"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Tenant Slug *
        </label>
        <input
          type="text"
          value={formData.slug}
          onChange={(e) =>
            setFormData({
              ...formData,
              slug: e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, ""),
            })
          }
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          placeholder="tenant-slug"
          pattern="[a-z0-9-]+"
          required
        />
        <p className="text-xs text-gray-500 mt-1">
          Only lowercase letters, numbers, and hyphens allowed
        </p>
      </div>

      {isEdit && (
        <div className="flex items-center">
          <input
            type="checkbox"
            id="isActive"
            checked={formData.isActive}
            onChange={(e) =>
              setFormData({ ...formData, isActive: e.target.checked })
            }
            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <label
            htmlFor="isActive"
            className="ml-2 block text-sm text-gray-700"
          >
            Active tenant
          </label>
        </div>
      )}

      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
          className="flex items-center space-x-2"
        >
          {isLoading ? (
            <LoadingSpinner size="sm" />
          ) : (
            <Save className="h-4 w-4" />
          )}
          <span>{isEdit ? "Update" : "Create"} Tenant</span>
        </Button>
      </div>
    </form>
  );
};

export const SettingsPage: React.FC = () => {
  const { userProfile, isLoading: authLoading } = useAuth();
  const { isSystemAdmin, isCompanyAdmin } = usePermissions();
  const api = useTypedApi();
  const queryClient = useQueryClient();

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingTenant, setEditingTenant] = useState<Tenant | null>(null);
  const [deletingTenant, setDeletingTenant] = useState<Tenant | null>(null);

  // Fetch tenants data
  const {
    data: tenantsResponse,
    isLoading: isLoadingTenants,
    error: tenantsError,
  } = useQuery({
    queryKey: ["tenants"],
    queryFn: () => api.tenants.getAll(),
    enabled: isSystemAdmin || isCompanyAdmin,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Get current tenant for non-system admins
  const currentTenant =
    tenantsResponse?.data.find((t) => t.id === userProfile?.tenantId) ||
    tenantsResponse?.data[0];

  // Mutations
  const createTenantMutation = useMutation({
    mutationFn: (data: CreateTenantData) => api.tenants.create(data),
    onSuccess: () => {
      toast.success("Tenant created successfully");
      queryClient.invalidateQueries({ queryKey: ["tenants"] });
      setIsCreateModalOpen(false);
    },
    onError: (error: Error & Partial<ApiError>) => {
      toast.error(error.message || "Failed to create tenant");
    },
  });

  const updateTenantMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTenantData }) =>
      api.tenants.update(id, data),
    onSuccess: () => {
      toast.success("Tenant updated successfully");
      queryClient.invalidateQueries({ queryKey: ["tenants"] });
      setEditingTenant(null);
    },
    onError: (error: Error & Partial<ApiError>) => {
      toast.error(error.message || "Failed to update tenant");
    },
  });

  const deleteTenantMutation = useMutation({
    mutationFn: (tenantId: string) => api.tenants.delete(tenantId),
    onSuccess: () => {
      toast.success("Tenant deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["tenants"] });
      setDeletingTenant(null);
    },
    onError: (error: Error & Partial<ApiError>) => {
      toast.error(error.message || "Failed to delete tenant");
    },
  });

  // Event handlers
  const handleCreateTenant = (data: CreateTenantData) => {
    createTenantMutation.mutate(data);
  };

  const handleUpdateTenant = (data: UpdateTenantData) => {
    if (editingTenant) {
      updateTenantMutation.mutate({ id: editingTenant.id, data });
    }
  };

  const handleDeleteTenant = () => {
    if (deletingTenant) {
      deleteTenantMutation.mutate(deletingTenant.id);
    }
  };

  if (authLoading) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Tenant Settings</h1>
          <p className="text-gray-600 mt-1">
            Manage tenant configuration and settings
          </p>
        </div>

        {/* System Admin: Create Tenant Button */}
        <SystemAdminOnly>
          <Button
            onClick={() => setIsCreateModalOpen(true)}
            className="flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Create Tenant</span>
          </Button>
        </SystemAdminOnly>
      </div>

      {/* Error Display */}
      {tenantsError && (
        <Alert variant="error">
          <p>
            Failed to load tenant information: {(tenantsError as Error).message}
          </p>
        </Alert>
      )}

      {/* Loading State */}
      {isLoadingTenants && (
        <div className="space-y-4">
          <div className="animate-pulse">
            <div className="h-32 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      )}

      {/* Current Tenant Information */}
      {!isLoadingTenants && currentTenant && (
        <Card>
          <CardHeader title="Current Tenant Information" />
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Tenant Name
                </label>
                <p className="mt-1 text-sm text-gray-900">
                  {currentTenant.name}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Tenant Slug
                </label>
                <code className="mt-1 text-xs bg-gray-100 px-2 py-1 rounded block">
                  {currentTenant.slug}
                </code>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Status
                </label>
                <div className="mt-1">
                  <Badge
                    variant={currentTenant.isActive ? "success" : "warning"}
                  >
                    {currentTenant.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Created
                </label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(currentTenant.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>

            {/* Company Admin: Edit Current Tenant */}
            <CompanyAdminOnly>
              <div className="pt-4 border-t border-gray-200">
                <Button
                  variant="outline"
                  onClick={() => setEditingTenant(currentTenant)}
                  className="flex items-center space-x-2"
                >
                  <Edit className="h-4 w-4" />
                  <span>Edit Tenant</span>
                </Button>
              </div>
            </CompanyAdminOnly>
          </CardContent>
        </Card>
      )}

      {/* System Admin: All Tenants Management */}
      <SystemAdminOnly>
        {!isLoadingTenants &&
          tenantsResponse &&
          tenantsResponse.data.length > 1 && (
            <Card>
              <CardHeader title="All Tenants" />
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-gray-600">
                    Manage all tenants in the system
                  </p>

                  <div className="space-y-3">
                    {tenantsResponse.data.map((tenant) => (
                      <div
                        key={tenant.id}
                        className="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
                      >
                        <div className="flex items-center space-x-4">
                          <Building2 className="h-5 w-5 text-gray-400" />
                          <div>
                            <h3 className="font-medium text-gray-900">
                              {tenant.name}
                            </h3>
                            <p className="text-sm text-gray-500">
                              {tenant.slug}
                            </p>
                          </div>
                          <Badge
                            variant={tenant.isActive ? "success" : "warning"}
                          >
                            {tenant.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingTenant(tenant)}
                            className="flex items-center space-x-1"
                          >
                            <Edit className="h-4 w-4" />
                            <span>Edit</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setDeletingTenant(tenant)}
                            className="flex items-center space-x-1 text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                            <span>Delete</span>
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
      </SystemAdminOnly>

      {/* Create Tenant Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create New Tenant"
        size="md"
      >
        <TenantForm
          onSubmit={handleCreateTenant}
          onCancel={() => setIsCreateModalOpen(false)}
          isLoading={createTenantMutation.isPending}
        />
      </Modal>

      {/* Edit Tenant Modal */}
      <Modal
        isOpen={!!editingTenant}
        onClose={() => setEditingTenant(null)}
        title={`Edit Tenant: ${editingTenant?.name}`}
        size="md"
      >
        {editingTenant && (
          <TenantForm
            tenant={editingTenant}
            onSubmit={handleUpdateTenant}
            onCancel={() => setEditingTenant(null)}
            isLoading={updateTenantMutation.isPending}
            isEdit
          />
        )}
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!deletingTenant}
        onClose={() => setDeletingTenant(null)}
        title="Delete Tenant"
        size="sm"
      >
        {deletingTenant && (
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                  <Trash2 className="h-5 w-5 text-red-600" />
                </div>
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  Delete "{deletingTenant.name}"?
                </h3>
                <p className="text-sm text-gray-500">
                  This action cannot be undone. All data associated with this
                  tenant will be permanently deleted.
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <Button
                variant="outline"
                onClick={() => setDeletingTenant(null)}
                disabled={deleteTenantMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={handleDeleteTenant}
                disabled={deleteTenantMutation.isPending}
                className="flex items-center space-x-2"
              >
                {deleteTenantMutation.isPending ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
                <span>Delete Tenant</span>
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};
